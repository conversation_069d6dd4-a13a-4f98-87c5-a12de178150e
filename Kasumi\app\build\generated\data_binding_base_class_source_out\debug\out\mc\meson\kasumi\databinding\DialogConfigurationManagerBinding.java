// Generated by view binder compiler. Do not edit!
package mc.meson.kasumi.databinding;

import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.LinearLayout;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.recyclerview.widget.RecyclerView;
import androidx.viewbinding.ViewBinding;
import androidx.viewbinding.ViewBindings;
import com.google.android.material.button.MaterialButton;
import java.lang.NullPointerException;
import java.lang.Override;
import java.lang.String;
import mc.meson.kasumi.R;

public final class DialogConfigurationManagerBinding implements ViewBinding {
  @NonNull
  private final LinearLayout rootView;

  @NonNull
  public final MaterialButton btnCloseDialog;

  @NonNull
  public final MaterialButton btnExportConfig;

  @NonNull
  public final MaterialButton btnImportConfig;

  @NonNull
  public final RecyclerView rvConfigurations;

  private DialogConfigurationManagerBinding(@NonNull LinearLayout rootView,
      @NonNull MaterialButton btnCloseDialog, @NonNull MaterialButton btnExportConfig,
      @NonNull MaterialButton btnImportConfig, @NonNull RecyclerView rvConfigurations) {
    this.rootView = rootView;
    this.btnCloseDialog = btnCloseDialog;
    this.btnExportConfig = btnExportConfig;
    this.btnImportConfig = btnImportConfig;
    this.rvConfigurations = rvConfigurations;
  }

  @Override
  @NonNull
  public LinearLayout getRoot() {
    return rootView;
  }

  @NonNull
  public static DialogConfigurationManagerBinding inflate(@NonNull LayoutInflater inflater) {
    return inflate(inflater, null, false);
  }

  @NonNull
  public static DialogConfigurationManagerBinding inflate(@NonNull LayoutInflater inflater,
      @Nullable ViewGroup parent, boolean attachToParent) {
    View root = inflater.inflate(R.layout.dialog_configuration_manager, parent, false);
    if (attachToParent) {
      parent.addView(root);
    }
    return bind(root);
  }

  @NonNull
  public static DialogConfigurationManagerBinding bind(@NonNull View rootView) {
    // The body of this method is generated in a way you would not otherwise write.
    // This is done to optimize the compiled bytecode for size and performance.
    int id;
    missingId: {
      id = R.id.btnCloseDialog;
      MaterialButton btnCloseDialog = ViewBindings.findChildViewById(rootView, id);
      if (btnCloseDialog == null) {
        break missingId;
      }

      id = R.id.btnExportConfig;
      MaterialButton btnExportConfig = ViewBindings.findChildViewById(rootView, id);
      if (btnExportConfig == null) {
        break missingId;
      }

      id = R.id.btnImportConfig;
      MaterialButton btnImportConfig = ViewBindings.findChildViewById(rootView, id);
      if (btnImportConfig == null) {
        break missingId;
      }

      id = R.id.rvConfigurations;
      RecyclerView rvConfigurations = ViewBindings.findChildViewById(rootView, id);
      if (rvConfigurations == null) {
        break missingId;
      }

      return new DialogConfigurationManagerBinding((LinearLayout) rootView, btnCloseDialog,
          btnExportConfig, btnImportConfig, rvConfigurations);
    }
    String missingId = rootView.getResources().getResourceName(id);
    throw new NullPointerException("Missing required view with ID: ".concat(missingId));
  }
}
