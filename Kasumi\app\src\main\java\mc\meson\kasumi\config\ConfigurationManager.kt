package mc.meson.kasumi.config

import android.content.Context
import android.util.Log
import kotlinx.serialization.json.Json
import kotlinx.serialization.encodeToString
import kotlinx.serialization.decodeFromString
import mc.meson.kasumi.module.ModuleRegistry
import mc.meson.kasumi.module.ModuleSettingsManager
import java.io.File
import java.io.FileInputStream
import java.io.FileOutputStream
import java.io.IOException

/**
 * 配置管理器
 * 负责配置的保存、加载、导入、导出等核心功能
 */
object ConfigurationManager {
    private const val TAG = "ConfigurationManager"
    private const val CONFIG_FILE_NAME = "kasumi_configurations.json"
    private const val CONFIG_DIR_NAME = "configurations"
    
    private var context: Context? = null
    private var configurationList = ConfigurationList()
    private val json = Json { 
        prettyPrint = true
        ignoreUnknownKeys = true
    }
    
    /**
     * 初始化配置管理器
     */
    fun init(context: Context) {
        this.context = context
        loadConfigurations()
        
        // 如果没有配置，创建默认配置
        if (configurationList.configurations.isEmpty()) {
            createDefaultConfiguration()
        }
    }
    
    /**
     * 获取所有配置
     */
    fun getAllConfigurations(): List<Configuration> {
        return configurationList.configurations.toList()
    }
    
    /**
     * 获取当前配置
     */
    fun getCurrentConfiguration(): Configuration? {
        return configurationList.getCurrentConfiguration()
    }
    
    /**
     * 创建新配置
     */
    fun createConfiguration(name: String, description: String = ""): Configuration {
        val currentStates = getCurrentModuleStates()
        val currentSettings = getCurrentModuleSettings()
        
        val config = Configuration(
            name = name,
            description = description,
            moduleStates = currentStates,
            moduleSettings = currentSettings
        )
        
        configurationList = configurationList.addConfiguration(config)
        saveConfigurations()
        
        Log.d(TAG, "Created configuration: $name")
        return config
    }
    
    /**
     * 删除配置
     */
    fun deleteConfiguration(configId: String): Boolean {
        val config = configurationList.configurations.find { it.id == configId }
        if (config?.isDefault == true) {
            Log.w(TAG, "Cannot delete default configuration")
            return false
        }
        
        configurationList = configurationList.removeConfiguration(configId)
        saveConfigurations()
        
        Log.d(TAG, "Deleted configuration: $configId")
        return true
    }
    
    /**
     * 更新配置
     */
    fun updateConfiguration(config: Configuration) {
        configurationList = configurationList.updateConfiguration(config)
        saveConfigurations()
        
        Log.d(TAG, "Updated configuration: ${config.name}")
    }
    
    /**
     * 应用配置
     */
    fun applyConfiguration(configId: String): Boolean {
        val config = configurationList.configurations.find { it.id == configId }
        if (config == null) {
            Log.e(TAG, "Configuration not found: $configId")
            return false
        }
        
        // 应用模块状态
        config.moduleStates.forEach { (moduleId, enabled) ->
            val module = ModuleRegistry.getModule(moduleId)
            module?.isEnabled = enabled
        }
        
        // 应用模块设置
        config.moduleSettings.forEach { (moduleId, settings) ->
            settings.forEach { (settingKey, configValue) ->
                when (configValue) {
                    is ConfigValue.BooleanValue -> 
                        ModuleSettingsManager.setBooleanSetting(moduleId, settingKey, configValue.value)
                    is ConfigValue.IntValue -> 
                        ModuleSettingsManager.setIntSetting(moduleId, settingKey, configValue.value)
                    is ConfigValue.FloatValue -> 
                        ModuleSettingsManager.setFloatSetting(moduleId, settingKey, configValue.value)
                    is ConfigValue.StringValue -> {
                        // 字符串设置需要根据实际类型处理
                        // 这里暂时跳过，后续可以扩展
                    }
                }
            }
        }
        
        configurationList = configurationList.setCurrentConfiguration(configId)
        saveConfigurations()
        
        Log.d(TAG, "Applied configuration: ${config.name}")
        return true
    }
    
    /**
     * 保存当前状态到配置
     */
    fun saveCurrentStateToConfiguration(configId: String): Boolean {
        val config = configurationList.configurations.find { it.id == configId }
        if (config == null) {
            Log.e(TAG, "Configuration not found: $configId")
            return false
        }
        
        val updatedConfig = config.copy(
            moduleStates = getCurrentModuleStates(),
            moduleSettings = getCurrentModuleSettings()
        )
        
        updateConfiguration(updatedConfig)
        return true
    }
    
    /**
     * 获取当前模块状态
     */
    private fun getCurrentModuleStates(): Map<String, Boolean> {
        return ModuleRegistry.getAllModules().associate { module ->
            module.id to module.isEnabled
        }
    }
    
    /**
     * 获取当前模块设置
     */
    private fun getCurrentModuleSettings(): Map<String, Map<String, ConfigValue>> {
        val result = mutableMapOf<String, Map<String, ConfigValue>>()
        
        ModuleRegistry.getAllModules().forEach { module ->
            val moduleSettings = mutableMapOf<String, ConfigValue>()
            
            module.settings.forEach { setting ->
                val value = when (setting) {
                    is mc.meson.kasumi.module.ToggleSetting -> {
                        val boolValue = ModuleSettingsManager.getBooleanSetting(module.id, setting.key, setting.defaultValue)
                        ConfigValue.BooleanValue(boolValue)
                    }
                    is mc.meson.kasumi.module.ModeSetting -> {
                        val intValue = ModuleSettingsManager.getIntSetting(module.id, setting.key, setting.defaultIndex)
                        ConfigValue.IntValue(intValue)
                    }
                    is mc.meson.kasumi.module.SliderSetting -> {
                        val floatValue = ModuleSettingsManager.getFloatSetting(module.id, setting.key, setting.defaultValue)
                        ConfigValue.FloatValue(floatValue)
                    }
                    else -> null
                }
                
                value?.let { moduleSettings[setting.key] = it }
            }
            
            if (moduleSettings.isNotEmpty()) {
                result[module.id] = moduleSettings
            }
        }
        
        return result
    }
    
    /**
     * 创建默认配置
     */
    private fun createDefaultConfiguration() {
        val defaultConfig = Configuration(
            name = "默认配置",
            description = "系统默认配置",
            isDefault = true,
            moduleStates = getCurrentModuleStates(),
            moduleSettings = getCurrentModuleSettings()
        )
        
        configurationList = configurationList.addConfiguration(defaultConfig)
        configurationList = configurationList.setCurrentConfiguration(defaultConfig.id)
        saveConfigurations()
        
        Log.d(TAG, "Created default configuration")
    }
    
    /**
     * 保存配置到文件
     */
    private fun saveConfigurations() {
        context?.let { ctx ->
            try {
                val configFile = File(ctx.filesDir, CONFIG_FILE_NAME)
                val jsonString = json.encodeToString(configurationList)
                
                FileOutputStream(configFile).use { fos ->
                    fos.write(jsonString.toByteArray())
                }
                
                Log.d(TAG, "Configurations saved successfully")
            } catch (e: Exception) {
                Log.e(TAG, "Failed to save configurations", e)
            }
        }
    }
    
    /**
     * 从文件加载配置
     */
    private fun loadConfigurations() {
        context?.let { ctx ->
            try {
                val configFile = File(ctx.filesDir, CONFIG_FILE_NAME)
                if (!configFile.exists()) {
                    Log.d(TAG, "Configuration file not found, using empty list")
                    return
                }
                
                FileInputStream(configFile).use { fis ->
                    val jsonString = fis.readBytes().toString(Charsets.UTF_8)
                    configurationList = json.decodeFromString(jsonString)
                }
                
                Log.d(TAG, "Configurations loaded successfully: ${configurationList.configurations.size} configs")
            } catch (e: Exception) {
                Log.e(TAG, "Failed to load configurations", e)
                configurationList = ConfigurationList()
            }
        }
    }

    /**
     * 导出配置到文件
     */
    fun exportConfiguration(config: Configuration, outputFile: File): Boolean {
        return try {
            val jsonString = json.encodeToString(config)
            FileOutputStream(outputFile).use { fos ->
                fos.write(jsonString.toByteArray())
            }
            Log.d(TAG, "Configuration exported successfully: ${config.name}")
            true
        } catch (e: Exception) {
            Log.e(TAG, "Failed to export configuration", e)
            false
        }
    }

    /**
     * 从文件导入配置
     */
    fun importConfiguration(inputFile: File): Configuration? {
        return try {
            FileInputStream(inputFile).use { fis ->
                val jsonString = fis.readBytes().toString(Charsets.UTF_8)
                val config = json.decodeFromString<Configuration>(jsonString)

                // 生成新的ID避免冲突
                val newConfig = config.copy(
                    id = java.util.UUID.randomUUID().toString(),
                    name = "${config.name} (导入)",
                    isDefault = false
                )

                configurationList = configurationList.addConfiguration(newConfig)
                saveConfigurations()

                Log.d(TAG, "Configuration imported successfully: ${newConfig.name}")
                newConfig
            }
        } catch (e: Exception) {
            Log.e(TAG, "Failed to import configuration", e)
            null
        }
    }

    /**
     * 导出所有配置
     */
    fun exportAllConfigurations(outputFile: File): Boolean {
        return try {
            val jsonString = json.encodeToString(configurationList)
            FileOutputStream(outputFile).use { fos ->
                fos.write(jsonString.toByteArray())
            }
            Log.d(TAG, "All configurations exported successfully")
            true
        } catch (e: Exception) {
            Log.e(TAG, "Failed to export all configurations", e)
            false
        }
    }

    /**
     * 导入所有配置
     */
    fun importAllConfigurations(inputFile: File): Boolean {
        return try {
            FileInputStream(inputFile).use { fis ->
                val jsonString = fis.readBytes().toString(Charsets.UTF_8)
                val importedList = json.decodeFromString<ConfigurationList>(jsonString)

                // 合并配置，避免ID冲突
                importedList.configurations.forEach { config ->
                    val newConfig = config.copy(
                        id = java.util.UUID.randomUUID().toString(),
                        name = "${config.name} (导入)",
                        isDefault = false
                    )
                    configurationList = configurationList.addConfiguration(newConfig)
                }

                saveConfigurations()
                Log.d(TAG, "All configurations imported successfully")
                true
            }
        } catch (e: Exception) {
            Log.e(TAG, "Failed to import all configurations", e)
            false
        }
    }
}
