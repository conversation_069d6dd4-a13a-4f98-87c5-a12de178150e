// Generated by view binder compiler. Do not edit!
package mc.meson.kasumi.databinding;

import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.LinearLayout;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.viewbinding.ViewBinding;
import androidx.viewbinding.ViewBindings;
import com.google.android.material.button.MaterialButton;
import com.google.android.material.textfield.TextInputEditText;
import java.lang.NullPointerException;
import java.lang.Override;
import java.lang.String;
import mc.meson.kasumi.R;

public final class DialogCreateConfigurationBinding implements ViewBinding {
  @NonNull
  private final LinearLayout rootView;

  @NonNull
  public final MaterialButton btnCancel;

  @NonNull
  public final MaterialButton btnCreate;

  @NonNull
  public final TextInputEditText etConfigDescription;

  @NonNull
  public final TextInputEditText etConfigName;

  private DialogCreateConfigurationBinding(@NonNull LinearLayout rootView,
      @NonNull MaterialButton btnCancel, @NonNull MaterialButton btnCreate,
      @NonNull TextInputEditText etConfigDescription, @NonNull TextInputEditText etConfigName) {
    this.rootView = rootView;
    this.btnCancel = btnCancel;
    this.btnCreate = btnCreate;
    this.etConfigDescription = etConfigDescription;
    this.etConfigName = etConfigName;
  }

  @Override
  @NonNull
  public LinearLayout getRoot() {
    return rootView;
  }

  @NonNull
  public static DialogCreateConfigurationBinding inflate(@NonNull LayoutInflater inflater) {
    return inflate(inflater, null, false);
  }

  @NonNull
  public static DialogCreateConfigurationBinding inflate(@NonNull LayoutInflater inflater,
      @Nullable ViewGroup parent, boolean attachToParent) {
    View root = inflater.inflate(R.layout.dialog_create_configuration, parent, false);
    if (attachToParent) {
      parent.addView(root);
    }
    return bind(root);
  }

  @NonNull
  public static DialogCreateConfigurationBinding bind(@NonNull View rootView) {
    // The body of this method is generated in a way you would not otherwise write.
    // This is done to optimize the compiled bytecode for size and performance.
    int id;
    missingId: {
      id = R.id.btnCancel;
      MaterialButton btnCancel = ViewBindings.findChildViewById(rootView, id);
      if (btnCancel == null) {
        break missingId;
      }

      id = R.id.btnCreate;
      MaterialButton btnCreate = ViewBindings.findChildViewById(rootView, id);
      if (btnCreate == null) {
        break missingId;
      }

      id = R.id.etConfigDescription;
      TextInputEditText etConfigDescription = ViewBindings.findChildViewById(rootView, id);
      if (etConfigDescription == null) {
        break missingId;
      }

      id = R.id.etConfigName;
      TextInputEditText etConfigName = ViewBindings.findChildViewById(rootView, id);
      if (etConfigName == null) {
        break missingId;
      }

      return new DialogCreateConfigurationBinding((LinearLayout) rootView, btnCancel, btnCreate,
          etConfigDescription, etConfigName);
    }
    String missingId = rootView.getResources().getResourceName(id);
    throw new NullPointerException("Missing required view with ID: ".concat(missingId));
  }
}
