package mc.meson.kasumi

import org.junit.Test
import org.junit.Assert.*
import mc.meson.kasumi.config.ConfigData
import mc.meson.kasumi.config.ConfigImportExport
import mc.meson.kasumi.config.ImportResult

/**
 * 配置系统测试
 */
class ConfigSystemTest {

    @Test
    fun testConfigDataCreation() {
        val config = ConfigData(
            name = "测试配置",
            description = "这是一个测试配置",
            moduleStates = mapOf("test_module" to true),
            moduleSettings = mapOf("test_module" to mapOf("test_setting" to 42))
        )
        
        assertEquals("测试配置", config.name)
        assertEquals("这是一个测试配置", config.description)
        assertTrue(config.moduleStates["test_module"] == true)
        assertEquals(42, config.moduleSettings["test_module"]?.get("test_setting"))
        assertEquals(1, config.getEnabledModuleCount())
    }
    
    @Test
    fun testConfigValidation() {
        val validConfig = ConfigData(
            name = "有效配置",
            description = "有效的配置"
        )
        
        val invalidConfig = ConfigData(
            name = "",
            description = "无效配置"
        )
        
        assertTrue(ConfigImportExport.validateConfigData(validConfig))
        assertFalse(ConfigImportExport.validateConfigData(invalidConfig))
    }
    
    @Test
    fun testConfigFileName() {
        val config = ConfigData(
            name = "测试配置/特殊字符",
            description = "测试文件名生成"
        )
        
        val fileName = ConfigImportExport.generateExportFileName(config)
        assertFalse(fileName.contains("/"))
        assertTrue(fileName.endsWith(".json"))
        assertTrue(fileName.contains("kasumi_config"))
    }
    
    @Test
    fun testBatchExportFileName() {
        val fileName = ConfigImportExport.generateBatchExportFileName()
        assertTrue(fileName.contains("kasumi_configs"))
        assertTrue(fileName.endsWith(".json"))
    }
}
