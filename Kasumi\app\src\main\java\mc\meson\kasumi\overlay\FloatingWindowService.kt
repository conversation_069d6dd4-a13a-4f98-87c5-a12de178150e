package mc.meson.kasumi.overlay

import android.app.Notification
import android.app.NotificationChannel
import android.app.NotificationManager
import android.app.PendingIntent
import android.app.Service
import android.content.Context
import android.content.Intent
import android.graphics.PixelFormat
import android.os.Build
import android.os.IBinder
import android.provider.Settings
import android.util.Log
import android.view.Gravity
import android.view.LayoutInflater
import android.view.MotionEvent
import android.view.View
import android.view.WindowManager
import android.view.animation.AccelerateDecelerateInterpolator
import android.view.animation.OvershootInterpolator
import android.view.animation.DecelerateInterpolator
import androidx.core.app.NotificationCompat
import androidx.recyclerview.widget.LinearLayoutManager
import androidx.recyclerview.widget.GridLayoutManager
import androidx.recyclerview.widget.RecyclerView
import androidx.viewpager2.widget.ViewPager2
import com.google.android.material.tabs.TabLayout
import android.widget.Switch
import android.widget.ScrollView
import android.widget.TextView
import android.widget.LinearLayout
import mc.meson.kasumi.module.KasumiModule
import mc.meson.kasumi.module.ModuleAdapter
import mc.meson.kasumi.module.ModuleCategory
import mc.meson.kasumi.module.ModuleRegistry
import mc.meson.kasumi.module.ModuleSettingsAdapter
import android.widget.ImageView
import android.widget.FrameLayout
import android.view.GestureDetector
import android.view.VelocityTracker
import kotlin.math.abs
import mc.meson.kasumi.MainActivity
import mc.meson.kasumi.R

class FloatingWindowService : Service() {

    private lateinit var windowManager: WindowManager
    private var floatingView: View? = null
    private var isExpanded = false
    private var isCompactMode = true // true=精简模式, false=展开模式
    private var currentCategory = ModuleCategory.COMBAT
    private var initialX = 0
    private var initialY = 0
    private var initialTouchX = 0f
    private var initialTouchY = 0f

    // 适配器
    private var compactAdapter: ModuleAdapter? = null
    private var expandedAdapter: ModuleAdapter? = null

    // 存储当前显示的设置面板
    private var currentSettingsPanel: View? = null

    // 手势检测
    private var gestureDetector: GestureDetector? = null

    companion object {
        private const val TAG = "FloatingWindowService"
        private const val NOTIFICATION_ID = 1001
        private const val CHANNEL_ID = "floating_window_channel"

        fun startService(context: Context) {
            try {
                if (canDrawOverlays(context)) {
                    Log.d(TAG, "Starting floating window service")
                    val intent = Intent(context, FloatingWindowService::class.java)
                    context.startForegroundService(intent)
                } else {
                    Log.w(TAG, "Cannot start service: overlay permission not granted")
                }
            } catch (e: Exception) {
                Log.e(TAG, "Failed to start floating window service", e)
            }
        }

        fun stopService(context: Context) {
            try {
                Log.d(TAG, "Stopping floating window service")
                val intent = Intent(context, FloatingWindowService::class.java)
                context.stopService(intent)
            } catch (e: Exception) {
                Log.e(TAG, "Failed to stop floating window service", e)
            }
        }

        fun canDrawOverlays(context: Context): Boolean {
            return try {
                if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.M) {
                    Settings.canDrawOverlays(context)
                } else {
                    true
                }
            } catch (e: Exception) {
                Log.e(TAG, "Failed to check overlay permission", e)
                false
            }
        }
    }

    override fun onCreate() {
        super.onCreate()
        try {
            Log.d(TAG, "FloatingWindowService onCreate")

            // 再次检查权限
            if (!canDrawOverlays(this)) {
                Log.e(TAG, "Overlay permission not granted, stopping service")
                stopSelf()
                return
            }

            windowManager = getSystemService(WINDOW_SERVICE) as WindowManager
            KasumiModule.init(this)
            createNotificationChannel()
            showFloatingWindow()

            Log.d(TAG, "FloatingWindowService created successfully")
        } catch (e: Exception) {
            Log.e(TAG, "Failed to create FloatingWindowService", e)
            stopSelf()
        }
    }

    override fun onStartCommand(intent: Intent?, flags: Int, startId: Int): Int {
        try {
            Log.d(TAG, "FloatingWindowService onStartCommand")
            startForeground(NOTIFICATION_ID, createNotification())
            return START_STICKY
        } catch (e: Exception) {
            Log.e(TAG, "Failed to start foreground service", e)
            stopSelf()
            return START_NOT_STICKY
        }
    }

    override fun onBind(intent: Intent?): IBinder? = null

    override fun onDestroy() {
        super.onDestroy()
        try {
            Log.d(TAG, "FloatingWindowService onDestroy")
            removeFloatingWindow()
        } catch (e: Exception) {
            Log.e(TAG, "Error during service destruction", e)
        }
    }

    private fun createNotificationChannel() {
        try {
            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O) {
                val channel = NotificationChannel(
                    CHANNEL_ID,
                    "悬浮窗服务",
                    NotificationManager.IMPORTANCE_LOW
                ).apply {
                    description = "Kasumi悬浮窗服务正在运行"
                    setShowBadge(false)
                }

                val notificationManager = getSystemService(NotificationManager::class.java)
                notificationManager.createNotificationChannel(channel)
                Log.d(TAG, "Notification channel created")
            }
        } catch (e: Exception) {
            Log.e(TAG, "Failed to create notification channel", e)
        }
    }

    private fun createNotification(): Notification {
        val intent = Intent(this, MainActivity::class.java)
        val pendingIntent = PendingIntent.getActivity(
            this, 0, intent,
            PendingIntent.FLAG_UPDATE_CURRENT or PendingIntent.FLAG_IMMUTABLE
        )

        return NotificationCompat.Builder(this, CHANNEL_ID)
            .setContentTitle("Kasumi Hack")
            .setContentText("悬浮窗正在运行")
            .setSmallIcon(R.drawable.ic_kasumi_logo)
            .setContentIntent(pendingIntent)
            .setOngoing(true)
            .setSilent(true)
            .build()
    }

    private fun showFloatingWindow() {
        try {
            if (floatingView != null) {
                Log.d(TAG, "Floating window already exists")
                return
            }

            Log.d(TAG, "Creating floating window")

            // 将dp转换为像素 - 初始状态为收起状态的尺寸
            val density = resources.displayMetrics.density
            val collapsedSize = (40 * density).toInt() // 40dp 更小更精致

            val layoutParams = WindowManager.LayoutParams(
                collapsedSize, // 初始为收起状态的尺寸
                collapsedSize,
                if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O) {
                    WindowManager.LayoutParams.TYPE_APPLICATION_OVERLAY
                } else {
                    WindowManager.LayoutParams.TYPE_PHONE
                },
                WindowManager.LayoutParams.FLAG_NOT_FOCUSABLE,
                PixelFormat.TRANSLUCENT
            ).apply {
                gravity = Gravity.TOP or Gravity.START
                x = 100
                y = 100
            }

            // 创建精简模式视图
            floatingView = createCompactView()
            if (floatingView != null) {
                // 先添加到 WindowManager
                windowManager.addView(floatingView, layoutParams)
                
                // 然后设置初始状态为收起（只显示圆点）
                collapseToIcon(false)
                
                setupDragListeners(floatingView!!)
                Log.d(TAG, "Floating window created successfully")
            } else {
                Log.e(TAG, "Failed to create floating view")
            }
        } catch (e: Exception) {
            Log.e(TAG, "Failed to show floating window", e)
            // 如果创建悬浮窗失败，停止服务
            stopSelf()
        }
    }

    private fun createCompactView(): View? {
        return try {
            Log.d(TAG, "Creating compact view with ViewPager2")

            val themedContext = android.view.ContextThemeWrapper(this, R.style.Theme_Kasumi_FloatingWindow)
            val view = LayoutInflater.from(themedContext).inflate(R.layout.floating_window_compact_v2, null)

            setupCompactViewListeners(view)
            setupCompactViewPager(view)
            updateCategoryDisplay()

            view
        } catch (e: Exception) {
            Log.e(TAG, "Failed to create compact view", e)
            null
        }
    }

    private fun createExpandedView(): View? {
        return try {
            Log.d(TAG, "Creating expanded view")

            // 创建带有正确主题的 ContextThemeWrapper
            val themedContext = android.view.ContextThemeWrapper(this, R.style.Theme_Kasumi_FloatingWindow)
            val view = LayoutInflater.from(themedContext).inflate(R.layout.floating_window_expanded_new, null)

            // 设置展开模式的监听器和适配器
            setupExpandedViewListeners(view)
            setupExpandedRecyclerView(view)
            setupExpandedTabLayout(view)

            // 功能开关监听器已集成到ModuleAdapter中

            Log.d(TAG, "Expanded view created successfully")
            view
        } catch (e: Exception) {
            Log.e(TAG, "Failed to create expanded view", e)
            null
        }
    }

    // 旧的开关监听器已移除，现在使用RecyclerView和ModuleAdapter

    private fun setupDragListeners(view: View) {
        try {
            // 为圆点设置拖拽监听器
            val collapsedDot = view.findViewById<View>(R.id.collapsedDot)
            collapsedDot?.let { setupDragListener(it, view) }

            // 为精简模式内容区域的顶部设置拖拽监听器
            val compactContent = view.findViewById<View>(R.id.compactContent)
            compactContent?.let { content ->
                // 为顶部操作栏设置拖拽（如果存在的话）
                val topBar = content.findViewById<View>(R.id.topBar)
                topBar?.let { setupDragListener(it, view) }
            }

            // 新版布局：拖拽区域为 topBar 或 titleBar
            val topBar = view.findViewById<View>(R.id.topBar)
            topBar?.let { setupDragListener(it, view) }
            val titleBar = view.findViewById<View>(R.id.titleBar)
            titleBar?.let { setupDragListener(it, view) }
            // contentArea 已移除，无需处理 ScrollView

            Log.d(TAG, "Drag listeners set up for specific areas")
        } catch (e: Exception) {
            Log.e(TAG, "Failed to setup drag listeners", e)
        }
    }

    private fun setupDragListener(dragView: View, floatingView: View) {
        try {
            var isDragging = false
            var dragStartTime = 0L

            dragView.setOnTouchListener { v, event ->
                try {
                    when (event.action) {
                        MotionEvent.ACTION_DOWN -> {
                            isDragging = false
                            dragStartTime = System.currentTimeMillis()
                            initialX = (floatingView.layoutParams as WindowManager.LayoutParams).x
                            initialY = (floatingView.layoutParams as WindowManager.LayoutParams).y
                            initialTouchX = event.rawX
                            initialTouchY = event.rawY
                            Log.d(TAG, "Touch down on drag area at (${event.rawX}, ${event.rawY})")
                            true
                        }
                        MotionEvent.ACTION_MOVE -> {
                            val deltaX = event.rawX - initialTouchX
                            val deltaY = event.rawY - initialTouchY
                            val distance = Math.sqrt((deltaX * deltaX + deltaY * deltaY).toDouble())

                            // 如果移动距离超过阈值，开始拖拽
                            if (distance > 15 && !isDragging) {
                                isDragging = true
                                Log.d(TAG, "Start dragging from ${v.javaClass.simpleName}")
                            }

                            if (isDragging) {
                                val layoutParams = floatingView.layoutParams as WindowManager.LayoutParams
                                layoutParams.x = initialX + deltaX.toInt()
                                layoutParams.y = initialY + deltaY.toInt()
                                windowManager.updateViewLayout(floatingView, layoutParams)
                            }
                            isDragging // 如果正在拖拽则消费事件，否则传递给子视图
                        }
                        MotionEvent.ACTION_UP -> {
                            val dragDuration = System.currentTimeMillis() - dragStartTime
                            Log.d(TAG, "Touch up, was dragging: $isDragging, duration: $dragDuration ms")

                            // 如果不是拖拽操作且是短时间点击，则传递给子视图处理
                            if (!isDragging && dragDuration < 300) {
                                v.performClick()
                                false // 让子视图处理点击事件
                            } else {
                                isDragging // 如果是拖拽操作，消费事件
                            }
                        }
                        else -> false
                    }
                } catch (e: Exception) {
                    Log.e(TAG, "Error handling touch event on ${v.javaClass.simpleName}", e)
                    false
                }
            }
        } catch (e: Exception) {
            Log.e(TAG, "Failed to setup drag listener for ${dragView.javaClass.simpleName}", e)
        }
    }

    private fun expandFloatingWindow() {
        try {
            if (isExpanded) {
                Log.d(TAG, "Window already expanded")
                return
            }

            val view = floatingView ?: run {
                Log.w(TAG, "No floating view to expand")
                return
            }

            Log.d(TAG, "Expanding floating window")
            expandViewContent(view, true)
            isExpanded = true
            Log.d(TAG, "Floating window expanded successfully")
        } catch (e: Exception) {
            Log.e(TAG, "Failed to expand floating window", e)
        }
    }

    private fun collapseFloatingWindow() {
        try {
            if (!isExpanded) {
                Log.d(TAG, "Window already collapsed")
                return
            }

            val view = floatingView ?: run {
                Log.w(TAG, "No floating view to collapse")
                return
            }

            Log.d(TAG, "Collapsing floating window")
            collapseViewContent(view, true)
            isExpanded = false
            Log.d(TAG, "Floating window collapsed successfully")
        } catch (e: Exception) {
            Log.e(TAG, "Failed to collapse floating window", e)
        }
    }

    private fun openMainApp() {
        try {
            Log.d(TAG, "Opening main app")
            val intent = Intent(this, MainActivity::class.java).apply {
                flags = Intent.FLAG_ACTIVITY_NEW_TASK or Intent.FLAG_ACTIVITY_CLEAR_TOP
            }
            startActivity(intent)
        } catch (e: Exception) {
            Log.e(TAG, "Failed to open main app", e)
        }
    }

    private fun expandViewContent(view: View, animate: Boolean) {
        try {
            val collapsedDot = view.findViewById<View>(R.id.collapsedDot)
            // 新版布局无 expandedContent，使用 moduleContainer 作为主内容区域
            val expandedContent = view.findViewById<View>(R.id.moduleContainer)

            if (animate) {
                // 先调整悬浮窗大小
                updateFloatingWindowSize(false)

                // 恢复CardView背景（展开状态）
                updateCardViewBackground(view, false)

                // 先隐藏圆点
                collapsedDot?.animate()
                    ?.alpha(0f)
                    ?.scaleX(0.3f)
                    ?.scaleY(0.3f)
                    ?.setDuration(200)
                    ?.setInterpolator(DecelerateInterpolator())
                    ?.withEndAction {
                        collapsedDot.visibility = View.GONE
                    }
                    ?.start()

                // 显示展开内容
                expandedContent?.let { content ->
                    content.visibility = View.VISIBLE
                    content.alpha = 0f
                    content.scaleX = 0.3f
                    content.scaleY = 0.3f
                    content.animate()
                        .alpha(1f)
                        .scaleX(1f)
                        .scaleY(1f)
                        .setDuration(350)
                        .setStartDelay(100)
                        .setInterpolator(OvershootInterpolator(0.8f))
                        .start()
                }
            } else {
                collapsedDot?.visibility = View.GONE
                expandedContent?.visibility = View.VISIBLE
                updateFloatingWindowSize(false)
                updateCardViewBackground(view, false)
            }
        } catch (e: Exception) {
            Log.e(TAG, "Error expanding view content", e)
        }
    }

    private fun collapseViewContent(view: View, animate: Boolean) {
        try {
            val collapsedDot = view.findViewById<View>(R.id.collapsedDot)
            // 新版布局无 expandedContent，使用 moduleContainer 作为主内容区域
            val expandedContent = view.findViewById<View>(R.id.moduleContainer)

            if (animate) {
                // 先隐藏展开内容
                expandedContent?.animate()
                    ?.alpha(0f)
                    ?.scaleX(0.3f)
                    ?.scaleY(0.3f)
                    ?.setDuration(250)
                    ?.setInterpolator(DecelerateInterpolator())
                    ?.withEndAction {
                        expandedContent.visibility = View.GONE

                        // 调整悬浮窗大小为圆点大小
                        updateFloatingWindowSize(true)

                        // 设置CardView背景为透明（收起状态）
                        updateCardViewBackground(view, true)

                        // 显示圆点
                        collapsedDot?.let { dot ->
                            dot.visibility = View.VISIBLE
                            dot.alpha = 0f
                            dot.scaleX = 0.3f
                            dot.scaleY = 0.3f
                            dot.animate()
                                .alpha(1f)
                                .scaleX(1f)
                                .scaleY(1f)
                                .setDuration(300)
                                .setInterpolator(OvershootInterpolator(0.6f))
                                .start()
                        }
                    }
                    ?.start()
            } else {
                expandedContent?.visibility = View.GONE
                collapsedDot?.visibility = View.VISIBLE
                updateFloatingWindowSize(true)
                updateCardViewBackground(view, true)
            }
        } catch (e: Exception) {
            Log.e(TAG, "Error collapsing view content", e)
        }
    }

    private fun updateFloatingWindowSize(isCollapsed: Boolean) {
        try {
            floatingView?.let { view ->
                val layoutParams = view.layoutParams as? WindowManager.LayoutParams
                if (layoutParams == null) {
                    Log.w(TAG, "LayoutParams is null, cannot update window size")
                    return
                }
                
                val density = resources.displayMetrics.density

                if (isCollapsed) {
                    // 收起状态：设置为更小的按钮大小
                    val collapsedSize = (40 * density).toInt() // 40dp 更小更精致
                    layoutParams.width = collapsedSize
                    layoutParams.height = collapsedSize
                } else {
                    // 精简模式展开状态：设置为精简模式宽度，高度自适应
                    val compactWidth = (210 * density).toInt()  // 精简模式宽度
                    layoutParams.width = compactWidth
                    layoutParams.height = WindowManager.LayoutParams.WRAP_CONTENT // 改为自适应高度
                }

                windowManager.updateViewLayout(view, layoutParams)
                Log.d(TAG, "Updated floating window size: collapsed=$isCollapsed")
            }
        } catch (e: Exception) {
            Log.e(TAG, "Failed to update floating window size", e)
        }
    }

    private fun updateCardViewBackground(view: View, isCollapsed: Boolean) {
        try {
            if (view is androidx.cardview.widget.CardView) {
                // 收起和展开状态都保持米色背景，形成统一的圆角矩形
                view.setCardBackgroundColor(resources.getColor(R.color.md_theme_light_primaryContainer, null))
                if (isCollapsed) {
                    // 收起状态：较低的阴影
                    view.cardElevation = 6f
                } else {
                    // 展开状态：较高的阴影
                    view.cardElevation = 12f
                }
                Log.d(TAG, "Updated CardView background: collapsed=$isCollapsed")
            }
        } catch (e: Exception) {
            Log.e(TAG, "Failed to update CardView background", e)
        }
    }

    private fun removeFloatingWindow() {
        try {
            floatingView?.let {
                Log.d(TAG, "Removing floating window")
                windowManager.removeView(it)
                floatingView = null
                Log.d(TAG, "Floating window removed successfully")
            }
        } catch (e: Exception) {
            Log.e(TAG, "Failed to remove floating window", e)
        }
    }

    // 新的方法：设置精简模式的监听器
    private fun setupCompactViewListeners(view: View) {
        try {
            // 收起状态的圆点点击展开
            view.findViewById<ImageView>(R.id.collapsedDot)?.setOnClickListener {
                expandToCompact(true)
            }

            // 展开按钮点击切换到展开模式 - 保留展开动画
            view.findViewById<ImageView>(R.id.btnExpand)?.setOnClickListener {
                switchToExpandedModeWithAnimation()
            }

            // 收起按钮点击收起到圆点
            view.findViewById<ImageView>(R.id.btnCollapse)?.setOnClickListener {
                collapseToIcon(true)
            }

            // 设置手势检测
            setupGestureDetector(view)

            // 设置拉伸按钮 - 已移除
            // setupResizeHandle(view)

        } catch (e: Exception) {
            Log.e(TAG, "Failed to setup compact view listeners", e)
        }
    }

    /*
    // 新增：显示精简模式的模块设置
    private fun showCompactModuleSettings(module: KasumiModule) {
        // 暂时注释掉，有编译错误
    }

    // 新增：隐藏精简模式的模块设置  
    private fun hideCompactModuleSettings(animate: Boolean = true) {
        // 暂时注释掉，有编译错误
    }
    */


    // 设置精简模式的RecyclerView
    private fun setupCompactRecyclerView(view: View) {
        try {
            val recyclerView = view.findViewById<RecyclerView>(R.id.rvCategoryModules)
            recyclerView?.layoutManager = LinearLayoutManager(this)

            compactAdapter = ModuleAdapter(
                ModuleRegistry.getModulesByCategory(currentCategory),
                ModuleAdapter.LayoutMode.LIST
            ) { module ->
                // 精简模式下点击有设置的模块，先切换到展开模式再显示设置
                switchToExpandedModeAndShowSettings(module)
            }

            recyclerView?.adapter = compactAdapter
        } catch (e: Exception) {
            Log.e(TAG, "Failed to setup compact RecyclerView", e)
        }
    }

    // 设置精简模式的ViewPager2
    private fun setupCompactViewPager(view: View) {
        try {
            val viewPager = view.findViewById<ViewPager2>(R.id.vpCompactCategories)

            // 创建所有分类的适配器
            val categories = ModuleCategory.values().toList()
            val moduleAdapters = mutableMapOf<ModuleCategory, ModuleAdapter>()

            categories.forEach { category ->
                moduleAdapters[category] = ModuleAdapter(
                    ModuleRegistry.getModulesByCategory(category),
                    ModuleAdapter.LayoutMode.LIST
                ) { module ->
                    // 精简模式下点击有设置的模块，切换到展开模式并显示设置
                    switchToExpandedModeAndShowSettings(module)
                }
            }

            // 设置ViewPager2适配器
            val pagerAdapter = CompactCategoryPagerAdapter(categories, moduleAdapters)
            viewPager?.adapter = pagerAdapter

            // 设置当前页面
            val currentIndex = categories.indexOf(currentCategory)
            if (currentIndex >= 0) {
                viewPager?.setCurrentItem(currentIndex, false)
            }

            // 监听页面切换
            viewPager?.registerOnPageChangeCallback(object : ViewPager2.OnPageChangeCallback() {
                override fun onPageSelected(position: Int) {
                    super.onPageSelected(position)
                    if (position < categories.size) {
                        currentCategory = categories[position]
                        updateCategoryDisplayForViewPager(view)
                        Log.d(TAG, "ViewPager page changed to: ${currentCategory.displayName}")
                    }
                }
            })

            Log.d(TAG, "ViewPager2 setup completed with ${categories.size} categories")
        } catch (e: Exception) {
            Log.e(TAG, "Failed to setup compact ViewPager", e)
        }
    }

    // 更新分类显示
    private fun updateCategoryDisplay() {
        try {
            floatingView?.let { view ->
                // 更新分类名称
                view.findViewById<TextView>(R.id.tvCategoryName)?.text = currentCategory.displayName

                // 更新指示器
                updateCategoryIndicators(view)

                // 更新RecyclerView数据
                compactAdapter?.updateModules(ModuleRegistry.getModulesByCategory(currentCategory))
            }
        } catch (e: Exception) {
            Log.e(TAG, "Failed to update category display", e)
        }
    }

    // 更新ViewPager模式的分类显示
    private fun updateCategoryDisplayForViewPager(view: View) {
        try {
            // 更新分类名称
            view.findViewById<TextView>(R.id.tvCategoryName)?.text = currentCategory.displayName

            // 更新指示器
            updateCategoryIndicators(view)

            Log.d(TAG, "Updated category display for ViewPager: ${currentCategory.displayName}")
        } catch (e: Exception) {
            Log.e(TAG, "Failed to update category display for ViewPager", e)
        }
    }

    // 更新分类指示器
    private fun updateCategoryIndicators(view: View) {
        try {
            val indicators = listOf(
                view.findViewById<View>(R.id.indicatorCombat),
                view.findViewById<View>(R.id.indicatorMovement),
                view.findViewById<View>(R.id.indicatorWorld),
                view.findViewById<View>(R.id.indicatorPlayer),
                view.findViewById<View>(R.id.indicatorVisual)
            )

            val categories = ModuleCategory.values()
            indicators.forEachIndexed { index, indicator ->
                if (index < categories.size) {
                    val isActive = categories[index] == currentCategory
                    indicator?.backgroundTintList = android.content.res.ColorStateList.valueOf(
                        if (isActive) getColor(R.color.md_theme_light_primary)
                        else getColor(R.color.md_theme_light_outline)
                    )
                }
            }
        } catch (e: Exception) {
            Log.e(TAG, "Failed to update category indicators", e)
        }
    }

    // 收起到圆点状态
    private fun collapseToIcon(animate: Boolean) {
        try {
            floatingView?.let { view ->
                val collapsedDot = view.findViewById<ImageView>(R.id.collapsedDot)
                val compactContent = view.findViewById<View>(R.id.compactContent)

                Log.d(TAG, "Collapsing to icon, animate=$animate")

                if (animate) {
                    compactContent?.animate()
                        ?.alpha(0f)
                        ?.scaleX(0.3f)
                        ?.scaleY(0.3f)
                        ?.setDuration(250)
                        ?.withEndAction {
                            compactContent?.visibility = View.GONE
                            collapsedDot?.visibility = View.VISIBLE
                            collapsedDot?.alpha = 1f // 确保圆点完全可见
                            updateFloatingWindowSize(true)
                            updateCardViewBackground(view, true)
                        }
                        ?.start()
                } else {
                    compactContent?.visibility = View.GONE
                    collapsedDot?.visibility = View.VISIBLE
                    collapsedDot?.alpha = 1f
                    updateFloatingWindowSize(true)
                    updateCardViewBackground(view, true)
                }

                isExpanded = false
            }
        } catch (e: Exception) {
            Log.e(TAG, "Failed to collapse to icon", e)
        }
    }

    // 展开到精简模式
    private fun expandToCompact(animate: Boolean) {
        try {
            floatingView?.let { view ->
                val collapsedDot = view.findViewById<ImageView>(R.id.collapsedDot)
                val compactContent = view.findViewById<View>(R.id.compactContent)

                Log.d(TAG, "Expanding to compact, animate=$animate")

                updateFloatingWindowSize(false)
                updateCardViewBackground(view, false)

                if (animate) {
                    collapsedDot?.animate()
                        ?.alpha(0f)
                        ?.setDuration(200)
                        ?.withEndAction {
                            collapsedDot?.visibility = View.GONE
                            compactContent?.visibility = View.VISIBLE
                            compactContent?.alpha = 0f
                            compactContent?.scaleX = 1f // 重置缩放
                            compactContent?.scaleY = 1f
                            compactContent?.animate()
                                ?.alpha(1f)
                                ?.setDuration(300)
                                ?.start()
                        }
                        ?.start()
                } else {
                    collapsedDot?.visibility = View.GONE
                    compactContent?.visibility = View.VISIBLE
                    compactContent?.alpha = 1f
                    compactContent?.scaleX = 1f
                    compactContent?.scaleY = 1f
                }

                isExpanded = true
            }
        } catch (e: Exception) {
            Log.e(TAG, "Failed to expand to compact", e)
        }
    }

    // 带动画的切换到展开模式
    private fun switchToExpandedModeWithAnimation() {
        try {
            Log.d(TAG, "Switching to expanded mode with animation")

            // 获取当前精简框的位置
            val currentLayoutParams = floatingView?.layoutParams as? WindowManager.LayoutParams
            val currentX = currentLayoutParams?.x ?: 100
            val currentY = currentLayoutParams?.y ?: 100

            // 创建展开模式视图
            val expandedView = createExpandedView()
            if (expandedView != null) {
                val density = resources.displayMetrics.density
                val targetWidth = (480 * density).toInt()
                val targetHeight = (280 * density).toInt()

                // 设置展开模式的最终尺寸和位置
                val layoutParams = WindowManager.LayoutParams(
                    targetWidth,
                    targetHeight,
                    if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O) {
                        WindowManager.LayoutParams.TYPE_APPLICATION_OVERLAY
                    } else {
                        WindowManager.LayoutParams.TYPE_PHONE
                    },
                    WindowManager.LayoutParams.FLAG_NOT_FOCUSABLE,
                    PixelFormat.TRANSLUCENT
                ).apply {
                    gravity = Gravity.TOP or Gravity.START
                    x = currentX
                    y = currentY
                }

                // 设置初始动画状态
                expandedView.alpha = 0f
                expandedView.scaleX = 0.3f
                expandedView.scaleY = 0.3f

                // 移除旧视图，添加新视图
                floatingView?.let { windowManager.removeView(it) }
                floatingView = expandedView
                windowManager.addView(expandedView, layoutParams)
                setupDragListeners(expandedView)

                // 刷新状态显示
                refreshExpandedViewStates(expandedView)

                // 执行展开动画
                expandedView.animate()
                    .alpha(1f)
                    .scaleX(1f)
                    .scaleY(1f)
                    .setDuration(300)
                    .setInterpolator(android.view.animation.OvershootInterpolator(0.3f))
                    .start()

                isCompactMode = false
                isExpanded = true

                Log.d(TAG, "Started smooth expansion animation")
            }

        } catch (e: Exception) {
            Log.e(TAG, "Failed to switch to expanded mode with animation", e)
            // 如果动画失败，回退到无动画版本
            switchToExpandedMode()
        }
    }

    // 切换到展开模式（无动画版本）
    private fun switchToExpandedMode() {
        try {
            Log.d(TAG, "Switching to expanded mode")

            // 获取当前精简框的位置
            val currentLayoutParams = floatingView?.layoutParams as? WindowManager.LayoutParams
            val currentX = currentLayoutParams?.x ?: 100
            val currentY = currentLayoutParams?.y ?: 100

            // 移除当前的精简模式视图
            floatingView?.let { currentView ->
                windowManager.removeView(currentView)
                floatingView = null
            }

            // 创建展开模式视图
            val expandedView = createExpandedView()
            if (expandedView != null) {
                // 设置展开模式的窗口参数 - 横向长方形适合横屏
                val density = resources.displayMetrics.density
                val expandedWidth = (480 * density).toInt() // 480dp 横向宽度
                val expandedHeight = (280 * density).toInt() // 280dp 高度

                val layoutParams = WindowManager.LayoutParams(
                    expandedWidth,
                    expandedHeight,
                    if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O) {
                        WindowManager.LayoutParams.TYPE_APPLICATION_OVERLAY
                    } else {
                        WindowManager.LayoutParams.TYPE_PHONE
                    },
                    WindowManager.LayoutParams.FLAG_NOT_FOCUSABLE,
                    PixelFormat.TRANSLUCENT
                ).apply {
                    gravity = Gravity.TOP or Gravity.START
                    // 从精简框的位置展开，但稍微调整以确保完全可见
                    x = currentX
                    y = currentY
                }

                floatingView = expandedView
                windowManager.addView(expandedView, layoutParams)
                setupDragListeners(expandedView)

                // 刷新状态显示
                refreshExpandedViewStates(expandedView)

                isCompactMode = false
                isExpanded = true

                Log.d(TAG, "Successfully switched to expanded mode at position ($currentX, $currentY)")
            }

        } catch (e: Exception) {
            Log.e(TAG, "Failed to switch to expanded mode", e)
        }
    }

    // 设置手势检测
    private fun setupGestureDetector(view: View) {
        try {
            gestureDetector = GestureDetector(this, object : GestureDetector.SimpleOnGestureListener() {
                override fun onScroll(
                    e1: MotionEvent?,
                    e2: MotionEvent,
                    distanceX: Float,
                    distanceY: Float
                ): Boolean {
                    if (e1 == null || !isExpanded) return false

                    val deltaX = e2.x - e1.x
                    val deltaY = e2.y - e1.y

                    // 判断是否为水平滑动（水平距离大于垂直距离）
                    if (abs(deltaX) > abs(deltaY) && abs(deltaX) > 20) {
                        Log.d(TAG, "Horizontal scroll detected: deltaX=$deltaX")
                        return true // 消费水平滑动事件
                    }
                    return false
                }

                override fun onFling(
                    e1: MotionEvent?,
                    e2: MotionEvent,
                    velocityX: Float,
                    velocityY: Float
                ): Boolean {
                    if (e1 == null || !isExpanded) return false

                    val deltaX = e2.x - e1.x
                    val deltaY = e2.y - e1.y

                    Log.d(TAG, "Fling detected: deltaX=$deltaX, deltaY=$deltaY, velocityX=$velocityX")

                    // 判断是否为水平快速滑动
                    if (abs(deltaX) > abs(deltaY) && abs(deltaX) > 50 && abs(velocityX) > 300) {
                        if (deltaX > 0) {
                            // 向右滑动 - 上一个分类
                            switchToPreviousCategory()
                        } else {
                            // 向左滑动 - 下一个分类
                            switchToNextCategory()
                        }
                        return true
                    }
                    return false
                }
            })

            // ViewPager2会自动处理滑动手势，不需要额外设置
            // 旧的RecyclerView滑动手势已被ViewPager2替代

        } catch (e: Exception) {
            Log.e(TAG, "Failed to setup gesture detector", e)
        }
    }

    // 切换到下一个分类
    private fun switchToNextCategory() {
        try {
            val categories = ModuleCategory.values()
            val currentIndex = categories.indexOf(currentCategory)
            val nextIndex = (currentIndex + 1) % categories.size
            currentCategory = categories[nextIndex]
            updateCategoryDisplay()
            Log.d(TAG, "Switched to next category: ${currentCategory.displayName}")
        } catch (e: Exception) {
            Log.e(TAG, "Failed to switch to next category", e)
        }
    }

    // 切换到上一个分类
    private fun switchToPreviousCategory() {
        try {
            val categories = ModuleCategory.values()
            val currentIndex = categories.indexOf(currentCategory)
            val prevIndex = if (currentIndex == 0) categories.size - 1 else currentIndex - 1
            currentCategory = categories[prevIndex]
            updateCategoryDisplay()
            Log.d(TAG, "Switched to previous category: ${currentCategory.displayName}")
        } catch (e: Exception) {
            Log.e(TAG, "Failed to switch to previous category", e)
        }
    }

    // 为RecyclerView设置专门的滑动手势
    private fun setupRecyclerViewSwipeGesture(recyclerView: RecyclerView) {
        try {
            var startX = 0f
            var startY = 0f
            var isHorizontalSwipe = false
            var hasDetectedDirection = false

            recyclerView.setOnTouchListener { v, event ->
                // 展开模式和精简模式都支持滑动切换分类
                if (!isExpanded && v.id != R.id.rvCategoryModules) return@setOnTouchListener false

                when (event.action) {
                    MotionEvent.ACTION_DOWN -> {
                        startX = event.x
                        startY = event.y
                        isHorizontalSwipe = false
                        hasDetectedDirection = false
                        Log.d(TAG, "Touch down at: ($startX, $startY)")
                    }

                    MotionEvent.ACTION_MOVE -> {
                        if (!hasDetectedDirection) {
                            val deltaX = event.x - startX
                            val deltaY = event.y - startY

                            // 检测滑动方向
                            if (abs(deltaX) > 20 || abs(deltaY) > 20) {
                                isHorizontalSwipe = abs(deltaX) > abs(deltaY)
                                hasDetectedDirection = true
                                Log.d(TAG, "Direction detected: horizontal=$isHorizontalSwipe, deltaX=$deltaX, deltaY=$deltaY")
                            }
                        }

                        // 如果是水平滑动，拦截事件
                        if (isHorizontalSwipe) {
                            return@setOnTouchListener true
                        }
                    }

                    MotionEvent.ACTION_UP -> {
                        if (isHorizontalSwipe) {
                            val deltaX = event.x - startX
                            Log.d(TAG, "Horizontal swipe completed: deltaX=$deltaX")

                            // 滑动距离足够就切换分类
                            if (abs(deltaX) > 60) {
                                if (deltaX > 0) {
                                    // 向右滑动 - 上一个分类
                                    switchToPreviousCategory()
                                } else {
                                    // 向左滑动 - 下一个分类
                                    switchToNextCategory()
                                }
                            }
                            return@setOnTouchListener true
                        }
                    }
                }

                // 如果不是水平滑动，让RecyclerView正常处理
                false
            }

        } catch (e: Exception) {
            Log.e(TAG, "Failed to setup RecyclerView swipe gesture", e)
        }
    }

    // 设置展开模式的监听器
    private fun setupExpandedViewListeners(view: View) {
        try {
            // 关闭按钮 - 直接切换，无动画
            view.findViewById<ImageView>(R.id.btnCollapseExpanded)?.setOnClickListener {
                switchBackToCompactMode()
            }



        } catch (e: Exception) {
            Log.e(TAG, "Failed to setup expanded view listeners", e)
        }
    }

    // 设置展开模式的RecyclerView
    private fun setupExpandedRecyclerView(view: View) {
        try {
            val recyclerView = view.findViewById<RecyclerView>(R.id.rvExpandedModules)
            recyclerView?.layoutManager = GridLayoutManager(this, 3) // 3列网格

            expandedAdapter = ModuleAdapter(
                ModuleRegistry.getModulesByCategory(currentCategory),
                ModuleAdapter.LayoutMode.GRID
            ) { module ->
                // 模块设置点击回调
                showModuleSettings(module)
            }

            recyclerView?.adapter = expandedAdapter

            // 为展开模式的RecyclerView也添加滑动手势
            recyclerView?.let { rv ->
                setupRecyclerViewSwipeGesture(rv)
            }

            Log.d(TAG, "Expanded RecyclerView setup completed")
        } catch (e: Exception) {
            Log.e(TAG, "Failed to setup expanded RecyclerView", e)
        }
    }

    // 设置展开模式的TabLayout
    private fun setupExpandedTabLayout(view: View) {
        try {
            val tabLayout = view.findViewById<TabLayout>(R.id.tabCategories)

            // 清除现有标签
            tabLayout?.removeAllTabs()

            // 添加分类标签
            ModuleCategory.values().forEach { category ->
                val tab = tabLayout?.newTab()?.setText(category.displayName)
                tab?.let { tabLayout.addTab(it) }
            }

            // 设置当前选中的标签
            val currentIndex = ModuleCategory.values().indexOf(currentCategory)
            tabLayout?.getTabAt(currentIndex)?.select()

            // 设置标签选择监听器
            tabLayout?.addOnTabSelectedListener(object : TabLayout.OnTabSelectedListener {
                override fun onTabSelected(tab: TabLayout.Tab?) {
                    tab?.position?.let { position ->
                        if (position < ModuleCategory.values().size) {
                            val newCategory = ModuleCategory.values()[position]
                            if (newCategory != currentCategory) {
                                // 添加切换动画
                                animateTabContentChange(newCategory)
                            }
                        }
                    }
                }

                override fun onTabUnselected(tab: TabLayout.Tab?) {}
                override fun onTabReselected(tab: TabLayout.Tab?) {}
            })

        } catch (e: Exception) {
            Log.e(TAG, "Failed to setup expanded TabLayout", e)
        }
    }

    // 带动画的Tab内容切换
    private fun animateTabContentChange(newCategory: ModuleCategory) {
        try {
            val recyclerView = floatingView?.findViewById<RecyclerView>(R.id.rvExpandedModules)

            if (recyclerView != null) {
                // 淡出当前内容
                recyclerView.animate()
                    .alpha(0.3f)
                    .scaleX(0.95f)
                    .scaleY(0.95f)
                    .setDuration(150)
                    .withEndAction {
                        // 更新分类和数据
                        currentCategory = newCategory
                        expandedAdapter?.updateModulesWithAnimation(ModuleRegistry.getModulesByCategory(currentCategory))

                        // 淡入新内容
                        recyclerView.animate()
                            .alpha(1f)
                            .scaleX(1f)
                            .scaleY(1f)
                            .setDuration(200)
                            .setInterpolator(android.view.animation.OvershootInterpolator(0.2f))
                            .start()
                    }
                    .start()
            } else {
                // 如果没有RecyclerView，直接更新
                currentCategory = newCategory
                updateExpandedCategoryDisplay()
            }

        } catch (e: Exception) {
            Log.e(TAG, "Failed to animate tab content change", e)
            // 如果动画失败，直接更新
            currentCategory = newCategory
            updateExpandedCategoryDisplay()
        }
    }

    // 更新展开模式的分类显示（无动画版本）
    private fun updateExpandedCategoryDisplay() {
        try {
            expandedAdapter?.updateModules(ModuleRegistry.getModulesByCategory(currentCategory))
        } catch (e: Exception) {
            Log.e(TAG, "Failed to update expanded category display", e)
        }
    }

    // 带动画的切换回精简模式
    private fun switchBackToCompactModeWithAnimation() {
        try {
            Log.d(TAG, "Switching back to compact mode with animation")

            // 获取当前展开框的位置
            val currentLayoutParams = floatingView?.layoutParams as? WindowManager.LayoutParams
            val currentX = currentLayoutParams?.x ?: 100
            val currentY = currentLayoutParams?.y ?: 100

            // 创建精简模式视图（先创建，但不显示）
            val compactView = createCompactView()
            if (compactView != null) {
                // 设置精简模式的正确尺寸
                val density = resources.displayMetrics.density
                val compactWidth = (210 * density).toInt()  // 精简模式宽度
                // 改为 wrap_content 让高度自适应

                val layoutParams = WindowManager.LayoutParams(
                    compactWidth,
                    WindowManager.LayoutParams.WRAP_CONTENT, // 改为自适应高度
                    if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O) {
                        WindowManager.LayoutParams.TYPE_APPLICATION_OVERLAY
                    } else {
                        WindowManager.LayoutParams.TYPE_PHONE
                    },
                    WindowManager.LayoutParams.FLAG_NOT_FOCUSABLE,
                    PixelFormat.TRANSLUCENT
                ).apply {
                    gravity = Gravity.TOP or Gravity.START
                    x = currentX
                    y = currentY
                }

                // 先设置精简视图为完全透明和缩小状态
                compactView.alpha = 0f
                compactView.scaleX = 0.3f
                compactView.scaleY = 0.3f

                // 手动设置精简模式内容可见，避免调用expandToCompact
                val collapsedDot = compactView.findViewById<ImageView>(R.id.collapsedDot)
                val compactContent = compactView.findViewById<View>(R.id.compactContent)
                collapsedDot?.visibility = View.GONE
                compactContent?.visibility = View.VISIBLE
                compactContent?.alpha = 1f
                compactContent?.scaleX = 1f
                compactContent?.scaleY = 1f

                // 先淡出当前展开视图
                floatingView?.animate()
                    ?.alpha(0f)
                    ?.scaleX(0.3f)
                    ?.scaleY(0.3f)
                    ?.setDuration(200)
                    ?.withEndAction {
                        // 在淡出完成后立即切换视图
                        floatingView?.let { windowManager.removeView(it) }
                        floatingView = compactView
                        windowManager.addView(compactView, layoutParams)
                        setupDragListeners(compactView)

                        // 立即开始精简视图的淡入动画
                        compactView.animate()
                            .alpha(1f)
                            .scaleX(1f)
                            .scaleY(1f)
                            .setDuration(250)
                            .setInterpolator(android.view.animation.OvershootInterpolator(0.2f))
                            .withEndAction {
                                // 动画结束后强制刷新ViewPager2布局
                                val viewPager = compactView.findViewById<androidx.viewpager2.widget.ViewPager2>(R.id.vpCompactCategories)
                                viewPager?.let { vp ->
                                    // 强制重新测量和布局
                                    vp.measure(
                                        View.MeasureSpec.makeMeasureSpec(compactView.width, View.MeasureSpec.EXACTLY),
                                        View.MeasureSpec.makeMeasureSpec(0, View.MeasureSpec.UNSPECIFIED)
                                    )
                                    vp.requestLayout()
                                }
                                compactView.requestLayout()

                                // 再次延迟刷新，确保布局完成
                                compactView.postDelayed({
                                    viewPager?.requestLayout()
                                    compactView.requestLayout()
                                }, 100)
                            }
                            .start()

                        isCompactMode = true
                        isExpanded = true

                        Log.d(TAG, "Completed smooth contraction animation")
                    }
                    ?.start()
            }

        } catch (e: Exception) {
            Log.e(TAG, "Failed to switch back to compact mode with animation", e)
            // 如果动画失败，回退到无动画版本
            switchBackToCompactMode()
        }
    }

    // 切换回精简模式（无动画版本）
    private fun switchBackToCompactMode() {
        try {
            Log.d(TAG, "Switching back to compact mode")

            // 获取当前展开框的位置
            val currentLayoutParams = floatingView?.layoutParams as? WindowManager.LayoutParams
            val currentX = currentLayoutParams?.x ?: 100
            val currentY = currentLayoutParams?.y ?: 100

            // 移除当前的展开模式视图
            floatingView?.let { currentView ->
                windowManager.removeView(currentView)
                floatingView = null
            }

            // 重新创建精简模式视图
            val compactView = createCompactView()
            if (compactView != null) {
                // 设置精简模式的窗口参数
                val density = resources.displayMetrics.density
                val compactWidth = (210 * density).toInt()  // 精简模式宽度
                // 改为 wrap_content 让高度自适应
                
                val layoutParams = WindowManager.LayoutParams(
                    compactWidth,
                    WindowManager.LayoutParams.WRAP_CONTENT, // 改为自适应高度
                    if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O) {
                        WindowManager.LayoutParams.TYPE_APPLICATION_OVERLAY
                    } else {
                        WindowManager.LayoutParams.TYPE_PHONE
                    },
                    WindowManager.LayoutParams.FLAG_NOT_FOCUSABLE,
                    PixelFormat.TRANSLUCENT
                ).apply {
                    gravity = Gravity.TOP or Gravity.START
                    // 保持在当前位置
                    x = currentX
                    y = currentY
                }

                floatingView = compactView
                windowManager.addView(compactView, layoutParams)
                setupDragListeners(compactView)

                // 状态显示已集成到ModuleAdapter中

                // 直接展开到精简模式（不显示圆点）
                expandToCompact(false)

                // 强制刷新ViewPager2布局，解决空白问题
                compactView.post {
                    val viewPager = compactView.findViewById<androidx.viewpager2.widget.ViewPager2>(R.id.vpCompactCategories)
                    viewPager?.let { vp ->
                        // 强制重新测量和布局
                        vp.measure(
                            View.MeasureSpec.makeMeasureSpec(compactView.width, View.MeasureSpec.EXACTLY),
                            View.MeasureSpec.makeMeasureSpec(0, View.MeasureSpec.UNSPECIFIED)
                        )
                        vp.requestLayout()
                    }
                    compactView.requestLayout()

                    // 再次延迟刷新，确保布局完成
                    compactView.postDelayed({
                        viewPager?.requestLayout()
                        compactView.requestLayout()
                    }, 100)
                }

                isCompactMode = true
                isExpanded = true

                Log.d(TAG, "Successfully switched back to compact mode at position ($currentX, $currentY)")
            }

        } catch (e: Exception) {
            Log.e(TAG, "Failed to switch back to compact mode", e)
        }
    }

    // 旧的状态刷新方法已移除，现在使用ModuleAdapter自动管理状态

    /**
     * 刷新展开视图的状态显示
     */
    private fun refreshExpandedViewStates(view: View) {
        try {
            Log.d(TAG, "Refreshing expanded view states for category: ${currentCategory.displayName}")
            // 刷新RecyclerView中的适配器数据
            val recyclerView = view.findViewById<androidx.recyclerview.widget.RecyclerView>(R.id.rvExpandedModules)
            recyclerView?.adapter?.let { adapter ->
                if (adapter is ModuleAdapter) {
                    // 获取当前分类的模块数据，而不是所有模块
                    val updatedModules = ModuleRegistry.getModulesByCategory(currentCategory)
                    adapter.updateModules(updatedModules)
                    Log.d(TAG, "Updated ${updatedModules.size} modules for category ${currentCategory.displayName} in expanded view")
                }
            }
        } catch (e: Exception) {
            Log.e(TAG, "Failed to refresh expanded view states", e)
        }
    }

    // 设置拉伸按钮功能 - 已移除拖拽指示器
    /*
    private fun setupResizeHandle(view: View) {
        try {
            val resizeHandle = view.findViewById<ImageView>(R.id.btnResize)
            resizeHandle?.let { handle ->
                var isResizing = false
                var startX = 0f
                var startY = 0f
                var startWidth = 0
                var startHeight = 0

                handle.setOnTouchListener { _, event ->
                    when (event.action) {
                        MotionEvent.ACTION_DOWN -> {
                            isResizing = true
                            startX = event.rawX
                            startY = event.rawY

                            // 获取当前悬浮窗尺寸
                            val layoutParams = floatingView?.layoutParams as? WindowManager.LayoutParams
                            startWidth = layoutParams?.width ?: 300
                            startHeight = layoutParams?.height ?: 200

                            // 改变拉伸按钮的视觉反馈
                            handle.alpha = 0.9f
                            handle.scaleX = 1.1f
                            handle.scaleY = 1.1f

                            Log.d(TAG, "Resize started: ${startWidth}x${startHeight}")
                            true
                        }

                        MotionEvent.ACTION_MOVE -> {
                            if (isResizing) {
                                val deltaX = event.rawX - startX
                                val deltaY = event.rawY - startY

                                // 获取屏幕尺寸
                                val displayMetrics = resources.displayMetrics
                                val screenWidth = displayMetrics.widthPixels
                                val screenHeight = displayMetrics.heightPixels

                                // 计算新的尺寸（更合理的限制）
                                val minWidth = 80  // 最小宽度
                                val maxWidth = (screenWidth * 0.8).toInt()  // 屏幕宽度的80%
                                val minHeight = 180  // 最小高度，确保能显示完整内容
                                val maxHeight = (screenHeight * 0.7).toInt()  // 屏幕高度的70%

                                val newWidth = (startWidth + deltaX.toInt()).coerceIn(minWidth, maxWidth)
                                val newHeight = (startHeight + deltaY.toInt()).coerceIn(minHeight, maxHeight)

                                // 实时更新悬浮窗尺寸
                                updateFloatingWindowSize(newWidth, newHeight)

                                Log.d(TAG, "Resizing: ${newWidth}x${newHeight} (max: ${maxWidth}x${maxHeight})")
                            }
                            true
                        }

                        MotionEvent.ACTION_UP, MotionEvent.ACTION_CANCEL -> {
                            if (isResizing) {
                                isResizing = false

                                // 恢复拉伸按钮的视觉状态
                                handle.animate()
                                    .alpha(0.6f)
                                    .scaleX(1.0f)
                                    .scaleY(1.0f)
                                    .setDuration(150)
                                    .start()

                                Log.d(TAG, "Resize ended")
                            }
                            true
                        }

                        else -> false
                    }
                }
            }
        } catch (e: Exception) {
            Log.e(TAG, "Failed to setup resize handle", e)
        }
    }
    */

    // 设置面板相关方法
    
    /**
     * 显示模块设置面板
     */
    private fun showModuleSettings(module: KasumiModule) {
        try {
            if (!isExpanded) return
            
            val view = floatingView ?: return
            val settingsPanel = view.findViewById<View>(R.id.settingsPanel)
            val moduleContainer = view.findViewById<FrameLayout>(R.id.moduleContainer)
            val rvExpandedModules = view.findViewById<RecyclerView>(R.id.rvExpandedModules)
            val tabLayout = view.findViewById<TabLayout>(R.id.tabCategories)
            
            if (settingsPanel == null || moduleContainer == null || rvExpandedModules == null) {
                Log.e(TAG, "Settings panel or module container not found")
                return
            }
            
            // 禁用TabLayout
            tabLayout?.isEnabled = false
            
            // 设置模块信息
            setupSettingsPanel(settingsPanel, module)
            
            // 设置面板从左侧滑入
            settingsPanel.visibility = View.VISIBLE
            settingsPanel.translationX = -212f * resources.displayMetrics.density
            settingsPanel.alpha = 1f
            
            // 关键修复：调整整个moduleContainer的位置，而不是RecyclerView
            val containerLayoutParams = moduleContainer.layoutParams as LinearLayout.LayoutParams
            containerLayoutParams.leftMargin = (212 * resources.displayMetrics.density).toInt() // 总共212dp左边距（设置面板宽度）
            containerLayoutParams.rightMargin = (16 * resources.displayMetrics.density).toInt() // 保持右边距
            moduleContainer.layoutParams = containerLayoutParams
            
            // 改变网格布局从3列变为2列，适应更窄的空间
            val gridLayoutManager = rvExpandedModules.layoutManager as? GridLayoutManager
            gridLayoutManager?.spanCount = 2
            
            // 动画设置面板从左侧滑入
            settingsPanel.animate()
                .translationX(0f)
                .setDuration(300)
                .setInterpolator(android.view.animation.DecelerateInterpolator())
                .start()
            
            // 动画功能区域移动和适配器刷新
            rvExpandedModules.animate()
                .alpha(0.3f)
                .setDuration(150)
                .withEndAction {
                    // 刷新适配器以应用新的布局
                    rvExpandedModules.adapter?.notifyDataSetChanged()
                    rvExpandedModules.animate()
                        .alpha(1f)
                        .setDuration(150)
                        .start()
                }
                .start()
            
            Log.d(TAG, "Settings panel shown for module: ${module.name}")
        } catch (e: Exception) {
            Log.e(TAG, "Failed to show module settings", e)
        }
    }
    
    /**
     * 隐藏模块设置面板
     */
    private fun hideModuleSettings() {
        try {
            val view = floatingView ?: return
            val settingsPanel = view.findViewById<View>(R.id.settingsPanel)
            val moduleContainer = view.findViewById<FrameLayout>(R.id.moduleContainer)
            val rvExpandedModules = view.findViewById<RecyclerView>(R.id.rvExpandedModules)
            val tabLayout = view.findViewById<TabLayout>(R.id.tabCategories)
            
            if (settingsPanel == null || moduleContainer == null || rvExpandedModules == null) return
            
            // 重新启用TabLayout
            tabLayout?.isEnabled = true
            
            // 恢复moduleContainer的原始布局参数
            val containerLayoutParams = moduleContainer.layoutParams as LinearLayout.LayoutParams
            containerLayoutParams.leftMargin = (16 * resources.displayMetrics.density).toInt() // 恢复原始左边距
            containerLayoutParams.rightMargin = (16 * resources.displayMetrics.density).toInt() // 保持右边距
            moduleContainer.layoutParams = containerLayoutParams
            
            // 恢复网格布局为3列
            val gridLayoutManager = rvExpandedModules.layoutManager as? GridLayoutManager
            gridLayoutManager?.spanCount = 3
            
            // 动画功能区域恢复和适配器刷新
            rvExpandedModules.animate()
                .alpha(0.3f)
                .setDuration(150)
                .withEndAction {
                    // 刷新适配器以应用新的布局
                    rvExpandedModules.adapter?.notifyDataSetChanged()
                    rvExpandedModules.animate()
                        .alpha(1f)
                        .setDuration(150)
                        .start()
                }
                .start()
            
            // 动画设置面板滑出到左侧
            settingsPanel.animate()
                .translationX(-212f * resources.displayMetrics.density) // 新的面板容器宽度
                .setDuration(300)
                .setInterpolator(android.view.animation.AccelerateDecelerateInterpolator())
                .withEndAction {
                    settingsPanel.visibility = View.GONE
                }
                .start()
            
            Log.d(TAG, "Settings panel hidden")
        } catch (e: Exception) {
            Log.e(TAG, "Failed to hide module settings", e)
        }
    }
    
    /**
     * 设置设置面板内容
     */
    private fun setupSettingsPanel(settingsPanel: View, module: KasumiModule) {
        try {
            // 设置模块名称（在设置面板内的简化版本）
            val moduleName = settingsPanel.findViewById<TextView>(R.id.tvModuleName)
            moduleName?.text = module.name
            
            // 设置返回按钮
            val btnBack = settingsPanel.findViewById<ImageView>(R.id.btnBack)
            btnBack?.setOnClickListener {
                hideModuleSettings()
            }

            // 设置设置列表
            val rvSettings = settingsPanel.findViewById<RecyclerView>(R.id.rvSettings)
            rvSettings?.layoutManager = LinearLayoutManager(this)
            rvSettings?.adapter = ModuleSettingsAdapter(module.id, module.settings)
            
            Log.d(TAG, "Settings panel setup completed for module: ${module.name}")
        } catch (e: Exception) {
            Log.e(TAG, "Failed to setup settings panel", e)
        }
    }

    /**
     * 从精简模式切换到展开模式并显示设置面板
     */
    private fun switchToExpandedModeAndShowSettings(module: KasumiModule) {
        try {
            // 先切换到展开模式
            switchToExpandedMode()
            
            // 延迟显示设置面板，等待展开动画完成
            floatingView?.postDelayed({
                showModuleSettings(module)
            }, 300) // 等待展开动画完成
            
            Log.d(TAG, "Switched to expanded mode and showing settings for: ${module.name}")
        } catch (e: Exception) {
            Log.e(TAG, "Failed to switch to expanded mode and show settings", e)
        }
    }
}
