package mc.meson.kasumi.config

import android.os.Bundle
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import androidx.fragment.app.Fragment
import androidx.recyclerview.widget.LinearLayoutManager
import com.google.android.material.snackbar.Snackbar
import mc.meson.kasumi.R
import mc.meson.kasumi.databinding.FragmentModuleSettingsBinding
import mc.meson.kasumi.module.KasumiModule
import mc.meson.kasumi.module.ModuleSettingsAdapter

/**
 * 模块设置页面
 * 显示单个模块的详细设置选项
 */
class ModuleSettingsFragment : Fragment() {
    
    private var _binding: FragmentModuleSettingsBinding? = null
    private val binding get() = _binding!!
    
    private lateinit var module: KasumiModule
    private lateinit var settingsAdapter: ModuleSettingsAdapter
    
    companion object {
        private const val ARG_MODULE_ID = "module_id"
        
        fun newInstance(moduleId: String): ModuleSettingsFragment {
            return ModuleSettingsFragment().apply {
                arguments = Bundle().apply {
                    putString(ARG_MODULE_ID, moduleId)
                }
            }
        }
    }
    
    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        
        val moduleId = arguments?.getString(ARG_MODULE_ID)
            ?: throw IllegalArgumentException("Module ID is required")
        
        module = mc.meson.kasumi.module.ModuleRegistry.getModule(moduleId)
            ?: throw IllegalArgumentException("Module not found: $moduleId")
    }
    
    override fun onCreateView(
        inflater: LayoutInflater,
        container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View {
        _binding = FragmentModuleSettingsBinding.inflate(inflater, container, false)
        return binding.root
    }
    
    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)
        
        setupUI()
        setupRecyclerView()
        setupListeners()
    }
    
    private fun setupUI() {
        // 设置模块信息
        binding.tvModuleName.text = module.name
        binding.tvModuleDescription.text = module.description
        binding.ivModuleIcon.setImageResource(module.iconRes)
        
        // 设置模块开关状态
        binding.switchModuleEnabled.isChecked = module.isEnabled
        
        // 设置分类颜色
        val categoryColor = module.category.color
        binding.viewCategoryIndicator.setBackgroundColor(categoryColor)
        binding.tvModuleCategory.text = module.category.displayName
        binding.tvModuleCategory.setTextColor(categoryColor)
    }
    
    private fun setupRecyclerView() {
        if (module.settings.isNotEmpty()) {
            settingsAdapter = ModuleSettingsAdapter(
                module.id,
                module.settings
            )
            
            binding.recyclerViewSettings.apply {
                layoutManager = LinearLayoutManager(requireContext())
                adapter = settingsAdapter
            }
            
            binding.recyclerViewSettings.visibility = View.VISIBLE
            binding.tvNoSettings.visibility = View.GONE
        } else {
            binding.recyclerViewSettings.visibility = View.GONE
            binding.tvNoSettings.visibility = View.VISIBLE
        }
    }
    
    private fun setupListeners() {
        // 返回按钮
        binding.btnBack.setOnClickListener {
            parentFragmentManager.popBackStack()
        }
        
        // 模块开关
        binding.switchModuleEnabled.setOnCheckedChangeListener { _, isChecked ->
            module.isEnabled = isChecked
            updateSettingsVisibility()
            
            val message = if (isChecked) {
                "${module.name} 已启用"
            } else {
                "${module.name} 已禁用"
            }
            
            val color = if (isChecked) {
                R.color.minecraft_green
            } else {
                R.color.minecraft_red
            }
            
            Snackbar.make(binding.root, message, Snackbar.LENGTH_SHORT)
                .setBackgroundTint(requireContext().getColor(color))
                .setTextColor(requireContext().getColor(R.color.white))
                .show()
        }
        
        // 重置设置按钮
        binding.btnResetSettings.setOnClickListener {
            resetModuleSettings()
        }
    }
    
    private fun updateSettingsVisibility() {
        // 当模块禁用时，可以选择是否隐藏设置项
        val alpha = if (module.isEnabled) 1.0f else 0.6f
        binding.recyclerViewSettings.alpha = alpha
    }
    

    
    private fun resetModuleSettings() {
        // 重置所有设置为默认值
        module.settings.forEach { setting ->
            when (setting) {
                is mc.meson.kasumi.module.ToggleSetting -> {
                    mc.meson.kasumi.module.ModuleSettingsManager.setBooleanSetting(module.id, setting.key, setting.defaultValue)
                }
                is mc.meson.kasumi.module.ModeSetting -> {
                    mc.meson.kasumi.module.ModuleSettingsManager.setIntSetting(module.id, setting.key, setting.defaultIndex)
                }
                is mc.meson.kasumi.module.SliderSetting -> {
                    mc.meson.kasumi.module.ModuleSettingsManager.setFloatSetting(module.id, setting.key, setting.defaultValue)
                }
            }
        }

        // 刷新设置显示
        if (::settingsAdapter.isInitialized) {
            settingsAdapter.notifyDataSetChanged()
        }

        Snackbar.make(binding.root, "设置已重置为默认值", Snackbar.LENGTH_SHORT)
            .setBackgroundTint(requireContext().getColor(R.color.md_theme_light_primaryContainer))
            .setTextColor(requireContext().getColor(R.color.md_theme_light_onPrimaryContainer))
            .show()
    }
    
    override fun onDestroyView() {
        super.onDestroyView()
        _binding = null
    }
}
