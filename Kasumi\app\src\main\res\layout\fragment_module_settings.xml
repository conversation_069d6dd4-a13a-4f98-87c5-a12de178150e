<?xml version="1.0" encoding="utf-8"?>
<androidx.coordinatorlayout.widget.CoordinatorLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="?attr/colorSurface">

    <!-- App Bar Layout with Collapsing Toolbar -->
    <com.google.android.material.appbar.AppBarLayout
        android:id="@+id/appBarLayout"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:theme="@style/ThemeOverlay.Material3.Dark">

        <com.google.android.material.appbar.CollapsingToolbarLayout
            android:id="@+id/collapsingToolbar"
            android:layout_width="match_parent"
            android:layout_height="200dp"
            app:layout_scrollFlags="scroll|exitUntilCollapsed"
            app:contentScrim="?attr/colorPrimary"
            app:statusBarScrim="?attr/colorPrimaryDark"
            app:expandedTitleMarginStart="72dp"
            app:expandedTitleMarginBottom="32dp">

            <!-- Background Gradient -->
            <View
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:background="@drawable/splash_gradient"
                app:layout_collapseMode="parallax" />

            <!-- Module Icon and Info -->
            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:orientation="horizontal"
                android:gravity="center_vertical"
                android:padding="24dp"
                app:layout_collapseMode="parallax">

                <com.google.android.material.card.MaterialCardView
                    android:layout_width="80dp"
                    android:layout_height="80dp"
                    app:cardCornerRadius="20dp"
                    app:cardElevation="8dp"
                    app:cardBackgroundColor="?attr/colorSurface">

                    <ImageView
                        android:id="@+id/ivModuleIcon"
                        android:layout_width="48dp"
                        android:layout_height="48dp"
                        android:layout_gravity="center"
                        android:src="@drawable/ic_features_24"
                        app:tint="?attr/colorPrimary" />

                </com.google.android.material.card.MaterialCardView>

                <LinearLayout
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_weight="1"
                    android:orientation="vertical"
                    android:layout_marginStart="20dp">

                    <TextView
                        android:id="@+id/tvModuleName"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="模块名称"
                        android:textAppearance="?attr/textAppearanceHeadlineSmall"
                        android:textColor="?attr/colorOnPrimary"
                        android:textStyle="bold" />

                    <LinearLayout
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:orientation="horizontal"
                        android:gravity="center_vertical"
                        android:layout_marginTop="4dp">

                        <View
                            android:id="@+id/viewCategoryIndicator"
                            android:layout_width="8dp"
                            android:layout_height="8dp"
                            android:background="@drawable/category_indicator" />

                        <TextView
                            android:id="@+id/tvModuleCategory"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:text="分类"
                            android:textAppearance="?attr/textAppearanceBodyMedium"
                            android:textColor="?attr/colorOnPrimary"
                            android:layout_marginStart="8dp" />

                    </LinearLayout>

                    <TextView
                        android:id="@+id/tvModuleDescription"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="模块描述"
                        android:textAppearance="?attr/textAppearanceBodyMedium"
                        android:textColor="?attr/colorOnPrimary"
                        android:alpha="0.8"
                        android:layout_marginTop="8dp"
                        android:maxLines="2"
                        android:ellipsize="end" />

                </LinearLayout>

            </LinearLayout>

            <!-- Toolbar -->
            <com.google.android.material.appbar.MaterialToolbar
                android:id="@+id/toolbar"
                android:layout_width="match_parent"
                android:layout_height="?attr/actionBarSize"
                app:layout_collapseMode="pin"
                app:navigationIcon="@drawable/ic_arrow_back_24"
                app:navigationIconTint="?attr/colorOnPrimary">

                <com.google.android.material.materialswitch.MaterialSwitch
                    android:id="@+id/switchModuleEnabled"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_gravity="end"
                    android:layout_marginEnd="16dp"
                    app:thumbTint="?attr/colorOnPrimary"
                    app:trackTint="?attr/colorPrimaryContainer" />

            </com.google.android.material.appbar.MaterialToolbar>

        </com.google.android.material.appbar.CollapsingToolbarLayout>

    </com.google.android.material.appbar.AppBarLayout>

    <!-- Content -->
    <androidx.core.widget.NestedScrollView
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        app:layout_behavior="@string/appbar_scrolling_view_behavior">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="vertical"
            android:padding="16dp">

            <!-- Status Card -->
            <com.google.android.material.card.MaterialCardView
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginBottom="16dp"
                app:cardCornerRadius="16dp"
                app:cardElevation="2dp"
                app:cardBackgroundColor="?attr/colorSurfaceVariant">

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="horizontal"
                    android:padding="20dp"
                    android:gravity="center_vertical">

                    <LinearLayout
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:layout_weight="1"
                        android:orientation="vertical">

                        <TextView
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:text="模块状态"
                            android:textAppearance="?attr/textAppearanceLabelMedium"
                            android:textColor="?attr/colorOnSurfaceVariant"
                            android:textStyle="bold" />

                        <TextView
                            android:id="@+id/tvModuleStatus"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:text="已启用"
                            android:textAppearance="?attr/textAppearanceTitleMedium"
                            android:textColor="?attr/colorPrimary"
                            android:layout_marginTop="4dp" />

                    </LinearLayout>

                    <com.google.android.material.card.MaterialCardView
                        android:layout_width="48dp"
                        android:layout_height="48dp"
                        app:cardCornerRadius="24dp"
                        app:cardElevation="0dp"
                        app:cardBackgroundColor="?attr/colorPrimary">

                        <ImageView
                            android:id="@+id/ivStatusIcon"
                            android:layout_width="24dp"
                            android:layout_height="24dp"
                            android:layout_gravity="center"
                            android:src="@drawable/ic_check_circle_24"
                            app:tint="?attr/colorOnPrimary" />

                    </com.google.android.material.card.MaterialCardView>

                </LinearLayout>

            </com.google.android.material.card.MaterialCardView>

            <!-- Settings Section -->
            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="设置选项"
                android:textAppearance="?attr/textAppearanceTitleMedium"
                android:textColor="?attr/colorOnSurface"
                android:textStyle="bold"
                android:layout_marginBottom="12dp" />

            <!-- Settings List -->
            <androidx.recyclerview.widget.RecyclerView
                android:id="@+id/recyclerViewSettings"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:nestedScrollingEnabled="false"
                android:layout_marginBottom="16dp" />

            <!-- No Settings State -->
            <com.google.android.material.card.MaterialCardView
                android:id="@+id/cardNoSettings"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginBottom="16dp"
                android:visibility="gone"
                app:cardCornerRadius="16dp"
                app:cardElevation="0dp"
                app:strokeWidth="1dp"
                app:strokeColor="?attr/colorOutline"
                app:cardBackgroundColor="?attr/colorSurface">

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="vertical"
                    android:gravity="center"
                    android:padding="32dp">

                    <com.google.android.material.card.MaterialCardView
                        android:layout_width="64dp"
                        android:layout_height="64dp"
                        app:cardCornerRadius="32dp"
                        app:cardElevation="0dp"
                        app:cardBackgroundColor="?attr/colorSurfaceVariant">

                        <ImageView
                            android:layout_width="32dp"
                            android:layout_height="32dp"
                            android:layout_gravity="center"
                            android:src="@drawable/ic_settings_24"
                            app:tint="?attr/colorOnSurfaceVariant" />

                    </com.google.android.material.card.MaterialCardView>

                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="无可配置选项"
                        android:textAppearance="?attr/textAppearanceTitleMedium"
                        android:textColor="?attr/colorOnSurface"
                        android:layout_marginTop="16dp" />

                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="此模块没有额外的配置选项\n您可以通过上方的开关来启用或禁用模块"
                        android:textAppearance="?attr/textAppearanceBodyMedium"
                        android:textColor="?attr/colorOnSurfaceVariant"
                        android:layout_marginTop="8dp"
                        android:gravity="center"
                        android:lineSpacingExtra="2dp" />

                </LinearLayout>

            </com.google.android.material.card.MaterialCardView>

            <!-- Action Buttons -->
            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="horizontal"
                android:layout_marginTop="8dp">

                <com.google.android.material.button.MaterialButton
                    android:id="@+id/btnResetSettings"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_weight="1"
                    android:text="重置设置"
                    android:layout_marginEnd="8dp"
                    style="@style/Widget.Material3.Button.OutlinedButton" />

                <com.google.android.material.button.MaterialButton
                    android:id="@+id/btnApplySettings"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_weight="1"
                    android:text="应用设置"
                    android:layout_marginStart="8dp"
                    style="@style/Widget.Material3.Button" />

            </LinearLayout>

        </LinearLayout>

    </androidx.core.widget.NestedScrollView>

</androidx.coordinatorlayout.widget.CoordinatorLayout>
