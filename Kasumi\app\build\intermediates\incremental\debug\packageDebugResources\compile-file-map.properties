#Thu Jul 31 16:29:52 CST 2025
mc.meson.kasumi.app-main-5\:/anim/bounce.xml=D\:\\Kasumi\\Kasumi\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\anim\\bounce.xml
mc.meson.kasumi.app-main-5\:/anim/fade_in.xml=D\:\\Kasumi\\Kasumi\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\anim\\fade_in.xml
mc.meson.kasumi.app-main-5\:/anim/fade_out.xml=D\:\\Kasumi\\Kasumi\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\anim\\fade_out.xml
mc.meson.kasumi.app-main-5\:/anim/fragment_fade_enter.xml=D\:\\Kasumi\\Kasumi\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\anim\\fragment_fade_enter.xml
mc.meson.kasumi.app-main-5\:/anim/fragment_fade_exit.xml=D\:\\Kasumi\\Kasumi\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\anim\\fragment_fade_exit.xml
mc.meson.kasumi.app-main-5\:/anim/slide_in_right.xml=D\:\\Kasumi\\Kasumi\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\anim\\slide_in_right.xml
mc.meson.kasumi.app-main-5\:/anim/slide_out_left.xml=D\:\\Kasumi\\Kasumi\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\anim\\slide_out_left.xml
mc.meson.kasumi.app-main-5\:/color/bottom_nav_item_color.xml=D\:\\Kasumi\\Kasumi\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\color\\bottom_nav_item_color.xml
mc.meson.kasumi.app-main-5\:/drawable/button_ripple.xml=D\:\\Kasumi\\Kasumi\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\drawable\\button_ripple.xml
mc.meson.kasumi.app-main-5\:/drawable/card_background.xml=D\:\\Kasumi\\Kasumi\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\drawable\\card_background.xml
mc.meson.kasumi.app-main-5\:/drawable/category_indicator.xml=D\:\\Kasumi\\Kasumi\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\drawable\\category_indicator.xml
mc.meson.kasumi.app-main-5\:/drawable/custom_scrollbar_thumb.xml=D\:\\Kasumi\\Kasumi\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\drawable\\custom_scrollbar_thumb.xml
mc.meson.kasumi.app-main-5\:/drawable/custom_scrollbar_track.xml=D\:\\Kasumi\\Kasumi\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\drawable\\custom_scrollbar_track.xml
mc.meson.kasumi.app-main-5\:/drawable/drag_indicator.xml=D\:\\Kasumi\\Kasumi\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\drawable\\drag_indicator.xml
mc.meson.kasumi.app-main-5\:/drawable/floating_button_background.xml=D\:\\Kasumi\\Kasumi\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\drawable\\floating_button_background.xml
mc.meson.kasumi.app-main-5\:/drawable/floating_button_ripple.xml=D\:\\Kasumi\\Kasumi\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\drawable\\floating_button_ripple.xml
mc.meson.kasumi.app-main-5\:/drawable/floating_button_style.xml=D\:\\Kasumi\\Kasumi\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\drawable\\floating_button_style.xml
mc.meson.kasumi.app-main-5\:/drawable/floating_divider.xml=D\:\\Kasumi\\Kasumi\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\drawable\\floating_divider.xml
mc.meson.kasumi.app-main-5\:/drawable/floating_item_background.xml=D\:\\Kasumi\\Kasumi\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\drawable\\floating_item_background.xml
mc.meson.kasumi.app-main-5\:/drawable/ic_arrow_back_24.xml=D\:\\Kasumi\\Kasumi\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\drawable\\ic_arrow_back_24.xml
mc.meson.kasumi.app-main-5\:/drawable/ic_arrow_drop_down_24.xml=D\:\\Kasumi\\Kasumi\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\drawable\\ic_arrow_drop_down_24.xml
mc.meson.kasumi.app-main-5\:/drawable/ic_check_circle_24.xml=D\:\\Kasumi\\Kasumi\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\drawable\\ic_check_circle_24.xml
mc.meson.kasumi.app-main-5\:/drawable/ic_close.xml=D\:\\Kasumi\\Kasumi\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\drawable\\ic_close.xml
mc.meson.kasumi.app-main-5\:/drawable/ic_close_24.xml=D\:\\Kasumi\\Kasumi\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\drawable\\ic_close_24.xml
mc.meson.kasumi.app-main-5\:/drawable/ic_collapse_24.xml=D\:\\Kasumi\\Kasumi\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\drawable\\ic_collapse_24.xml
mc.meson.kasumi.app-main-5\:/drawable/ic_compress.xml=D\:\\Kasumi\\Kasumi\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\drawable\\ic_compress.xml
mc.meson.kasumi.app-main-5\:/drawable/ic_expand_more.xml=D\:\\Kasumi\\Kasumi\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\drawable\\ic_expand_more.xml
mc.meson.kasumi.app-main-5\:/drawable/ic_features_24.xml=D\:\\Kasumi\\Kasumi\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\drawable\\ic_features_24.xml
mc.meson.kasumi.app-main-5\:/drawable/ic_home_24.xml=D\:\\Kasumi\\Kasumi\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\drawable\\ic_home_24.xml
mc.meson.kasumi.app-main-5\:/drawable/ic_kasumi_floating.xml=D\:\\Kasumi\\Kasumi\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\drawable\\ic_kasumi_floating.xml
mc.meson.kasumi.app-main-5\:/drawable/ic_kasumi_logo.xml=D\:\\Kasumi\\Kasumi\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\drawable\\ic_kasumi_logo.xml
mc.meson.kasumi.app-main-5\:/drawable/ic_launcher_background.xml=D\:\\Kasumi\\Kasumi\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\drawable\\ic_launcher_background.xml
mc.meson.kasumi.app-main-5\:/drawable/ic_launcher_foreground.xml=D\:\\Kasumi\\Kasumi\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\drawable\\ic_launcher_foreground.xml
mc.meson.kasumi.app-main-5\:/drawable/ic_resize.xml=D\:\\Kasumi\\Kasumi\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\drawable\\ic_resize.xml
mc.meson.kasumi.app-main-5\:/drawable/ic_settings_24.xml=D\:\\Kasumi\\Kasumi\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\drawable\\ic_settings_24.xml
mc.meson.kasumi.app-main-5\:/drawable/ic_zoom_out_map.xml=D\:\\Kasumi\\Kasumi\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\drawable\\ic_zoom_out_map.xml
mc.meson.kasumi.app-main-5\:/drawable/mode_option_indicator.xml=D\:\\Kasumi\\Kasumi\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\drawable\\mode_option_indicator.xml
mc.meson.kasumi.app-main-5\:/drawable/resize_handle_background.xml=D\:\\Kasumi\\Kasumi\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\drawable\\resize_handle_background.xml
mc.meson.kasumi.app-main-5\:/drawable/settings_panel_background.xml=D\:\\Kasumi\\Kasumi\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\drawable\\settings_panel_background.xml
mc.meson.kasumi.app-main-5\:/drawable/splash_gradient.xml=D\:\\Kasumi\\Kasumi\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\drawable\\splash_gradient.xml
mc.meson.kasumi.app-main-5\:/drawable/status_indicator.xml=D\:\\Kasumi\\Kasumi\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\drawable\\status_indicator.xml
mc.meson.kasumi.app-main-5\:/drawable/tab_background_selector.xml=D\:\\Kasumi\\Kasumi\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\drawable\\tab_background_selector.xml
mc.meson.kasumi.app-main-5\:/drawable/tab_indicator_rounded.xml=D\:\\Kasumi\\Kasumi\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\drawable\\tab_indicator_rounded.xml
mc.meson.kasumi.app-main-5\:/drawable/tab_indicator_simple.xml=D\:\\Kasumi\\Kasumi\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\drawable\\tab_indicator_simple.xml
mc.meson.kasumi.app-main-5\:/drawable/tab_layout_background.xml=D\:\\Kasumi\\Kasumi\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\drawable\\tab_layout_background.xml
mc.meson.kasumi.app-main-5\:/menu/bottom_navigation_menu.xml=D\:\\Kasumi\\Kasumi\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\menu\\bottom_navigation_menu.xml
mc.meson.kasumi.app-main-5\:/mipmap-anydpi/ic_launcher.xml=D\:\\Kasumi\\Kasumi\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\mipmap-anydpi-v4\\ic_launcher.xml
mc.meson.kasumi.app-main-5\:/mipmap-anydpi/ic_launcher_round.xml=D\:\\Kasumi\\Kasumi\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\mipmap-anydpi-v4\\ic_launcher_round.xml
mc.meson.kasumi.app-main-5\:/mipmap-hdpi/ic_launcher.webp=D\:\\Kasumi\\Kasumi\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\mipmap-hdpi-v4\\ic_launcher.webp
mc.meson.kasumi.app-main-5\:/mipmap-hdpi/ic_launcher_round.webp=D\:\\Kasumi\\Kasumi\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\mipmap-hdpi-v4\\ic_launcher_round.webp
mc.meson.kasumi.app-main-5\:/mipmap-mdpi/ic_launcher.webp=D\:\\Kasumi\\Kasumi\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\mipmap-mdpi-v4\\ic_launcher.webp
mc.meson.kasumi.app-main-5\:/mipmap-mdpi/ic_launcher_round.webp=D\:\\Kasumi\\Kasumi\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\mipmap-mdpi-v4\\ic_launcher_round.webp
mc.meson.kasumi.app-main-5\:/mipmap-xhdpi/ic_launcher.webp=D\:\\Kasumi\\Kasumi\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\mipmap-xhdpi-v4\\ic_launcher.webp
mc.meson.kasumi.app-main-5\:/mipmap-xhdpi/ic_launcher_round.webp=D\:\\Kasumi\\Kasumi\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\mipmap-xhdpi-v4\\ic_launcher_round.webp
mc.meson.kasumi.app-main-5\:/mipmap-xxhdpi/ic_launcher.webp=D\:\\Kasumi\\Kasumi\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\mipmap-xxhdpi-v4\\ic_launcher.webp
mc.meson.kasumi.app-main-5\:/mipmap-xxhdpi/ic_launcher_round.webp=D\:\\Kasumi\\Kasumi\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\mipmap-xxhdpi-v4\\ic_launcher_round.webp
mc.meson.kasumi.app-main-5\:/mipmap-xxxhdpi/ic_launcher.webp=D\:\\Kasumi\\Kasumi\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\mipmap-xxxhdpi-v4\\ic_launcher.webp
mc.meson.kasumi.app-main-5\:/mipmap-xxxhdpi/ic_launcher_round.webp=D\:\\Kasumi\\Kasumi\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\mipmap-xxxhdpi-v4\\ic_launcher_round.webp
mc.meson.kasumi.app-main-5\:/navigation/nav_graph.xml=D\:\\Kasumi\\Kasumi\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\navigation\\nav_graph.xml
mc.meson.kasumi.app-main-5\:/xml/backup_rules.xml=D\:\\Kasumi\\Kasumi\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\xml\\backup_rules.xml
mc.meson.kasumi.app-main-5\:/xml/data_extraction_rules.xml=D\:\\Kasumi\\Kasumi\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\xml\\data_extraction_rules.xml
mc.meson.kasumi.app-packageDebugResources-2\:/layout/activity_main.xml=D\:\\Kasumi\\Kasumi\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\layout\\activity_main.xml
mc.meson.kasumi.app-packageDebugResources-2\:/layout/activity_main_with_navigation.xml=D\:\\Kasumi\\Kasumi\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\layout\\activity_main_with_navigation.xml
mc.meson.kasumi.app-packageDebugResources-2\:/layout/activity_splash.xml=D\:\\Kasumi\\Kasumi\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\layout\\activity_splash.xml
mc.meson.kasumi.app-packageDebugResources-2\:/layout/compact_category_page.xml=D\:\\Kasumi\\Kasumi\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\layout\\compact_category_page.xml
mc.meson.kasumi.app-packageDebugResources-2\:/layout/dialog_create_config.xml=D\:\\Kasumi\\Kasumi\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\layout\\dialog_create_config.xml
mc.meson.kasumi.app-packageDebugResources-2\:/layout/floating_window_compact_v2.xml=D\:\\Kasumi\\Kasumi\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\layout\\floating_window_compact_v2.xml
mc.meson.kasumi.app-packageDebugResources-2\:/layout/floating_window_expanded_new.xml=D\:\\Kasumi\\Kasumi\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\layout\\floating_window_expanded_new.xml
mc.meson.kasumi.app-packageDebugResources-2\:/layout/fragment_config_management.xml=D\:\\Kasumi\\Kasumi\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\layout\\fragment_config_management.xml
mc.meson.kasumi.app-packageDebugResources-2\:/layout/fragment_hack_features.xml=D\:\\Kasumi\\Kasumi\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\layout\\fragment_hack_features.xml
mc.meson.kasumi.app-packageDebugResources-2\:/layout/fragment_home.xml=D\:\\Kasumi\\Kasumi\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\layout\\fragment_home.xml
mc.meson.kasumi.app-packageDebugResources-2\:/layout/fragment_module_settings.xml=D\:\\Kasumi\\Kasumi\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\layout\\fragment_module_settings.xml
mc.meson.kasumi.app-packageDebugResources-2\:/layout/fragment_settings.xml=D\:\\Kasumi\\Kasumi\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\layout\\fragment_settings.xml
mc.meson.kasumi.app-packageDebugResources-2\:/layout/item_compact_module.xml=D\:\\Kasumi\\Kasumi\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\layout\\item_compact_module.xml
mc.meson.kasumi.app-packageDebugResources-2\:/layout/item_config.xml=D\:\\Kasumi\\Kasumi\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\layout\\item_config.xml
mc.meson.kasumi.app-packageDebugResources-2\:/layout/item_expanded_module.xml=D\:\\Kasumi\\Kasumi\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\layout\\item_expanded_module.xml
mc.meson.kasumi.app-packageDebugResources-2\:/layout/item_mode_option.xml=D\:\\Kasumi\\Kasumi\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\layout\\item_mode_option.xml
mc.meson.kasumi.app-packageDebugResources-2\:/layout/item_module_grid.xml=D\:\\Kasumi\\Kasumi\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\layout\\item_module_grid.xml
mc.meson.kasumi.app-packageDebugResources-2\:/layout/item_module_list.xml=D\:\\Kasumi\\Kasumi\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\layout\\item_module_list.xml
mc.meson.kasumi.app-packageDebugResources-2\:/layout/item_setting_mode.xml=D\:\\Kasumi\\Kasumi\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\layout\\item_setting_mode.xml
mc.meson.kasumi.app-packageDebugResources-2\:/layout/item_setting_slider.xml=D\:\\Kasumi\\Kasumi\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\layout\\item_setting_slider.xml
mc.meson.kasumi.app-packageDebugResources-2\:/layout/item_setting_toggle.xml=D\:\\Kasumi\\Kasumi\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\layout\\item_setting_toggle.xml
mc.meson.kasumi.app-packageDebugResources-2\:/layout/module_settings_panel.xml=D\:\\Kasumi\\Kasumi\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\layout\\module_settings_panel.xml
