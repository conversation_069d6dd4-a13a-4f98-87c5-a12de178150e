[{"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.7\\mc.meson.kasumi.app-debug-49:\\drawable_category_indicator.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.7\\mc.meson.kasumi.app-main-51:\\drawable\\category_indicator.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.7\\mc.meson.kasumi.app-debug-49:\\drawable_ic_zoom_out_map.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.7\\mc.meson.kasumi.app-main-51:\\drawable\\ic_zoom_out_map.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.7\\mc.meson.kasumi.app-debug-49:\\drawable_resize_handle_background.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.7\\mc.meson.kasumi.app-main-51:\\drawable\\resize_handle_background.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.7\\mc.meson.kasumi.app-debug-49:\\drawable_card_background.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.7\\mc.meson.kasumi.app-main-51:\\drawable\\card_background.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.7\\mc.meson.kasumi.app-debug-49:\\drawable_tab_background_selector.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.7\\mc.meson.kasumi.app-main-51:\\drawable\\tab_background_selector.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.7\\mc.meson.kasumi.app-debug-49:\\mipmap-anydpi_ic_launcher_round.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.7\\mc.meson.kasumi.app-main-51:\\mipmap-anydpi\\ic_launcher_round.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.7\\mc.meson.kasumi.app-debug-49:\\mipmap-xxhdpi_ic_launcher_round.webp.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.7\\mc.meson.kasumi.app-main-51:\\mipmap-xxhdpi\\ic_launcher_round.webp"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.7\\mc.meson.kasumi.app-debug-49:\\layout_floating_window_compact_v2.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.7\\mc.meson.kasumi.app-main-51:\\layout\\floating_window_compact_v2.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.7\\mc.meson.kasumi.app-debug-49:\\mipmap-xhdpi_ic_launcher.webp.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.7\\mc.meson.kasumi.app-main-51:\\mipmap-xhdpi\\ic_launcher.webp"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.7\\mc.meson.kasumi.app-debug-49:\\anim_fragment_fade_enter.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.7\\mc.meson.kasumi.app-main-51:\\anim\\fragment_fade_enter.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.7\\mc.meson.kasumi.app-debug-49:\\anim_fragment_fade_exit.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.7\\mc.meson.kasumi.app-main-51:\\anim\\fragment_fade_exit.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.7\\mc.meson.kasumi.app-debug-49:\\drawable_tab_layout_background.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.7\\mc.meson.kasumi.app-main-51:\\drawable\\tab_layout_background.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.7\\mc.meson.kasumi.app-debug-49:\\mipmap-hdpi_ic_launcher.webp.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.7\\mc.meson.kasumi.app-main-51:\\mipmap-hdpi\\ic_launcher.webp"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.7\\mc.meson.kasumi.app-debug-49:\\xml_backup_rules.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.7\\mc.meson.kasumi.app-main-51:\\xml\\backup_rules.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.7\\mc.meson.kasumi.app-debug-49:\\drawable_floating_button_ripple.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.7\\mc.meson.kasumi.app-main-51:\\drawable\\floating_button_ripple.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.7\\mc.meson.kasumi.app-debug-49:\\mipmap-mdpi_ic_launcher_round.webp.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.7\\mc.meson.kasumi.app-main-51:\\mipmap-mdpi\\ic_launcher_round.webp"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.7\\mc.meson.kasumi.app-debug-49:\\drawable_mode_option_indicator.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.7\\mc.meson.kasumi.app-main-51:\\drawable\\mode_option_indicator.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.7\\mc.meson.kasumi.app-debug-49:\\drawable_ic_kasumi_logo.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.7\\mc.meson.kasumi.app-main-51:\\drawable\\ic_kasumi_logo.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.7\\mc.meson.kasumi.app-debug-49:\\layout_fragment_settings.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.7\\mc.meson.kasumi.app-main-51:\\layout\\fragment_settings.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.7\\mc.meson.kasumi.app-debug-49:\\drawable_ic_kasumi_floating.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.7\\mc.meson.kasumi.app-main-51:\\drawable\\ic_kasumi_floating.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.7\\mc.meson.kasumi.app-debug-49:\\drawable_ic_compress.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.7\\mc.meson.kasumi.app-main-51:\\drawable\\ic_compress.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.7\\mc.meson.kasumi.app-debug-49:\\drawable_tab_indicator_rounded.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.7\\mc.meson.kasumi.app-main-51:\\drawable\\tab_indicator_rounded.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.7\\mc.meson.kasumi.app-debug-49:\\anim_fade_in.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.7\\mc.meson.kasumi.app-main-51:\\anim\\fade_in.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.7\\mc.meson.kasumi.app-debug-49:\\layout_compact_category_page.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.7\\mc.meson.kasumi.app-main-51:\\layout\\compact_category_page.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.7\\mc.meson.kasumi.app-debug-49:\\drawable_ic_collapse_24.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.7\\mc.meson.kasumi.app-main-51:\\drawable\\ic_collapse_24.xml"}, {"merged": "mc.meson.kasumi.app-debug-49:/layout_fragment_module_settings.xml.flat", "source": "mc.meson.kasumi.app-main-51:/layout/fragment_module_settings.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.7\\mc.meson.kasumi.app-debug-49:\\mipmap-hdpi_ic_launcher_round.webp.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.7\\mc.meson.kasumi.app-main-51:\\mipmap-hdpi\\ic_launcher_round.webp"}, {"merged": "mc.meson.kasumi.app-debug-49:/drawable_ic_circle.xml.flat", "source": "mc.meson.kasumi.app-main-51:/drawable/ic_circle.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.7\\mc.meson.kasumi.app-debug-49:\\color_bottom_nav_item_color.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.7\\mc.meson.kasumi.app-main-51:\\color\\bottom_nav_item_color.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.7\\mc.meson.kasumi.app-debug-49:\\anim_slide_out_left.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.7\\mc.meson.kasumi.app-main-51:\\anim\\slide_out_left.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.7\\mc.meson.kasumi.app-debug-49:\\mipmap-xhdpi_ic_launcher_round.webp.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.7\\mc.meson.kasumi.app-main-51:\\mipmap-xhdpi\\ic_launcher_round.webp"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.7\\mc.meson.kasumi.app-debug-49:\\drawable_ic_launcher_background.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.7\\mc.meson.kasumi.app-main-51:\\drawable\\ic_launcher_background.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.7\\mc.meson.kasumi.app-debug-49:\\drawable_floating_button_style.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.7\\mc.meson.kasumi.app-main-51:\\drawable\\floating_button_style.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.7\\mc.meson.kasumi.app-debug-49:\\drawable_ic_expand_more.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.7\\mc.meson.kasumi.app-main-51:\\drawable\\ic_expand_more.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.7\\mc.meson.kasumi.app-debug-49:\\layout_item_module_grid.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.7\\mc.meson.kasumi.app-main-51:\\layout\\item_module_grid.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.7\\mc.meson.kasumi.app-debug-49:\\drawable_custom_scrollbar_thumb.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.7\\mc.meson.kasumi.app-main-51:\\drawable\\custom_scrollbar_thumb.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.7\\mc.meson.kasumi.app-debug-49:\\layout_item_expanded_module.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.7\\mc.meson.kasumi.app-main-51:\\layout\\item_expanded_module.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.7\\mc.meson.kasumi.app-debug-49:\\drawable_floating_divider.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.7\\mc.meson.kasumi.app-main-51:\\drawable\\floating_divider.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.7\\mc.meson.kasumi.app-debug-49:\\mipmap-xxhdpi_ic_launcher.webp.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.7\\mc.meson.kasumi.app-main-51:\\mipmap-xxhdpi\\ic_launcher.webp"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.7\\mc.meson.kasumi.app-debug-49:\\xml_data_extraction_rules.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.7\\mc.meson.kasumi.app-main-51:\\xml\\data_extraction_rules.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.7\\mc.meson.kasumi.app-debug-49:\\drawable_ic_close.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.7\\mc.meson.kasumi.app-main-51:\\drawable\\ic_close.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.7\\mc.meson.kasumi.app-debug-49:\\mipmap-mdpi_ic_launcher.webp.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.7\\mc.meson.kasumi.app-main-51:\\mipmap-mdpi\\ic_launcher.webp"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.7\\mc.meson.kasumi.app-debug-49:\\drawable_custom_scrollbar_track.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.7\\mc.meson.kasumi.app-main-51:\\drawable\\custom_scrollbar_track.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.7\\mc.meson.kasumi.app-debug-49:\\drawable_ic_close_24.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.7\\mc.meson.kasumi.app-main-51:\\drawable\\ic_close_24.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.7\\mc.meson.kasumi.app-debug-49:\\drawable_ic_arrow_back_24.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.7\\mc.meson.kasumi.app-main-51:\\drawable\\ic_arrow_back_24.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.7\\mc.meson.kasumi.app-debug-49:\\mipmap-xxxhdpi_ic_launcher_round.webp.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.7\\mc.meson.kasumi.app-main-51:\\mipmap-xxxhdpi\\ic_launcher_round.webp"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.7\\mc.meson.kasumi.app-debug-49:\\layout_fragment_home.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.7\\mc.meson.kasumi.app-main-51:\\layout\\fragment_home.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.7\\mc.meson.kasumi.app-debug-49:\\menu_bottom_navigation_menu.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.7\\mc.meson.kasumi.app-main-51:\\menu\\bottom_navigation_menu.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.7\\mc.meson.kasumi.app-debug-49:\\drawable_ic_launcher_foreground.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.7\\mc.meson.kasumi.app-main-51:\\drawable\\ic_launcher_foreground.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.7\\mc.meson.kasumi.app-debug-49:\\layout_floating_window_expanded_new.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.7\\mc.meson.kasumi.app-main-51:\\layout\\floating_window_expanded_new.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.7\\mc.meson.kasumi.app-debug-49:\\navigation_nav_graph.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.7\\mc.meson.kasumi.app-main-51:\\navigation\\nav_graph.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.7\\mc.meson.kasumi.app-debug-49:\\drawable_ic_check_circle_24.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.7\\mc.meson.kasumi.app-main-51:\\drawable\\ic_check_circle_24.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.7\\mc.meson.kasumi.app-debug-49:\\layout_fragment_config_management.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.7\\mc.meson.kasumi.app-main-51:\\layout\\fragment_config_management.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.7\\mc.meson.kasumi.app-debug-49:\\drawable_ic_features_24.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.7\\mc.meson.kasumi.app-main-51:\\drawable\\ic_features_24.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.7\\mc.meson.kasumi.app-debug-49:\\drawable_status_indicator.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.7\\mc.meson.kasumi.app-main-51:\\drawable\\status_indicator.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.7\\mc.meson.kasumi.app-debug-49:\\drawable_ic_settings_24.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.7\\mc.meson.kasumi.app-main-51:\\drawable\\ic_settings_24.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.7\\mc.meson.kasumi.app-debug-49:\\layout_item_mode_option.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.7\\mc.meson.kasumi.app-main-51:\\layout\\item_mode_option.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.7\\mc.meson.kasumi.app-debug-49:\\layout_item_config.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.7\\mc.meson.kasumi.app-main-51:\\layout\\item_config.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.7\\mc.meson.kasumi.app-debug-49:\\mipmap-xxxhdpi_ic_launcher.webp.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.7\\mc.meson.kasumi.app-main-51:\\mipmap-xxxhdpi\\ic_launcher.webp"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.7\\mc.meson.kasumi.app-debug-49:\\layout_fragment_module_settings.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.7\\mc.meson.kasumi.app-main-51:\\layout\\fragment_module_settings.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.7\\mc.meson.kasumi.app-debug-49:\\layout_module_settings_panel.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.7\\mc.meson.kasumi.app-main-51:\\layout\\module_settings_panel.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.7\\mc.meson.kasumi.app-debug-49:\\layout_activity_main.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.7\\mc.meson.kasumi.app-main-51:\\layout\\activity_main.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.7\\mc.meson.kasumi.app-debug-49:\\drawable_button_ripple.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.7\\mc.meson.kasumi.app-main-51:\\drawable\\button_ripple.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.7\\mc.meson.kasumi.app-debug-49:\\drawable_ic_arrow_drop_down_24.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.7\\mc.meson.kasumi.app-main-51:\\drawable\\ic_arrow_drop_down_24.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.7\\mc.meson.kasumi.app-debug-49:\\drawable_floating_button_background.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.7\\mc.meson.kasumi.app-main-51:\\drawable\\floating_button_background.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.7\\mc.meson.kasumi.app-debug-49:\\layout_activity_splash.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.7\\mc.meson.kasumi.app-main-51:\\layout\\activity_splash.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.7\\mc.meson.kasumi.app-debug-49:\\drawable_tab_indicator_simple.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.7\\mc.meson.kasumi.app-main-51:\\drawable\\tab_indicator_simple.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.7\\mc.meson.kasumi.app-debug-49:\\drawable_splash_gradient.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.7\\mc.meson.kasumi.app-main-51:\\drawable\\splash_gradient.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.7\\mc.meson.kasumi.app-debug-49:\\layout_item_module_list.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.7\\mc.meson.kasumi.app-main-51:\\layout\\item_module_list.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.7\\mc.meson.kasumi.app-debug-49:\\layout_activity_main_with_navigation.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.7\\mc.meson.kasumi.app-main-51:\\layout\\activity_main_with_navigation.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.7\\mc.meson.kasumi.app-debug-49:\\layout_item_setting_toggle.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.7\\mc.meson.kasumi.app-main-51:\\layout\\item_setting_toggle.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.7\\mc.meson.kasumi.app-debug-49:\\mipmap-anydpi_ic_launcher.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.7\\mc.meson.kasumi.app-main-51:\\mipmap-anydpi\\ic_launcher.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.7\\mc.meson.kasumi.app-debug-49:\\anim_fade_out.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.7\\mc.meson.kasumi.app-main-51:\\anim\\fade_out.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.7\\mc.meson.kasumi.app-debug-49:\\layout_item_setting_slider.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.7\\mc.meson.kasumi.app-main-51:\\layout\\item_setting_slider.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.7\\mc.meson.kasumi.app-debug-49:\\drawable_drag_indicator.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.7\\mc.meson.kasumi.app-main-51:\\drawable\\drag_indicator.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.7\\mc.meson.kasumi.app-debug-49:\\layout_dialog_create_config.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.7\\mc.meson.kasumi.app-main-51:\\layout\\dialog_create_config.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.7\\mc.meson.kasumi.app-debug-49:\\anim_slide_in_right.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.7\\mc.meson.kasumi.app-main-51:\\anim\\slide_in_right.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.7\\mc.meson.kasumi.app-debug-49:\\drawable_ic_resize.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.7\\mc.meson.kasumi.app-main-51:\\drawable\\ic_resize.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.7\\mc.meson.kasumi.app-debug-49:\\layout_fragment_hack_features.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.7\\mc.meson.kasumi.app-main-51:\\layout\\fragment_hack_features.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.7\\mc.meson.kasumi.app-debug-49:\\drawable_ic_home_24.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.7\\mc.meson.kasumi.app-main-51:\\drawable\\ic_home_24.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.7\\mc.meson.kasumi.app-debug-49:\\layout_item_setting_mode.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.7\\mc.meson.kasumi.app-main-51:\\layout\\item_setting_mode.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.7\\mc.meson.kasumi.app-debug-49:\\drawable_settings_panel_background.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.7\\mc.meson.kasumi.app-main-51:\\drawable\\settings_panel_background.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.7\\mc.meson.kasumi.app-debug-49:\\drawable_floating_item_background.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.7\\mc.meson.kasumi.app-main-51:\\drawable\\floating_item_background.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.7\\mc.meson.kasumi.app-debug-49:\\layout_item_compact_module.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.7\\mc.meson.kasumi.app-main-51:\\layout\\item_compact_module.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.7\\mc.meson.kasumi.app-debug-49:\\anim_bounce.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.7\\mc.meson.kasumi.app-main-51:\\anim\\bounce.xml"}]