<?xml version="1.0" encoding="utf-8"?>
<!-- 滑块设置项 -->
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:orientation="vertical"
    android:padding="12dp"
    android:background="?attr/selectableItemBackground">

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="horizontal"
        android:gravity="center_vertical"
        android:layout_marginBottom="8dp">

        <LinearLayout
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:orientation="vertical">

            <TextView
                android:id="@+id/tvSettingName"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="设置名称"
                android:textSize="14sp"
                android:textStyle="bold"
                android:textColor="@color/md_theme_light_onPrimaryContainer" />

            <TextView
                android:id="@+id/tvSettingDescription"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="设置描述"
                android:textSize="12sp"
                android:textColor="@color/md_theme_light_onSurfaceVariant"
                android:layout_marginTop="2dp" />

        </LinearLayout>

        <TextView
            android:id="@+id/tvCurrentValue"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="0.0"
            android:textSize="12sp"
            android:textStyle="bold"
            android:textColor="@color/md_theme_light_primary"
            android:minWidth="48dp"
            android:gravity="center" />

    </LinearLayout>

    <SeekBar
        android:id="@+id/seekBar"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:progressTint="@color/md_theme_light_primary"
        android:thumbTint="@color/md_theme_light_primary" />

</LinearLayout>
