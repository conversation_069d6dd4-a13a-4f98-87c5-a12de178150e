// Generated by view binder compiler. Do not edit!
package mc.meson.kasumi.databinding;

import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.ImageButton;
import android.widget.LinearLayout;
import android.widget.TextView;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.coordinatorlayout.widget.CoordinatorLayout;
import androidx.recyclerview.widget.RecyclerView;
import androidx.viewbinding.ViewBinding;
import androidx.viewbinding.ViewBindings;
import java.lang.NullPointerException;
import java.lang.Override;
import java.lang.String;
import mc.meson.kasumi.R;

public final class FragmentConfigManagementBinding implements ViewBinding {
  @NonNull
  private final CoordinatorLayout rootView;

  @NonNull
  public final ImageButton btnBack;

  @NonNull
  public final ImageButton btnCreateConfig;

  @NonNull
  public final ImageButton btnExportAll;

  @NonNull
  public final ImageButton btnImportConfig;

  @NonNull
  public final LinearLayout layoutEmptyState;

  @NonNull
  public final RecyclerView recyclerViewConfigs;

  @NonNull
  public final TextView tvCurrentConfigDescription;

  @NonNull
  public final TextView tvCurrentConfigName;

  @NonNull
  public final TextView tvCurrentConfigStats;

  private FragmentConfigManagementBinding(@NonNull CoordinatorLayout rootView,
      @NonNull ImageButton btnBack, @NonNull ImageButton btnCreateConfig,
      @NonNull ImageButton btnExportAll, @NonNull ImageButton btnImportConfig,
      @NonNull LinearLayout layoutEmptyState, @NonNull RecyclerView recyclerViewConfigs,
      @NonNull TextView tvCurrentConfigDescription, @NonNull TextView tvCurrentConfigName,
      @NonNull TextView tvCurrentConfigStats) {
    this.rootView = rootView;
    this.btnBack = btnBack;
    this.btnCreateConfig = btnCreateConfig;
    this.btnExportAll = btnExportAll;
    this.btnImportConfig = btnImportConfig;
    this.layoutEmptyState = layoutEmptyState;
    this.recyclerViewConfigs = recyclerViewConfigs;
    this.tvCurrentConfigDescription = tvCurrentConfigDescription;
    this.tvCurrentConfigName = tvCurrentConfigName;
    this.tvCurrentConfigStats = tvCurrentConfigStats;
  }

  @Override
  @NonNull
  public CoordinatorLayout getRoot() {
    return rootView;
  }

  @NonNull
  public static FragmentConfigManagementBinding inflate(@NonNull LayoutInflater inflater) {
    return inflate(inflater, null, false);
  }

  @NonNull
  public static FragmentConfigManagementBinding inflate(@NonNull LayoutInflater inflater,
      @Nullable ViewGroup parent, boolean attachToParent) {
    View root = inflater.inflate(R.layout.fragment_config_management, parent, false);
    if (attachToParent) {
      parent.addView(root);
    }
    return bind(root);
  }

  @NonNull
  public static FragmentConfigManagementBinding bind(@NonNull View rootView) {
    // The body of this method is generated in a way you would not otherwise write.
    // This is done to optimize the compiled bytecode for size and performance.
    int id;
    missingId: {
      id = R.id.btnBack;
      ImageButton btnBack = ViewBindings.findChildViewById(rootView, id);
      if (btnBack == null) {
        break missingId;
      }

      id = R.id.btnCreateConfig;
      ImageButton btnCreateConfig = ViewBindings.findChildViewById(rootView, id);
      if (btnCreateConfig == null) {
        break missingId;
      }

      id = R.id.btnExportAll;
      ImageButton btnExportAll = ViewBindings.findChildViewById(rootView, id);
      if (btnExportAll == null) {
        break missingId;
      }

      id = R.id.btnImportConfig;
      ImageButton btnImportConfig = ViewBindings.findChildViewById(rootView, id);
      if (btnImportConfig == null) {
        break missingId;
      }

      id = R.id.layoutEmptyState;
      LinearLayout layoutEmptyState = ViewBindings.findChildViewById(rootView, id);
      if (layoutEmptyState == null) {
        break missingId;
      }

      id = R.id.recyclerViewConfigs;
      RecyclerView recyclerViewConfigs = ViewBindings.findChildViewById(rootView, id);
      if (recyclerViewConfigs == null) {
        break missingId;
      }

      id = R.id.tvCurrentConfigDescription;
      TextView tvCurrentConfigDescription = ViewBindings.findChildViewById(rootView, id);
      if (tvCurrentConfigDescription == null) {
        break missingId;
      }

      id = R.id.tvCurrentConfigName;
      TextView tvCurrentConfigName = ViewBindings.findChildViewById(rootView, id);
      if (tvCurrentConfigName == null) {
        break missingId;
      }

      id = R.id.tvCurrentConfigStats;
      TextView tvCurrentConfigStats = ViewBindings.findChildViewById(rootView, id);
      if (tvCurrentConfigStats == null) {
        break missingId;
      }

      return new FragmentConfigManagementBinding((CoordinatorLayout) rootView, btnBack,
          btnCreateConfig, btnExportAll, btnImportConfig, layoutEmptyState, recyclerViewConfigs,
          tvCurrentConfigDescription, tvCurrentConfigName, tvCurrentConfigStats);
    }
    String missingId = rootView.getResources().getResourceName(id);
    throw new NullPointerException("Missing required view with ID: ".concat(missingId));
  }
}
