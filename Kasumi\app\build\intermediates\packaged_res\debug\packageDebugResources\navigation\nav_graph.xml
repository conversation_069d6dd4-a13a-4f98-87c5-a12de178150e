<?xml version="1.0" encoding="utf-8"?>
<navigation xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:id="@+id/nav_graph"
    app:startDestination="@id/nav_home">

    <fragment
        android:id="@+id/nav_home"
        android:name="mc.meson.kasumi.HomeFragment"
        android:label="主页"
        tools:layout="@layout/fragment_home">
        <action
            android:id="@+id/action_home_to_features"
            app:destination="@id/nav_features"
            app:enterAnim="@anim/fragment_fade_enter"
            app:exitAnim="@anim/fragment_fade_exit" />
        <action
            android:id="@+id/action_home_to_settings"
            app:destination="@id/nav_settings"
            app:enterAnim="@anim/fragment_fade_enter"
            app:exitAnim="@anim/fragment_fade_exit" />
    </fragment>

    <fragment
        android:id="@+id/nav_features"
        android:name="mc.meson.kasumi.HackFeaturesFragment"
        android:label="功能"
        tools:layout="@layout/fragment_hack_features">
        <action
            android:id="@+id/action_features_to_home"
            app:destination="@id/nav_home"
            app:enterAnim="@anim/fragment_fade_enter"
            app:exitAnim="@anim/fragment_fade_exit" />
        <action
            android:id="@+id/action_features_to_settings"
            app:destination="@id/nav_settings"
            app:enterAnim="@anim/fragment_fade_enter"
            app:exitAnim="@anim/fragment_fade_exit" />
    </fragment>

    <fragment
        android:id="@+id/nav_settings"
        android:name="mc.meson.kasumi.SettingsFragment"
        android:label="设置"
        tools:layout="@layout/fragment_settings">
        <action
            android:id="@+id/action_settings_to_home"
            app:destination="@id/nav_home"
            app:enterAnim="@anim/fragment_fade_enter"
            app:exitAnim="@anim/fragment_fade_exit" />
        <action
            android:id="@+id/action_settings_to_features"
            app:destination="@id/nav_features"
            app:enterAnim="@anim/fragment_fade_enter"
            app:exitAnim="@anim/fragment_fade_exit" />
    </fragment>

</navigation>
