<?xml version="1.0" encoding="utf-8" standalone="yes"?><Layout layout="item_compact_module" modulePackage="mc.meson.kasumi" filePath="app\src\main\res\layout\item_compact_module.xml" directory="layout" isMerge="false" isBindingData="false" rootNodeType="android.widget.LinearLayout"><Targets><Target tag="layout/item_compact_module_0" view="LinearLayout"><Expressions/><location startLine="1" startOffset="0" endLine="27" endOffset="14"/></Target><Target id="@+id/tvModuleName" view="TextView"><Expressions/><location startLine="10" startOffset="4" endLine="18" endOffset="40"/></Target><Target id="@+id/switchModule" view="Switch"><Expressions/><location startLine="20" startOffset="4" endLine="25" endOffset="30"/></Target></Targets></Layout>