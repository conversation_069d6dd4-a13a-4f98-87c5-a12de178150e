package mc.meson.kasumi.config

import android.os.Bundle
import androidx.lifecycle.SavedStateHandle
import androidx.navigation.NavArgs
import java.lang.IllegalArgumentException
import kotlin.String
import kotlin.jvm.JvmStatic

public data class ModuleSettingsFragmentArgs(
  public val moduleId: String,
) : NavArgs {
  public fun toBundle(): Bundle {
    val result = Bundle()
    result.putString("module_id", this.moduleId)
    return result
  }

  public fun toSavedStateHandle(): SavedStateHandle {
    val result = SavedStateHandle()
    result.set("module_id", this.moduleId)
    return result
  }

  public companion object {
    @JvmStatic
    public fun fromBundle(bundle: Bundle): ModuleSettingsFragmentArgs {
      bundle.setClassLoader(ModuleSettingsFragmentArgs::class.java.classLoader)
      val __moduleId : String?
      if (bundle.containsKey("module_id")) {
        __moduleId = bundle.getString("module_id")
        if (__moduleId == null) {
          throw IllegalArgumentException("Argument \"module_id\" is marked as non-null but was passed a null value.")
        }
      } else {
        throw IllegalArgumentException("Required argument \"module_id\" is missing and does not have an android:defaultValue")
      }
      return ModuleSettingsFragmentArgs(__moduleId)
    }

    @JvmStatic
    public fun fromSavedStateHandle(savedStateHandle: SavedStateHandle):
        ModuleSettingsFragmentArgs {
      val __moduleId : String?
      if (savedStateHandle.contains("module_id")) {
        __moduleId = savedStateHandle["module_id"]
        if (__moduleId == null) {
          throw IllegalArgumentException("Argument \"module_id\" is marked as non-null but was passed a null value")
        }
      } else {
        throw IllegalArgumentException("Required argument \"module_id\" is missing and does not have an android:defaultValue")
      }
      return ModuleSettingsFragmentArgs(__moduleId)
    }
  }
}
