<?xml version="1.0" encoding="utf-8" standalone="yes"?><Layout layout="compact_category_page" modulePackage="mc.meson.kasumi" filePath="app\src\main\res\layout\compact_category_page.xml" directory="layout" isMerge="false" isBindingData="false" rootNodeType="android.widget.FrameLayout"><Targets><Target tag="layout/compact_category_page_0" view="FrameLayout"><Expressions/><location startLine="1" startOffset="0" endLine="14" endOffset="13"/></Target><Target id="@+id/rvCategoryModules" view="androidx.recyclerview.widget.RecyclerView"><Expressions/><location startLine="5" startOffset="4" endLine="12" endOffset="37"/></Target></Targets></Layout>