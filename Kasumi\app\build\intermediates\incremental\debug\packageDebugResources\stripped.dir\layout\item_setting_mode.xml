<?xml version="1.0" encoding="utf-8"?>
<!-- 模式选择设置项 - 紧凑版本 -->
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:orientation="vertical"
    android:padding="12dp"
    android:background="?attr/selectableItemBackground">

    <!-- 设置信息区域 -->
    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="vertical"
        android:layout_marginBottom="8dp">

        <TextView
            android:id="@+id/tvSettingName"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="设置名称"
            android:textSize="14sp"
            android:textStyle="bold"
            android:textColor="@color/md_theme_light_onPrimaryContainer" />

        <TextView
            android:id="@+id/tvSettingDescription"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="设置描述"
            android:textSize="11sp"
            android:textColor="@color/md_theme_light_onSurfaceVariant"
            android:layout_marginTop="2dp" />

    </LinearLayout>

    <!-- 紧凑的横向选择器 -->
    <HorizontalScrollView
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:scrollbars="none"
        android:overScrollMode="never">

        <LinearLayout
            android:id="@+id/llModeOptions"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:orientation="horizontal"
            android:paddingHorizontal="4dp" />

    </HorizontalScrollView>

</LinearLayout>
