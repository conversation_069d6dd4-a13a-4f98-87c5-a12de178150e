# Navigation 修复说明

## 问题描述

之前在点击功能卡片和配置管理按钮时会出现以下错误：

```
java.lang.IllegalArgumentException: The fragment ModuleSettingsFragment{...} is unknown to the FragmentNavigator. Please use the navigate() function to add fragments to the FragmentNavigator managed FragmentManager.
```

## 问题原因

应用使用了 Navigation Component，但我们在代码中直接使用 `FragmentTransaction` 来替换 Fragment，这与 Navigation Component 的管理机制冲突。

## 修复方案

### 1. 更新导航图 (nav_graph.xml)

在导航图中添加了新的 Fragment 定义：

- `nav_config_management`: 配置管理页面
- `nav_module_settings`: 模块设置页面

并添加了相应的导航动作：

- `action_features_to_config_management`: 从功能页面到配置管理
- `action_features_to_module_settings`: 从功能页面到模块设置

### 2. 添加 Navigation Safe Args

在 `build.gradle.kts` 中添加了 Safe Args 插件：

```kotlin
plugins {
    // ...
    id("androidx.navigation.safeargs.kotlin")
}
```

这样可以安全地传递参数给 Fragment。

### 3. 更新 Fragment 代码

#### HackFeaturesFragment
- 使用 `findNavController().navigate()` 替代 `FragmentTransaction`
- 使用 Navigation Actions 进行页面跳转

#### ModuleSettingsFragment
- 使用 `navArgs()` 获取传递的参数
- 使用 `findNavController().navigateUp()` 返回上一页

#### ConfigManagementFragment
- 使用 `findNavController().navigateUp()` 返回上一页
- 移除了不必要的 `newInstance()` 方法

## 修复后的功能

1. **点击功能卡片**: 现在可以正常打开模块设置页面
2. **点击配置管理**: 现在可以正常打开配置管理页面
3. **返回导航**: 使用系统返回键或返回按钮都能正常返回
4. **参数传递**: 模块ID可以安全地传递给设置页面

## 测试建议

1. 在功能页面点击任意功能卡片，验证能否打开设置页面
2. 在功能页面点击配置管理卡片，验证能否打开配置管理页面
3. 在设置页面和配置管理页面使用返回按钮，验证能否正常返回
4. 使用系统返回键验证导航栈是否正确

## 注意事项

- 确保在构建项目前同步 Gradle 文件
- Navigation Safe Args 会在编译时生成相应的参数类
- 如果添加新的 Fragment，需要在导航图中定义相应的导航路径
