<?xml version="1.0" encoding="utf-8" standalone="yes"?><Layout layout="fragment_hack_features" modulePackage="mc.meson.kasumi" filePath="app\src\main\res\layout\fragment_hack_features.xml" directory="layout" isMerge="false" isBindingData="false" rootNodeType="android.widget.LinearLayout"><Targets><Target tag="layout/fragment_hack_features_0" view="LinearLayout"><Expressions/><location startLine="1" startOffset="0" endLine="60" endOffset="14"/></Target><Target id="@+id/tvCurrentConfigName" view="TextView"><Expressions/><location startLine="34" startOffset="12" endLine="41" endOffset="48"/></Target><Target id="@+id/btnQuickSwitchConfig" view="com.google.android.material.button.MaterialButton"><Expressions/><location startLine="43" startOffset="12" endLine="48" endOffset="67"/></Target><Target id="@+id/recyclerViewModules" view="androidx.recyclerview.widget.RecyclerView"><Expressions/><location startLine="55" startOffset="4" endLine="58" endOffset="46"/></Target></Targets></Layout>