<?xml version="1.0" encoding="utf-8" standalone="yes"?><Layout layout="fragment_hack_features" modulePackage="mc.meson.kasumi" filePath="app\src\main\res\layout\fragment_hack_features.xml" directory="layout" isMerge="false" isBindingData="false" rootNodeType="androidx.coordinatorlayout.widget.CoordinatorLayout"><Targets><Target tag="layout/fragment_hack_features_0" view="androidx.coordinatorlayout.widget.CoordinatorLayout"><Expressions/><location startLine="1" startOffset="0" endLine="82" endOffset="53"/></Target><Target id="@+id/cardConfigManagement" view="com.google.android.material.card.MaterialCardView"><Expressions/><location startLine="12" startOffset="8" endLine="69" endOffset="59"/></Target><Target id="@+id/recyclerViewModules" view="androidx.recyclerview.widget.RecyclerView"><Expressions/><location startLine="72" startOffset="8" endLine="78" endOffset="43"/></Target></Targets></Layout>