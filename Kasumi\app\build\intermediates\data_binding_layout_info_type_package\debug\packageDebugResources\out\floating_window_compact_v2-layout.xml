<?xml version="1.0" encoding="utf-8" standalone="yes"?><Layout layout="floating_window_compact_v2" modulePackage="mc.meson.kasumi" filePath="app\src\main\res\layout\floating_window_compact_v2.xml" directory="layout" isMerge="false" isBindingData="false" rootNodeType="androidx.cardview.widget.CardView"><Targets><Target tag="layout/floating_window_compact_v2_0" view="androidx.cardview.widget.CardView"><Expressions/><location startLine="1" startOffset="0" endLine="135" endOffset="35"/></Target><Target id="@+id/collapsedDot" view="ImageView"><Expressions/><location startLine="11" startOffset="4" endLine="20" endOffset="34"/></Target><Target id="@+id/compactContent" view="LinearLayout"><Expressions/><location startLine="23" startOffset="4" endLine="133" endOffset="18"/></Target><Target id="@+id/topBar" view="LinearLayout"><Expressions/><location startLine="31" startOffset="8" endLine="74" endOffset="22"/></Target><Target id="@+id/btnExpand" view="ImageView"><Expressions/><location startLine="40" startOffset="12" endLine="49" endOffset="48"/></Target><Target id="@+id/tvCategoryName" view="TextView"><Expressions/><location startLine="52" startOffset="12" endLine="61" endOffset="42"/></Target><Target id="@+id/btnCollapse" view="ImageView"><Expressions/><location startLine="64" startOffset="12" endLine="72" endOffset="42"/></Target><Target id="@+id/vpCompactCategories" view="androidx.viewpager2.widget.ViewPager2"><Expressions/><location startLine="77" startOffset="8" endLine="82" endOffset="46"/></Target><Target id="@+id/indicatorCombat" view="View"><Expressions/><location startLine="91" startOffset="12" endLine="97" endOffset="72"/></Target><Target id="@+id/indicatorMovement" view="View"><Expressions/><location startLine="99" startOffset="12" endLine="105" endOffset="72"/></Target><Target id="@+id/indicatorWorld" view="View"><Expressions/><location startLine="107" startOffset="12" endLine="113" endOffset="72"/></Target><Target id="@+id/indicatorPlayer" view="View"><Expressions/><location startLine="115" startOffset="12" endLine="121" endOffset="72"/></Target><Target id="@+id/indicatorVisual" view="View"><Expressions/><location startLine="123" startOffset="12" endLine="129" endOffset="72"/></Target></Targets></Layout>