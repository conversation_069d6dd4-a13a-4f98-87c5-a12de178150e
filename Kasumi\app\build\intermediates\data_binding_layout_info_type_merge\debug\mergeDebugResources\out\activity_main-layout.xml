<?xml version="1.0" encoding="utf-8" standalone="yes"?><Layout layout="activity_main" modulePackage="mc.meson.kasumi" filePath="app\src\main\res\layout\activity_main.xml" directory="layout" isMerge="false" isBindingData="false" rootNodeType="androidx.coordinatorlayout.widget.CoordinatorLayout"><Targets><Target tag="layout/activity_main_0" view="androidx.coordinatorlayout.widget.CoordinatorLayout"><Expressions/><location startLine="1" startOffset="0" endLine="211" endOffset="53"/></Target><Target id="@+id/appBarLayout" view="com.google.android.material.appbar.AppBarLayout"><Expressions/><location startLine="10" startOffset="4" endLine="25" endOffset="53"/></Target><Target id="@+id/toolbar" view="com.google.android.material.appbar.MaterialToolbar"><Expressions/><location startLine="17" startOffset="8" endLine="23" endOffset="62"/></Target><Target id="@+id/welcomeText" view="TextView"><Expressions/><location startLine="64" startOffset="20" endLine="70" endOffset="73"/></Target><Target id="@+id/statusIndicator" view="View"><Expressions/><location startLine="107" startOffset="24" endLine="112" endOffset="61"/></Target><Target id="@+id/statusText" view="TextView"><Expressions/><location startLine="114" startOffset="24" endLine="121" endOffset="77"/></Target><Target id="@+id/btnFloatingWindow" view="com.google.android.material.button.MaterialButton"><Expressions/><location startLine="168" startOffset="24" endLine="178" endOffset="70"/></Target><Target id="@+id/btnLandscape" view="com.google.android.material.button.MaterialButton"><Expressions/><location startLine="180" startOffset="24" endLine="186" endOffset="65"/></Target><Target id="@+id/fabSettings" view="com.google.android.material.floatingactionbutton.ExtendedFloatingActionButton"><Expressions/><location startLine="199" startOffset="4" endLine="209" endOffset="46"/></Target></Targets></Layout>