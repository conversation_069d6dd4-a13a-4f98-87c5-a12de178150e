// Generated by view binder compiler. Do not edit!
package mc.meson.kasumi.databinding;

import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.LinearLayout;
import android.widget.TextView;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.recyclerview.widget.RecyclerView;
import androidx.viewbinding.ViewBinding;
import androidx.viewbinding.ViewBindings;
import com.google.android.material.button.MaterialButton;
import java.lang.NullPointerException;
import java.lang.Override;
import java.lang.String;
import mc.meson.kasumi.R;

public final class FragmentHackFeaturesBinding implements ViewBinding {
  @NonNull
  private final LinearLayout rootView;

  @NonNull
  public final MaterialButton btnQuickSwitchConfig;

  @NonNull
  public final RecyclerView recyclerViewModules;

  @NonNull
  public final TextView tvCurrentConfigName;

  private FragmentHackFeaturesBinding(@NonNull LinearLayout rootView,
      @NonNull MaterialButton btnQuickSwitchConfig, @NonNull RecyclerView recyclerViewModules,
      @NonNull TextView tvCurrentConfigName) {
    this.rootView = rootView;
    this.btnQuickSwitchConfig = btnQuickSwitchConfig;
    this.recyclerViewModules = recyclerViewModules;
    this.tvCurrentConfigName = tvCurrentConfigName;
  }

  @Override
  @NonNull
  public LinearLayout getRoot() {
    return rootView;
  }

  @NonNull
  public static FragmentHackFeaturesBinding inflate(@NonNull LayoutInflater inflater) {
    return inflate(inflater, null, false);
  }

  @NonNull
  public static FragmentHackFeaturesBinding inflate(@NonNull LayoutInflater inflater,
      @Nullable ViewGroup parent, boolean attachToParent) {
    View root = inflater.inflate(R.layout.fragment_hack_features, parent, false);
    if (attachToParent) {
      parent.addView(root);
    }
    return bind(root);
  }

  @NonNull
  public static FragmentHackFeaturesBinding bind(@NonNull View rootView) {
    // The body of this method is generated in a way you would not otherwise write.
    // This is done to optimize the compiled bytecode for size and performance.
    int id;
    missingId: {
      id = R.id.btnQuickSwitchConfig;
      MaterialButton btnQuickSwitchConfig = ViewBindings.findChildViewById(rootView, id);
      if (btnQuickSwitchConfig == null) {
        break missingId;
      }

      id = R.id.recyclerViewModules;
      RecyclerView recyclerViewModules = ViewBindings.findChildViewById(rootView, id);
      if (recyclerViewModules == null) {
        break missingId;
      }

      id = R.id.tvCurrentConfigName;
      TextView tvCurrentConfigName = ViewBindings.findChildViewById(rootView, id);
      if (tvCurrentConfigName == null) {
        break missingId;
      }

      return new FragmentHackFeaturesBinding((LinearLayout) rootView, btnQuickSwitchConfig,
          recyclerViewModules, tvCurrentConfigName);
    }
    String missingId = rootView.getResources().getResourceName(id);
    throw new NullPointerException("Missing required view with ID: ".concat(missingId));
  }
}
