// Generated by view binder compiler. Do not edit!
package mc.meson.kasumi.databinding;

import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.coordinatorlayout.widget.CoordinatorLayout;
import androidx.recyclerview.widget.RecyclerView;
import androidx.viewbinding.ViewBinding;
import androidx.viewbinding.ViewBindings;
import com.google.android.material.card.MaterialCardView;
import java.lang.NullPointerException;
import java.lang.Override;
import java.lang.String;
import mc.meson.kasumi.R;

public final class FragmentHackFeaturesBinding implements ViewBinding {
  @NonNull
  private final CoordinatorLayout rootView;

  @NonNull
  public final MaterialCardView cardConfigManagement;

  @NonNull
  public final RecyclerView recyclerViewModules;

  private FragmentHackFeaturesBinding(@NonNull CoordinatorLayout rootView,
      @NonNull MaterialCardView cardConfigManagement, @NonNull RecyclerView recyclerViewModules) {
    this.rootView = rootView;
    this.cardConfigManagement = cardConfigManagement;
    this.recyclerViewModules = recyclerViewModules;
  }

  @Override
  @NonNull
  public CoordinatorLayout getRoot() {
    return rootView;
  }

  @NonNull
  public static FragmentHackFeaturesBinding inflate(@NonNull LayoutInflater inflater) {
    return inflate(inflater, null, false);
  }

  @NonNull
  public static FragmentHackFeaturesBinding inflate(@NonNull LayoutInflater inflater,
      @Nullable ViewGroup parent, boolean attachToParent) {
    View root = inflater.inflate(R.layout.fragment_hack_features, parent, false);
    if (attachToParent) {
      parent.addView(root);
    }
    return bind(root);
  }

  @NonNull
  public static FragmentHackFeaturesBinding bind(@NonNull View rootView) {
    // The body of this method is generated in a way you would not otherwise write.
    // This is done to optimize the compiled bytecode for size and performance.
    int id;
    missingId: {
      id = R.id.cardConfigManagement;
      MaterialCardView cardConfigManagement = ViewBindings.findChildViewById(rootView, id);
      if (cardConfigManagement == null) {
        break missingId;
      }

      id = R.id.recyclerViewModules;
      RecyclerView recyclerViewModules = ViewBindings.findChildViewById(rootView, id);
      if (recyclerViewModules == null) {
        break missingId;
      }

      return new FragmentHackFeaturesBinding((CoordinatorLayout) rootView, cardConfigManagement,
          recyclerViewModules);
    }
    String missingId = rootView.getResources().getResourceName(id);
    throw new NullPointerException("Missing required view with ID: ".concat(missingId));
  }
}
