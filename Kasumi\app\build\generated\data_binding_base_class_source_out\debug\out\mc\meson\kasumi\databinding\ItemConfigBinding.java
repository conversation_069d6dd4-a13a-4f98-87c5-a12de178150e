// Generated by view binder compiler. Do not edit!
package mc.meson.kasumi.databinding;

import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.ImageButton;
import android.widget.ImageView;
import android.widget.TextView;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.viewbinding.ViewBinding;
import androidx.viewbinding.ViewBindings;
import com.google.android.material.card.MaterialCardView;
import java.lang.NullPointerException;
import java.lang.Override;
import java.lang.String;
import mc.meson.kasumi.R;

public final class ItemConfigBinding implements ViewBinding {
  @NonNull
  private final MaterialCardView rootView;

  @NonNull
  public final ImageButton btnDeleteConfig;

  @NonNull
  public final ImageButton btnEditConfig;

  @NonNull
  public final ImageButton btnExportConfig;

  @NonNull
  public final MaterialCardView cardConfig;

  @NonNull
  public final ImageView ivCurrentIndicator;

  @NonNull
  public final TextView tvConfigDescription;

  @NonNull
  public final TextView tvConfigName;

  @NonNull
  public final TextView tvConfigStats;

  @NonNull
  public final TextView tvConfigTime;

  private ItemConfigBinding(@NonNull MaterialCardView rootView,
      @NonNull ImageButton btnDeleteConfig, @NonNull ImageButton btnEditConfig,
      @NonNull ImageButton btnExportConfig, @NonNull MaterialCardView cardConfig,
      @NonNull ImageView ivCurrentIndicator, @NonNull TextView tvConfigDescription,
      @NonNull TextView tvConfigName, @NonNull TextView tvConfigStats,
      @NonNull TextView tvConfigTime) {
    this.rootView = rootView;
    this.btnDeleteConfig = btnDeleteConfig;
    this.btnEditConfig = btnEditConfig;
    this.btnExportConfig = btnExportConfig;
    this.cardConfig = cardConfig;
    this.ivCurrentIndicator = ivCurrentIndicator;
    this.tvConfigDescription = tvConfigDescription;
    this.tvConfigName = tvConfigName;
    this.tvConfigStats = tvConfigStats;
    this.tvConfigTime = tvConfigTime;
  }

  @Override
  @NonNull
  public MaterialCardView getRoot() {
    return rootView;
  }

  @NonNull
  public static ItemConfigBinding inflate(@NonNull LayoutInflater inflater) {
    return inflate(inflater, null, false);
  }

  @NonNull
  public static ItemConfigBinding inflate(@NonNull LayoutInflater inflater,
      @Nullable ViewGroup parent, boolean attachToParent) {
    View root = inflater.inflate(R.layout.item_config, parent, false);
    if (attachToParent) {
      parent.addView(root);
    }
    return bind(root);
  }

  @NonNull
  public static ItemConfigBinding bind(@NonNull View rootView) {
    // The body of this method is generated in a way you would not otherwise write.
    // This is done to optimize the compiled bytecode for size and performance.
    int id;
    missingId: {
      id = R.id.btnDeleteConfig;
      ImageButton btnDeleteConfig = ViewBindings.findChildViewById(rootView, id);
      if (btnDeleteConfig == null) {
        break missingId;
      }

      id = R.id.btnEditConfig;
      ImageButton btnEditConfig = ViewBindings.findChildViewById(rootView, id);
      if (btnEditConfig == null) {
        break missingId;
      }

      id = R.id.btnExportConfig;
      ImageButton btnExportConfig = ViewBindings.findChildViewById(rootView, id);
      if (btnExportConfig == null) {
        break missingId;
      }

      MaterialCardView cardConfig = (MaterialCardView) rootView;

      id = R.id.ivCurrentIndicator;
      ImageView ivCurrentIndicator = ViewBindings.findChildViewById(rootView, id);
      if (ivCurrentIndicator == null) {
        break missingId;
      }

      id = R.id.tvConfigDescription;
      TextView tvConfigDescription = ViewBindings.findChildViewById(rootView, id);
      if (tvConfigDescription == null) {
        break missingId;
      }

      id = R.id.tvConfigName;
      TextView tvConfigName = ViewBindings.findChildViewById(rootView, id);
      if (tvConfigName == null) {
        break missingId;
      }

      id = R.id.tvConfigStats;
      TextView tvConfigStats = ViewBindings.findChildViewById(rootView, id);
      if (tvConfigStats == null) {
        break missingId;
      }

      id = R.id.tvConfigTime;
      TextView tvConfigTime = ViewBindings.findChildViewById(rootView, id);
      if (tvConfigTime == null) {
        break missingId;
      }

      return new ItemConfigBinding((MaterialCardView) rootView, btnDeleteConfig, btnEditConfig,
          btnExportConfig, cardConfig, ivCurrentIndicator, tvConfigDescription, tvConfigName,
          tvConfigStats, tvConfigTime);
    }
    String missingId = rootView.getResources().getResourceName(id);
    throw new NullPointerException("Missing required view with ID: ".concat(missingId));
  }
}
