<?xml version="1.0" encoding="utf-8"?>
<shape xmlns:android="http://schemas.android.com/apk/res/android"
    android:shape="rectangle">

    <!-- 圆角指示器，更加柔和 -->
    <corners android:radius="1dp" />

    <!-- 使用主色调，但更柔和 -->
    <solid android:color="@color/md_theme_light_primary" />

    <!-- 添加渐变效果，让指示器更柔和 -->
    <gradient
        android:startColor="@color/md_theme_light_primary"
        android:endColor="@color/md_theme_light_primaryContainer"
        android:angle="90" />

</shape>
