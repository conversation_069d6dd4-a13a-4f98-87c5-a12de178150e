<?xml version="1.0" encoding="utf-8" standalone="yes"?><Layout layout="fragment_settings" modulePackage="mc.meson.kasumi" filePath="app\src\main\res\layout\fragment_settings.xml" directory="layout" isMerge="false" isBindingData="false" rootNodeType="androidx.core.widget.NestedScrollView"><Targets><Target tag="layout/fragment_settings_0" view="androidx.core.widget.NestedScrollView"><Expressions/><location startLine="1" startOffset="0" endLine="214" endOffset="39"/></Target><Target id="@+id/switchDarkMode" view="com.google.android.material.materialswitch.MaterialSwitch"><Expressions/><location startLine="68" startOffset="20" endLine="73" endOffset="67"/></Target><Target id="@+id/switchNotifications" view="com.google.android.material.materialswitch.MaterialSwitch"><Expressions/><location startLine="107" startOffset="20" endLine="113" endOffset="67"/></Target><Target id="@+id/switchAutoConnect" view="com.google.android.material.materialswitch.MaterialSwitch"><Expressions/><location startLine="146" startOffset="20" endLine="152" endOffset="67"/></Target><Target id="@+id/btnCheckUpdate" view="com.google.android.material.button.MaterialButton"><Expressions/><location startLine="201" startOffset="16" endLine="206" endOffset="57"/></Target></Targets></Layout>