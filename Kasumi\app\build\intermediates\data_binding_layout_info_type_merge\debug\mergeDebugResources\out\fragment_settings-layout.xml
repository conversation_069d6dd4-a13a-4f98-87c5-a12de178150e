<?xml version="1.0" encoding="utf-8" standalone="yes"?><Layout layout="fragment_settings" modulePackage="mc.meson.kasumi" filePath="app\src\main\res\layout\fragment_settings.xml" directory="layout" isMerge="false" isBindingData="false" rootNodeType="androidx.core.widget.NestedScrollView"><Targets><Target tag="layout/fragment_settings_0" view="androidx.core.widget.NestedScrollView"><Expressions/><location startLine="1" startOffset="0" endLine="215" endOffset="39"/></Target><Target id="@+id/switchDarkMode" view="com.google.android.material.materialswitch.MaterialSwitch"><Expressions/><location startLine="69" startOffset="20" endLine="74" endOffset="67"/></Target><Target id="@+id/switchNotifications" view="com.google.android.material.materialswitch.MaterialSwitch"><Expressions/><location startLine="108" startOffset="20" endLine="114" endOffset="67"/></Target><Target id="@+id/switchAutoConnect" view="com.google.android.material.materialswitch.MaterialSwitch"><Expressions/><location startLine="147" startOffset="20" endLine="153" endOffset="67"/></Target><Target id="@+id/btnCheckUpdate" view="com.google.android.material.button.MaterialButton"><Expressions/><location startLine="202" startOffset="16" endLine="207" endOffset="57"/></Target></Targets></Layout>