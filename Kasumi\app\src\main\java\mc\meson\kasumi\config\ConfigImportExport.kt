package mc.meson.kasumi.config

import android.content.Context
import android.net.Uri
import android.util.Log
import com.google.gson.Gson
import com.google.gson.GsonBuilder
import com.google.gson.reflect.TypeToken
import java.io.*
import java.text.SimpleDateFormat
import java.util.*

/**
 * 配置导入导出管理器
 * 负责配置的导入和导出功能
 */
object ConfigImportExport {
    private const val TAG = "ConfigImportExport"
    private val gson: Gson = GsonBuilder().setPrettyPrinting().create()
    
    /**
     * 导出配置到文件
     */
    fun exportConfig(context: Context, config: ConfigData, uri: Uri): Boolean {
        return try {
            context.contentResolver.openOutputStream(uri)?.use { outputStream ->
                val exportData = ConfigExportData(
                    version = 1,
                    exportTime = System.currentTimeMillis(),
                    config = config
                )
                
                val json = gson.toJson(exportData)
                outputStream.write(json.toByteArray())
                outputStream.flush()
                
                Log.d(TAG, "Successfully exported config: ${config.name}")
                true
            } ?: false
        } catch (e: Exception) {
            Log.e(TAG, "Error exporting config", e)
            false
        }
    }
    
    /**
     * 导出多个配置到文件
     */
    fun exportConfigs(context: Context, configs: List<ConfigData>, uri: Uri): Boolean {
        return try {
            context.contentResolver.openOutputStream(uri)?.use { outputStream ->
                val exportData = ConfigBatchExportData(
                    version = 1,
                    exportTime = System.currentTimeMillis(),
                    configs = configs
                )
                
                val json = gson.toJson(exportData)
                outputStream.write(json.toByteArray())
                outputStream.flush()
                
                Log.d(TAG, "Successfully exported ${configs.size} configs")
                true
            } ?: false
        } catch (e: Exception) {
            Log.e(TAG, "Error exporting configs", e)
            false
        }
    }
    
    /**
     * 从文件导入配置
     */
    fun importConfig(context: Context, uri: Uri): ImportResult {
        return try {
            context.contentResolver.openInputStream(uri)?.use { inputStream ->
                val json = inputStream.bufferedReader().use { it.readText() }
                
                // 尝试解析为单个配置
                try {
                    val exportData = gson.fromJson(json, ConfigExportData::class.java)
                    if (exportData.version == 1 && exportData.config != null) {
                        return ImportResult.Success(listOf(exportData.config))
                    }
                } catch (e: Exception) {
                    // 忽略，尝试解析为批量配置
                }
                
                // 尝试解析为批量配置
                try {
                    val batchExportData = gson.fromJson(json, ConfigBatchExportData::class.java)
                    if (batchExportData.version == 1 && batchExportData.configs.isNotEmpty()) {
                        return ImportResult.Success(batchExportData.configs)
                    }
                } catch (e: Exception) {
                    // 忽略，尝试直接解析为配置列表
                }
                
                // 尝试直接解析为配置列表（兼容旧格式）
                try {
                    val type = object : TypeToken<List<ConfigData>>() {}.type
                    val configs = gson.fromJson<List<ConfigData>>(json, type)
                    if (configs.isNotEmpty()) {
                        return ImportResult.Success(configs)
                    }
                } catch (e: Exception) {
                    // 忽略，尝试解析为单个配置
                }
                
                // 尝试直接解析为单个配置（兼容旧格式）
                try {
                    val config = gson.fromJson(json, ConfigData::class.java)
                    if (config.name.isNotEmpty()) {
                        return ImportResult.Success(listOf(config))
                    }
                } catch (e: Exception) {
                    Log.e(TAG, "Error parsing config data", e)
                }
                
                ImportResult.Error("无法解析配置文件格式")
            } ?: ImportResult.Error("无法读取文件")
        } catch (e: Exception) {
            Log.e(TAG, "Error importing config", e)
            ImportResult.Error("导入失败: ${e.message}")
        }
    }
    
    /**
     * 生成默认的导出文件名
     */
    fun generateExportFileName(config: ConfigData): String {
        val timestamp = SimpleDateFormat("yyyyMMdd_HHmmss", Locale.getDefault()).format(Date())
        val safeName = config.name.replace(Regex("[^a-zA-Z0-9\\u4e00-\\u9fa5_-]"), "_")
        return "kasumi_config_${safeName}_$timestamp.json"
    }
    
    /**
     * 生成批量导出文件名
     */
    fun generateBatchExportFileName(): String {
        val timestamp = SimpleDateFormat("yyyyMMdd_HHmmss", Locale.getDefault()).format(Date())
        return "kasumi_configs_$timestamp.json"
    }
    
    /**
     * 验证配置数据的有效性
     */
    fun validateConfigData(config: ConfigData): Boolean {
        return try {
            config.name.isNotEmpty() && 
            config.id.isNotEmpty() &&
            config.createdTime > 0
        } catch (e: Exception) {
            false
        }
    }
    
    /**
     * 处理导入的配置，避免ID冲突
     */
    fun processImportedConfigs(configs: List<ConfigData>): List<ConfigData> {
        val existingIds = ConfigManager.getAllConfigs().map { it.id }.toSet()
        val existingNames = ConfigManager.getAllConfigs().map { it.name }.toSet()
        
        return configs.map { config ->
            var processedConfig = config
            
            // 处理ID冲突
            if (existingIds.contains(config.id)) {
                processedConfig = processedConfig.copy(id = UUID.randomUUID().toString())
            }
            
            // 处理名称冲突
            var newName = config.name
            var counter = 1
            while (existingNames.contains(newName)) {
                newName = "${config.name} ($counter)"
                counter++
            }
            
            if (newName != config.name) {
                processedConfig = processedConfig.copy(name = newName)
            }
            
            processedConfig
        }
    }
}

/**
 * 单个配置导出数据格式
 */
data class ConfigExportData(
    val version: Int,
    val exportTime: Long,
    val config: ConfigData
)

/**
 * 批量配置导出数据格式
 */
data class ConfigBatchExportData(
    val version: Int,
    val exportTime: Long,
    val configs: List<ConfigData>
)

/**
 * 导入结果
 */
sealed class ImportResult {
    data class Success(val configs: List<ConfigData>) : ImportResult()
    data class Error(val message: String) : ImportResult()
}
