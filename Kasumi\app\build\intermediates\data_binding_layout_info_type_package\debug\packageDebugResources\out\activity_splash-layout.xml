<?xml version="1.0" encoding="utf-8" standalone="yes"?><Layout layout="activity_splash" modulePackage="mc.meson.kasumi" filePath="app\src\main\res\layout\activity_splash.xml" directory="layout" isMerge="false" isBindingData="false" rootNodeType="androidx.constraintlayout.widget.ConstraintLayout"><Targets><Target tag="layout/activity_splash_0" view="androidx.constraintlayout.widget.ConstraintLayout"><Expressions/><location startLine="1" startOffset="0" endLine="95" endOffset="51"/></Target><Target id="@+id/logoContainer" view="LinearLayout"><Expressions/><location startLine="19" startOffset="4" endLine="65" endOffset="18"/></Target><Target id="@+id/appIcon" view="ImageView"><Expressions/><location startLine="31" startOffset="8" endLine="39" endOffset="34"/></Target><Target id="@+id/appName" view="TextView"><Expressions/><location startLine="42" startOffset="8" endLine="52" endOffset="41"/></Target><Target id="@+id/appSubtitle" view="TextView"><Expressions/><location startLine="55" startOffset="8" endLine="63" endOffset="41"/></Target><Target id="@+id/loadingIndicator" view="com.google.android.material.progressindicator.CircularProgressIndicator"><Expressions/><location startLine="68" startOffset="4" endLine="79" endOffset="27"/></Target><Target id="@+id/versionText" view="TextView"><Expressions/><location startLine="82" startOffset="4" endLine="93" endOffset="27"/></Target></Targets></Layout>