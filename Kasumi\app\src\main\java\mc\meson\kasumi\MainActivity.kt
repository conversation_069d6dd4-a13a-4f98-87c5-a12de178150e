package mc.meson.kasumi

import androidx.appcompat.app.AppCompatActivity
import android.os.Bundle
import android.util.Log
import androidx.core.view.WindowCompat
import androidx.navigation.fragment.NavHostFragment
import androidx.navigation.ui.setupWithNavController
import mc.meson.kasumi.databinding.ActivityMainWithNavigationBinding

class MainActivity : AppCompatActivity() {

    private lateinit var binding: ActivityMainWithNavigationBinding

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)

        try {
            Log.d(TAG, "MainActivity onCreate")

            // Enable edge-to-edge display
            WindowCompat.setDecorFitsSystemWindows(window, false)

            binding = ActivityMainWithNavigationBinding.inflate(layoutInflater)
            setContentView(binding.root)

            setupNavigation()

            Log.d(TAG, "MainActivity created successfully")
        } catch (e: Exception) {
            Log.e(TAG, "Error creating MainActivity", e)
        }
    }

    private fun setupNavigation() {
        // Set up the toolbar
        setSupportActionBar(binding.toolbar)

        // Set up navigation
        val navHostFragment = supportFragmentManager.findFragmentById(R.id.nav_host_fragment) as NavHostFragment
        val navController = navHostFragment.navController

        // Connect bottom navigation with navigation controller
        binding.bottomNavigation.setupWithNavController(navController)

        // Update toolbar title and bottom navigation state based on current destination
        navController.addOnDestinationChangedListener { _, destination, _ ->
            binding.toolbar.title = when (destination.id) {
                R.id.nav_home -> "Kasumi Hack"
                R.id.nav_features -> "功能"
                R.id.nav_settings -> "设置"
                R.id.nav_module_settings -> "模块设置"
                R.id.nav_config_management -> "配置管理"
                else -> "Kasumi Hack"
            }

            // 确保底部导航正确显示选中状态
            // 对于模块设置和配置管理页面，应该保持"功能"页面为选中状态
            when (destination.id) {
                R.id.nav_module_settings, R.id.nav_config_management -> {
                    binding.bottomNavigation.selectedItemId = R.id.nav_features
                }
            }
        }
    }



    companion object {
        private const val TAG = "MainActivity"
    }
}