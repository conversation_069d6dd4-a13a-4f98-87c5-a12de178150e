<?xml version="1.0" encoding="utf-8"?>
<com.google.android.material.card.MaterialCardView xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:layout_marginVertical="4dp"
    app:cardElevation="2dp"
    app:cardCornerRadius="12dp"
    app:strokeWidth="1dp"
    app:strokeColor="?attr/colorOutline"
    style="@style/Widget.Material3.CardView.Outlined">

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="horizontal"
        android:padding="16dp"
        android:gravity="center_vertical">

        <!-- Configuration Info -->
        <LinearLayout
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:orientation="vertical">

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="horizontal"
                android:gravity="center_vertical">

                <TextView
                    android:id="@+id/tvConfigName"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_weight="1"
                    android:text="配置名称"
                    android:textAppearance="?attr/textAppearanceBodyLarge"
                    android:textColor="?attr/colorOnSurface"
                    android:maxLines="1"
                    android:ellipsize="end" />

                <!-- Current Indicator -->
                <TextView
                    android:id="@+id/tvCurrentIndicator"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="当前"
                    android:textAppearance="?attr/textAppearanceBodySmall"
                    android:textColor="?attr/colorPrimary"
                    android:background="@drawable/status_indicator"
                    android:paddingHorizontal="8dp"
                    android:paddingVertical="2dp"
                    android:visibility="gone" />

            </LinearLayout>

            <TextView
                android:id="@+id/tvConfigDescription"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:text="配置描述"
                android:textAppearance="?attr/textAppearanceBodySmall"
                android:textColor="?attr/colorOnSurfaceVariant"
                android:maxLines="2"
                android:ellipsize="end"
                android:layout_marginTop="4dp" />

            <TextView
                android:id="@+id/tvConfigTime"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:text="创建时间"
                android:textAppearance="?attr/textAppearanceBodySmall"
                android:textColor="?attr/colorOnSurfaceVariant"
                android:layout_marginTop="4dp" />

        </LinearLayout>

        <!-- Action Buttons -->
        <LinearLayout
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:orientation="horizontal"
            android:layout_marginStart="8dp">

            <com.google.android.material.button.MaterialButton
                android:id="@+id/btnApplyConfig"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="应用"
                android:layout_marginEnd="4dp"
                style="@style/Widget.Material3.Button.TextButton" />

            <com.google.android.material.button.MaterialButton
                android:id="@+id/btnConfigMenu"
                android:layout_width="48dp"
                android:layout_height="48dp"
                android:insetTop="0dp"
                android:insetBottom="0dp"
                app:icon="@drawable/ic_expand_more"
                app:iconGravity="textStart"
                app:iconPadding="0dp"
                style="@style/Widget.Material3.Button.TextButton" />

        </LinearLayout>

    </LinearLayout>

</com.google.android.material.card.MaterialCardView>
