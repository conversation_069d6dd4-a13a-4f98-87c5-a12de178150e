package mc.meson.kasumi.config

import android.content.Context
import android.content.SharedPreferences
import android.util.Log
import com.google.gson.Gson
import com.google.gson.reflect.TypeToken
import mc.meson.kasumi.module.ModuleRegistry
import mc.meson.kasumi.module.ModuleSettingsManager
import java.text.SimpleDateFormat
import java.util.*

/**
 * 配置数据类
 * 包含所有模块的状态和设置
 */
data class ConfigData(
    val id: String = UUID.randomUUID().toString(),
    val name: String,
    val description: String = "",
    val createdTime: Long = System.currentTimeMillis(),
    val moduleStates: Map<String, Boolean> = emptyMap(),
    val moduleSettings: Map<String, Map<String, Any>> = emptyMap()
) {
    /**
     * 获取格式化的创建时间
     */
    fun getFormattedTime(): String {
        val formatter = SimpleDateFormat("yyyy-MM-dd HH:mm", Locale.getDefault())
        return formatter.format(Date(createdTime))
    }
    
    /**
     * 获取启用的模块数量
     */
    fun getEnabledModuleCount(): Int {
        return moduleStates.values.count { it }
    }
    
    /**
     * 获取总模块数量
     */
    fun getTotalModuleCount(): Int {
        return ModuleRegistry.getAllModules().size
    }
}

/**
 * 配置管理器
 * 负责配置的创建、保存、加载、导入导出等操作
 */
object ConfigManager {
    private const val TAG = "ConfigManager"
    private const val PREFS_NAME = "kasumi_configs"
    private const val KEY_CONFIGS = "configs"
    private const val KEY_CURRENT_CONFIG = "current_config"
    private const val KEY_DEFAULT_CONFIG_CREATED = "default_config_created"
    
    private var prefs: SharedPreferences? = null
    private val gson = Gson()
    private val listeners = mutableSetOf<ConfigChangeListener>()
    
    // 缓存的配置列表
    private var cachedConfigs: MutableList<ConfigData>? = null
    private var currentConfigId: String? = null
    
    /**
     * 初始化配置管理器
     */
    fun init(context: Context) {
        prefs = context.getSharedPreferences(PREFS_NAME, Context.MODE_PRIVATE)
        loadConfigs()

        // 如果没有配置，创建默认配置
        if (getAllConfigs().isEmpty()) {
            createDefaultConfigs()
        }

        // 加载当前配置
        val currentConfig = getCurrentConfig()
        if (currentConfig != null) {
            applyConfig(currentConfig)
            Log.d(TAG, "Loaded current config: ${currentConfig.name}")
        } else if (getAllConfigs().isNotEmpty()) {
            // 如果没有当前配置但有配置列表，设置第一个为当前配置
            setCurrentConfig(getAllConfigs().first().id)
        }

        Log.d(TAG, "ConfigManager initialized with ${getAllConfigs().size} configs")
    }
    
    /**
     * 添加配置变更监听器
     */
    fun addListener(listener: ConfigChangeListener) {
        listeners.add(listener)
    }
    
    /**
     * 移除配置变更监听器
     */
    fun removeListener(listener: ConfigChangeListener) {
        listeners.remove(listener)
    }
    
    /**
     * 通知监听器配置已变更
     */
    private fun notifyConfigChanged() {
        listeners.forEach { listener ->
            try {
                listener.onConfigChanged()
            } catch (e: Exception) {
                Log.e(TAG, "Error notifying config listener", e)
            }
        }
    }
    
    /**
     * 通知监听器当前配置已切换
     */
    private fun notifyCurrentConfigChanged(config: ConfigData?) {
        listeners.forEach { listener ->
            try {
                listener.onCurrentConfigChanged(config)
            } catch (e: Exception) {
                Log.e(TAG, "Error notifying current config listener", e)
            }
        }
    }
    
    /**
     * 创建默认配置
     */
    private fun createDefaultConfigs() {
        val defaultConfigs = listOf(
            ConfigData(
                name = "默认配置",
                description = "系统默认配置，包含基础功能设置"
            ),
            ConfigData(
                name = "PVP配置",
                description = "专为PVP战斗优化的配置",
                moduleStates = mapOf(
                    "kill_aura" to true,
                    "auto_clicker" to true,
                    "reach" to true,
                    "critical_hits" to true,
                    "no_knockback" to true
                )
            ),
            ConfigData(
                name = "生存配置",
                description = "适合生存模式的配置",
                moduleStates = mapOf(
                    "fullbright" to true,
                    "xray" to true,
                    "auto_mine" to true,
                    "fast_break" to true
                )
            )
        )
        
        defaultConfigs.forEach { config ->
            saveConfig(config)
        }
        
        // 设置第一个为当前配置
        setCurrentConfig(defaultConfigs.first().id)
        
        prefs?.edit()?.putBoolean(KEY_DEFAULT_CONFIG_CREATED, true)?.apply()
        Log.d(TAG, "Created ${defaultConfigs.size} default configs")
    }
    
    /**
     * 获取所有配置
     */
    fun getAllConfigs(): List<ConfigData> {
        if (cachedConfigs == null) {
            loadConfigs()
        }
        return cachedConfigs ?: emptyList()
    }
    
    /**
     * 获取当前配置
     */
    fun getCurrentConfig(): ConfigData? {
        val currentId = getCurrentConfigId()
        return getAllConfigs().find { it.id == currentId }
    }
    
    /**
     * 获取当前配置ID
     */
    fun getCurrentConfigId(): String? {
        if (currentConfigId == null) {
            currentConfigId = prefs?.getString(KEY_CURRENT_CONFIG, null)
        }
        return currentConfigId
    }
    
    /**
     * 设置当前配置
     */
    fun setCurrentConfig(configId: String): Boolean {
        val config = getAllConfigs().find { it.id == configId }
        if (config != null) {
            currentConfigId = configId
            prefs?.edit()?.putString(KEY_CURRENT_CONFIG, configId)?.apply()
            
            // 应用配置
            applyConfig(config)
            notifyCurrentConfigChanged(config)
            
            Log.d(TAG, "Switched to config: ${config.name}")
            return true
        }
        return false
    }
    
    /**
     * 创建新配置
     */
    fun createConfig(name: String, description: String = ""): ConfigData {
        // 获取当前模块状态作为新配置的基础
        val currentModuleStates = getCurrentModuleStates()
        val currentModuleSettings = getCurrentModuleSettings()
        
        val config = ConfigData(
            name = name,
            description = description,
            moduleStates = currentModuleStates,
            moduleSettings = currentModuleSettings
        )
        
        saveConfig(config)
        notifyConfigChanged()
        
        Log.d(TAG, "Created new config: $name")
        return config
    }
    
    /**
     * 保存配置
     */
    fun saveConfig(config: ConfigData) {
        val configs = getAllConfigs().toMutableList()
        val existingIndex = configs.indexOfFirst { it.id == config.id }
        
        if (existingIndex >= 0) {
            configs[existingIndex] = config
        } else {
            configs.add(config)
        }
        
        saveConfigs(configs)
        notifyConfigChanged()
    }
    
    /**
     * 删除配置
     */
    fun deleteConfig(configId: String): Boolean {
        val configs = getAllConfigs().toMutableList()
        val removed = configs.removeAll { it.id == configId }

        if (removed) {
            // 如果删除的是当前配置，切换到第一个配置
            if (configId == getCurrentConfigId() && configs.isNotEmpty()) {
                setCurrentConfig(configs.first().id)
            }

            saveConfigs(configs)
            notifyConfigChanged()
            Log.d(TAG, "Deleted config: $configId")
        }

        return removed
    }

    /**
     * 保存当前状态到当前配置
     */
    fun saveCurrentStateToConfig() {
        val currentConfig = getCurrentConfig()
        if (currentConfig != null) {
            val updatedConfig = currentConfig.copy(
                moduleStates = getCurrentModuleStates(),
                moduleSettings = getCurrentModuleSettings()
            )
            saveConfig(updatedConfig)
            Log.d(TAG, "Saved current state to config: ${currentConfig.name}")
        }
    }

    /**
     * 更新当前配置的状态（在模块状态改变时调用）
     */
    fun updateCurrentConfigState() {
        saveCurrentStateToConfig()
    }
    
    /**
     * 应用配置
     */
    private fun applyConfig(config: ConfigData) {
        Log.d(TAG, "Applying config: ${config.name}")

        // 先清除所有模块的当前状态
        ModuleRegistry.getAllModules().forEach { module ->
            module.isEnabled = false
        }

        // 清除所有设置
        ModuleSettingsManager.clearAllSettings()

        // 应用模块状态
        config.moduleStates.forEach { (moduleId, enabled) ->
            ModuleRegistry.setModuleEnabled(moduleId, enabled)
            Log.d(TAG, "Set module $moduleId enabled: $enabled")
        }

        // 应用模块设置
        config.moduleSettings.forEach { (moduleId, settings) ->
            settings.forEach { (key, value) ->
                when (value) {
                    is Boolean -> ModuleSettingsManager.setBooleanSetting(moduleId, key, value)
                    is Int -> ModuleSettingsManager.setIntSetting(moduleId, key, value)
                    is Float -> ModuleSettingsManager.setFloatSetting(moduleId, key, value)
                    is Double -> ModuleSettingsManager.setFloatSetting(moduleId, key, value.toFloat())
                }
                Log.d(TAG, "Set setting $moduleId.$key = $value")
            }
        }

        Log.d(TAG, "Applied config: ${config.name}")
    }
    
    /**
     * 获取当前模块状态
     */
    private fun getCurrentModuleStates(): Map<String, Boolean> {
        return ModuleRegistry.getAllModules().associate { module ->
            module.id to module.isEnabled
        }
    }
    
    /**
     * 获取当前模块设置
     */
    private fun getCurrentModuleSettings(): Map<String, Map<String, Any>> {
        return ModuleSettingsManager.getAllSettings()
    }
    
    /**
     * 加载配置列表
     */
    private fun loadConfigs() {
        try {
            val configsJson = prefs?.getString(KEY_CONFIGS, null)
            if (configsJson != null) {
                val type = object : TypeToken<List<ConfigData>>() {}.type
                cachedConfigs = gson.fromJson<List<ConfigData>>(configsJson, type).toMutableList()
            } else {
                cachedConfigs = mutableListOf()
            }
        } catch (e: Exception) {
            Log.e(TAG, "Error loading configs", e)
            cachedConfigs = mutableListOf()
        }
    }
    
    /**
     * 保存配置列表
     */
    private fun saveConfigs(configs: List<ConfigData>) {
        try {
            val configsJson = gson.toJson(configs)
            prefs?.edit()?.putString(KEY_CONFIGS, configsJson)?.apply()
            cachedConfigs = configs.toMutableList()
        } catch (e: Exception) {
            Log.e(TAG, "Error saving configs", e)
        }
    }
}

/**
 * 配置变更监听器
 */
interface ConfigChangeListener {
    /**
     * 配置列表发生变更时调用
     */
    fun onConfigChanged()
    
    /**
     * 当前配置切换时调用
     */
    fun onCurrentConfigChanged(config: ConfigData?)
}
