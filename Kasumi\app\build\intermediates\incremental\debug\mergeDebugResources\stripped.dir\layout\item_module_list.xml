<?xml version="1.0" encoding="utf-8"?>
<androidx.cardview.widget.CardView xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:id="@+id/cardModule"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:layout_margin="2dp"
    app:cardCornerRadius="8dp"
    app:cardElevation="1dp"
    android:background="?attr/selectableItemBackground">

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="horizontal"
        android:padding="8dp"
        android:gravity="center_vertical">

        <!-- 图标 -->
        <ImageView
            android:id="@+id/ivModuleIcon"
            android:layout_width="24dp"
            android:layout_height="24dp"
            android:layout_marginEnd="8dp"
            android:src="@drawable/ic_close"
            android:tint="@color/md_theme_light_onSurface" />

        <!-- 文本信息 -->
        <LinearLayout
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:orientation="vertical">

            <!-- 模块名称 -->
            <TextView
                android:id="@+id/tvModuleName"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:text="模块名称"
                android:textSize="12sp"
                android:textStyle="bold"
                android:textColor="@color/md_theme_light_onSurface"
                android:maxLines="1"
                android:ellipsize="end" />

            <!-- 模块描述 -->
            <TextView
                android:id="@+id/tvModuleDescription"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:text="模块描述"
                android:textSize="10sp"
                android:textColor="@color/md_theme_light_onSurfaceVariant"
                android:maxLines="1"
                android:ellipsize="end" />

        </LinearLayout>

        <!-- 开关 -->
        <Switch
            android:id="@+id/switchModule"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginStart="8dp" />

    </LinearLayout>

</androidx.cardview.widget.CardView>
