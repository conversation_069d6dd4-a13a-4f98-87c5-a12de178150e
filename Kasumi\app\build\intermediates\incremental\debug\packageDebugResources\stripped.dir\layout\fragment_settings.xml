<?xml version="1.0" encoding="utf-8"?>
<androidx.core.widget.NestedScrollView xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:fillViewport="true">

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="vertical"
        android:padding="16dp">

        <!-- General Settings -->
        <com.google.android.material.card.MaterialCardView
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginBottom="16dp"
            app:cardElevation="0dp"
            app:strokeWidth="1dp"
            app:strokeColor="?attr/colorOutline"
            app:cardBackgroundColor="@color/card_background_primary"
            style="@style/Widget.Material3.CardView.Outlined">

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="vertical"
                android:padding="20dp">

                <TextView
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:text="通用设置"
                    android:textAppearance="?attr/textAppearanceTitleMedium"
                    android:textColor="?attr/colorOnSurface"
                    android:layout_marginBottom="16dp" />

                <!-- Dark Mode -->
                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="horizontal"
                    android:gravity="center_vertical"
                    android:layout_marginBottom="12dp">

                    <LinearLayout
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:layout_weight="1"
                        android:orientation="vertical">

                        <TextView
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:text="深色模式"
                            android:textAppearance="?attr/textAppearanceBodyLarge"
                            android:textColor="?attr/colorOnSurface" />

                        <TextView
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:text="切换应用主题"
                            android:textAppearance="?attr/textAppearanceBodySmall"
                            android:textColor="?attr/colorOnSurfaceVariant" />

                    </LinearLayout>

                    <com.google.android.material.materialswitch.MaterialSwitch
                        android:id="@+id/switchDarkMode"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        app:thumbTint="?attr/colorPrimary"
                        app:trackTint="?attr/colorSurfaceVariant" />

                </LinearLayout>

                <!-- Notifications -->
                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="horizontal"
                    android:gravity="center_vertical"
                    android:layout_marginBottom="12dp">

                    <LinearLayout
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:layout_weight="1"
                        android:orientation="vertical">

                        <TextView
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:text="通知"
                            android:textAppearance="?attr/textAppearanceBodyLarge"
                            android:textColor="?attr/colorOnSurface" />

                        <TextView
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:text="启用功能状态通知"
                            android:textAppearance="?attr/textAppearanceBodySmall"
                            android:textColor="?attr/colorOnSurfaceVariant" />

                    </LinearLayout>

                    <com.google.android.material.materialswitch.MaterialSwitch
                        android:id="@+id/switchNotifications"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:checked="true"
                        app:thumbTint="?attr/colorPrimary"
                        app:trackTint="?attr/colorSurfaceVariant" />

                </LinearLayout>

                <!-- Auto Connect -->
                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="horizontal"
                    android:gravity="center_vertical">

                    <LinearLayout
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:layout_weight="1"
                        android:orientation="vertical">

                        <TextView
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:text="自动连接"
                            android:textAppearance="?attr/textAppearanceBodyLarge"
                            android:textColor="?attr/colorOnSurface" />

                        <TextView
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:text="启动时自动连接Minecraft"
                            android:textAppearance="?attr/textAppearanceBodySmall"
                            android:textColor="?attr/colorOnSurfaceVariant" />

                    </LinearLayout>

                    <com.google.android.material.materialswitch.MaterialSwitch
                        android:id="@+id/switchAutoConnect"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:checked="true"
                        app:thumbTint="?attr/colorPrimary"
                        app:trackTint="?attr/colorSurfaceVariant" />

                </LinearLayout>

            </LinearLayout>

        </com.google.android.material.card.MaterialCardView>

        <!-- Configuration Management -->
        <com.google.android.material.card.MaterialCardView
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginBottom="16dp"
            app:cardElevation="0dp"
            app:strokeWidth="1dp"
            app:strokeColor="?attr/colorOutline"
            app:cardBackgroundColor="@color/card_background_primary"
            style="@style/Widget.Material3.CardView.Outlined">

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="vertical"
                android:padding="20dp">

                <TextView
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:text="配置管理"
                    android:textAppearance="?attr/textAppearanceTitleMedium"
                    android:textColor="?attr/colorOnSurface"
                    android:layout_marginBottom="16dp" />

                <!-- Current Configuration -->
                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="horizontal"
                    android:gravity="center_vertical"
                    android:layout_marginBottom="12dp">

                    <LinearLayout
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:layout_weight="1"
                        android:orientation="vertical">

                        <TextView
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:text="当前配置"
                            android:textAppearance="?attr/textAppearanceBodyLarge"
                            android:textColor="?attr/colorOnSurface" />

                        <TextView
                            android:id="@+id/tvCurrentConfig"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:text="默认配置"
                            android:textAppearance="?attr/textAppearanceBodySmall"
                            android:textColor="?attr/colorPrimary" />

                    </LinearLayout>

                    <com.google.android.material.button.MaterialButton
                        android:id="@+id/btnSwitchConfig"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="切换"
                        style="@style/Widget.Material3.Button.TextButton" />

                </LinearLayout>

                <!-- Configuration Actions -->
                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="horizontal"
                    android:gravity="center_vertical">

                    <com.google.android.material.button.MaterialButton
                        android:id="@+id/btnCreateConfig"
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:layout_weight="1"
                        android:layout_marginEnd="8dp"
                        android:text="新建配置"
                        style="@style/Widget.Material3.Button.OutlinedButton" />

                    <com.google.android.material.button.MaterialButton
                        android:id="@+id/btnManageConfigs"
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:layout_weight="1"
                        android:layout_marginStart="8dp"
                        android:text="管理配置"
                        style="@style/Widget.Material3.Button.OutlinedButton" />

                </LinearLayout>

            </LinearLayout>

        </com.google.android.material.card.MaterialCardView>

        <!-- About -->
        <com.google.android.material.card.MaterialCardView
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginBottom="16dp"
            app:cardElevation="0dp"
            app:strokeWidth="1dp"
            app:strokeColor="?attr/colorOutline"
            app:cardBackgroundColor="@color/card_background_secondary"
            style="@style/Widget.Material3.CardView.Outlined">

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="vertical"
                android:padding="20dp">

                <TextView
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:text="关于"
                    android:textAppearance="?attr/textAppearanceTitleMedium"
                    android:textColor="?attr/colorOnSurface"
                    android:layout_marginBottom="16dp" />

                <TextView
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:text="Kasumi Hack v1.0"
                    android:textAppearance="?attr/textAppearanceBodyLarge"
                    android:textColor="?attr/colorOnSurface"
                    android:layout_marginBottom="8dp" />

                <TextView
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:text="强大的Minecraft辅助工具，采用Material You设计语言，为您提供最佳的用户体验。"
                    android:textAppearance="?attr/textAppearanceBodyMedium"
                    android:textColor="?attr/colorOnSurfaceVariant"
                    android:layout_marginBottom="16dp" />

                <com.google.android.material.button.MaterialButton
                    android:id="@+id/btnCheckUpdate"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="检查更新"
                    style="@style/App.Button.Secondary" />

            </LinearLayout>

        </com.google.android.material.card.MaterialCardView>

    </LinearLayout>

</androidx.core.widget.NestedScrollView>
