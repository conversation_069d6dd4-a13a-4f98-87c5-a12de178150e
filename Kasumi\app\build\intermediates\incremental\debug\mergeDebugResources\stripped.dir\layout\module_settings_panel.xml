<?xml version="1.0" encoding="utf-8"?>
<FrameLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="match_parent">    <!-- 实际的设置内容，添加左边距 -->
    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:layout_marginStart="8dp"
        android:orientation="vertical"
        android:background="@drawable/settings_panel_background"
        android:layout_marginEnd="4dp"
        android:layout_marginBottom="8dp"
        android:padding="8dp">    <!-- 紧凑的设置标题栏 -->
    <RelativeLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginBottom="8dp"
        android:paddingVertical="4dp">

        <!-- 返回按钮 - 左上角固定位置 -->
        <ImageView
            android:id="@+id/btnBack"
            android:layout_width="16dp"
            android:layout_height="16dp"
            android:layout_alignParentStart="true"
            android:layout_centerVertical="true"
            android:src="@drawable/ic_arrow_back_24"
            android:tint="@color/md_theme_light_onSurfaceVariant"
            android:background="?attr/selectableItemBackgroundBorderless"
            android:clickable="true"
            android:focusable="true" />

        <!-- 模块名称 - 居中显示 -->
        <TextView
            android:id="@+id/tvModuleName"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_centerInParent="true"
            android:text="杀戮光环"
            android:textSize="13sp"
            android:textStyle="bold"
            android:textColor="@color/md_theme_light_onSurfaceVariant" />

    </RelativeLayout>    <!-- 设置项列表 - 美化滚动条 -->
    <androidx.recyclerview.widget.RecyclerView
        android:id="@+id/rvSettings"
        android:layout_width="match_parent"
        android:layout_height="0dp"        android:layout_weight="1"
        android:scrollbars="none" />

    </LinearLayout>

</FrameLayout>
