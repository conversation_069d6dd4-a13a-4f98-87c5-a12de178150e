<?xml version="1.0" encoding="utf-8" standalone="yes"?><Layout layout="floating_window_expanded_new" modulePackage="mc.meson.kasumi" filePath="app\src\main\res\layout\floating_window_expanded_new.xml" directory="layout" isMerge="false" isBindingData="false" rootNodeType="androidx.cardview.widget.CardView"><Targets><Target tag="layout/floating_window_expanded_new_0" view="androidx.cardview.widget.CardView"><Expressions/><location startLine="1" startOffset="0" endLine="112" endOffset="35"/></Target><Target tag="binding_1" view="FrameLayout"><Expressions/><location startLine="8" startOffset="73" endLine="110" endOffset="17"/></Target><Target id="@+id/settingsPanel" tag="binding_1" include="module_settings_panel"><Expressions/><location startLine="99" startOffset="8" endLine="108" endOffset="37"/></Target><Target id="@+id/mainContent" view="LinearLayout"><Expressions/><location startLine="13" startOffset="8" endLine="96" endOffset="22"/></Target><Target id="@+id/titleBar" view="LinearLayout"><Expressions/><location startLine="20" startOffset="12" endLine="75" endOffset="26"/></Target><Target id="@+id/tabCategories" view="com.google.android.material.tabs.TabLayout"><Expressions/><location startLine="46" startOffset="16" endLine="63" endOffset="48"/></Target><Target id="@+id/btnCollapseExpanded" view="ImageView"><Expressions/><location startLine="65" startOffset="16" endLine="73" endOffset="46"/></Target><Target id="@+id/moduleContainer" view="FrameLayout"><Expressions/><location startLine="78" startOffset="12" endLine="94" endOffset="25"/></Target><Target id="@+id/rvExpandedModules" view="androidx.recyclerview.widget.RecyclerView"><Expressions/><location startLine="88" startOffset="16" endLine="92" endOffset="59"/></Target></Targets></Layout>