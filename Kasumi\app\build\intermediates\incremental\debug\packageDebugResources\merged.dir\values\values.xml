<?xml version="1.0" encoding="utf-8"?>
<resources xmlns:ns1="http://schemas.android.com/tools">
    <color name="black">#FF000000</color>
    <color name="card_background_primary">#F7E4D0</color>
    <color name="card_background_secondary">#F9E8D4</color>
    <color name="card_background_tertiary">#FBECD8</color>
    <color name="md_theme_dark_background">#1A1611</color>
    <color name="md_theme_dark_error">#FFB4AB</color>
    <color name="md_theme_dark_errorContainer">#93000A</color>
    <color name="md_theme_dark_inverseOnSurface">#1A1611</color>
    <color name="md_theme_dark_inversePrimary">#B8956A</color>
    <color name="md_theme_dark_inverseSurface">#E8E2DC</color>
    <color name="md_theme_dark_onBackground">#E8E2DC</color>
    <color name="md_theme_dark_onError">#690005</color>
    <color name="md_theme_dark_onErrorContainer">#FFDAD6</color>
    <color name="md_theme_dark_onPrimary">#3D2914</color>
    <color name="md_theme_dark_onPrimaryContainer">#F7E4D0</color>
    <color name="md_theme_dark_onSecondary">#3A2F20</color>
    <color name="md_theme_dark_onSecondaryContainer">#F0E1CC</color>
    <color name="md_theme_dark_onSurface">#E8E2DC</color>
    <color name="md_theme_dark_onSurfaceVariant">#D4C4B0</color>
    <color name="md_theme_dark_onTertiary">#3F2A15</color>
    <color name="md_theme_dark_onTertiaryContainer">#F5DCC0</color>
    <color name="md_theme_dark_outline">#9D8F7E</color>
    <color name="md_theme_dark_outlineVariant">#4F453A</color>
    <color name="md_theme_dark_primary">#E6D1A8</color>
    <color name="md_theme_dark_primaryContainer">#6B4E2A</color>
    <color name="md_theme_dark_scrim">#000000</color>
    <color name="md_theme_dark_secondary">#D4C4B0</color>
    <color name="md_theme_dark_secondaryContainer">#524635</color>
    <color name="md_theme_dark_shadow">#000000</color>
    <color name="md_theme_dark_surface">#1A1611</color>
    <color name="md_theme_dark_surfaceTint">#E6D1A8</color>
    <color name="md_theme_dark_surfaceVariant">#4F453A</color>
    <color name="md_theme_dark_tertiary">#E0C49A</color>
    <color name="md_theme_dark_tertiaryContainer">#5A3F2A</color>
    <color name="md_theme_light_background">#FDF9F3</color>
    <color name="md_theme_light_error">#BA1A1A</color>
    <color name="md_theme_light_errorContainer">#FFDAD6</color>
    <color name="md_theme_light_inverseOnSurface">#F7F2EC</color>
    <color name="md_theme_light_inversePrimary">#E6D1A8</color>
    <color name="md_theme_light_inverseSurface">#343026</color>
    <color name="md_theme_light_onBackground">#1F1B16</color>
    <color name="md_theme_light_onError">#FFFFFF</color>
    <color name="md_theme_light_onErrorContainer">#410002</color>
    <color name="md_theme_light_onPrimary">#FFFFFF</color>
    <color name="md_theme_light_onPrimaryContainer">#3D2914</color>
    <color name="md_theme_light_onSecondary">#FFFFFF</color>
    <color name="md_theme_light_onSecondaryContainer">#2A1F0F</color>
    <color name="md_theme_light_onSurface">#1F1B16</color>
    <color name="md_theme_light_onSurfaceVariant">#4F453A</color>
    <color name="md_theme_light_onTertiary">#FFFFFF</color>
    <color name="md_theme_light_onTertiaryContainer">#2F1A08</color>
    <color name="md_theme_light_outline">#7F7368</color>
    <color name="md_theme_light_outlineVariant">#D4C4B0</color>
    <color name="md_theme_light_primary">#B8956A</color>
    <color name="md_theme_light_primaryContainer">#F7E4D0</color>
    <color name="md_theme_light_scrim">#000000</color>
    <color name="md_theme_light_secondary">#8B7355</color>
    <color name="md_theme_light_secondaryContainer">#F0E1CC</color>
    <color name="md_theme_light_shadow">#000000</color>
    <color name="md_theme_light_surface">#FDF9F3</color>
    <color name="md_theme_light_surfaceTint">#B8956A</color>
    <color name="md_theme_light_surfaceVariant">#F0E6D8</color>
    <color name="md_theme_light_tertiary">#A67C52</color>
    <color name="md_theme_light_tertiaryContainer">#F5DCC0</color>
    <color name="minecraft_blue">#5555FF</color>
    <color name="minecraft_blue_dark">#4444CC</color>
    <color name="minecraft_green">#00FF00</color>
    <color name="minecraft_green_dark">#00CC00</color>
    <color name="minecraft_orange">#FFAA00</color>
    <color name="minecraft_orange_dark">#CC8800</color>
    <color name="minecraft_purple">#AA00AA</color>
    <color name="minecraft_purple_dark">#880088</color>
    <color name="minecraft_red">#FF5555</color>
    <color name="minecraft_red_dark">#CC4444</color>
    <color name="minecraft_yellow">#FFFF55</color>
    <color name="minecraft_yellow_dark">#CCCC44</color>
    <color name="purple_200">#FFBB86FC</color>
    <color name="purple_500">#FF6200EE</color>
    <color name="purple_700">#FF3700B3</color>
    <color name="teal_200">#FF03DAC5</color>
    <color name="teal_700">#FF018786</color>
    <color name="white">#FFFFFFFF</color>
    <item name="btnBackInTitle" type="id"/>
    <item name="tvModuleTitleInTitle" type="id"/>
    <string name="about">关于</string>
    <string name="about_desc">强大的Minecraft辅助工具，采用Material You设计语言，为您提供最佳的用户体验。</string>
    <string name="app_name">Kasumi</string>
    <string name="auto_clicker">自动点击</string>
    <string name="auto_clicker_desc">自动连续点击攻击</string>
    <string name="auto_connect">自动连接</string>
    <string name="auto_connect_desc">启动时自动连接Minecraft</string>
    <string name="check_update">检查更新</string>
    <string name="checking_updates">正在检查更新...</string>
    <string name="combat_features">战斗功能</string>
    <string name="connected">已连接到Minecraft</string>
    <string name="connecting">正在连接...</string>
    <string name="connection_status">连接状态</string>
    <string name="dark_mode">深色模式</string>
    <string name="dark_mode_desc">切换应用主题</string>
    <string name="disconnected">未连接</string>
    <string name="feature_disabled">%s 已禁用</string>
    <string name="feature_enabled">%s 已启用</string>
    <string name="floating_window_desc">在游戏中显示悬浮窗，快速切换hack功能</string>
    <string name="floating_window_notification_text">悬浮窗正在运行</string>
    <string name="floating_window_notification_title">Kasumi Hack</string>
    <string name="floating_window_permission_needed">需要悬浮窗权限才能使用此功能</string>
    <string name="floating_window_started">悬浮窗已启动！可以在游戏中使用了</string>
    <string name="floating_window_title">游戏内悬浮窗</string>
    <string name="fly">飞行模式</string>
    <string name="fly_desc">在生存模式中飞行</string>
    <string name="general_settings">通用设置</string>
    <string name="kill_aura">杀戮光环</string>
    <string name="kill_aura_desc">自动攻击附近的敌人</string>
    <string name="latest_version">您正在使用最新版本！</string>
    <string name="movement_features">移动功能</string>
    <string name="nav_features">功能</string>
    <string name="nav_home">主页</string>
    <string name="nav_settings">设置</string>
    <string name="no_fall">无摔落伤害</string>
    <string name="no_fall_desc">免疫摔落伤害</string>
    <string name="notifications">通知</string>
    <string name="notifications_desc">启用功能状态通知</string>
    <string name="reach">攻击距离</string>
    <string name="reach_desc">扩展攻击范围</string>
    <string name="setting_disabled">%s 已禁用</string>
    <string name="setting_enabled">%s 已启用</string>
    <string name="settings">设置</string>
    <string name="settings_coming_soon">设置功能即将推出！</string>
    <string name="speed">速度提升</string>
    <string name="speed_desc">提高移动速度</string>
    <string name="start_floating_window">启动悬浮窗</string>
    <string name="version">Kasumi Hack v1.0</string>
    <string name="welcome_description">强大的Minecraft辅助工具，让你的游戏体验更上一层楼</string>
    <string name="welcome_title">欢迎使用 Kasumi</string>
    <style name="App.Button.Primary" parent="Widget.Material3.Button">
        <item name="backgroundTint">?attr/colorPrimary</item>
        <item name="android:textColor">?attr/colorOnPrimary</item>
        <item name="cornerRadius">12dp</item>
    </style>
    <style name="App.Button.Secondary" parent="Widget.Material3.Button.TonalButton">
        <item name="backgroundTint">?attr/colorSecondaryContainer</item>
        <item name="android:textColor">?attr/colorOnSecondaryContainer</item>
        <item name="cornerRadius">12dp</item>
    </style>
    <style name="App.Card.Elevated" parent="Widget.Material3.CardView.Elevated">
        <item name="cardElevation">4dp</item>
        <item name="cardCornerRadius">16dp</item>
        <item name="android:layout_margin">8dp</item>
    </style>
    <style name="App.Card.Outlined" parent="Widget.Material3.CardView.Outlined">
        <item name="cardCornerRadius">16dp</item>
        <item name="android:layout_margin">8dp</item>
        <item name="strokeWidth">1dp</item>
        <item name="strokeColor">?attr/colorOutline</item>
    </style>
    <style name="App.FloatingActionButton" parent="Widget.Material3.FloatingActionButton.Primary">
        <item name="backgroundTint">?attr/colorPrimaryContainer</item>
        <item name="tint">?attr/colorOnPrimaryContainer</item>
        <item name="rippleColor">?attr/colorPrimary</item>
    </style>
    <style name="App.Switch.Material" parent="Widget.Material3.CompoundButton.MaterialSwitch">
        <item name="thumbTint">?attr/colorPrimary</item>
        <item name="trackTint">?attr/colorSurfaceVariant</item>
    </style>
    <style name="App.TabLayout.Rounded" parent="Widget.Material3.TabLayout">
        <item name="android:background">@drawable/tab_layout_background</item>
        <item name="tabIndicatorColor">@color/md_theme_light_primary</item>
        <item name="tabSelectedTextColor">@color/md_theme_light_primary</item>
        <item name="tabTextColor">@color/md_theme_light_onSurfaceVariant</item>
        <item name="tabIndicatorHeight">2dp</item>
        <item name="tabTextAppearance">@style/App.TabLayout.TextAppearance</item>
    </style>
    <style name="App.TabLayout.TextAppearance" parent="TextAppearance.Material3.LabelMedium">
        <item name="android:textSize">13sp</item>
        <item name="android:fontFamily">sans-serif-medium</item>
        <item name="textAllCaps">false</item>
    </style>
    <style name="App.Text.Body" parent="@style/TextAppearance.Material3.BodyMedium">
        <item name="android:textColor">?attr/colorOnSurfaceVariant</item>
    </style>
    <style name="App.Text.Headline" parent="@style/TextAppearance.Material3.HeadlineSmall">
        <item name="android:textColor">?attr/colorOnSurface</item>
        <item name="android:fontFamily">sans-serif-medium</item>
    </style>
    <style name="App.Text.Title" parent="@style/TextAppearance.Material3.TitleMedium">
        <item name="android:textColor">?attr/colorOnSurface</item>
        <item name="android:fontFamily">sans-serif-medium</item>
    </style>
    <style name="App.Toolbar" parent="Widget.Material3.Toolbar">
        <item name="titleTextColor">?attr/colorOnSurface</item>
        <item name="subtitleTextColor">?attr/colorOnSurfaceVariant</item>
    </style>
    <style name="App.Widget.BottomNavigationView.ActiveIndicator" parent="Widget.Material3.BottomNavigationView.ActiveIndicator">
        <item name="android:color">?attr/colorPrimaryContainer</item>
    </style>
    <style name="Theme.Kasumi" parent="Theme.Material3.DayNight.NoActionBar">
        
        <item name="colorPrimary">@color/md_theme_light_primary</item>
        <item name="colorOnPrimary">@color/md_theme_light_onPrimary</item>
        <item name="colorPrimaryContainer">@color/md_theme_light_primaryContainer</item>
        <item name="colorOnPrimaryContainer">@color/md_theme_light_onPrimaryContainer</item>
        <item name="colorSecondary">@color/md_theme_light_secondary</item>
        <item name="colorOnSecondary">@color/md_theme_light_onSecondary</item>
        <item name="colorSecondaryContainer">@color/md_theme_light_secondaryContainer</item>
        <item name="colorOnSecondaryContainer">@color/md_theme_light_onSecondaryContainer</item>
        <item name="colorTertiary">@color/md_theme_light_tertiary</item>
        <item name="colorOnTertiary">@color/md_theme_light_onTertiary</item>
        <item name="colorTertiaryContainer">@color/md_theme_light_tertiaryContainer</item>
        <item name="colorOnTertiaryContainer">@color/md_theme_light_onTertiaryContainer</item>
        <item name="colorError">@color/md_theme_light_error</item>
        <item name="colorErrorContainer">@color/md_theme_light_errorContainer</item>
        <item name="colorOnError">@color/md_theme_light_onError</item>
        <item name="colorOnErrorContainer">@color/md_theme_light_onErrorContainer</item>
        <item name="android:colorBackground">@color/md_theme_light_background</item>
        <item name="colorOnBackground">@color/md_theme_light_onBackground</item>
        <item name="colorSurface">@color/md_theme_light_surface</item>
        <item name="colorOnSurface">@color/md_theme_light_onSurface</item>
        <item name="colorSurfaceVariant">@color/md_theme_light_surfaceVariant</item>
        <item name="colorOnSurfaceVariant">@color/md_theme_light_onSurfaceVariant</item>
        <item name="colorOutline">@color/md_theme_light_outline</item>
        <item name="colorOnSurfaceInverse">@color/md_theme_light_inverseOnSurface</item>
        <item name="colorSurfaceInverse">@color/md_theme_light_inverseSurface</item>
        <item name="colorPrimaryInverse">@color/md_theme_light_inversePrimary</item>

        
        <item name="android:statusBarColor">@android:color/transparent</item>
        <item name="android:navigationBarColor">@android:color/transparent</item>
        <item name="android:windowLightStatusBar">true</item>
        <item name="android:windowLightNavigationBar" ns1:targetApi="o_mr1">true</item>

        
        <item name="android:windowDrawsSystemBarBackgrounds">true</item>
        <item name="android:enforceStatusBarContrast" ns1:targetApi="q">false</item>
        <item name="android:enforceNavigationBarContrast" ns1:targetApi="q">false</item>
    </style>
    <style name="Theme.Kasumi.FloatingWindow" parent="Theme.MaterialComponents.DayNight.NoActionBar">
        
        <item name="colorPrimary">@color/md_theme_light_primary</item>
        <item name="colorPrimaryDark">@color/md_theme_light_primary</item>
        <item name="colorAccent">@color/md_theme_light_secondary</item>
        <item name="colorSecondary">@color/md_theme_light_secondary</item>
        <item name="android:colorBackground">@color/md_theme_light_background</item>
        <item name="colorSurface">@color/md_theme_light_surface</item>
        <item name="colorOnSurface">@color/md_theme_light_onSurface</item>
        <item name="colorOnPrimary">@color/md_theme_light_onPrimary</item>
        <item name="colorOnSecondary">@color/md_theme_light_onSecondary</item>

        
        <item name="materialCardViewStyle">@style/Widget.MaterialComponents.CardView</item>
    </style>
</resources>