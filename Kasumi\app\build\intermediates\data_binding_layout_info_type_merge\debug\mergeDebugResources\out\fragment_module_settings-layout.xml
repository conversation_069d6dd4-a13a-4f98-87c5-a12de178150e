<?xml version="1.0" encoding="utf-8" standalone="yes"?><Layout layout="fragment_module_settings" modulePackage="mc.meson.kasumi" filePath="app\src\main\res\layout\fragment_module_settings.xml" directory="layout" isMerge="false" isBindingData="false" rootNodeType="android.widget.LinearLayout"><Targets><Target tag="layout/fragment_module_settings_0" view="LinearLayout"><Expressions/><location startLine="1" startOffset="0" endLine="194" endOffset="14"/></Target><Target id="@+id/btnBack" view="ImageButton"><Expressions/><location startLine="24" startOffset="12" endLine="31" endOffset="49"/></Target><Target id="@+id/switchModuleEnabled" view="com.google.android.material.materialswitch.MaterialSwitch"><Expressions/><location startLine="42" startOffset="12" endLine="47" endOffset="61"/></Target><Target id="@+id/ivModuleIcon" view="ImageView"><Expressions/><location startLine="60" startOffset="12" endLine="65" endOffset="49"/></Target><Target id="@+id/tvModuleName" view="TextView"><Expressions/><location startLine="74" startOffset="16" endLine="81" endOffset="46"/></Target><Target id="@+id/viewCategoryIndicator" view="View"><Expressions/><location startLine="90" startOffset="20" endLine="95" endOffset="67"/></Target><Target id="@+id/tvModuleCategory" view="TextView"><Expressions/><location startLine="97" startOffset="20" endLine="104" endOffset="58"/></Target><Target id="@+id/tvModuleDescription" view="TextView"><Expressions/><location startLine="128" startOffset="12" endLine="136" endOffset="48"/></Target><Target id="@+id/recyclerViewSettings" view="androidx.recyclerview.widget.RecyclerView"><Expressions/><location startLine="139" startOffset="12" endLine="143" endOffset="56"/></Target><Target id="@+id/layoutNoSettings" view="LinearLayout"><Expressions/><location startLine="146" startOffset="12" endLine="179" endOffset="26"/></Target><Target id="@+id/btnResetSettings" view="com.google.android.material.button.MaterialButton"><Expressions/><location startLine="182" startOffset="12" endLine="188" endOffset="71"/></Target></Targets></Layout>