<?xml version="1.0" encoding="utf-8" standalone="yes"?><Layout layout="fragment_module_settings" modulePackage="mc.meson.kasumi" filePath="app\src\main\res\layout\fragment_module_settings.xml" directory="layout" isMerge="false" isBindingData="false" rootNodeType="android.widget.LinearLayout"><Targets><Target tag="layout/fragment_module_settings_0" view="LinearLayout"><Expressions/><location startLine="1" startOffset="0" endLine="124" endOffset="14"/></Target><Target id="@+id/btnBack" view="ImageButton"><Expressions/><location startLine="18" startOffset="8" endLine="25" endOffset="45"/></Target><Target id="@+id/tvModuleName" view="TextView"><Expressions/><location startLine="27" startOffset="8" endLine="35" endOffset="46"/></Target><Target id="@+id/switchModuleEnabled" view="com.google.android.material.materialswitch.MaterialSwitch"><Expressions/><location startLine="37" startOffset="8" endLine="40" endOffset="50"/></Target><Target id="@+id/tvModuleDescription" view="TextView"><Expressions/><location startLine="58" startOffset="12" endLine="66" endOffset="48"/></Target><Target id="@+id/recyclerViewSettings" view="androidx.recyclerview.widget.RecyclerView"><Expressions/><location startLine="69" startOffset="12" endLine="73" endOffset="56"/></Target><Target id="@+id/layoutNoSettings" view="LinearLayout"><Expressions/><location startLine="76" startOffset="12" endLine="109" endOffset="26"/></Target><Target id="@+id/btnResetSettings" view="com.google.android.material.button.MaterialButton"><Expressions/><location startLine="112" startOffset="12" endLine="118" endOffset="71"/></Target></Targets></Layout>