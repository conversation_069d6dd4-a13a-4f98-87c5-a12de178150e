<?xml version="1.0" encoding="utf-8" standalone="yes"?><Layout layout="fragment_module_settings" modulePackage="mc.meson.kasumi" filePath="app\src\main\res\layout\fragment_module_settings.xml" directory="layout" isMerge="false" isBindingData="false" rootNodeType="android.widget.LinearLayout"><Targets><Target tag="layout/fragment_module_settings_0" view="LinearLayout"><Expressions/><location startLine="1" startOffset="0" endLine="192" endOffset="14"/></Target><Target id="@+id/btnBack" view="ImageButton"><Expressions/><location startLine="18" startOffset="8" endLine="25" endOffset="45"/></Target><Target id="@+id/switchModuleEnabled" view="com.google.android.material.materialswitch.MaterialSwitch"><Expressions/><location startLine="36" startOffset="8" endLine="39" endOffset="50"/></Target><Target id="@+id/ivModuleIcon" view="ImageView"><Expressions/><location startLine="71" startOffset="20" endLine="76" endOffset="55"/></Target><Target id="@+id/tvModuleName" view="TextView"><Expressions/><location startLine="85" startOffset="24" endLine="92" endOffset="54"/></Target><Target id="@+id/viewCategoryIndicator" view="View"><Expressions/><location startLine="101" startOffset="28" endLine="106" endOffset="73"/></Target><Target id="@+id/tvModuleCategory" view="TextView"><Expressions/><location startLine="108" startOffset="28" endLine="115" endOffset="66"/></Target><Target id="@+id/tvModuleDescription" view="TextView"><Expressions/><location startLine="119" startOffset="24" endLine="128" endOffset="53"/></Target><Target id="@+id/recyclerViewSettings" view="androidx.recyclerview.widget.RecyclerView"><Expressions/><location startLine="137" startOffset="12" endLine="141" endOffset="56"/></Target><Target id="@+id/layoutNoSettings" view="LinearLayout"><Expressions/><location startLine="144" startOffset="12" endLine="177" endOffset="26"/></Target><Target id="@+id/btnResetSettings" view="com.google.android.material.button.MaterialButton"><Expressions/><location startLine="180" startOffset="12" endLine="186" endOffset="71"/></Target></Targets></Layout>