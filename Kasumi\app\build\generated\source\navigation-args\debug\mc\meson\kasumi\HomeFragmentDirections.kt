package mc.meson.kasumi

import androidx.navigation.ActionOnlyNavDirections
import androidx.navigation.NavDirections

public class HomeFragmentDirections private constructor() {
  public companion object {
    public fun actionHomeToFeatures(): NavDirections =
        ActionOnlyNavDirections(R.id.action_home_to_features)

    public fun actionHomeToSettings(): NavDirections =
        ActionOnlyNavDirections(R.id.action_home_to_settings)
  }
}
