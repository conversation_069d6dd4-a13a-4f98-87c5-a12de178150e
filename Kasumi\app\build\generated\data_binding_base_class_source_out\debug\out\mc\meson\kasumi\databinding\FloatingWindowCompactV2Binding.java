// Generated by view binder compiler. Do not edit!
package mc.meson.kasumi.databinding;

import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.ImageView;
import android.widget.LinearLayout;
import android.widget.TextView;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.cardview.widget.CardView;
import androidx.viewbinding.ViewBinding;
import androidx.viewbinding.ViewBindings;
import androidx.viewpager2.widget.ViewPager2;
import java.lang.NullPointerException;
import java.lang.Override;
import java.lang.String;
import mc.meson.kasumi.R;

public final class FloatingWindowCompactV2Binding implements ViewBinding {
  @NonNull
  private final CardView rootView;

  @NonNull
  public final ImageView btnCollapse;

  @NonNull
  public final ImageView btnConfigManager;

  @NonNull
  public final ImageView btnExpand;

  @NonNull
  public final ImageView collapsedDot;

  @NonNull
  public final LinearLayout compactContent;

  @NonNull
  public final View indicatorCombat;

  @NonNull
  public final View indicatorMovement;

  @NonNull
  public final View indicatorPlayer;

  @NonNull
  public final View indicatorVisual;

  @NonNull
  public final View indicatorWorld;

  @NonNull
  public final LinearLayout topBar;

  @NonNull
  public final TextView tvCategoryName;

  @NonNull
  public final ViewPager2 vpCompactCategories;

  private FloatingWindowCompactV2Binding(@NonNull CardView rootView, @NonNull ImageView btnCollapse,
      @NonNull ImageView btnConfigManager, @NonNull ImageView btnExpand,
      @NonNull ImageView collapsedDot, @NonNull LinearLayout compactContent,
      @NonNull View indicatorCombat, @NonNull View indicatorMovement, @NonNull View indicatorPlayer,
      @NonNull View indicatorVisual, @NonNull View indicatorWorld, @NonNull LinearLayout topBar,
      @NonNull TextView tvCategoryName, @NonNull ViewPager2 vpCompactCategories) {
    this.rootView = rootView;
    this.btnCollapse = btnCollapse;
    this.btnConfigManager = btnConfigManager;
    this.btnExpand = btnExpand;
    this.collapsedDot = collapsedDot;
    this.compactContent = compactContent;
    this.indicatorCombat = indicatorCombat;
    this.indicatorMovement = indicatorMovement;
    this.indicatorPlayer = indicatorPlayer;
    this.indicatorVisual = indicatorVisual;
    this.indicatorWorld = indicatorWorld;
    this.topBar = topBar;
    this.tvCategoryName = tvCategoryName;
    this.vpCompactCategories = vpCompactCategories;
  }

  @Override
  @NonNull
  public CardView getRoot() {
    return rootView;
  }

  @NonNull
  public static FloatingWindowCompactV2Binding inflate(@NonNull LayoutInflater inflater) {
    return inflate(inflater, null, false);
  }

  @NonNull
  public static FloatingWindowCompactV2Binding inflate(@NonNull LayoutInflater inflater,
      @Nullable ViewGroup parent, boolean attachToParent) {
    View root = inflater.inflate(R.layout.floating_window_compact_v2, parent, false);
    if (attachToParent) {
      parent.addView(root);
    }
    return bind(root);
  }

  @NonNull
  public static FloatingWindowCompactV2Binding bind(@NonNull View rootView) {
    // The body of this method is generated in a way you would not otherwise write.
    // This is done to optimize the compiled bytecode for size and performance.
    int id;
    missingId: {
      id = R.id.btnCollapse;
      ImageView btnCollapse = ViewBindings.findChildViewById(rootView, id);
      if (btnCollapse == null) {
        break missingId;
      }

      id = R.id.btnConfigManager;
      ImageView btnConfigManager = ViewBindings.findChildViewById(rootView, id);
      if (btnConfigManager == null) {
        break missingId;
      }

      id = R.id.btnExpand;
      ImageView btnExpand = ViewBindings.findChildViewById(rootView, id);
      if (btnExpand == null) {
        break missingId;
      }

      id = R.id.collapsedDot;
      ImageView collapsedDot = ViewBindings.findChildViewById(rootView, id);
      if (collapsedDot == null) {
        break missingId;
      }

      id = R.id.compactContent;
      LinearLayout compactContent = ViewBindings.findChildViewById(rootView, id);
      if (compactContent == null) {
        break missingId;
      }

      id = R.id.indicatorCombat;
      View indicatorCombat = ViewBindings.findChildViewById(rootView, id);
      if (indicatorCombat == null) {
        break missingId;
      }

      id = R.id.indicatorMovement;
      View indicatorMovement = ViewBindings.findChildViewById(rootView, id);
      if (indicatorMovement == null) {
        break missingId;
      }

      id = R.id.indicatorPlayer;
      View indicatorPlayer = ViewBindings.findChildViewById(rootView, id);
      if (indicatorPlayer == null) {
        break missingId;
      }

      id = R.id.indicatorVisual;
      View indicatorVisual = ViewBindings.findChildViewById(rootView, id);
      if (indicatorVisual == null) {
        break missingId;
      }

      id = R.id.indicatorWorld;
      View indicatorWorld = ViewBindings.findChildViewById(rootView, id);
      if (indicatorWorld == null) {
        break missingId;
      }

      id = R.id.topBar;
      LinearLayout topBar = ViewBindings.findChildViewById(rootView, id);
      if (topBar == null) {
        break missingId;
      }

      id = R.id.tvCategoryName;
      TextView tvCategoryName = ViewBindings.findChildViewById(rootView, id);
      if (tvCategoryName == null) {
        break missingId;
      }

      id = R.id.vpCompactCategories;
      ViewPager2 vpCompactCategories = ViewBindings.findChildViewById(rootView, id);
      if (vpCompactCategories == null) {
        break missingId;
      }

      return new FloatingWindowCompactV2Binding((CardView) rootView, btnCollapse, btnConfigManager,
          btnExpand, collapsedDot, compactContent, indicatorCombat, indicatorMovement,
          indicatorPlayer, indicatorVisual, indicatorWorld, topBar, tvCategoryName,
          vpCompactCategories);
    }
    String missingId = rootView.getResources().getResourceName(id);
    throw new NullPointerException("Missing required view with ID: ".concat(missingId));
  }
}
