package mc.meson.kasumi.config

import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.PopupMenu
import androidx.recyclerview.widget.RecyclerView
import mc.meson.kasumi.R
import mc.meson.kasumi.databinding.ItemConfigurationBinding

/**
 * 配置列表适配器
 */
class ConfigurationAdapter(
    private var configurations: List<Configuration>,
    private var currentConfigId: String?,
    private val onConfigurationClick: (Configuration) -> Unit,
    private val onConfigurationApply: (Configuration) -> Unit,
    private val onConfigurationEdit: (Configuration) -> Unit,
    private val onConfigurationDelete: (Configuration) -> Unit,
    private val onConfigurationSave: (Configuration) -> Unit
) : RecyclerView.Adapter<ConfigurationAdapter.ConfigurationViewHolder>() {

    override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): ConfigurationViewHolder {
        val binding = ItemConfigurationBinding.inflate(
            LayoutInflater.from(parent.context), parent, false
        )
        return ConfigurationViewHolder(binding)
    }

    override fun onBindViewHolder(holder: ConfigurationViewHolder, position: Int) {
        holder.bind(configurations[position])
    }

    override fun getItemCount(): Int = configurations.size

    fun updateConfigurations(newConfigurations: List<Configuration>, newCurrentConfigId: String?) {
        configurations = newConfigurations
        currentConfigId = newCurrentConfigId
        notifyDataSetChanged()
    }

    inner class ConfigurationViewHolder(
        private val binding: ItemConfigurationBinding
    ) : RecyclerView.ViewHolder(binding.root) {

        fun bind(configuration: Configuration) {
            binding.apply {
                tvConfigName.text = configuration.name
                tvConfigDescription.text = configuration.description.ifEmpty { "无描述" }
                tvConfigTime.text = "创建于 ${configuration.getFormattedCreatedTime()}"

                // 显示当前配置指示器
                tvCurrentIndicator.visibility = if (configuration.id == currentConfigId) {
                    View.VISIBLE
                } else {
                    View.GONE
                }

                // 应用配置按钮
                btnApplyConfig.setOnClickListener {
                    onConfigurationApply(configuration)
                }

                // 配置菜单按钮
                btnConfigMenu.setOnClickListener { view ->
                    showConfigurationMenu(view, configuration)
                }

                // 点击整个项目
                root.setOnClickListener {
                    onConfigurationClick(configuration)
                }
            }
        }

        private fun showConfigurationMenu(view: View, configuration: Configuration) {
            val popup = PopupMenu(view.context, view)
            popup.menuInflater.inflate(R.menu.configuration_menu, popup.menu)

            // 如果是默认配置，禁用删除选项
            if (configuration.isDefault) {
                popup.menu.findItem(R.id.menu_delete_config)?.isEnabled = false
            }

            popup.setOnMenuItemClickListener { menuItem ->
                when (menuItem.itemId) {
                    R.id.menu_apply_config -> {
                        onConfigurationApply(configuration)
                        true
                    }
                    R.id.menu_edit_config -> {
                        onConfigurationEdit(configuration)
                        true
                    }
                    R.id.menu_save_current_to_config -> {
                        onConfigurationSave(configuration)
                        true
                    }
                    R.id.menu_delete_config -> {
                        if (!configuration.isDefault) {
                            onConfigurationDelete(configuration)
                        }
                        true
                    }
                    else -> false
                }
            }

            popup.show()
        }
    }
}

/**
 * 简单的配置选择适配器
 */
class ConfigurationSelectAdapter(
    private var configurations: List<Configuration>,
    private var currentConfigId: String?,
    private val onConfigurationSelect: (Configuration) -> Unit
) : RecyclerView.Adapter<ConfigurationSelectAdapter.SelectViewHolder>() {

    override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): SelectViewHolder {
        val binding = ItemConfigurationBinding.inflate(
            LayoutInflater.from(parent.context), parent, false
        )
        return SelectViewHolder(binding)
    }

    override fun onBindViewHolder(holder: SelectViewHolder, position: Int) {
        holder.bind(configurations[position])
    }

    override fun getItemCount(): Int = configurations.size

    fun updateConfigurations(newConfigurations: List<Configuration>, newCurrentConfigId: String?) {
        configurations = newConfigurations
        currentConfigId = newCurrentConfigId
        notifyDataSetChanged()
    }

    inner class SelectViewHolder(
        private val binding: ItemConfigurationBinding
    ) : RecyclerView.ViewHolder(binding.root) {

        fun bind(configuration: Configuration) {
            binding.apply {
                tvConfigName.text = configuration.name
                tvConfigDescription.text = configuration.description.ifEmpty { "无描述" }
                tvConfigTime.text = "创建于 ${configuration.getFormattedCreatedTime()}"

                // 显示当前配置指示器
                tvCurrentIndicator.visibility = if (configuration.id == currentConfigId) {
                    View.VISIBLE
                } else {
                    View.GONE
                }

                // 隐藏操作按钮，只显示应用按钮
                btnConfigMenu.visibility = View.GONE
                btnApplyConfig.text = "选择"
                btnApplyConfig.setOnClickListener {
                    onConfigurationSelect(configuration)
                }

                // 点击整个项目也可以选择
                root.setOnClickListener {
                    onConfigurationSelect(configuration)
                }
            }
        }
    }
}
