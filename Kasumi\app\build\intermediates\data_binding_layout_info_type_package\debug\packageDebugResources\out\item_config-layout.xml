<?xml version="1.0" encoding="utf-8" standalone="yes"?><Layout layout="item_config" modulePackage="mc.meson.kasumi" filePath="app\src\main\res\layout\item_config.xml" directory="layout" isMerge="false" isBindingData="false" rootNodeType="com.google.android.material.card.MaterialCardView" rootNodeViewId="@+id/cardConfig"><Targets><Target id="@+id/cardConfig" tag="layout/item_config_0" view="com.google.android.material.card.MaterialCardView"><Expressions/><location startLine="1" startOffset="0" endLine="120" endOffset="51"/></Target><Target id="@+id/tvConfigName" view="TextView"><Expressions/><location startLine="27" startOffset="12" endLine="36" endOffset="41"/></Target><Target id="@+id/ivCurrentIndicator" view="ImageView"><Expressions/><location startLine="39" startOffset="12" endLine="43" endOffset="43"/></Target><Target id="@+id/tvConfigDescription" view="TextView"><Expressions/><location startLine="45" startOffset="12" endLine="54" endOffset="41"/></Target><Target id="@+id/tvConfigStats" view="TextView"><Expressions/><location startLine="57" startOffset="12" endLine="65" endOffset="48"/></Target><Target id="@+id/tvConfigTime" view="TextView"><Expressions/><location startLine="68" startOffset="12" endLine="75" endOffset="48"/></Target><Target id="@+id/btnEditConfig" view="ImageButton"><Expressions/><location startLine="87" startOffset="12" endLine="94" endOffset="56"/></Target><Target id="@+id/btnExportConfig" view="ImageButton"><Expressions/><location startLine="96" startOffset="12" endLine="104" endOffset="48"/></Target><Target id="@+id/btnDeleteConfig" view="ImageButton"><Expressions/><location startLine="106" startOffset="12" endLine="114" endOffset="48"/></Target></Targets></Layout>