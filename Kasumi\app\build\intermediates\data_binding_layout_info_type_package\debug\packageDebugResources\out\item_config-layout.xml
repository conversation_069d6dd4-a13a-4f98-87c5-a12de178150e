<?xml version="1.0" encoding="utf-8" standalone="yes"?><Layout layout="item_config" modulePackage="mc.meson.kasumi" filePath="app\src\main\res\layout\item_config.xml" directory="layout" isMerge="false" isBindingData="false" rootNodeType="com.google.android.material.card.MaterialCardView" rootNodeViewId="@+id/cardConfig"><Targets><Target id="@+id/cardConfig" tag="layout/item_config_0" view="com.google.android.material.card.MaterialCardView"><Expressions/><location startLine="1" startOffset="0" endLine="132" endOffset="51"/></Target><Target id="@+id/tvConfigName" view="TextView"><Expressions/><location startLine="32" startOffset="16" endLine="40" endOffset="46"/></Target><Target id="@+id/ivCurrentIndicator" view="ImageView"><Expressions/><location startLine="42" startOffset="16" endLine="48" endOffset="51"/></Target><Target id="@+id/tvConfigDescription" view="TextView"><Expressions/><location startLine="52" startOffset="12" endLine="61" endOffset="41"/></Target><Target id="@+id/tvConfigStats" view="TextView"><Expressions/><location startLine="69" startOffset="16" endLine="77" endOffset="46"/></Target><Target id="@+id/tvConfigTime" view="TextView"><Expressions/><location startLine="79" startOffset="16" endLine="85" endOffset="69"/></Target><Target id="@+id/btnEditConfig" view="ImageButton"><Expressions/><location startLine="98" startOffset="12" endLine="105" endOffset="56"/></Target><Target id="@+id/btnExportConfig" view="ImageButton"><Expressions/><location startLine="107" startOffset="12" endLine="116" endOffset="40"/></Target><Target id="@+id/btnDeleteConfig" view="ImageButton"><Expressions/><location startLine="118" startOffset="12" endLine="126" endOffset="50"/></Target></Targets></Layout>