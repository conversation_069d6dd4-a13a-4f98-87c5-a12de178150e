<?xml version="1.0" encoding="utf-8"?>
<androidx.coordinatorlayout.widget.CoordinatorLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:fitsSystemWindows="true"
    tools:context=".MainActivity">

    <!-- App Bar Layout with Material 3 styling -->
    <com.google.android.material.appbar.AppBarLayout
        android:id="@+id/appBarLayout"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:fitsSystemWindows="true"
        app:liftOnScroll="true">

        <com.google.android.material.appbar.MaterialToolbar
            android:id="@+id/toolbar"
            android:layout_width="match_parent"
            android:layout_height="?attr/actionBarSize"
            app:title="Kasumi 提拉米苏"
            app:titleTextColor="?attr/colorOnSurface"
            app:layout_scrollFlags="scroll|enterAlways|snap" />

    </com.google.android.material.appbar.AppBarLayout>

    <!-- Main Content -->
    <androidx.core.widget.NestedScrollView
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        app:layout_behavior="@string/appbar_scrolling_view_behavior">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="vertical"
            android:padding="16dp">

            <!-- Welcome Card -->
            <com.google.android.material.card.MaterialCardView
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginBottom="16dp"
                app:cardElevation="0dp"
                app:strokeWidth="1dp"
                app:strokeColor="?attr/colorOutline"
                app:cardBackgroundColor="@color/card_background_primary"
                style="?attr/materialCardViewFilledStyle">

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="vertical"
                    android:padding="20dp">

                    <TextView
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:text="欢迎使用 Kasumi"
                        android:textAppearance="?attr/textAppearanceHeadlineSmall"
                        android:textColor="?attr/colorOnSurface"
                        android:layout_marginBottom="8dp" />

                    <TextView
                        android:id="@+id/welcomeText"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:text="强大的Minecraft辅助工具，让你的游戏体验更上一层楼"
                        android:textAppearance="?attr/textAppearanceBodyMedium"
                        android:textColor="?attr/colorOnSurfaceVariant" />

                </LinearLayout>

            </com.google.android.material.card.MaterialCardView>

            <!-- Status Card -->
            <com.google.android.material.card.MaterialCardView
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginBottom="16dp"
                app:cardElevation="0dp"
                app:strokeWidth="1dp"
                app:strokeColor="?attr/colorOutline"
                app:cardBackgroundColor="@color/card_background_secondary"
                style="?attr/materialCardViewFilledStyle">

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="vertical"
                    android:padding="20dp">

                    <TextView
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:text="连接状态"
                        android:textAppearance="?attr/textAppearanceTitleMedium"
                        android:textColor="?attr/colorOnSurface"
                        android:layout_marginBottom="12dp" />

                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:orientation="horizontal"
                        android:gravity="center_vertical">

                        <View
                            android:id="@+id/statusIndicator"
                            android:layout_width="12dp"
                            android:layout_height="12dp"
                            android:background="@drawable/status_indicator"
                            android:layout_marginEnd="12dp" />

                        <TextView
                            android:id="@+id/statusText"
                            android:layout_width="0dp"
                            android:layout_height="wrap_content"
                            android:layout_weight="1"
                            android:text="正在连接..."
                            android:textAppearance="?attr/textAppearanceBodyMedium"
                            android:textColor="?attr/colorOnSurfaceVariant" />

                    </LinearLayout>

                </LinearLayout>

            </com.google.android.material.card.MaterialCardView>

            <!-- 悬浮窗功能卡片 -->
            <com.google.android.material.card.MaterialCardView
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginBottom="16dp"
                app:cardElevation="0dp"
                app:strokeWidth="1dp"
                app:strokeColor="?attr/colorOutline"
                app:cardBackgroundColor="@color/card_background_tertiary"
                style="?attr/materialCardViewFilledStyle">

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="vertical"
                    android:padding="20dp">

                    <TextView
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:text="游戏内悬浮窗"
                        android:textAppearance="?attr/textAppearanceTitleMedium"
                        android:textColor="?attr/colorOnSurface"
                        android:layout_marginBottom="8dp" />

                    <TextView
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:text="在游戏中显示悬浮窗，快速切换hack功能"
                        android:textAppearance="?attr/textAppearanceBodyMedium"
                        android:textColor="?attr/colorOnSurfaceVariant"
                        android:layout_marginBottom="16dp" />

                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:orientation="horizontal"
                        android:gravity="center">

                        <com.google.android.material.button.MaterialButton
                            android:id="@+id/btnFloatingWindow"
                            android:layout_width="0dp"
                            android:layout_height="wrap_content"
                            android:layout_weight="1"
                            android:layout_marginEnd="8dp"
                            android:text="启动悬浮窗"
                            app:icon="@drawable/ic_kasumi_logo"
                            app:backgroundTint="?attr/colorPrimary"
                            app:iconTint="?attr/colorOnPrimary"
                            android:textColor="?attr/colorOnPrimary" />

                        <com.google.android.material.button.MaterialButton
                            android:id="@+id/btnLandscape"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:text="横屏"
                            android:textSize="14sp"
                            style="@style/App.Button.Secondary" />

                    </LinearLayout>

                </LinearLayout>

            </com.google.android.material.card.MaterialCardView>

        </LinearLayout>

    </androidx.core.widget.NestedScrollView>

    <!-- Floating Action Button -->
    <com.google.android.material.floatingactionbutton.ExtendedFloatingActionButton
        android:id="@+id/fabSettings"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_gravity="bottom|end"
        android:layout_margin="16dp"
        android:text="设置"
        app:icon="@drawable/ic_settings_24"
        app:backgroundTint="?attr/colorPrimaryContainer"
        app:iconTint="?attr/colorOnPrimaryContainer"
        app:rippleColor="?attr/colorPrimary" />

</androidx.coordinatorlayout.widget.CoordinatorLayout>