<?xml version="1.0" encoding="utf-8"?>
<androidx.cardview.widget.CardView xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="360dp"
    android:layout_height="280dp"
    android:alpha="0.95"
    app:cardCornerRadius="20dp"
    app:cardElevation="16dp"
    app:cardBackgroundColor="@color/md_theme_light_primaryContainer">    <FrameLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent">

        <!-- 主内容区域 - 移除padding，改为分别给子元素设置margin -->
        <LinearLayout
            android:id="@+id/mainContent"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:orientation="vertical">

            <!-- 顶部标题栏 -->
            <LinearLayout
                android:id="@+id/titleBar"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="horizontal"
                android:gravity="center_vertical"
                android:layout_margin="16dp"
                android:layout_marginBottom="12dp">

                <ImageView
                    android:layout_width="20dp"
                    android:layout_height="20dp"
                    android:src="@drawable/ic_kasumi_logo"
                    android:tint="@color/md_theme_light_onPrimaryContainer"
                    android:layout_marginEnd="6dp" />

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="Kasumi"
                    android:textSize="16sp"
                    android:textStyle="bold"
                    android:textColor="@color/md_theme_light_onPrimaryContainer"
                    android:layout_marginEnd="12dp" />

                <!-- 分类标签栏 -->
                <com.google.android.material.tabs.TabLayout
                    android:id="@+id/tabCategories"
                    android:layout_width="0dp"
                    android:layout_height="32dp"
                    android:layout_weight="1"
                    android:layout_marginEnd="12dp"
                    android:background="@drawable/tab_layout_background"
                    android:elevation="2dp"
                    app:tabMode="fixed"
                    app:tabGravity="fill"
                    app:tabIndicatorHeight="0dp"
                    app:tabBackground="@drawable/tab_background_selector"
                    app:tabSelectedTextColor="@color/md_theme_light_onPrimary"
                    app:tabTextColor="@color/md_theme_light_onSurfaceVariant"
                    app:tabRippleColor="@color/md_theme_light_primaryContainer"
                    app:tabTextAppearance="@style/TextAppearance.Material3.LabelMedium"
                    app:tabPaddingTop="2dp"
                    app:tabPaddingBottom="2dp" />

                <ImageView
                    android:id="@+id/btnCollapseExpanded"
                    android:layout_width="20dp"
                    android:layout_height="20dp"
                    android:src="@drawable/ic_compress"
                    android:tint="@color/md_theme_light_onPrimaryContainer"
                    android:background="?attr/selectableItemBackgroundBorderless"
                    android:clickable="true"
                    android:focusable="true" />

            </LinearLayout>

            <!-- 功能模块网格容器 -->
            <FrameLayout
                android:id="@+id/moduleContainer"
                android:layout_width="match_parent"
                android:layout_height="0dp"
                android:layout_weight="1"
                android:layout_marginStart="16dp"
                android:layout_marginEnd="16dp"
                android:layout_marginBottom="16dp">

                <!-- 功能模块网格 -->
                <androidx.recyclerview.widget.RecyclerView
                    android:id="@+id/rvExpandedModules"
                    android:layout_width="match_parent"
                    android:layout_height="match_parent"
                    android:nestedScrollingEnabled="true" />

            </FrameLayout>

        </LinearLayout>

        <!-- 设置面板 - 提高层级，确保在最上方 -->
        <include
            android:id="@+id/settingsPanel"
            layout="@layout/module_settings_panel"
            android:layout_width="212dp"
            android:layout_height="match_parent"
            android:layout_gravity="start"
            android:layout_marginTop="60dp"
            android:visibility="gone"
            android:translationX="-212dp"
            android:elevation="8dp" />

    </FrameLayout>

</androidx.cardview.widget.CardView>