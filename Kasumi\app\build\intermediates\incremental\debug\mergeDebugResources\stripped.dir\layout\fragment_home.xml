<?xml version="1.0" encoding="utf-8"?>
<androidx.core.widget.NestedScrollView xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:fillViewport="true">

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="vertical"
        android:padding="16dp">

        <!-- Welcome Card -->
        <com.google.android.material.card.MaterialCardView
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginBottom="16dp"
            app:cardElevation="0dp"
            app:strokeWidth="1dp"
            app:strokeColor="?attr/colorOutline"
            app:cardBackgroundColor="@color/card_background_primary"
            style="@style/Widget.Material3.CardView.Outlined">

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="vertical"
                android:padding="20dp">

                <TextView
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:text="@string/welcome_title"
                    android:textAppearance="?attr/textAppearanceHeadlineSmall"
                    android:textColor="?attr/colorOnSurface"
                    android:layout_marginBottom="8dp" />

                <TextView
                    android:id="@+id/welcomeText"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:text="@string/welcome_description"
                    android:textAppearance="?attr/textAppearanceBodyMedium"
                    android:textColor="?attr/colorOnSurfaceVariant" />

            </LinearLayout>

        </com.google.android.material.card.MaterialCardView>

        <!-- Status Card -->
        <com.google.android.material.card.MaterialCardView
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginBottom="16dp"
            app:cardElevation="0dp"
            app:strokeWidth="1dp"
            app:strokeColor="?attr/colorOutline"
            app:cardBackgroundColor="@color/card_background_secondary"
            style="@style/Widget.Material3.CardView.Outlined">

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="vertical"
                android:padding="20dp">

                <TextView
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:text="@string/connection_status"
                    android:textAppearance="?attr/textAppearanceTitleMedium"
                    android:textColor="?attr/colorOnSurface"
                    android:layout_marginBottom="12dp" />

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="horizontal"
                    android:gravity="center_vertical">

                    <View
                        android:id="@+id/statusIndicator"
                        android:layout_width="12dp"
                        android:layout_height="12dp"
                        android:background="@drawable/status_indicator"
                        android:layout_marginEnd="12dp" />

                    <TextView
                        android:id="@+id/statusText"
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:layout_weight="1"
                        android:text="@string/connecting"
                        android:textAppearance="?attr/textAppearanceBodyMedium"
                        android:textColor="?attr/colorOnSurfaceVariant" />

                </LinearLayout>

            </LinearLayout>

        </com.google.android.material.card.MaterialCardView>

        <!-- 悬浮窗功能卡片 -->
        <com.google.android.material.card.MaterialCardView
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginBottom="16dp"
            app:cardElevation="0dp"
            app:strokeWidth="1dp"
            app:strokeColor="?attr/colorOutline"
            app:cardBackgroundColor="@color/card_background_tertiary"
            style="@style/Widget.Material3.CardView.Outlined">

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="vertical"
                android:padding="20dp">

                <TextView
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:text="游戏内悬浮窗"
                    android:textAppearance="?attr/textAppearanceTitleMedium"
                    android:textColor="?attr/colorOnSurface"
                    android:layout_marginBottom="8dp" />

                <TextView
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:text="在游戏中显示悬浮窗，快速切换hack功能"
                    android:textAppearance="?attr/textAppearanceBodyMedium"
                    android:textColor="?attr/colorOnSurfaceVariant"
                    android:layout_marginBottom="16dp" />

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="horizontal"
                    android:gravity="center">

                    <com.google.android.material.button.MaterialButton
                        android:id="@+id/btnFloatingWindow"
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:layout_weight="1"
                        android:layout_marginEnd="8dp"
                        android:text="启动悬浮窗"
                        app:icon="@drawable/ic_kasumi_logo"
                        app:backgroundTint="?attr/colorPrimary"
                        app:iconTint="?attr/colorOnPrimary"
                        android:textColor="?attr/colorOnPrimary" />

                    <com.google.android.material.button.MaterialButton
                        android:id="@+id/btnLandscape"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="横屏"
                        android:textSize="14sp"
                        style="@style/App.Button.Secondary" />

                </LinearLayout>

            </LinearLayout>

        </com.google.android.material.card.MaterialCardView>

    </LinearLayout>

</androidx.core.widget.NestedScrollView>
