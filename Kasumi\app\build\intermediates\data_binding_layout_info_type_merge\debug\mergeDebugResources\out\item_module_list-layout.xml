<?xml version="1.0" encoding="utf-8" standalone="yes"?><Layout layout="item_module_list" modulePackage="mc.meson.kasumi" filePath="app\src\main\res\layout\item_module_list.xml" directory="layout" isMerge="false" isBindingData="false" rootNodeType="androidx.cardview.widget.CardView" rootNodeViewId="@+id/cardModule"><Targets><Target id="@+id/cardModule" tag="layout/item_module_list_0" view="androidx.cardview.widget.CardView"><Expressions/><location startLine="1" startOffset="0" endLine="68" endOffset="35"/></Target><Target id="@+id/ivModuleIcon" view="ImageView"><Expressions/><location startLine="19" startOffset="8" endLine="25" endOffset="60"/></Target><Target id="@+id/tvModuleName" view="TextView"><Expressions/><location startLine="35" startOffset="12" endLine="44" endOffset="41"/></Target><Target id="@+id/tvModuleDescription" view="TextView"><Expressions/><location startLine="47" startOffset="12" endLine="55" endOffset="41"/></Target><Target id="@+id/switchModule" view="Switch"><Expressions/><location startLine="60" startOffset="8" endLine="64" endOffset="46"/></Target></Targets></Layout>