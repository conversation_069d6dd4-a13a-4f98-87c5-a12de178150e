<?xml version="1.0" encoding="utf-8"?>
<navigation xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:id="@+id/nav_graph"
    app:startDestination="@id/nav_home">

    <fragment
        android:id="@+id/nav_home"
        android:name="mc.meson.kasumi.HomeFragment"
        android:label="主页"
        tools:layout="@layout/fragment_home">
        <action
            android:id="@+id/action_home_to_features"
            app:destination="@id/nav_features"
            app:enterAnim="@anim/fragment_fade_enter"
            app:exitAnim="@anim/fragment_fade_exit" />
        <action
            android:id="@+id/action_home_to_settings"
            app:destination="@id/nav_settings"
            app:enterAnim="@anim/fragment_fade_enter"
            app:exitAnim="@anim/fragment_fade_exit" />
    </fragment>

    <fragment
        android:id="@+id/nav_features"
        android:name="mc.meson.kasumi.HackFeaturesFragment"
        android:label="功能"
        tools:layout="@layout/fragment_hack_features">
        <action
            android:id="@+id/action_features_to_home"
            app:destination="@id/nav_home"
            app:enterAnim="@anim/fragment_fade_enter"
            app:exitAnim="@anim/fragment_fade_exit" />
        <action
            android:id="@+id/action_features_to_settings"
            app:destination="@id/nav_settings"
            app:enterAnim="@anim/fragment_fade_enter"
            app:exitAnim="@anim/fragment_fade_exit" />
        <action
            android:id="@+id/action_features_to_config_management"
            app:destination="@id/nav_config_management"
            app:enterAnim="@anim/slide_in_right"
            app:exitAnim="@anim/slide_out_left" />
        <action
            android:id="@+id/action_features_to_module_settings"
            app:destination="@id/nav_module_settings"
            app:enterAnim="@anim/slide_in_right"
            app:exitAnim="@anim/slide_out_left" />
    </fragment>

    <fragment
        android:id="@+id/nav_settings"
        android:name="mc.meson.kasumi.SettingsFragment"
        android:label="设置"
        tools:layout="@layout/fragment_settings">
        <action
            android:id="@+id/action_settings_to_home"
            app:destination="@id/nav_home"
            app:enterAnim="@anim/fragment_fade_enter"
            app:exitAnim="@anim/fragment_fade_exit" />
        <action
            android:id="@+id/action_settings_to_features"
            app:destination="@id/nav_features"
            app:enterAnim="@anim/fragment_fade_enter"
            app:exitAnim="@anim/fragment_fade_exit" />
    </fragment>

    <fragment
        android:id="@+id/nav_config_management"
        android:name="mc.meson.kasumi.config.ConfigManagementFragment"
        android:label="配置管理"
        tools:layout="@layout/fragment_config_management">
        <action
            android:id="@+id/action_config_management_back"
            app:destination="@id/nav_features"
            app:enterAnim="@anim/slide_out_left"
            app:exitAnim="@anim/slide_in_right"
            app:popUpTo="@id/nav_features" />
    </fragment>

    <fragment
        android:id="@+id/nav_module_settings"
        android:name="mc.meson.kasumi.config.ModuleSettingsFragment"
        android:label="模块设置"
        tools:layout="@layout/fragment_module_settings">
        <argument
            android:name="module_id"
            app:argType="string" />
        <action
            android:id="@+id/action_module_settings_back"
            app:destination="@id/nav_features"
            app:enterAnim="@anim/slide_out_left"
            app:exitAnim="@anim/slide_in_right"
            app:popUpTo="@id/nav_features" />
    </fragment>

</navigation>
