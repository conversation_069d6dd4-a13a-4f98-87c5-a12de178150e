<?xml version="1.0" encoding="utf-8" standalone="yes"?><Layout layout="item_expanded_module" modulePackage="mc.meson.kasumi" filePath="app\src\main\res\layout\item_expanded_module.xml" directory="layout" isMerge="false" isBindingData="false" rootNodeType="androidx.cardview.widget.CardView"><Targets><Target tag="layout/item_expanded_module_0" view="androidx.cardview.widget.CardView"><Expressions/><location startLine="1" startOffset="0" endLine="74" endOffset="35"/></Target><Target id="@+id/moduleContainer" view="LinearLayout"><Expressions/><location startLine="13" startOffset="4" endLine="72" endOffset="18"/></Target><Target id="@+id/ivModuleIcon" view="ImageView"><Expressions/><location startLine="28" startOffset="12" endLine="34" endOffset="48"/></Target><Target id="@+id/switchModule" view="Switch"><Expressions/><location startLine="41" startOffset="12" endLine="46" endOffset="38"/></Target><Target id="@+id/tvModuleName" view="TextView"><Expressions/><location startLine="51" startOffset="8" endLine="59" endOffset="47"/></Target><Target id="@+id/tvModuleDescription" view="TextView"><Expressions/><location startLine="62" startOffset="8" endLine="70" endOffset="37"/></Target></Targets></Layout>