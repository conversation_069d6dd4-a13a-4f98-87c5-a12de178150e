<?xml version="1.0" encoding="utf-8"?>
<androidx.cardview.widget.CardView xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:layout_margin="4dp"
    android:foreground="?attr/selectableItemBackground"
    android:clickable="true"
    android:focusable="true"
    app:cardCornerRadius="12dp"
    app:cardElevation="2dp"
    app:cardBackgroundColor="@color/card_background_secondary">

    <LinearLayout
        android:id="@+id/moduleContainer"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="vertical"
        android:padding="12dp">

        <!-- 顶部：图标和开关 -->
        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="horizontal"
            android:gravity="center_vertical"
            android:layout_marginBottom="8dp">

            <ImageView
                android:id="@+id/ivModuleIcon"
                android:layout_width="24dp"
                android:layout_height="24dp"
                android:src="@android:drawable/ic_menu_preferences"
                android:tint="@color/md_theme_light_onSurface"
                android:layout_marginEnd="8dp" />

            <View
                android:layout_width="0dp"
                android:layout_height="1dp"
                android:layout_weight="1" />

            <Switch
                android:id="@+id/switchModule"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:scaleX="0.8"
                android:scaleY="0.8" />

        </LinearLayout>

        <!-- 模块名称 -->
        <TextView
            android:id="@+id/tvModuleName"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:text="模块名称"
            android:textSize="14sp"
            android:textStyle="bold"
            android:textColor="@color/md_theme_light_onSurface"
            android:layout_marginBottom="4dp" />

        <!-- 模块描述 -->
        <TextView
            android:id="@+id/tvModuleDescription"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:text="模块描述"
            android:textSize="11sp"
            android:textColor="@color/md_theme_light_onSurfaceVariant"
            android:maxLines="2"
            android:ellipsize="end" />

    </LinearLayout>

</androidx.cardview.widget.CardView>