<?xml version="1.0" encoding="utf-8"?>
<androidx.coordinatorlayout.widget.CoordinatorLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="?attr/colorSurface">

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:orientation="vertical">

        <!-- 顶部工具栏 -->
        <com.google.android.material.card.MaterialCardView
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            app:cardElevation="4dp"
            app:cardCornerRadius="0dp"
            app:cardBackgroundColor="?attr/colorPrimary">

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="horizontal"
                android:gravity="center_vertical"
                android:padding="16dp">

                <ImageButton
                    android:id="@+id/btnBack"
                    android:layout_width="48dp"
                    android:layout_height="48dp"
                    android:src="@drawable/ic_arrow_back_24"
                    android:background="?attr/selectableItemBackgroundBorderless"
                    android:contentDescription="返回"
                    app:tint="?attr/colorOnPrimary" />

                <TextView
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_weight="1"
                    android:text="配置管理"
                    android:textAppearance="?attr/textAppearanceTitleLarge"
                    android:textColor="?attr/colorOnPrimary"
                    android:textStyle="bold"
                    android:layout_marginStart="12dp" />

                <ImageButton
                    android:id="@+id/btnImportConfig"
                    android:layout_width="48dp"
                    android:layout_height="48dp"
                    android:src="@drawable/ic_expand_more"
                    android:background="?attr/selectableItemBackgroundBorderless"
                    android:contentDescription="导入配置"
                    app:tint="?attr/colorOnPrimary" />

                <ImageButton
                    android:id="@+id/btnExportAll"
                    android:layout_width="48dp"
                    android:layout_height="48dp"
                    android:src="@drawable/ic_compress"
                    android:background="?attr/selectableItemBackgroundBorderless"
                    android:contentDescription="导出所有配置"
                    app:tint="?attr/colorOnPrimary" />

            </LinearLayout>

        </com.google.android.material.card.MaterialCardView>

        <!-- 当前配置信息 -->
        <com.google.android.material.card.MaterialCardView
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_margin="16dp"
            app:cardElevation="2dp"
            app:cardCornerRadius="12dp"
            app:cardBackgroundColor="?attr/colorPrimaryContainer">

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="vertical"
                android:padding="16dp">

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="当前配置"
                    android:textAppearance="?attr/textAppearanceLabelMedium"
                    android:textColor="?attr/colorOnPrimaryContainer"
                    android:textStyle="bold" />

                <TextView
                    android:id="@+id/tvCurrentConfigName"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="默认配置"
                    android:textAppearance="?attr/textAppearanceTitleMedium"
                    android:textColor="?attr/colorOnPrimaryContainer"
                    android:layout_marginTop="4dp" />

                <TextView
                    android:id="@+id/tvCurrentConfigDescription"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="系统默认配置"
                    android:textAppearance="?attr/textAppearanceBodyMedium"
                    android:textColor="?attr/colorOnPrimaryContainer"
                    android:layout_marginTop="2dp" />

                <TextView
                    android:id="@+id/tvCurrentConfigStats"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="0/20 功能已启用"
                    android:textAppearance="?attr/textAppearanceBodySmall"
                    android:textColor="?attr/colorPrimary"
                    android:layout_marginTop="8dp" />

            </LinearLayout>

        </com.google.android.material.card.MaterialCardView>

        <!-- 配置列表 -->
        <androidx.core.widget.NestedScrollView
            android:layout_width="match_parent"
            android:layout_height="0dp"
            android:layout_weight="1"
            android:fillViewport="true">

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="vertical">

                <!-- 配置列表标题 -->
                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="所有配置"
                    android:textAppearance="?attr/textAppearanceTitleMedium"
                    android:textColor="?attr/colorOnSurface"
                    android:layout_marginHorizontal="16dp"
                    android:layout_marginTop="8dp"
                    android:layout_marginBottom="12dp" />

                <!-- 配置列表 -->
                <androidx.recyclerview.widget.RecyclerView
                    android:id="@+id/recyclerViewConfigs"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:nestedScrollingEnabled="false"
                    android:paddingHorizontal="16dp"
                    android:clipToPadding="false" />

                <!-- 空状态 -->
                <LinearLayout
                    android:id="@+id/layoutEmptyState"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="vertical"
                    android:gravity="center"
                    android:padding="32dp"
                    android:visibility="gone">

                    <ImageView
                        android:layout_width="64dp"
                        android:layout_height="64dp"
                        android:src="@drawable/ic_settings_24"
                        android:alpha="0.6"
                        app:tint="?attr/colorOnSurfaceVariant" />

                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="暂无配置"
                        android:textAppearance="?attr/textAppearanceTitleMedium"
                        android:textColor="?attr/colorOnSurfaceVariant"
                        android:layout_marginTop="16dp" />

                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="点击右下角的按钮创建您的第一个配置"
                        android:textAppearance="?attr/textAppearanceBodyMedium"
                        android:textColor="?attr/colorOnSurfaceVariant"
                        android:layout_marginTop="8dp"
                        android:gravity="center" />

                </LinearLayout>

            </LinearLayout>

        </androidx.core.widget.NestedScrollView>

    </LinearLayout>

    <!-- 创建配置按钮 -->
    <com.google.android.material.floatingactionbutton.FloatingActionButton
        android:id="@+id/fabCreateConfig"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_gravity="bottom|end"
        android:layout_margin="16dp"
        android:src="@drawable/ic_kasumi_logo"
        android:contentDescription="创建新配置"
        app:tint="?attr/colorOnPrimary" />

</androidx.coordinatorlayout.widget.CoordinatorLayout>
