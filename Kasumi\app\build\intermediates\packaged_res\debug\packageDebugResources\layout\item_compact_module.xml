<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:orientation="horizontal"
    android:gravity="center_vertical"
    android:padding="8dp"
    android:background="?attr/selectableItemBackground">

    <TextView
        android:id="@+id/tvModuleName"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_weight="1"
        android:text="模块名称"
        android:textSize="12sp"
        android:textColor="@color/md_theme_light_onPrimaryContainer"
        android:layout_marginEnd="8dp" />

    <Switch
        android:id="@+id/switchModule"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:scaleX="0.8"
        android:scaleY="0.8" />

</LinearLayout>