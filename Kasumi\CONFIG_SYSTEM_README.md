# Kasumi 配置系统

## 概述

Kasumi 配置系统是一个完整的功能配置管理解决方案，支持创建、保存、切换、导入和导出配置。该系统解决了之前版本中功能状态不能正确保存和同步的问题。

## 主要功能

### 1. 配置管理
- **创建配置**: 基于当前功能状态创建新配置
- **删除配置**: 删除不需要的配置（至少保留一个配置）
- **切换配置**: 在不同配置间快速切换
- **编辑配置**: 修改配置名称和描述

### 2. 导入导出
- **单个配置导出**: 导出指定配置为JSON文件
- **批量配置导出**: 导出所有配置为JSON文件
- **配置导入**: 从JSON文件导入配置，自动处理名称冲突

### 3. 功能设置
- **点击功能卡片**: 直接打开功能的详细设置页面
- **实时保存**: 功能状态和设置自动保存到当前配置
- **状态同步**: 页面切换和应用重启后状态正确恢复

## 使用方法

### 访问配置管理
1. 打开应用，进入"功能"页面
2. 点击顶部的"配置管理"卡片
3. 进入配置管理界面

### 创建新配置
1. 在功能页面调整好所需的功能状态
2. 进入配置管理界面
3. 点击右下角的"+"按钮
4. 输入配置名称和描述
5. 点击"创建"

### 切换配置
1. 在配置管理界面中
2. 点击要切换的配置卡片
3. 系统会自动应用该配置的所有设置

### 功能设置
1. 在功能页面点击任意功能卡片
2. 进入该功能的详细设置页面
3. 调整各项设置参数
4. 设置会自动保存到当前配置

### 导出配置
1. 在配置管理界面
2. 点击配置卡片右侧的导出按钮
3. 选择保存位置
4. 配置将以JSON格式保存

### 导入配置
1. 在配置管理界面
2. 点击顶部的导入按钮
3. 选择JSON配置文件
4. 系统会自动导入并处理冲突

## 技术特性

### 数据持久化
- 使用SharedPreferences存储配置数据
- 使用Gson进行JSON序列化
- 支持配置的完整备份和恢复

### 状态同步
- 模块状态变更时自动保存到当前配置
- 设置值变更时自动保存到当前配置
- 应用启动时自动加载当前配置

### 错误处理
- 导入时自动处理ID和名称冲突
- 配置文件格式验证
- 异常情况的优雅降级

## 默认配置

系统提供三个默认配置：

1. **默认配置**: 系统基础配置
2. **PVP配置**: 专为PVP战斗优化，启用战斗相关功能
3. **生存配置**: 适合生存模式，启用挖掘和视觉辅助功能

## 文件格式

配置文件采用JSON格式，包含以下字段：

```json
{
  "version": 1,
  "exportTime": 1640995200000,
  "config": {
    "id": "uuid",
    "name": "配置名称",
    "description": "配置描述",
    "createdTime": 1640995200000,
    "moduleStates": {
      "module_id": true
    },
    "moduleSettings": {
      "module_id": {
        "setting_key": "setting_value"
      }
    }
  }
}
```

## 故障排除

### 配置不生效
1. 确认已正确切换到目标配置
2. 检查功能页面的功能状态是否正确
3. 重启应用重新加载配置

### 导入失败
1. 检查JSON文件格式是否正确
2. 确认文件没有损坏
3. 查看错误提示信息

### 功能状态不同步
1. 确认已启用自动保存功能
2. 检查是否有权限问题
3. 清除应用数据重新设置

## 开发说明

### 核心类
- `ConfigManager`: 配置管理核心类
- `ConfigData`: 配置数据模型
- `ConfigImportExport`: 导入导出功能
- `ModuleSettingsManager`: 模块设置管理

### 扩展配置系统
1. 在`ConfigData`中添加新字段
2. 更新导入导出逻辑
3. 修改UI界面显示新字段

### 添加新的设置类型
1. 在`ModuleSetting.kt`中定义新的设置类型
2. 在`ModuleSettingsManager`中添加对应的getter/setter
3. 在设置适配器中添加UI支持
