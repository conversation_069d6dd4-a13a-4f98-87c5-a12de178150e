package mc.meson.kasumi

import android.os.Bundle
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import androidx.fragment.app.Fragment
import androidx.recyclerview.widget.GridLayoutManager
import com.google.android.material.snackbar.Snackbar
import mc.meson.kasumi.databinding.FragmentHackFeaturesBinding
import mc.meson.kasumi.module.KasumiModule
import mc.meson.kasumi.module.ModuleAdapter
import mc.meson.kasumi.module.ModuleRegistry
import mc.meson.kasumi.module.ModuleStateListener

class HackFeaturesFragment : Fragment(), ModuleStateListener {

    private var _binding: FragmentHackFeaturesBinding? = null
    private val binding get() = _binding!!
    private lateinit var moduleAdapter: ModuleAdapter

    override fun onCreateView(
        inflater: LayoutInflater,
        container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View {
        _binding = FragmentHackFeaturesBinding.inflate(inflater, container, false)
        return binding.root
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)

        // 只在第一次初始化模块，避免重复初始化导致闪烁
        if (!isModuleInitialized) {
            KasumiModule.init(requireContext())
            isModuleInitialized = true
        }
        KasumiModule.addListener(this)
        setupRecyclerView()
        setupAnimations()
    }

    companion object {
        private var isModuleInitialized = false
    }

    private fun setupRecyclerView() {
        // 只在第一次创建时设置适配器，避免重复创建导致闪烁
        if (!::moduleAdapter.isInitialized) {
            moduleAdapter = ModuleAdapter(
                ModuleRegistry.getAllModules(),
                ModuleAdapter.LayoutMode.GRID
            )

            binding.recyclerViewModules.apply {
                layoutManager = GridLayoutManager(requireContext(), 2)
                adapter = moduleAdapter
            }
        } else {
            // 如果适配器已存在，只需要刷新数据
            moduleAdapter.notifyDataSetChanged()
        }
    }

    private fun setupAnimations() {
        // Simple fade in animation for the entire view
        // Only animate on first load, not on navigation
        if (binding.root.alpha == 0f) {
            binding.root.alpha = 0f
            binding.root.animate()
                .alpha(1f)
                .setDuration(300)
                .setStartDelay(50)
                .start()
        }
    }

    override fun onModuleStateChanged(module: KasumiModule, enabled: Boolean) {
        val message = if (enabled) {
            "${module.name} 已启用"
        } else {
            "${module.name} 已禁用"
        }

        val color = if (enabled) {
            R.color.minecraft_green
        } else {
            R.color.minecraft_red
        }

        Snackbar.make(binding.root, message, Snackbar.LENGTH_SHORT)
            .setBackgroundTint(requireContext().getColor(color))
            .setTextColor(requireContext().getColor(R.color.white))
            .show()
    }

    override fun onDestroyView() {
        super.onDestroyView()
        KasumiModule.removeListener(this)
        _binding = null
    }
}
