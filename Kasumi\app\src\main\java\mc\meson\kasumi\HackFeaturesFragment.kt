package mc.meson.kasumi

import android.os.Bundle
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import androidx.fragment.app.Fragment
import androidx.recyclerview.widget.GridLayoutManager
import com.google.android.material.snackbar.Snackbar
import mc.meson.kasumi.config.ConfigChangeListener
import mc.meson.kasumi.config.ConfigData
import mc.meson.kasumi.config.ConfigManager
import mc.meson.kasumi.config.ConfigManagementFragment
import mc.meson.kasumi.config.ModuleSettingsFragment
import mc.meson.kasumi.databinding.FragmentHackFeaturesBinding
import mc.meson.kasumi.module.KasumiModule
import mc.meson.kasumi.module.ModuleAdapter
import mc.meson.kasumi.module.ModuleRegistry
import mc.meson.kasumi.module.ModuleStateListener

class HackFeaturesFragment : Fragment(), ModuleStateListener, ConfigChangeListener {

    private var _binding: FragmentHackFeaturesBinding? = null
    private val binding get() = _binding!!
    private lateinit var moduleAdapter: ModuleAdapter

    override fun onCreateView(
        inflater: LayoutInflater,
        container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View {
        _binding = FragmentHackFeaturesBinding.inflate(inflater, container, false)
        return binding.root
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)

        // 只在第一次初始化模块，避免重复初始化导致闪烁
        if (!isModuleInitialized) {
            KasumiModule.init(requireContext())
            isModuleInitialized = true
        }
        KasumiModule.addListener(this)
        ConfigManager.addListener(this)
        setupRecyclerView()
        setupListeners()
        setupAnimations()
    }

    companion object {
        private var isModuleInitialized = false
    }

    private fun setupRecyclerView() {
        // 只在第一次创建时设置适配器，避免重复创建导致闪烁
        if (!::moduleAdapter.isInitialized) {
            moduleAdapter = ModuleAdapter(
                ModuleRegistry.getAllModules(),
                ModuleAdapter.LayoutMode.GRID,
                onModuleSettingsClick = { module ->
                    openModuleSettings(module)
                }
            )

            binding.recyclerViewModules.apply {
                layoutManager = GridLayoutManager(requireContext(), 2)
                adapter = moduleAdapter
            }
        } else {
            // 如果适配器已存在，只需要刷新数据
            moduleAdapter.notifyDataSetChanged()
        }
    }

    private fun setupListeners() {
        // 配置管理按钮点击监听器
        binding.cardConfigManagement.setOnClickListener {
            openConfigManagement()
        }
    }

    private fun setupAnimations() {
        // Simple fade in animation for the entire view
        // Only animate on first load, not on navigation
        if (binding.root.alpha == 0f) {
            binding.root.alpha = 0f
            binding.root.animate()
                .alpha(1f)
                .setDuration(300)
                .setStartDelay(50)
                .start()
        }
    }

    override fun onModuleStateChanged(module: KasumiModule, enabled: Boolean) {
        val message = if (enabled) {
            "${module.name} 已启用"
        } else {
            "${module.name} 已禁用"
        }

        val color = if (enabled) {
            R.color.minecraft_green
        } else {
            R.color.minecraft_red
        }

        Snackbar.make(binding.root, message, Snackbar.LENGTH_SHORT)
            .setBackgroundTint(requireContext().getColor(color))
            .setTextColor(requireContext().getColor(R.color.white))
            .show()
    }

    /**
     * 打开模块设置页面
     */
    private fun openModuleSettings(module: KasumiModule) {
        val fragment = ModuleSettingsFragment.newInstance(module.id)
        parentFragmentManager.beginTransaction()
            .replace(R.id.nav_host_fragment, fragment)
            .addToBackStack(null)
            .commit()
    }

    /**
     * 打开配置管理页面
     */
    private fun openConfigManagement() {
        val fragment = ConfigManagementFragment.newInstance()
        parentFragmentManager.beginTransaction()
            .replace(R.id.nav_host_fragment, fragment)
            .addToBackStack(null)
            .commit()
    }

    override fun onConfigChanged() {
        // 配置列表发生变更时刷新UI
        updateUI()
    }

    override fun onCurrentConfigChanged(config: ConfigData?) {
        // 当前配置切换时刷新模块状态
        updateUI()
    }

    private fun updateUI() {
        // 刷新模块适配器以反映最新状态
        if (::moduleAdapter.isInitialized) {
            moduleAdapter.notifyDataSetChanged()
        }
    }

    override fun onDestroyView() {
        super.onDestroyView()
        KasumiModule.removeListener(this)
        ConfigManager.removeListener(this)
        _binding = null
    }
}
