<?xml version="1.0" encoding="utf-8" standalone="yes"?><Layout layout="fragment_config_management" modulePackage="mc.meson.kasumi" filePath="app\src\main\res\layout\fragment_config_management.xml" directory="layout" isMerge="false" isBindingData="false" rootNodeType="androidx.coordinatorlayout.widget.CoordinatorLayout"><Targets><Target tag="layout/fragment_config_management_0" view="androidx.coordinatorlayout.widget.CoordinatorLayout"><Expressions/><location startLine="1" startOffset="0" endLine="200" endOffset="53"/></Target><Target id="@+id/btnBack" view="ImageButton"><Expressions/><location startLine="22" startOffset="12" endLine="29" endOffset="49"/></Target><Target id="@+id/btnCreateConfig" view="ImageButton"><Expressions/><location startLine="40" startOffset="12" endLine="47" endOffset="49"/></Target><Target id="@+id/btnImportConfig" view="ImageButton"><Expressions/><location startLine="49" startOffset="12" endLine="56" endOffset="49"/></Target><Target id="@+id/btnExportAll" view="ImageButton"><Expressions/><location startLine="58" startOffset="12" endLine="65" endOffset="49"/></Target><Target id="@+id/tvCurrentConfigName" view="TextView"><Expressions/><location startLine="92" startOffset="16" endLine="99" endOffset="52"/></Target><Target id="@+id/tvCurrentConfigDescription" view="TextView"><Expressions/><location startLine="101" startOffset="16" endLine="108" endOffset="52"/></Target><Target id="@+id/tvCurrentConfigStats" view="TextView"><Expressions/><location startLine="110" startOffset="16" endLine="117" endOffset="52"/></Target><Target id="@+id/recyclerViewConfigs" view="androidx.recyclerview.widget.RecyclerView"><Expressions/><location startLine="147" startOffset="16" endLine="154" endOffset="51"/></Target><Target id="@+id/layoutEmptyState" view="LinearLayout"><Expressions/><location startLine="157" startOffset="16" endLine="190" endOffset="30"/></Target></Targets></Layout>