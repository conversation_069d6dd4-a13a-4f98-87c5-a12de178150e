package mc.meson.kasumi.config

import kotlinx.serialization.Serializable
import java.util.*

/**
 * 配置文件数据模型
 */
@Serializable
data class Configuration(
    val id: String = UUID.randomUUID().toString(),
    val name: String,
    val description: String = "",
    val createdTime: Long = System.currentTimeMillis(),
    val lastModified: Long = System.currentTimeMillis(),
    val moduleStates: Map<String, Boolean> = emptyMap(),
    val moduleSettings: Map<String, Map<String, ConfigValue>> = emptyMap(),
    val isDefault: Boolean = false
) {
    /**
     * 获取显示用的创建时间
     */
    fun getFormattedCreatedTime(): String {
        val date = Date(createdTime)
        return android.text.format.DateFormat.format("yyyy-MM-dd HH:mm", date).toString()
    }
    
    /**
     * 获取显示用的最后修改时间
     */
    fun getFormattedLastModified(): String {
        val date = Date(lastModified)
        return android.text.format.DateFormat.format("yyyy-MM-dd HH:mm", date).toString()
    }
    
    /**
     * 更新最后修改时间
     */
    fun updateLastModified(): Configuration {
        return this.copy(lastModified = System.currentTimeMillis())
    }
}

/**
 * 配置值的封装类，支持不同类型的设置值
 */
@Serializable
sealed class ConfigValue {
    @Serializable
    data class BooleanValue(val value: Boolean) : ConfigValue()
    
    @Serializable
    data class IntValue(val value: Int) : ConfigValue()
    
    @Serializable
    data class FloatValue(val value: Float) : ConfigValue()
    
    @Serializable
    data class StringValue(val value: String) : ConfigValue()
    
    /**
     * 获取实际值
     */
    fun getValue(): Any {
        return when (this) {
            is BooleanValue -> value
            is IntValue -> value
            is FloatValue -> value
            is StringValue -> value
        }
    }
    
    companion object {
        /**
         * 从任意值创建ConfigValue
         */
        fun fromValue(value: Any): ConfigValue {
            return when (value) {
                is Boolean -> BooleanValue(value)
                is Int -> IntValue(value)
                is Float -> FloatValue(value)
                is String -> StringValue(value)
                else -> StringValue(value.toString())
            }
        }
    }
}

/**
 * 配置列表数据
 */
@Serializable
data class ConfigurationList(
    val configurations: MutableList<Configuration> = mutableListOf(),
    val currentConfigId: String? = null,
    val version: Int = 1
) {
    /**
     * 获取当前配置
     */
    fun getCurrentConfiguration(): Configuration? {
        return configurations.find { it.id == currentConfigId }
    }
    
    /**
     * 添加配置
     */
    fun addConfiguration(config: Configuration): ConfigurationList {
        val updatedConfigs = configurations.toMutableList()
        updatedConfigs.add(config)
        return this.copy(configurations = updatedConfigs)
    }
    
    /**
     * 删除配置
     */
    fun removeConfiguration(configId: String): ConfigurationList {
        val updatedConfigs = configurations.toMutableList()
        updatedConfigs.removeAll { it.id == configId }
        val newCurrentId = if (currentConfigId == configId) {
            configurations.firstOrNull { it.id != configId }?.id
        } else {
            currentConfigId
        }
        return this.copy(configurations = updatedConfigs, currentConfigId = newCurrentId)
    }
    
    /**
     * 更新配置
     */
    fun updateConfiguration(config: Configuration): ConfigurationList {
        val updatedConfigs = configurations.toMutableList()
        val index = updatedConfigs.indexOfFirst { it.id == config.id }
        if (index >= 0) {
            updatedConfigs[index] = config.updateLastModified()
        }
        return this.copy(configurations = updatedConfigs)
    }
    
    /**
     * 设置当前配置
     */
    fun setCurrentConfiguration(configId: String): ConfigurationList {
        return this.copy(currentConfigId = configId)
    }
}
