<?xml version="1.0" encoding="utf-8"?>
<!-- 紧凑的横向模式选项 -->
<androidx.cardview.widget.CardView xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="wrap_content"
    android:layout_height="wrap_content"
    android:layout_marginHorizontal="3dp"
    app:cardCornerRadius="16dp"
    app:cardElevation="1dp"
    app:cardBackgroundColor="@color/md_theme_light_surface"
    android:clickable="true"
    android:focusable="true"
    android:foreground="?attr/selectableItemBackground">

    <TextView
        android:id="@+id/tvOption"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:text="选项"
        android:textSize="12sp"
        android:textColor="@color/md_theme_light_onSurface"
        android:paddingHorizontal="16dp"
        android:paddingVertical="8dp"
        android:gravity="center"
        android:minWidth="60dp" />

</androidx.cardview.widget.CardView>
