<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="?attr/colorSurface"
    android:fitsSystemWindows="true">

    <!-- Background gradient -->
    <View
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:background="@drawable/splash_gradient"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintEnd_toEndOf="parent" />

    <!-- Logo Container -->
    <LinearLayout
        android:id="@+id/logoContainer"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:orientation="vertical"
        android:gravity="center"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintEnd_toEndOf="parent">

        <!-- App Icon -->
        <ImageView
            android:id="@+id/appIcon"
            android:layout_width="120dp"
            android:layout_height="120dp"
            android:src="@mipmap/ic_launcher"
            android:layout_marginBottom="24dp"
            android:alpha="0"
            android:scaleX="0.8"
            android:scaleY="0.8" />

        <!-- App Name -->
        <TextView
            android:id="@+id/appName"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="Kasumi"
            android:textSize="32sp"
            android:textStyle="bold"
            android:textColor="?attr/colorOnSurface"
            android:layout_marginBottom="8dp"
            android:alpha="0"
            android:translationY="20dp" />

        <!-- App Subtitle -->
        <TextView
            android:id="@+id/appSubtitle"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="Minecraft Hack Tool"
            android:textSize="16sp"
            android:textColor="?attr/colorOnSurfaceVariant"
            android:alpha="0"
            android:translationY="20dp" />

    </LinearLayout>

    <!-- Loading Indicator -->
    <com.google.android.material.progressindicator.CircularProgressIndicator
        android:id="@+id/loadingIndicator"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginTop="48dp"
        android:indeterminate="true"
        app:indicatorColor="?attr/colorPrimary"
        app:trackColor="?attr/colorSurfaceVariant"
        app:layout_constraintTop_toBottomOf="@id/logoContainer"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        android:alpha="0" />

    <!-- Version Text -->
    <TextView
        android:id="@+id/versionText"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:text="v1.0.0"
        android:textSize="12sp"
        android:textColor="?attr/colorOnSurfaceVariant"
        android:layout_marginBottom="32dp"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        android:alpha="0" />

</androidx.constraintlayout.widget.ConstraintLayout>
