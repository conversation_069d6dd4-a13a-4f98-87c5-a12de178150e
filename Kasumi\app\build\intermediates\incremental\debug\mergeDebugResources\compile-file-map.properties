#Thu Jul 31 16:42:11 CST 2025
mc.meson.kasumi.app-main-51\:/anim/bounce.xml=D\:\\Kasumi\\Kasumi\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\anim_bounce.xml.flat
mc.meson.kasumi.app-main-51\:/anim/fade_in.xml=D\:\\Kasumi\\Kasumi\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\anim_fade_in.xml.flat
mc.meson.kasumi.app-main-51\:/anim/fade_out.xml=D\:\\Kasumi\\Kasumi\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\anim_fade_out.xml.flat
mc.meson.kasumi.app-main-51\:/anim/fragment_fade_enter.xml=D\:\\Kasumi\\Kasumi\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\anim_fragment_fade_enter.xml.flat
mc.meson.kasumi.app-main-51\:/anim/fragment_fade_exit.xml=D\:\\Kasumi\\Kasumi\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\anim_fragment_fade_exit.xml.flat
mc.meson.kasumi.app-main-51\:/anim/slide_in_right.xml=D\:\\Kasumi\\Kasumi\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\anim_slide_in_right.xml.flat
mc.meson.kasumi.app-main-51\:/anim/slide_out_left.xml=D\:\\Kasumi\\Kasumi\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\anim_slide_out_left.xml.flat
mc.meson.kasumi.app-main-51\:/color/bottom_nav_item_color.xml=D\:\\Kasumi\\Kasumi\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\color_bottom_nav_item_color.xml.flat
mc.meson.kasumi.app-main-51\:/drawable/button_ripple.xml=D\:\\Kasumi\\Kasumi\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\drawable_button_ripple.xml.flat
mc.meson.kasumi.app-main-51\:/drawable/card_background.xml=D\:\\Kasumi\\Kasumi\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\drawable_card_background.xml.flat
mc.meson.kasumi.app-main-51\:/drawable/category_indicator.xml=D\:\\Kasumi\\Kasumi\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\drawable_category_indicator.xml.flat
mc.meson.kasumi.app-main-51\:/drawable/custom_scrollbar_thumb.xml=D\:\\Kasumi\\Kasumi\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\drawable_custom_scrollbar_thumb.xml.flat
mc.meson.kasumi.app-main-51\:/drawable/custom_scrollbar_track.xml=D\:\\Kasumi\\Kasumi\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\drawable_custom_scrollbar_track.xml.flat
mc.meson.kasumi.app-main-51\:/drawable/drag_indicator.xml=D\:\\Kasumi\\Kasumi\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\drawable_drag_indicator.xml.flat
mc.meson.kasumi.app-main-51\:/drawable/floating_button_background.xml=D\:\\Kasumi\\Kasumi\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\drawable_floating_button_background.xml.flat
mc.meson.kasumi.app-main-51\:/drawable/floating_button_ripple.xml=D\:\\Kasumi\\Kasumi\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\drawable_floating_button_ripple.xml.flat
mc.meson.kasumi.app-main-51\:/drawable/floating_button_style.xml=D\:\\Kasumi\\Kasumi\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\drawable_floating_button_style.xml.flat
mc.meson.kasumi.app-main-51\:/drawable/floating_divider.xml=D\:\\Kasumi\\Kasumi\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\drawable_floating_divider.xml.flat
mc.meson.kasumi.app-main-51\:/drawable/floating_item_background.xml=D\:\\Kasumi\\Kasumi\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\drawable_floating_item_background.xml.flat
mc.meson.kasumi.app-main-51\:/drawable/ic_arrow_back_24.xml=D\:\\Kasumi\\Kasumi\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\drawable_ic_arrow_back_24.xml.flat
mc.meson.kasumi.app-main-51\:/drawable/ic_arrow_drop_down_24.xml=D\:\\Kasumi\\Kasumi\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\drawable_ic_arrow_drop_down_24.xml.flat
mc.meson.kasumi.app-main-51\:/drawable/ic_check_circle_24.xml=D\:\\Kasumi\\Kasumi\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\drawable_ic_check_circle_24.xml.flat
mc.meson.kasumi.app-main-51\:/drawable/ic_close.xml=D\:\\Kasumi\\Kasumi\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\drawable_ic_close.xml.flat
mc.meson.kasumi.app-main-51\:/drawable/ic_close_24.xml=D\:\\Kasumi\\Kasumi\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\drawable_ic_close_24.xml.flat
mc.meson.kasumi.app-main-51\:/drawable/ic_collapse_24.xml=D\:\\Kasumi\\Kasumi\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\drawable_ic_collapse_24.xml.flat
mc.meson.kasumi.app-main-51\:/drawable/ic_compress.xml=D\:\\Kasumi\\Kasumi\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\drawable_ic_compress.xml.flat
mc.meson.kasumi.app-main-51\:/drawable/ic_expand_more.xml=D\:\\Kasumi\\Kasumi\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\drawable_ic_expand_more.xml.flat
mc.meson.kasumi.app-main-51\:/drawable/ic_features_24.xml=D\:\\Kasumi\\Kasumi\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\drawable_ic_features_24.xml.flat
mc.meson.kasumi.app-main-51\:/drawable/ic_home_24.xml=D\:\\Kasumi\\Kasumi\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\drawable_ic_home_24.xml.flat
mc.meson.kasumi.app-main-51\:/drawable/ic_kasumi_floating.xml=D\:\\Kasumi\\Kasumi\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\drawable_ic_kasumi_floating.xml.flat
mc.meson.kasumi.app-main-51\:/drawable/ic_kasumi_logo.xml=D\:\\Kasumi\\Kasumi\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\drawable_ic_kasumi_logo.xml.flat
mc.meson.kasumi.app-main-51\:/drawable/ic_launcher_background.xml=D\:\\Kasumi\\Kasumi\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\drawable_ic_launcher_background.xml.flat
mc.meson.kasumi.app-main-51\:/drawable/ic_launcher_foreground.xml=D\:\\Kasumi\\Kasumi\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\drawable_ic_launcher_foreground.xml.flat
mc.meson.kasumi.app-main-51\:/drawable/ic_resize.xml=D\:\\Kasumi\\Kasumi\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\drawable_ic_resize.xml.flat
mc.meson.kasumi.app-main-51\:/drawable/ic_settings_24.xml=D\:\\Kasumi\\Kasumi\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\drawable_ic_settings_24.xml.flat
mc.meson.kasumi.app-main-51\:/drawable/ic_zoom_out_map.xml=D\:\\Kasumi\\Kasumi\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\drawable_ic_zoom_out_map.xml.flat
mc.meson.kasumi.app-main-51\:/drawable/mode_option_indicator.xml=D\:\\Kasumi\\Kasumi\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\drawable_mode_option_indicator.xml.flat
mc.meson.kasumi.app-main-51\:/drawable/resize_handle_background.xml=D\:\\Kasumi\\Kasumi\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\drawable_resize_handle_background.xml.flat
mc.meson.kasumi.app-main-51\:/drawable/settings_panel_background.xml=D\:\\Kasumi\\Kasumi\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\drawable_settings_panel_background.xml.flat
mc.meson.kasumi.app-main-51\:/drawable/splash_gradient.xml=D\:\\Kasumi\\Kasumi\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\drawable_splash_gradient.xml.flat
mc.meson.kasumi.app-main-51\:/drawable/status_indicator.xml=D\:\\Kasumi\\Kasumi\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\drawable_status_indicator.xml.flat
mc.meson.kasumi.app-main-51\:/drawable/tab_background_selector.xml=D\:\\Kasumi\\Kasumi\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\drawable_tab_background_selector.xml.flat
mc.meson.kasumi.app-main-51\:/drawable/tab_indicator_rounded.xml=D\:\\Kasumi\\Kasumi\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\drawable_tab_indicator_rounded.xml.flat
mc.meson.kasumi.app-main-51\:/drawable/tab_indicator_simple.xml=D\:\\Kasumi\\Kasumi\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\drawable_tab_indicator_simple.xml.flat
mc.meson.kasumi.app-main-51\:/drawable/tab_layout_background.xml=D\:\\Kasumi\\Kasumi\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\drawable_tab_layout_background.xml.flat
mc.meson.kasumi.app-main-51\:/menu/bottom_navigation_menu.xml=D\:\\Kasumi\\Kasumi\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\menu_bottom_navigation_menu.xml.flat
mc.meson.kasumi.app-main-51\:/mipmap-anydpi/ic_launcher.xml=D\:\\Kasumi\\Kasumi\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\mipmap-anydpi_ic_launcher.xml.flat
mc.meson.kasumi.app-main-51\:/mipmap-anydpi/ic_launcher_round.xml=D\:\\Kasumi\\Kasumi\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\mipmap-anydpi_ic_launcher_round.xml.flat
mc.meson.kasumi.app-main-51\:/mipmap-hdpi/ic_launcher.webp=D\:\\Kasumi\\Kasumi\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\mipmap-hdpi_ic_launcher.webp.flat
mc.meson.kasumi.app-main-51\:/mipmap-hdpi/ic_launcher_round.webp=D\:\\Kasumi\\Kasumi\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\mipmap-hdpi_ic_launcher_round.webp.flat
mc.meson.kasumi.app-main-51\:/mipmap-mdpi/ic_launcher.webp=D\:\\Kasumi\\Kasumi\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\mipmap-mdpi_ic_launcher.webp.flat
mc.meson.kasumi.app-main-51\:/mipmap-mdpi/ic_launcher_round.webp=D\:\\Kasumi\\Kasumi\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\mipmap-mdpi_ic_launcher_round.webp.flat
mc.meson.kasumi.app-main-51\:/mipmap-xhdpi/ic_launcher.webp=D\:\\Kasumi\\Kasumi\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\mipmap-xhdpi_ic_launcher.webp.flat
mc.meson.kasumi.app-main-51\:/mipmap-xhdpi/ic_launcher_round.webp=D\:\\Kasumi\\Kasumi\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\mipmap-xhdpi_ic_launcher_round.webp.flat
mc.meson.kasumi.app-main-51\:/mipmap-xxhdpi/ic_launcher.webp=D\:\\Kasumi\\Kasumi\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\mipmap-xxhdpi_ic_launcher.webp.flat
mc.meson.kasumi.app-main-51\:/mipmap-xxhdpi/ic_launcher_round.webp=D\:\\Kasumi\\Kasumi\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\mipmap-xxhdpi_ic_launcher_round.webp.flat
mc.meson.kasumi.app-main-51\:/mipmap-xxxhdpi/ic_launcher.webp=D\:\\Kasumi\\Kasumi\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\mipmap-xxxhdpi_ic_launcher.webp.flat
mc.meson.kasumi.app-main-51\:/mipmap-xxxhdpi/ic_launcher_round.webp=D\:\\Kasumi\\Kasumi\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\mipmap-xxxhdpi_ic_launcher_round.webp.flat
mc.meson.kasumi.app-main-51\:/navigation/nav_graph.xml=D\:\\Kasumi\\Kasumi\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\navigation_nav_graph.xml.flat
mc.meson.kasumi.app-main-51\:/xml/backup_rules.xml=D\:\\Kasumi\\Kasumi\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\xml_backup_rules.xml.flat
mc.meson.kasumi.app-main-51\:/xml/data_extraction_rules.xml=D\:\\Kasumi\\Kasumi\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\xml_data_extraction_rules.xml.flat
mc.meson.kasumi.app-mergeDebugResources-48\:/layout/activity_main.xml=D\:\\Kasumi\\Kasumi\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\layout_activity_main.xml.flat
mc.meson.kasumi.app-mergeDebugResources-48\:/layout/activity_main_with_navigation.xml=D\:\\Kasumi\\Kasumi\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\layout_activity_main_with_navigation.xml.flat
mc.meson.kasumi.app-mergeDebugResources-48\:/layout/activity_splash.xml=D\:\\Kasumi\\Kasumi\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\layout_activity_splash.xml.flat
mc.meson.kasumi.app-mergeDebugResources-48\:/layout/compact_category_page.xml=D\:\\Kasumi\\Kasumi\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\layout_compact_category_page.xml.flat
mc.meson.kasumi.app-mergeDebugResources-48\:/layout/dialog_create_config.xml=D\:\\Kasumi\\Kasumi\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\layout_dialog_create_config.xml.flat
mc.meson.kasumi.app-mergeDebugResources-48\:/layout/floating_window_compact_v2.xml=D\:\\Kasumi\\Kasumi\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\layout_floating_window_compact_v2.xml.flat
mc.meson.kasumi.app-mergeDebugResources-48\:/layout/floating_window_expanded_new.xml=D\:\\Kasumi\\Kasumi\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\layout_floating_window_expanded_new.xml.flat
mc.meson.kasumi.app-mergeDebugResources-48\:/layout/fragment_config_management.xml=D\:\\Kasumi\\Kasumi\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\layout_fragment_config_management.xml.flat
mc.meson.kasumi.app-mergeDebugResources-48\:/layout/fragment_hack_features.xml=D\:\\Kasumi\\Kasumi\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\layout_fragment_hack_features.xml.flat
mc.meson.kasumi.app-mergeDebugResources-48\:/layout/fragment_home.xml=D\:\\Kasumi\\Kasumi\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\layout_fragment_home.xml.flat
mc.meson.kasumi.app-mergeDebugResources-48\:/layout/fragment_module_settings.xml=D\:\\Kasumi\\Kasumi\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\layout_fragment_module_settings.xml.flat
mc.meson.kasumi.app-mergeDebugResources-48\:/layout/fragment_settings.xml=D\:\\Kasumi\\Kasumi\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\layout_fragment_settings.xml.flat
mc.meson.kasumi.app-mergeDebugResources-48\:/layout/item_compact_module.xml=D\:\\Kasumi\\Kasumi\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\layout_item_compact_module.xml.flat
mc.meson.kasumi.app-mergeDebugResources-48\:/layout/item_config.xml=D\:\\Kasumi\\Kasumi\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\layout_item_config.xml.flat
mc.meson.kasumi.app-mergeDebugResources-48\:/layout/item_expanded_module.xml=D\:\\Kasumi\\Kasumi\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\layout_item_expanded_module.xml.flat
mc.meson.kasumi.app-mergeDebugResources-48\:/layout/item_mode_option.xml=D\:\\Kasumi\\Kasumi\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\layout_item_mode_option.xml.flat
mc.meson.kasumi.app-mergeDebugResources-48\:/layout/item_module_grid.xml=D\:\\Kasumi\\Kasumi\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\layout_item_module_grid.xml.flat
mc.meson.kasumi.app-mergeDebugResources-48\:/layout/item_module_list.xml=D\:\\Kasumi\\Kasumi\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\layout_item_module_list.xml.flat
mc.meson.kasumi.app-mergeDebugResources-48\:/layout/item_setting_mode.xml=D\:\\Kasumi\\Kasumi\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\layout_item_setting_mode.xml.flat
mc.meson.kasumi.app-mergeDebugResources-48\:/layout/item_setting_slider.xml=D\:\\Kasumi\\Kasumi\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\layout_item_setting_slider.xml.flat
mc.meson.kasumi.app-mergeDebugResources-48\:/layout/item_setting_toggle.xml=D\:\\Kasumi\\Kasumi\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\layout_item_setting_toggle.xml.flat
mc.meson.kasumi.app-mergeDebugResources-48\:/layout/module_settings_panel.xml=D\:\\Kasumi\\Kasumi\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\layout_module_settings_panel.xml.flat
