R_DEF: Internal format may change without notice
local
anim bounce
anim fade_in
anim fade_out
anim fragment_fade_enter
anim fragment_fade_exit
anim slide_in_right
anim slide_out_left
color black
color bottom_nav_item_color
color card_background_primary
color card_background_secondary
color card_background_tertiary
color md_theme_dark_background
color md_theme_dark_error
color md_theme_dark_errorContainer
color md_theme_dark_inverseOnSurface
color md_theme_dark_inversePrimary
color md_theme_dark_inverseSurface
color md_theme_dark_onBackground
color md_theme_dark_onError
color md_theme_dark_onErrorContainer
color md_theme_dark_onPrimary
color md_theme_dark_onPrimaryContainer
color md_theme_dark_onSecondary
color md_theme_dark_onSecondaryContainer
color md_theme_dark_onSurface
color md_theme_dark_onSurfaceVariant
color md_theme_dark_onTertiary
color md_theme_dark_onTertiaryContainer
color md_theme_dark_outline
color md_theme_dark_outlineVariant
color md_theme_dark_primary
color md_theme_dark_primaryContainer
color md_theme_dark_scrim
color md_theme_dark_secondary
color md_theme_dark_secondaryContainer
color md_theme_dark_shadow
color md_theme_dark_surface
color md_theme_dark_surfaceTint
color md_theme_dark_surfaceVariant
color md_theme_dark_tertiary
color md_theme_dark_tertiaryContainer
color md_theme_light_background
color md_theme_light_error
color md_theme_light_errorContainer
color md_theme_light_inverseOnSurface
color md_theme_light_inversePrimary
color md_theme_light_inverseSurface
color md_theme_light_onBackground
color md_theme_light_onError
color md_theme_light_onErrorContainer
color md_theme_light_onPrimary
color md_theme_light_onPrimaryContainer
color md_theme_light_onSecondary
color md_theme_light_onSecondaryContainer
color md_theme_light_onSurface
color md_theme_light_onSurfaceVariant
color md_theme_light_onTertiary
color md_theme_light_onTertiaryContainer
color md_theme_light_outline
color md_theme_light_outlineVariant
color md_theme_light_primary
color md_theme_light_primaryContainer
color md_theme_light_scrim
color md_theme_light_secondary
color md_theme_light_secondaryContainer
color md_theme_light_shadow
color md_theme_light_surface
color md_theme_light_surfaceTint
color md_theme_light_surfaceVariant
color md_theme_light_tertiary
color md_theme_light_tertiaryContainer
color minecraft_blue
color minecraft_blue_dark
color minecraft_green
color minecraft_green_dark
color minecraft_orange
color minecraft_orange_dark
color minecraft_purple
color minecraft_purple_dark
color minecraft_red
color minecraft_red_dark
color minecraft_yellow
color minecraft_yellow_dark
color purple_200
color purple_500
color purple_700
color teal_200
color teal_700
color white
drawable button_ripple
drawable card_background
drawable category_indicator
drawable custom_scrollbar_thumb
drawable custom_scrollbar_track
drawable drag_indicator
drawable floating_button_background
drawable floating_button_ripple
drawable floating_button_style
drawable floating_divider
drawable floating_item_background
drawable ic_arrow_back_24
drawable ic_arrow_drop_down_24
drawable ic_check_circle_24
drawable ic_circle
drawable ic_close
drawable ic_close_24
drawable ic_collapse_24
drawable ic_compress
drawable ic_expand_more
drawable ic_features_24
drawable ic_home_24
drawable ic_kasumi_floating
drawable ic_kasumi_logo
drawable ic_launcher_background
drawable ic_launcher_foreground
drawable ic_resize
drawable ic_settings_24
drawable ic_zoom_out_map
drawable mode_option_indicator
drawable resize_handle_background
drawable settings_panel_background
drawable splash_gradient
drawable status_indicator
drawable tab_background_selector
drawable tab_indicator_rounded
drawable tab_indicator_simple
drawable tab_layout_background
id action_config_management_back
id action_features_to_config_management
id action_features_to_home
id action_features_to_module_settings
id action_features_to_settings
id action_home_to_features
id action_home_to_settings
id action_module_settings_back
id action_settings_to_features
id action_settings_to_home
id appBarLayout
id appIcon
id appName
id appSubtitle
id bottom_navigation
id btnBack
id btnBackInTitle
id btnCheckUpdate
id btnCollapse
id btnCollapseExpanded
id btnDeleteConfig
id btnEditConfig
id btnExpand
id btnExportAll
id btnExportConfig
id btnFloatingWindow
id btnImportConfig
id btnLandscape
id btnResetSettings
id cardConfig
id cardConfigManagement
id cardModule
id collapsedDot
id compactContent
id etConfigDescription
id etConfigName
id fabCreateConfig
id fabSettings
id indicatorCombat
id indicatorMovement
id indicatorPlayer
id indicatorVisual
id indicatorWorld
id ivCurrentIndicator
id ivModuleIcon
id layoutEmptyState
id layoutNoSettings
id llModeOptions
id loadingIndicator
id logoContainer
id mainContent
id moduleContainer
id nav_config_management
id nav_features
id nav_graph
id nav_home
id nav_host_fragment
id nav_module_settings
id nav_settings
id recyclerViewConfigs
id recyclerViewModules
id recyclerViewSettings
id rvCategoryModules
id rvExpandedModules
id rvSettings
id seekBar
id settingsPanel
id statusIndicator
id statusText
id switchAutoConnect
id switchDarkMode
id switchModule
id switchModuleEnabled
id switchNotifications
id switchSetting
id tabCategories
id titleBar
id toolbar
id topBar
id tvCategoryName
id tvConfigDescription
id tvConfigName
id tvConfigStats
id tvConfigTime
id tvCurrentConfigDescription
id tvCurrentConfigName
id tvCurrentConfigStats
id tvCurrentValue
id tvModuleCategory
id tvModuleDescription
id tvModuleName
id tvModuleTitleInTitle
id tvOption
id tvSettingDescription
id tvSettingName
id versionText
id viewCategoryIndicator
id vpCompactCategories
id welcomeText
layout activity_main
layout activity_main_with_navigation
layout activity_splash
layout compact_category_page
layout dialog_create_config
layout floating_window_compact_v2
layout floating_window_expanded_new
layout fragment_config_management
layout fragment_hack_features
layout fragment_home
layout fragment_module_settings
layout fragment_settings
layout item_compact_module
layout item_config
layout item_expanded_module
layout item_mode_option
layout item_module_grid
layout item_module_list
layout item_setting_mode
layout item_setting_slider
layout item_setting_toggle
layout module_settings_panel
menu bottom_navigation_menu
mipmap ic_launcher
mipmap ic_launcher_round
navigation nav_graph
string about
string about_desc
string app_name
string auto_clicker
string auto_clicker_desc
string auto_connect
string auto_connect_desc
string check_update
string checking_updates
string combat_features
string connected
string connecting
string connection_status
string dark_mode
string dark_mode_desc
string disconnected
string feature_disabled
string feature_enabled
string floating_window_desc
string floating_window_notification_text
string floating_window_notification_title
string floating_window_permission_needed
string floating_window_started
string floating_window_title
string fly
string fly_desc
string general_settings
string kill_aura
string kill_aura_desc
string latest_version
string movement_features
string nav_features
string nav_home
string nav_settings
string no_fall
string no_fall_desc
string notifications
string notifications_desc
string reach
string reach_desc
string setting_disabled
string setting_enabled
string settings
string settings_coming_soon
string speed
string speed_desc
string start_floating_window
string version
string welcome_description
string welcome_title
style App.Button.Primary
style App.Button.Secondary
style App.Card.Elevated
style App.Card.Outlined
style App.FloatingActionButton
style App.Switch.Material
style App.TabLayout.Rounded
style App.TabLayout.TextAppearance
style App.Text.Body
style App.Text.Headline
style App.Text.Title
style App.Toolbar
style App.Widget.BottomNavigationView.ActiveIndicator
style Theme.Kasumi
style Theme.Kasumi.FloatingWindow
xml backup_rules
xml data_extraction_rules
