// Generated by view binder compiler. Do not edit!
package mc.meson.kasumi.databinding;

import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.LinearLayout;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.recyclerview.widget.RecyclerView;
import androidx.viewbinding.ViewBinding;
import androidx.viewbinding.ViewBindings;
import com.google.android.material.button.MaterialButton;
import java.lang.NullPointerException;
import java.lang.Override;
import java.lang.String;
import mc.meson.kasumi.R;

public final class DialogSelectConfigurationBinding implements ViewBinding {
  @NonNull
  private final LinearLayout rootView;

  @NonNull
  public final MaterialButton btnCancelSelect;

  @NonNull
  public final RecyclerView rvConfigurationSelect;

  private DialogSelectConfigurationBinding(@NonNull LinearLayout rootView,
      @NonNull MaterialButton btnCancelSelect, @NonNull RecyclerView rvConfigurationSelect) {
    this.rootView = rootView;
    this.btnCancelSelect = btnCancelSelect;
    this.rvConfigurationSelect = rvConfigurationSelect;
  }

  @Override
  @NonNull
  public LinearLayout getRoot() {
    return rootView;
  }

  @NonNull
  public static DialogSelectConfigurationBinding inflate(@NonNull LayoutInflater inflater) {
    return inflate(inflater, null, false);
  }

  @NonNull
  public static DialogSelectConfigurationBinding inflate(@NonNull LayoutInflater inflater,
      @Nullable ViewGroup parent, boolean attachToParent) {
    View root = inflater.inflate(R.layout.dialog_select_configuration, parent, false);
    if (attachToParent) {
      parent.addView(root);
    }
    return bind(root);
  }

  @NonNull
  public static DialogSelectConfigurationBinding bind(@NonNull View rootView) {
    // The body of this method is generated in a way you would not otherwise write.
    // This is done to optimize the compiled bytecode for size and performance.
    int id;
    missingId: {
      id = R.id.btnCancelSelect;
      MaterialButton btnCancelSelect = ViewBindings.findChildViewById(rootView, id);
      if (btnCancelSelect == null) {
        break missingId;
      }

      id = R.id.rvConfigurationSelect;
      RecyclerView rvConfigurationSelect = ViewBindings.findChildViewById(rootView, id);
      if (rvConfigurationSelect == null) {
        break missingId;
      }

      return new DialogSelectConfigurationBinding((LinearLayout) rootView, btnCancelSelect,
          rvConfigurationSelect);
    }
    String missingId = rootView.getResources().getResourceName(id);
    throw new NullPointerException("Missing required view with ID: ".concat(missingId));
  }
}
