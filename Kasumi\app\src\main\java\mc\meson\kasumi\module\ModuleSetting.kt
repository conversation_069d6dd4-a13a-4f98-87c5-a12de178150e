package mc.meson.kasumi.module

/**
 * 模块设置项基类
 */
sealed class ModuleSetting {
    abstract val key: String
    abstract val name: String
    abstract val description: String
}

/**
 * 开关设置
 */
data class ToggleSetting(
    override val key: String,
    override val name: String,
    override val description: String,
    val defaultValue: Boolean = false
) : ModuleSetting()

/**
 * 模式选择设置
 */
data class ModeSetting(
    override val key: String,
    override val name: String,
    override val description: String,
    val options: List<String>,
    val defaultIndex: Int = 0
) : ModuleSetting()

/**
 * 滑块设置
 */
data class SliderSetting(
    override val key: String,
    override val name: String,
    override val description: String,
    val minValue: Float,
    val maxValue: Float,
    val defaultValue: Float,
    val stepSize: Float = 1f,
    val unit: String = ""
) : ModuleSetting()

/**
 * 设置值存储
 */
object ModuleSettingsManager {
    private const val PREFS_NAME = "kasumi_module_settings"
    private var prefs: android.content.SharedPreferences? = null
    private val settingsMap = mutableMapOf<String, MutableMap<String, Any>>()

    /**
     * 初始化设置管理器
     */
    fun init(context: android.content.Context) {
        prefs = context.getSharedPreferences(PREFS_NAME, android.content.Context.MODE_PRIVATE)
        loadAllSettings()
    }

    /**
     * 加载所有设置
     */
    private fun loadAllSettings() {
        prefs?.all?.forEach { (key, value) ->
            if (key.contains(".") && value != null) {
                val parts = key.split(".", limit = 2)
                if (parts.size == 2) {
                    val moduleKey = parts[0]
                    val settingKey = parts[1]
                    getOrCreateModuleSettings(moduleKey)[settingKey] = value
                }
            }
        }
    }

    /**
     * 保存设置到SharedPreferences
     */
    private fun saveSetting(moduleKey: String, settingKey: String, value: Any) {
        val prefKey = "$moduleKey.$settingKey"
        prefs?.edit()?.apply {
            when (value) {
                is Boolean -> putBoolean(prefKey, value)
                is Int -> putInt(prefKey, value)
                is Float -> putFloat(prefKey, value)
                is String -> putString(prefKey, value)
            }
        }?.apply()
    }

    fun getBooleanSetting(moduleKey: String, settingKey: String, defaultValue: Boolean = false): Boolean {
        return settingsMap[moduleKey]?.get(settingKey) as? Boolean ?: defaultValue
    }

    fun setBooleanSetting(moduleKey: String, settingKey: String, value: Boolean) {
        getOrCreateModuleSettings(moduleKey)[settingKey] = value
        saveSetting(moduleKey, settingKey, value)
        notifyConfigUpdate()
    }

    fun getIntSetting(moduleKey: String, settingKey: String, defaultValue: Int = 0): Int {
        return settingsMap[moduleKey]?.get(settingKey) as? Int ?: defaultValue
    }

    fun setIntSetting(moduleKey: String, settingKey: String, value: Int) {
        getOrCreateModuleSettings(moduleKey)[settingKey] = value
        saveSetting(moduleKey, settingKey, value)
        notifyConfigUpdate()
    }

    fun getFloatSetting(moduleKey: String, settingKey: String, defaultValue: Float = 0f): Float {
        return settingsMap[moduleKey]?.get(settingKey) as? Float ?: defaultValue
    }

    fun setFloatSetting(moduleKey: String, settingKey: String, value: Float) {
        getOrCreateModuleSettings(moduleKey)[settingKey] = value
        saveSetting(moduleKey, settingKey, value)
        notifyConfigUpdate()
    }

    /**
     * 获取指定模块的所有设置
     */
    fun getModuleSettings(moduleKey: String): Map<String, Any> {
        return settingsMap[moduleKey]?.toMap() ?: emptyMap()
    }

    /**
     * 获取所有模块的设置
     */
    fun getAllSettings(): Map<String, Map<String, Any>> {
        return settingsMap.mapValues { it.value.toMap() }
    }

    /**
     * 设置指定模块的所有设置
     */
    fun setModuleSettings(moduleKey: String, settings: Map<String, Any>) {
        val moduleSettings = getOrCreateModuleSettings(moduleKey)
        moduleSettings.clear()
        moduleSettings.putAll(settings)
    }

    /**
     * 清除指定模块的所有设置
     */
    fun clearModuleSettings(moduleKey: String) {
        settingsMap.remove(moduleKey)
    }

    /**
     * 清除所有设置
     */
    fun clearAllSettings() {
        settingsMap.clear()
    }

    /**
     * 通知配置更新
     */
    private fun notifyConfigUpdate() {
        try {
            mc.meson.kasumi.config.ConfigManager.updateCurrentConfigState()
        } catch (e: Exception) {
            // 忽略错误，避免循环依赖
        }
    }

    private fun getOrCreateModuleSettings(moduleKey: String): MutableMap<String, Any> {
        return settingsMap.getOrPut(moduleKey) { mutableMapOf() }
    }
}
