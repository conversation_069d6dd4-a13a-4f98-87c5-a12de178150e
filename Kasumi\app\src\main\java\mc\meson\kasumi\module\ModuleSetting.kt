package mc.meson.kasumi.module

/**
 * 模块设置项基类
 */
sealed class ModuleSetting {
    abstract val key: String
    abstract val name: String
    abstract val description: String
}

/**
 * 开关设置
 */
data class ToggleSetting(
    override val key: String,
    override val name: String,
    override val description: String,
    val defaultValue: Boolean = false
) : ModuleSetting()

/**
 * 模式选择设置
 */
data class ModeSetting(
    override val key: String,
    override val name: String,
    override val description: String,
    val options: List<String>,
    val defaultIndex: Int = 0
) : ModuleSetting()

/**
 * 滑块设置
 */
data class SliderSetting(
    override val key: String,
    override val name: String,
    override val description: String,
    val minValue: Float,
    val maxValue: Float,
    val defaultValue: Float,
    val stepSize: Float = 1f,
    val unit: String = ""
) : ModuleSetting()

/**
 * 设置值存储
 */
object ModuleSettingsManager {
    private const val TAG = "ModuleSettingsManager"
    private const val SETTINGS_PREFS_NAME = "kasumi_module_settings"

    private val settingsMap = mutableMapOf<String, MutableMap<String, Any>>()
    private var prefs: android.content.SharedPreferences? = null

    /**
     * 初始化设置管理器
     */
    fun init(context: android.content.Context) {
        prefs = context.getSharedPreferences(SETTINGS_PREFS_NAME, android.content.Context.MODE_PRIVATE)
        loadAllSettings()
        android.util.Log.d(TAG, "ModuleSettingsManager initialized")
    }

    fun getBooleanSetting(moduleKey: String, settingKey: String, defaultValue: Boolean = false): Boolean {
        return settingsMap[moduleKey]?.get(settingKey) as? Boolean ?: defaultValue
    }

    fun setBooleanSetting(moduleKey: String, settingKey: String, value: Boolean) {
        getOrCreateModuleSettings(moduleKey)[settingKey] = value
        saveBooleanSetting(moduleKey, settingKey, value)
    }

    fun getIntSetting(moduleKey: String, settingKey: String, defaultValue: Int = 0): Int {
        return settingsMap[moduleKey]?.get(settingKey) as? Int ?: defaultValue
    }

    fun setIntSetting(moduleKey: String, settingKey: String, value: Int) {
        getOrCreateModuleSettings(moduleKey)[settingKey] = value
        saveIntSetting(moduleKey, settingKey, value)
    }

    fun getFloatSetting(moduleKey: String, settingKey: String, defaultValue: Float = 0f): Float {
        return settingsMap[moduleKey]?.get(settingKey) as? Float ?: defaultValue
    }

    fun setFloatSetting(moduleKey: String, settingKey: String, value: Float) {
        getOrCreateModuleSettings(moduleKey)[settingKey] = value
        saveFloatSetting(moduleKey, settingKey, value)
    }

    /**
     * 获取模块的所有设置
     */
    fun getModuleSettings(moduleKey: String): Map<String, Any> {
        return settingsMap[moduleKey]?.toMap() ?: emptyMap()
    }

    /**
     * 批量设置模块设置
     */
    fun setModuleSettings(moduleKey: String, settings: Map<String, Any>) {
        val moduleSettings = getOrCreateModuleSettings(moduleKey)
        moduleSettings.clear()
        moduleSettings.putAll(settings)

        // 保存到SharedPreferences
        settings.forEach { (key, value) ->
            when (value) {
                is Boolean -> saveBooleanSetting(moduleKey, key, value)
                is Int -> saveIntSetting(moduleKey, key, value)
                is Float -> saveFloatSetting(moduleKey, key, value)
            }
        }
    }

    /**
     * 清除模块的所有设置
     */
    fun clearModuleSettings(moduleKey: String) {
        settingsMap.remove(moduleKey)
        prefs?.edit()?.apply {
            // 移除该模块的所有设置
            val allKeys = prefs?.all?.keys ?: emptySet()
            allKeys.filter { it.startsWith("${moduleKey}_") }.forEach { key ->
                remove(key)
            }
            apply()
        }
    }

    private fun getOrCreateModuleSettings(moduleKey: String): MutableMap<String, Any> {
        return settingsMap.getOrPut(moduleKey) { mutableMapOf() }
    }

    private fun saveBooleanSetting(moduleKey: String, settingKey: String, value: Boolean) {
        prefs?.edit()?.putBoolean("${moduleKey}_${settingKey}", value)?.apply()
    }

    private fun saveIntSetting(moduleKey: String, settingKey: String, value: Int) {
        prefs?.edit()?.putInt("${moduleKey}_${settingKey}", value)?.apply()
    }

    private fun saveFloatSetting(moduleKey: String, settingKey: String, value: Float) {
        prefs?.edit()?.putFloat("${moduleKey}_${settingKey}", value)?.apply()
    }

    private fun loadAllSettings() {
        prefs?.all?.forEach { (key, value) ->
            if (key.contains("_")) {
                val parts = key.split("_", limit = 2)
                if (parts.size == 2) {
                    val moduleKey = parts[0]
                    val settingKey = parts[1]
                    getOrCreateModuleSettings(moduleKey)[settingKey] = value
                }
            }
        }
    }
}
