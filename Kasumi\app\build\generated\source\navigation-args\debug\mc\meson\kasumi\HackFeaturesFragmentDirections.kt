package mc.meson.kasumi

import android.os.Bundle
import androidx.navigation.ActionOnlyNavDirections
import androidx.navigation.NavDirections
import kotlin.Int
import kotlin.String

public class HackFeaturesFragmentDirections private constructor() {
  private data class ActionFeaturesToModuleSettings(
    public val moduleId: String,
  ) : NavDirections {
    public override val actionId: Int = R.id.action_features_to_module_settings

    public override val arguments: Bundle
      get() {
        val result = Bundle()
        result.putString("module_id", this.moduleId)
        return result
      }
  }

  public companion object {
    public fun actionFeaturesToHome(): NavDirections =
        ActionOnlyNavDirections(R.id.action_features_to_home)

    public fun actionFeaturesToSettings(): NavDirections =
        ActionOnlyNavDirections(R.id.action_features_to_settings)

    public fun actionFeaturesToConfigManagement(): NavDirections =
        ActionOnlyNavDirections(R.id.action_features_to_config_management)

    public fun actionFeaturesToModuleSettings(moduleId: String): NavDirections =
        ActionFeaturesToModuleSettings(moduleId)
  }
}
