<?xml version="1.0" encoding="utf-8" standalone="yes"?><Layout layout="fragment_settings" modulePackage="mc.meson.kasumi" filePath="app\src\main\res\layout\fragment_settings.xml" directory="layout" isMerge="false" isBindingData="false" rootNodeType="androidx.core.widget.NestedScrollView"><Targets><Target tag="layout/fragment_settings_0" view="androidx.core.widget.NestedScrollView"><Expressions/><location startLine="1" startOffset="0" endLine="310" endOffset="39"/></Target><Target id="@+id/switchDarkMode" view="com.google.android.material.materialswitch.MaterialSwitch"><Expressions/><location startLine="68" startOffset="20" endLine="73" endOffset="67"/></Target><Target id="@+id/switchNotifications" view="com.google.android.material.materialswitch.MaterialSwitch"><Expressions/><location startLine="107" startOffset="20" endLine="113" endOffset="67"/></Target><Target id="@+id/switchAutoConnect" view="com.google.android.material.materialswitch.MaterialSwitch"><Expressions/><location startLine="146" startOffset="20" endLine="152" endOffset="67"/></Target><Target id="@+id/tvCurrentConfig" view="TextView"><Expressions/><location startLine="206" startOffset="24" endLine="212" endOffset="68"/></Target><Target id="@+id/btnSwitchConfig" view="com.google.android.material.button.MaterialButton"><Expressions/><location startLine="216" startOffset="20" endLine="221" endOffset="75"/></Target><Target id="@+id/btnCreateConfig" view="com.google.android.material.button.MaterialButton"><Expressions/><location startLine="232" startOffset="20" endLine="239" endOffset="79"/></Target><Target id="@+id/btnManageConfigs" view="com.google.android.material.button.MaterialButton"><Expressions/><location startLine="241" startOffset="20" endLine="248" endOffset="79"/></Target><Target id="@+id/btnCheckUpdate" view="com.google.android.material.button.MaterialButton"><Expressions/><location startLine="297" startOffset="16" endLine="302" endOffset="57"/></Target></Targets></Layout>