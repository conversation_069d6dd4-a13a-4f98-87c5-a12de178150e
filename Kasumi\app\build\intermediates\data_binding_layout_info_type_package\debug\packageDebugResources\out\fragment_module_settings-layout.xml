<?xml version="1.0" encoding="utf-8" standalone="yes"?><Layout layout="fragment_module_settings" modulePackage="mc.meson.kasumi" filePath="app\src\main\res\layout\fragment_module_settings.xml" directory="layout" isMerge="false" isBindingData="false" rootNodeType="android.widget.LinearLayout"><Targets><Target tag="layout/fragment_module_settings_0" view="LinearLayout"><Expressions/><location startLine="1" startOffset="0" endLine="189" endOffset="14"/></Target><Target id="@+id/btnBack" view="ImageButton"><Expressions/><location startLine="23" startOffset="12" endLine="30" endOffset="49"/></Target><Target id="@+id/tvModuleName" view="TextView"><Expressions/><location startLine="39" startOffset="16" endLine="46" endOffset="46"/></Target><Target id="@+id/tvModuleCategory" view="TextView"><Expressions/><location startLine="48" startOffset="16" endLine="55" endOffset="52"/></Target><Target id="@+id/switchModuleEnabled" view="com.google.android.material.materialswitch.MaterialSwitch"><Expressions/><location startLine="59" startOffset="12" endLine="64" endOffset="61"/></Target><Target id="@+id/ivModuleIcon" view="ImageView"><Expressions/><location startLine="86" startOffset="12" endLine="91" endOffset="56"/></Target><Target id="@+id/tvModuleDescription" view="TextView"><Expressions/><location startLine="100" startOffset="16" endLine="106" endOffset="69"/></Target><Target id="@+id/viewCategoryIndicator" view="View"><Expressions/><location startLine="110" startOffset="12" endLine="114" endOffset="57"/></Target><Target id="@+id/recyclerViewSettings" view="androidx.recyclerview.widget.RecyclerView"><Expressions/><location startLine="134" startOffset="12" endLine="138" endOffset="56"/></Target><Target id="@+id/tvNoSettings" view="LinearLayout"><Expressions/><location startLine="141" startOffset="12" endLine="174" endOffset="26"/></Target><Target id="@+id/btnResetSettings" view="com.google.android.material.button.MaterialButton"><Expressions/><location startLine="177" startOffset="12" endLine="183" endOffset="71"/></Target></Targets></Layout>