package mc.meson.kasumi

import android.os.Bundle
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.view.animation.AnimationUtils
import androidx.appcompat.app.AppCompatDelegate
import androidx.fragment.app.Fragment
import androidx.recyclerview.widget.LinearLayoutManager
import com.google.android.material.dialog.MaterialAlertDialogBuilder
import com.google.android.material.snackbar.Snackbar
import mc.meson.kasumi.databinding.FragmentSettingsBinding
import mc.meson.kasumi.databinding.DialogConfigurationManagerBinding
import mc.meson.kasumi.databinding.DialogCreateConfigurationBinding
import mc.meson.kasumi.databinding.DialogSelectConfigurationBinding
import mc.meson.kasumi.config.Configuration
import mc.meson.kasumi.config.ConfigurationAdapter
import mc.meson.kasumi.config.ConfigurationManager
import mc.meson.kasumi.config.ConfigurationSelectAdapter

class SettingsFragment : Fragment() {

    private var _binding: FragmentSettingsBinding? = null
    private val binding get() = _binding!!

    override fun onCreateView(
        inflater: LayoutInflater,
        container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View {
        _binding = FragmentSettingsBinding.inflate(inflater, container, false)
        return binding.root
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)

        // 先加载设置状态，再设置监听器，避免触发不必要的回调
        loadSettings()
        setupSwitchListeners()
        setupButtonListeners()
        setupConfigurationListeners()
        setupAnimations()
        updateCurrentConfigurationDisplay()
    }

    private fun setupSwitchListeners() {
        binding.switchDarkMode.setOnCheckedChangeListener { _, isChecked ->
            animateSwitch(binding.switchDarkMode)
            toggleDarkMode(isChecked)
        }

        binding.switchNotifications.setOnCheckedChangeListener { _, isChecked ->
            animateSwitch(binding.switchNotifications)
            showSettingToggle("通知", isChecked)
        }

        binding.switchAutoConnect.setOnCheckedChangeListener { _, isChecked ->
            animateSwitch(binding.switchAutoConnect)
            showSettingToggle("自动连接", isChecked)
        }
    }

    private fun setupButtonListeners() {
        binding.btnCheckUpdate.setOnClickListener {
            animateButton(it)
            checkForUpdates()
        }
    }

    private fun setupConfigurationListeners() {
        binding.btnSwitchConfig.setOnClickListener {
            animateButton(it)
            showConfigurationSelectDialog()
        }

        binding.btnCreateConfig.setOnClickListener {
            animateButton(it)
            showCreateConfigurationDialog()
        }

        binding.btnManageConfigs.setOnClickListener {
            animateButton(it)
            showConfigurationManagerDialog()
        }
    }

    private fun setupAnimations() {
        // Simple fade in animation for the entire view
        // Only animate on first load, not on navigation
        if (binding.root.alpha == 0f) {
            binding.root.alpha = 0f
            binding.root.animate()
                .alpha(1f)
                .setDuration(300)
                .setStartDelay(50)
                .start()
        }
    }

    private fun loadSettings() {
        // Load current dark mode state
        val currentNightMode = AppCompatDelegate.getDefaultNightMode()
        binding.switchDarkMode.isChecked = when (currentNightMode) {
            AppCompatDelegate.MODE_NIGHT_YES -> true
            AppCompatDelegate.MODE_NIGHT_NO -> false
            else -> false // Follow system default
        }
    }

    private fun toggleDarkMode(enabled: Boolean) {
        val mode = if (enabled) {
            AppCompatDelegate.MODE_NIGHT_YES
        } else {
            AppCompatDelegate.MODE_NIGHT_NO
        }
        
        AppCompatDelegate.setDefaultNightMode(mode)
        showSettingToggle("深色模式", enabled)
    }

    private fun checkForUpdates() {
        // Simulate update check
        view?.postDelayed({
            Snackbar.make(binding.root, "您正在使用最新版本！", Snackbar.LENGTH_SHORT)
                .setBackgroundTint(requireContext().getColor(R.color.minecraft_green))
                .setTextColor(requireContext().getColor(R.color.white))
                .show()
        }, 1000)
        
        Snackbar.make(binding.root, "正在检查更新...", Snackbar.LENGTH_SHORT)
            .setBackgroundTint(requireContext().getColor(R.color.md_theme_light_primaryContainer))
            .setTextColor(requireContext().getColor(R.color.md_theme_light_onPrimaryContainer))
            .show()
    }

    private fun animateSwitch(switch: View) {
        switch.animate()
            .scaleX(1.1f)
            .scaleY(1.1f)
            .setDuration(100)
            .withEndAction {
                switch.animate()
                    .scaleX(1.0f)
                    .scaleY(1.0f)
                    .setDuration(100)
                    .start()
            }
            .start()
    }

    private fun animateButton(button: View) {
        button.animate()
            .scaleX(0.95f)
            .scaleY(0.95f)
            .setDuration(100)
            .withEndAction {
                button.animate()
                    .scaleX(1.0f)
                    .scaleY(1.0f)
                    .setDuration(100)
                    .start()
            }
            .start()
    }

    private fun showSettingToggle(settingName: String, enabled: Boolean) {
        // 移除 Snackbar 提示，保持界面简洁
        // 用户可以通过开关状态直接看到设置是否已启用
    }

    private fun updateCurrentConfigurationDisplay() {
        val currentConfig = ConfigurationManager.getCurrentConfiguration()
        binding.tvCurrentConfig.text = currentConfig?.name ?: "无配置"
    }

    private fun showConfigurationSelectDialog() {
        val dialogBinding = DialogSelectConfigurationBinding.inflate(layoutInflater)
        val dialog = MaterialAlertDialogBuilder(requireContext())
            .setView(dialogBinding.root)
            .create()

        val configurations = ConfigurationManager.getAllConfigurations()
        val currentConfigId = ConfigurationManager.getCurrentConfiguration()?.id

        val adapter = ConfigurationSelectAdapter(
            configurations = configurations,
            currentConfigId = currentConfigId,
            onConfigurationSelect = { config ->
                ConfigurationManager.applyConfiguration(config.id)
                updateCurrentConfigurationDisplay()
                dialog.dismiss()
                showSnackbar("已切换到配置: ${config.name}")
            }
        )

        dialogBinding.rvConfigurationSelect.layoutManager = LinearLayoutManager(requireContext())
        dialogBinding.rvConfigurationSelect.adapter = adapter

        dialogBinding.btnCancelSelect.setOnClickListener {
            dialog.dismiss()
        }

        dialog.show()
    }

    private fun showCreateConfigurationDialog() {
        val dialogBinding = DialogCreateConfigurationBinding.inflate(layoutInflater)
        val dialog = MaterialAlertDialogBuilder(requireContext())
            .setView(dialogBinding.root)
            .create()

        dialogBinding.btnCancel.setOnClickListener {
            dialog.dismiss()
        }

        dialogBinding.btnCreate.setOnClickListener {
            val name = dialogBinding.etConfigName.text.toString().trim()
            val description = dialogBinding.etConfigDescription.text.toString().trim()

            if (name.isEmpty()) {
                dialogBinding.etConfigName.error = "请输入配置名称"
                return@setOnClickListener
            }

            try {
                val config = ConfigurationManager.createConfiguration(name, description)
                updateCurrentConfigurationDisplay()
                dialog.dismiss()
                showSnackbar("配置 \"${config.name}\" 创建成功")
            } catch (e: Exception) {
                showSnackbar("创建配置失败: ${e.message}")
            }
        }

        dialog.show()
    }

    private fun showConfigurationManagerDialog() {
        val dialogBinding = DialogConfigurationManagerBinding.inflate(layoutInflater)
        val dialog = MaterialAlertDialogBuilder(requireContext())
            .setView(dialogBinding.root)
            .create()

        val configurations = ConfigurationManager.getAllConfigurations()
        val currentConfigId = ConfigurationManager.getCurrentConfiguration()?.id

        val adapter = ConfigurationAdapter(
            configurations = configurations,
            currentConfigId = currentConfigId,
            onConfigurationClick = { config ->
                // 点击配置项的处理
            },
            onConfigurationApply = { config ->
                ConfigurationManager.applyConfiguration(config.id)
                updateCurrentConfigurationDisplay()
                adapter.updateConfigurations(
                    ConfigurationManager.getAllConfigurations(),
                    config.id
                )
                showSnackbar("已应用配置: ${config.name}")
            },
            onConfigurationEdit = { config ->
                showEditConfigurationDialog(config) { updatedConfig ->
                    adapter.updateConfigurations(
                        ConfigurationManager.getAllConfigurations(),
                        ConfigurationManager.getCurrentConfiguration()?.id
                    )
                }
            },
            onConfigurationDelete = { config ->
                showDeleteConfigurationDialog(config) {
                    adapter.updateConfigurations(
                        ConfigurationManager.getAllConfigurations(),
                        ConfigurationManager.getCurrentConfiguration()?.id
                    )
                    updateCurrentConfigurationDisplay()
                }
            },
            onConfigurationSave = { config ->
                ConfigurationManager.saveCurrentStateToConfiguration(config.id)
                adapter.updateConfigurations(
                    ConfigurationManager.getAllConfigurations(),
                    ConfigurationManager.getCurrentConfiguration()?.id
                )
                showSnackbar("当前状态已保存到配置: ${config.name}")
            }
        )

        dialogBinding.rvConfigurations.layoutManager = LinearLayoutManager(requireContext())
        dialogBinding.rvConfigurations.adapter = adapter

        dialogBinding.btnImportConfig.setOnClickListener {
            // TODO: 实现导入功能
            showSnackbar("导入功能开发中...")
        }

        dialogBinding.btnExportConfig.setOnClickListener {
            // TODO: 实现导出功能
            showSnackbar("导出功能开发中...")
        }

        dialogBinding.btnCloseDialog.setOnClickListener {
            dialog.dismiss()
        }

        dialog.show()
    }

    private fun showEditConfigurationDialog(config: Configuration, onUpdated: (Configuration) -> Unit) {
        val dialogBinding = DialogCreateConfigurationBinding.inflate(layoutInflater)
        val dialog = MaterialAlertDialogBuilder(requireContext())
            .setView(dialogBinding.root)
            .create()

        // 预填充现有数据
        dialogBinding.etConfigName.setText(config.name)
        dialogBinding.etConfigDescription.setText(config.description)

        dialogBinding.btnCancel.setOnClickListener {
            dialog.dismiss()
        }

        dialogBinding.btnCreate.text = "保存"
        dialogBinding.btnCreate.setOnClickListener {
            val name = dialogBinding.etConfigName.text.toString().trim()
            val description = dialogBinding.etConfigDescription.text.toString().trim()

            if (name.isEmpty()) {
                dialogBinding.etConfigName.error = "请输入配置名称"
                return@setOnClickListener
            }

            try {
                val updatedConfig = config.copy(name = name, description = description)
                ConfigurationManager.updateConfiguration(updatedConfig)
                onUpdated(updatedConfig)
                dialog.dismiss()
                showSnackbar("配置已更新")
            } catch (e: Exception) {
                showSnackbar("更新配置失败: ${e.message}")
            }
        }

        dialog.show()
    }

    private fun showDeleteConfigurationDialog(config: Configuration, onDeleted: () -> Unit) {
        MaterialAlertDialogBuilder(requireContext())
            .setTitle("删除配置")
            .setMessage("确定要删除配置 \"${config.name}\" 吗？此操作无法撤销。")
            .setPositiveButton("删除") { _, _ ->
                if (ConfigurationManager.deleteConfiguration(config.id)) {
                    onDeleted()
                    showSnackbar("配置已删除")
                } else {
                    showSnackbar("无法删除默认配置")
                }
            }
            .setNegativeButton("取消", null)
            .show()
    }

    private fun showSnackbar(message: String) {
        Snackbar.make(binding.root, message, Snackbar.LENGTH_SHORT).show()
    }

    override fun onDestroyView() {
        super.onDestroyView()
        _binding = null
    }
}
