package mc.meson.kasumi

import android.os.Bundle
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.view.animation.AnimationUtils
import androidx.appcompat.app.AppCompatDelegate
import androidx.fragment.app.Fragment
import com.google.android.material.snackbar.Snackbar
import mc.meson.kasumi.databinding.FragmentSettingsBinding

class SettingsFragment : Fragment() {

    private var _binding: FragmentSettingsBinding? = null
    private val binding get() = _binding!!

    override fun onCreateView(
        inflater: LayoutInflater,
        container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View {
        _binding = FragmentSettingsBinding.inflate(inflater, container, false)
        return binding.root
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)

        // 先加载设置状态，再设置监听器，避免触发不必要的回调
        loadSettings()
        setupSwitchListeners()
        setupButtonListeners()
        setupAnimations()
    }

    private fun setupSwitchListeners() {
        binding.switchDarkMode.setOnCheckedChangeListener { _, isChecked ->
            animateSwitch(binding.switchDarkMode)
            toggleDarkMode(isChecked)
        }

        binding.switchNotifications.setOnCheckedChangeListener { _, isChecked ->
            animateSwitch(binding.switchNotifications)
            showSettingToggle("通知", isChecked)
        }

        binding.switchAutoConnect.setOnCheckedChangeListener { _, isChecked ->
            animateSwitch(binding.switchAutoConnect)
            showSettingToggle("自动连接", isChecked)
        }
    }

    private fun setupButtonListeners() {
        binding.btnCheckUpdate.setOnClickListener {
            animateButton(it)
            checkForUpdates()
        }
    }

    private fun setupAnimations() {
        // Simple fade in animation for the entire view
        // Only animate on first load, not on navigation
        if (binding.root.alpha == 0f) {
            binding.root.alpha = 0f
            binding.root.animate()
                .alpha(1f)
                .setDuration(300)
                .setStartDelay(50)
                .start()
        }
    }

    private fun loadSettings() {
        // Load current dark mode state
        val currentNightMode = AppCompatDelegate.getDefaultNightMode()
        binding.switchDarkMode.isChecked = when (currentNightMode) {
            AppCompatDelegate.MODE_NIGHT_YES -> true
            AppCompatDelegate.MODE_NIGHT_NO -> false
            else -> false // Follow system default
        }
    }

    private fun toggleDarkMode(enabled: Boolean) {
        val mode = if (enabled) {
            AppCompatDelegate.MODE_NIGHT_YES
        } else {
            AppCompatDelegate.MODE_NIGHT_NO
        }
        
        AppCompatDelegate.setDefaultNightMode(mode)
        showSettingToggle("深色模式", enabled)
    }

    private fun checkForUpdates() {
        // Simulate update check
        view?.postDelayed({
            Snackbar.make(binding.root, "您正在使用最新版本！", Snackbar.LENGTH_SHORT)
                .setBackgroundTint(requireContext().getColor(R.color.minecraft_green))
                .setTextColor(requireContext().getColor(R.color.white))
                .show()
        }, 1000)
        
        Snackbar.make(binding.root, "正在检查更新...", Snackbar.LENGTH_SHORT)
            .setBackgroundTint(requireContext().getColor(R.color.md_theme_light_primaryContainer))
            .setTextColor(requireContext().getColor(R.color.md_theme_light_onPrimaryContainer))
            .show()
    }

    private fun animateSwitch(switch: View) {
        switch.animate()
            .scaleX(1.1f)
            .scaleY(1.1f)
            .setDuration(100)
            .withEndAction {
                switch.animate()
                    .scaleX(1.0f)
                    .scaleY(1.0f)
                    .setDuration(100)
                    .start()
            }
            .start()
    }

    private fun animateButton(button: View) {
        button.animate()
            .scaleX(0.95f)
            .scaleY(0.95f)
            .setDuration(100)
            .withEndAction {
                button.animate()
                    .scaleX(1.0f)
                    .scaleY(1.0f)
                    .setDuration(100)
                    .start()
            }
            .start()
    }

    private fun showSettingToggle(settingName: String, enabled: Boolean) {
        // 移除 Snackbar 提示，保持界面简洁
        // 用户可以通过开关状态直接看到设置是否已启用
    }

    override fun onDestroyView() {
        super.onDestroyView()
        _binding = null
    }
}
