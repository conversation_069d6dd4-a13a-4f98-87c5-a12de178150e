-- Merging decision tree log ---
manifest
ADDED from D:\Kasumi\Kasumi\app\src\main\AndroidManifest.xml:2:1-52:12
INJECTED from D:\Ka<PERSON>mi\Kasumi\app\src\main\AndroidManifest.xml:2:1-52:12
INJECTED from D:\Kasumi\Kasumi\app\src\main\AndroidManifest.xml:2:1-52:12
INJECTED from D:\Kasumi\Kasumi\app\src\main\AndroidManifest.xml:2:1-52:12
MERGED from [androidx.databinding:viewbinding:8.5.1] C:\Users\<USER>\.gradle\caches\transforms-4\247dc4ee9be3d8f8242409cb0fcebea1\transformed\viewbinding-8.5.1\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.navigation:navigation-common:2.7.7] C:\Users\<USER>\.gradle\caches\transforms-4\10dc1e93663764fa06b58d8728fccd96\transformed\navigation-common-2.7.7\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.navigation:navigation-common-ktx:2.7.7] C:\Users\<USER>\.gradle\caches\transforms-4\8d6c7ea3af30ec440b513f9e52509518\transformed\navigation-common-ktx-2.7.7\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.navigation:navigation-runtime:2.7.7] C:\Users\<USER>\.gradle\caches\transforms-4\fa98777c5e63d6fc6f03b354d7681c04\transformed\navigation-runtime-2.7.7\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.navigation:navigation-runtime-ktx:2.7.7] C:\Users\<USER>\.gradle\caches\transforms-4\7618122027ee7987d9e084eb02a8fd03\transformed\navigation-runtime-ktx-2.7.7\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.navigation:navigation-fragment:2.7.7] C:\Users\<USER>\.gradle\caches\transforms-4\fbe55d87e179147488beb39081aed127\transformed\navigation-fragment-2.7.7\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.navigation:navigation-fragment-ktx:2.7.7] C:\Users\<USER>\.gradle\caches\transforms-4\e667f901dc6d0ef00df3175d7bd61c1c\transformed\navigation-fragment-ktx-2.7.7\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.navigation:navigation-ui-ktx:2.7.7] C:\Users\<USER>\.gradle\caches\transforms-4\919786330728eddfb7b27495d4da6d5a\transformed\navigation-ui-ktx-2.7.7\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.navigation:navigation-ui:2.7.7] C:\Users\<USER>\.gradle\caches\transforms-4\50962530c2d5164d93d0160cbaa0b0f7\transformed\navigation-ui-2.7.7\AndroidManifest.xml:17:1-22:12
MERGED from [com.google.android.material:material:1.12.0] C:\Users\<USER>\.gradle\caches\transforms-4\b81f8393eeb9e6264a611fd1f0fbf2f8\transformed\material-1.12.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.constraintlayout:constraintlayout:2.2.1] C:\Users\<USER>\.gradle\caches\transforms-4\b6817f59b67a51b427529b951685c92b\transformed\constraintlayout-2.2.1\AndroidManifest.xml:2:1-9:12
MERGED from [com.airbnb.android:lottie:6.4.0] C:\Users\<USER>\.gradle\caches\transforms-4\d57108f86e3241494e1cbcb6a06b0629\transformed\lottie-6.4.0\AndroidManifest.xml:2:1-9:12
MERGED from [androidx.appcompat:appcompat-resources:1.7.1] C:\Users\<USER>\.gradle\caches\transforms-4\3cdecb75963f4d9e3bbfef1630c35e85\transformed\appcompat-resources-1.7.1\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.appcompat:appcompat:1.7.1] C:\Users\<USER>\.gradle\caches\transforms-4\d40d1bbec92ff79897ca07a5833574ee\transformed\appcompat-1.7.1\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.recyclerview:recyclerview:1.3.2] C:\Users\<USER>\.gradle\caches\transforms-4\ec58bc64d225e090cd79e1363b35b5d0\transformed\recyclerview-1.3.2\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.viewpager2:viewpager2:1.1.0] C:\Users\<USER>\.gradle\caches\transforms-4\2457362645e078b0b754d97b2546c477\transformed\viewpager2-1.1.0\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.fragment:fragment-ktx:1.6.2] C:\Users\<USER>\.gradle\caches\transforms-4\193251ecbc4f3f03af0b4c7d9eecba70\transformed\fragment-ktx-1.6.2\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.fragment:fragment:1.6.2] C:\Users\<USER>\.gradle\caches\transforms-4\329f45c7e06125c196bff6af2d1dd567\transformed\fragment-1.6.2\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.activity:activity-ktx:1.8.0] C:\Users\<USER>\.gradle\caches\transforms-4\40f1e8118dad553bd22b5f0d28abf835\transformed\activity-ktx-1.8.0\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.activity:activity:1.8.0] C:\Users\<USER>\.gradle\caches\transforms-4\91e615aff14331f2f65f934995c0d81a\transformed\activity-1.8.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.customview:customview-poolingcontainer:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\0aeb273b9b43e305b9c6c367c84074f4\transformed\customview-poolingcontainer-1.0.0\AndroidManifest.xml:17:1-23:12
MERGED from [androidx.core:core-ktx:1.13.1] C:\Users\<USER>\.gradle\caches\transforms-4\69383c41f5e16d2764e7c21f619f40d6\transformed\core-ktx-1.13.1\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.emoji2:emoji2-views-helper:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-4\b084cc6ffcb0d3d3ba6d2c72f15b8e39\transformed\emoji2-views-helper-1.3.0\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-4\07782488d9d333cff68182712e97e36a\transformed\emoji2-1.3.0\AndroidManifest.xml:17:1-35:12
MERGED from [androidx.drawerlayout:drawerlayout:1.1.1] C:\Users\<USER>\.gradle\caches\transforms-4\e6cba0cecec11f0eede4754373dfa12a\transformed\drawerlayout-1.1.1\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.coordinatorlayout:coordinatorlayout:1.1.0] C:\Users\<USER>\.gradle\caches\transforms-4\74c541ccc28566c9e6bb4390d8e7e6ff\transformed\coordinatorlayout-1.1.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.slidingpanelayout:slidingpanelayout:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-4\007d08bde7a630c073b58646d668bd31\transformed\slidingpanelayout-1.2.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.transition:transition:1.5.0] C:\Users\<USER>\.gradle\caches\transforms-4\299d2c9394b73422e85cd4b2aeca2813\transformed\transition-1.5.0\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.dynamicanimation:dynamicanimation:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\9d5a2b0ad9b4fd0a35f51ce7a7f0a53a\transformed\dynamicanimation-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.vectordrawable:vectordrawable-animated:1.1.0] C:\Users\<USER>\.gradle\caches\transforms-4\e003cab6ae88c33e85d9f287f64fb6ed\transformed\vectordrawable-animated-1.1.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.vectordrawable:vectordrawable:1.1.0] C:\Users\<USER>\.gradle\caches\transforms-4\481fe64bccbb7071d98905fa6e8ee285\transformed\vectordrawable-1.1.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.legacy:legacy-support-core-utils:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\c11c56d4bd287aa5b6c6bc9f9e644bd1\transformed\legacy-support-core-utils-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.loader:loader:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\cc8cf2866a793a86bd728b9f2d10e5cc\transformed\loader-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.viewpager:viewpager:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\13a48dd3856ba8cf9467e0497c3a0a05\transformed\viewpager-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.customview:customview:1.1.0] C:\Users\<USER>\.gradle\caches\transforms-4\0223d1c8c13423018e0f00492479cab7\transformed\customview-1.1.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.window:window:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\8fb39156b81ea7e0af57d2eaba813d1e\transformed\window-1.0.0\AndroidManifest.xml:17:1-33:12
MERGED from [androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\transforms-4\3e36922ed6e480cd35722d6ef2c25ef7\transformed\core-1.13.1\AndroidManifest.xml:17:1-30:12
MERGED from [androidx.lifecycle:lifecycle-livedata-core:2.6.2] C:\Users\<USER>\.gradle\caches\transforms-4\cc8253833a2fc5744c6487e9fba7615d\transformed\lifecycle-livedata-core-2.6.2\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.lifecycle:lifecycle-runtime-ktx:2.6.2] C:\Users\<USER>\.gradle\caches\transforms-4\cb790eac636509db58d76c589a4b1b3d\transformed\lifecycle-runtime-ktx-2.6.2\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.lifecycle:lifecycle-viewmodel-ktx:2.6.2] C:\Users\<USER>\.gradle\caches\transforms-4\be4331682e506b3e9528268a165b06e7\transformed\lifecycle-viewmodel-ktx-2.6.2\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.lifecycle:lifecycle-livedata-core-ktx:2.6.2] C:\Users\<USER>\.gradle\caches\transforms-4\a4f066446be511c1dfd6d0da6eccb5e8\transformed\lifecycle-livedata-core-ktx-2.6.2\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.lifecycle:lifecycle-livedata:2.6.2] C:\Users\<USER>\.gradle\caches\transforms-4\48563ef32932d130e2c8d482eb1c0af4\transformed\lifecycle-livedata-2.6.2\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.lifecycle:lifecycle-viewmodel:2.6.2] C:\Users\<USER>\.gradle\caches\transforms-4\5a6cc01c00ef72a224ab77106f0a983d\transformed\lifecycle-viewmodel-2.6.2\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.lifecycle:lifecycle-process:2.6.2] C:\Users\<USER>\.gradle\caches\transforms-4\a81848633018603888e9437eeee1e7a0\transformed\lifecycle-process-2.6.2\AndroidManifest.xml:17:1-35:12
MERGED from [androidx.lifecycle:lifecycle-runtime:2.6.2] C:\Users\<USER>\.gradle\caches\transforms-4\d35bffe38b019ebd0d57ee6a1e9c62cb\transformed\lifecycle-runtime-2.6.2\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.lifecycle:lifecycle-viewmodel-savedstate:2.6.2] C:\Users\<USER>\.gradle\caches\transforms-4\1e8243ed7d11c0ef2f22be82b2b3016f\transformed\lifecycle-viewmodel-savedstate-2.6.2\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.savedstate:savedstate-ktx:1.2.1] C:\Users\<USER>\.gradle\caches\transforms-4\93735b939af6f758a2260579cbad569e\transformed\savedstate-ktx-1.2.1\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.savedstate:savedstate:1.2.1] C:\Users\<USER>\.gradle\caches\transforms-4\c455d48269e70dbdc6c937206bc85283\transformed\savedstate-1.2.1\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.cardview:cardview:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\5bfac2802553748cc92db19a3f21b6bf\transformed\cardview-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.cursoradapter:cursoradapter:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\2538a85b76fda9b085a69e7c235b0940\transformed\cursoradapter-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\transforms-4\e1aab3cc3064f8754a3c1233a3dc1ffc\transformed\profileinstaller-1.4.0\AndroidManifest.xml:17:1-55:12
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.1] C:\Users\<USER>\.gradle\caches\transforms-4\c375047ba81d00c01d5f9bac2dc2d373\transformed\versionedparcelable-1.1.1\AndroidManifest.xml:17:1-27:12
MERGED from [androidx.interpolator:interpolator:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\23d36ddf2969498845872f4cd0b43ddb\transformed\interpolator-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\transforms-4\7efada4acf2a3dcbf00161eda206d01c\transformed\startup-runtime-1.1.1\AndroidManifest.xml:17:1-33:12
MERGED from [androidx.tracing:tracing:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\7b15d87a2b514a5aca6faf36440d1630\transformed\tracing-1.0.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.arch.core:core-runtime:2.2.0] C:\Users\<USER>\.gradle\caches\transforms-4\7f3a69a693f7b9255153cb4b951c3719\transformed\core-runtime-2.2.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.documentfile:documentfile:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\18532dd40684edb3bc01f2430cf7f9c3\transformed\documentfile-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.localbroadcastmanager:localbroadcastmanager:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\1841ce48141661c5b7ae70cf83224341\transformed\localbroadcastmanager-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.print:print:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\e13dcd9b8f0fc12a8052a0d295468c42\transformed\print-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.annotation:annotation-experimental:1.4.0] C:\Users\<USER>\.gradle\caches\transforms-4\97e072479b2278be3920f441b01ae50a\transformed\annotation-experimental-1.4.0\AndroidManifest.xml:2:1-7:12
	package
		INJECTED from D:\Kasumi\Kasumi\app\src\main\AndroidManifest.xml
	android:versionName
		INJECTED from D:\Kasumi\Kasumi\app\src\main\AndroidManifest.xml
	xmlns:tools
		ADDED from D:\Kasumi\Kasumi\app\src\main\AndroidManifest.xml:3:5-51
	android:versionCode
		INJECTED from D:\Kasumi\Kasumi\app\src\main\AndroidManifest.xml
	xmlns:android
		ADDED from D:\Kasumi\Kasumi\app\src\main\AndroidManifest.xml:2:11-69
uses-permission#android.permission.SYSTEM_ALERT_WINDOW
ADDED from D:\Kasumi\Kasumi\app\src\main\AndroidManifest.xml:6:5-78
	android:name
		ADDED from D:\Kasumi\Kasumi\app\src\main\AndroidManifest.xml:6:22-75
uses-permission#android.permission.FOREGROUND_SERVICE
ADDED from D:\Kasumi\Kasumi\app\src\main\AndroidManifest.xml:7:5-77
	android:name
		ADDED from D:\Kasumi\Kasumi\app\src\main\AndroidManifest.xml:7:22-74
uses-permission#android.permission.FOREGROUND_SERVICE_SPECIAL_USE
ADDED from D:\Kasumi\Kasumi\app\src\main\AndroidManifest.xml:8:5-89
	android:name
		ADDED from D:\Kasumi\Kasumi\app\src\main\AndroidManifest.xml:8:22-86
uses-permission#android.permission.READ_EXTERNAL_STORAGE
ADDED from D:\Kasumi\Kasumi\app\src\main\AndroidManifest.xml:11:5-80
	android:name
		ADDED from D:\Kasumi\Kasumi\app\src\main\AndroidManifest.xml:11:22-77
uses-permission#android.permission.WRITE_EXTERNAL_STORAGE
ADDED from D:\Kasumi\Kasumi\app\src\main\AndroidManifest.xml:12:5-81
	android:name
		ADDED from D:\Kasumi\Kasumi\app\src\main\AndroidManifest.xml:12:22-78
application
ADDED from D:\Kasumi\Kasumi\app\src\main\AndroidManifest.xml:14:5-50:19
INJECTED from D:\Kasumi\Kasumi\app\src\main\AndroidManifest.xml:14:5-50:19
MERGED from [com.google.android.material:material:1.12.0] C:\Users\<USER>\.gradle\caches\transforms-4\b81f8393eeb9e6264a611fd1f0fbf2f8\transformed\material-1.12.0\AndroidManifest.xml:22:5-20
MERGED from [com.google.android.material:material:1.12.0] C:\Users\<USER>\.gradle\caches\transforms-4\b81f8393eeb9e6264a611fd1f0fbf2f8\transformed\material-1.12.0\AndroidManifest.xml:22:5-20
MERGED from [androidx.constraintlayout:constraintlayout:2.2.1] C:\Users\<USER>\.gradle\caches\transforms-4\b6817f59b67a51b427529b951685c92b\transformed\constraintlayout-2.2.1\AndroidManifest.xml:7:5-20
MERGED from [androidx.constraintlayout:constraintlayout:2.2.1] C:\Users\<USER>\.gradle\caches\transforms-4\b6817f59b67a51b427529b951685c92b\transformed\constraintlayout-2.2.1\AndroidManifest.xml:7:5-20
MERGED from [com.airbnb.android:lottie:6.4.0] C:\Users\<USER>\.gradle\caches\transforms-4\d57108f86e3241494e1cbcb6a06b0629\transformed\lottie-6.4.0\AndroidManifest.xml:7:5-20
MERGED from [com.airbnb.android:lottie:6.4.0] C:\Users\<USER>\.gradle\caches\transforms-4\d57108f86e3241494e1cbcb6a06b0629\transformed\lottie-6.4.0\AndroidManifest.xml:7:5-20
MERGED from [androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-4\07782488d9d333cff68182712e97e36a\transformed\emoji2-1.3.0\AndroidManifest.xml:23:5-33:19
MERGED from [androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-4\07782488d9d333cff68182712e97e36a\transformed\emoji2-1.3.0\AndroidManifest.xml:23:5-33:19
MERGED from [androidx.window:window:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\8fb39156b81ea7e0af57d2eaba813d1e\transformed\window-1.0.0\AndroidManifest.xml:24:5-31:19
MERGED from [androidx.window:window:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\8fb39156b81ea7e0af57d2eaba813d1e\transformed\window-1.0.0\AndroidManifest.xml:24:5-31:19
MERGED from [androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\transforms-4\3e36922ed6e480cd35722d6ef2c25ef7\transformed\core-1.13.1\AndroidManifest.xml:28:5-89
MERGED from [androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\transforms-4\3e36922ed6e480cd35722d6ef2c25ef7\transformed\core-1.13.1\AndroidManifest.xml:28:5-89
MERGED from [androidx.lifecycle:lifecycle-process:2.6.2] C:\Users\<USER>\.gradle\caches\transforms-4\a81848633018603888e9437eeee1e7a0\transformed\lifecycle-process-2.6.2\AndroidManifest.xml:23:5-33:19
MERGED from [androidx.lifecycle:lifecycle-process:2.6.2] C:\Users\<USER>\.gradle\caches\transforms-4\a81848633018603888e9437eeee1e7a0\transformed\lifecycle-process-2.6.2\AndroidManifest.xml:23:5-33:19
MERGED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\transforms-4\e1aab3cc3064f8754a3c1233a3dc1ffc\transformed\profileinstaller-1.4.0\AndroidManifest.xml:23:5-53:19
MERGED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\transforms-4\e1aab3cc3064f8754a3c1233a3dc1ffc\transformed\profileinstaller-1.4.0\AndroidManifest.xml:23:5-53:19
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.1] C:\Users\<USER>\.gradle\caches\transforms-4\c375047ba81d00c01d5f9bac2dc2d373\transformed\versionedparcelable-1.1.1\AndroidManifest.xml:24:5-25:19
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.1] C:\Users\<USER>\.gradle\caches\transforms-4\c375047ba81d00c01d5f9bac2dc2d373\transformed\versionedparcelable-1.1.1\AndroidManifest.xml:24:5-25:19
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\transforms-4\7efada4acf2a3dcbf00161eda206d01c\transformed\startup-runtime-1.1.1\AndroidManifest.xml:25:5-31:19
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\transforms-4\7efada4acf2a3dcbf00161eda206d01c\transformed\startup-runtime-1.1.1\AndroidManifest.xml:25:5-31:19
	android:extractNativeLibs
		INJECTED from D:\Kasumi\Kasumi\app\src\main\AndroidManifest.xml
	android:appComponentFactory
		ADDED from [androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\transforms-4\3e36922ed6e480cd35722d6ef2c25ef7\transformed\core-1.13.1\AndroidManifest.xml:28:18-86
	android:supportsRtl
		ADDED from D:\Kasumi\Kasumi\app\src\main\AndroidManifest.xml:21:9-35
	android:label
		ADDED from D:\Kasumi\Kasumi\app\src\main\AndroidManifest.xml:19:9-41
	android:fullBackupContent
		ADDED from D:\Kasumi\Kasumi\app\src\main\AndroidManifest.xml:17:9-54
	android:roundIcon
		ADDED from D:\Kasumi\Kasumi\app\src\main\AndroidManifest.xml:20:9-54
	tools:targetApi
		ADDED from D:\Kasumi\Kasumi\app\src\main\AndroidManifest.xml:23:9-29
	android:icon
		ADDED from D:\Kasumi\Kasumi\app\src\main\AndroidManifest.xml:18:9-43
	android:allowBackup
		ADDED from D:\Kasumi\Kasumi\app\src\main\AndroidManifest.xml:15:9-35
	android:theme
		ADDED from D:\Kasumi\Kasumi\app\src\main\AndroidManifest.xml:22:9-44
	android:dataExtractionRules
		ADDED from D:\Kasumi\Kasumi\app\src\main\AndroidManifest.xml:16:9-65
activity#mc.meson.kasumi.SplashActivity
ADDED from D:\Kasumi\Kasumi\app\src\main\AndroidManifest.xml:24:9-33:20
	android:exported
		ADDED from D:\Kasumi\Kasumi\app\src\main\AndroidManifest.xml:26:13-36
	android:theme
		ADDED from D:\Kasumi\Kasumi\app\src\main\AndroidManifest.xml:27:13-48
	android:name
		ADDED from D:\Kasumi\Kasumi\app\src\main\AndroidManifest.xml:25:13-43
intent-filter#action:name:android.intent.action.MAIN+category:name:android.intent.category.LAUNCHER
ADDED from D:\Kasumi\Kasumi\app\src\main\AndroidManifest.xml:28:13-32:29
action#android.intent.action.MAIN
ADDED from D:\Kasumi\Kasumi\app\src\main\AndroidManifest.xml:29:17-69
	android:name
		ADDED from D:\Kasumi\Kasumi\app\src\main\AndroidManifest.xml:29:25-66
category#android.intent.category.LAUNCHER
ADDED from D:\Kasumi\Kasumi\app\src\main\AndroidManifest.xml:31:17-77
	android:name
		ADDED from D:\Kasumi\Kasumi\app\src\main\AndroidManifest.xml:31:27-74
activity#mc.meson.kasumi.MainActivity
ADDED from D:\Kasumi\Kasumi\app\src\main\AndroidManifest.xml:35:9-38:51
	android:exported
		ADDED from D:\Kasumi\Kasumi\app\src\main\AndroidManifest.xml:37:13-37
	android:theme
		ADDED from D:\Kasumi\Kasumi\app\src\main\AndroidManifest.xml:38:13-48
	android:name
		ADDED from D:\Kasumi\Kasumi\app\src\main\AndroidManifest.xml:36:13-41
service#mc.meson.kasumi.overlay.FloatingWindowService
ADDED from D:\Kasumi\Kasumi\app\src\main\AndroidManifest.xml:41:9-49:19
	android:enabled
		ADDED from D:\Kasumi\Kasumi\app\src\main\AndroidManifest.xml:43:13-35
	android:exported
		ADDED from D:\Kasumi\Kasumi\app\src\main\AndroidManifest.xml:44:13-37
	android:foregroundServiceType
		ADDED from D:\Kasumi\Kasumi\app\src\main\AndroidManifest.xml:45:13-55
	android:name
		ADDED from D:\Kasumi\Kasumi\app\src\main\AndroidManifest.xml:42:13-58
property#android.app.PROPERTY_SPECIAL_USE_FGS_SUBTYPE
ADDED from D:\Kasumi\Kasumi\app\src\main\AndroidManifest.xml:46:13-48:43
	android:value
		ADDED from D:\Kasumi\Kasumi\app\src\main\AndroidManifest.xml:48:17-40
	android:name
		ADDED from D:\Kasumi\Kasumi\app\src\main\AndroidManifest.xml:47:17-76
uses-sdk
INJECTED from D:\Kasumi\Kasumi\app\src\main\AndroidManifest.xml reason: use-sdk injection requested
INJECTED from D:\Kasumi\Kasumi\app\src\main\AndroidManifest.xml
INJECTED from D:\Kasumi\Kasumi\app\src\main\AndroidManifest.xml
MERGED from [androidx.databinding:viewbinding:8.5.1] C:\Users\<USER>\.gradle\caches\transforms-4\247dc4ee9be3d8f8242409cb0fcebea1\transformed\viewbinding-8.5.1\AndroidManifest.xml:5:5-44
MERGED from [androidx.databinding:viewbinding:8.5.1] C:\Users\<USER>\.gradle\caches\transforms-4\247dc4ee9be3d8f8242409cb0fcebea1\transformed\viewbinding-8.5.1\AndroidManifest.xml:5:5-44
MERGED from [androidx.navigation:navigation-common:2.7.7] C:\Users\<USER>\.gradle\caches\transforms-4\10dc1e93663764fa06b58d8728fccd96\transformed\navigation-common-2.7.7\AndroidManifest.xml:20:5-44
MERGED from [androidx.navigation:navigation-common:2.7.7] C:\Users\<USER>\.gradle\caches\transforms-4\10dc1e93663764fa06b58d8728fccd96\transformed\navigation-common-2.7.7\AndroidManifest.xml:20:5-44
MERGED from [androidx.navigation:navigation-common-ktx:2.7.7] C:\Users\<USER>\.gradle\caches\transforms-4\8d6c7ea3af30ec440b513f9e52509518\transformed\navigation-common-ktx-2.7.7\AndroidManifest.xml:5:5-44
MERGED from [androidx.navigation:navigation-common-ktx:2.7.7] C:\Users\<USER>\.gradle\caches\transforms-4\8d6c7ea3af30ec440b513f9e52509518\transformed\navigation-common-ktx-2.7.7\AndroidManifest.xml:5:5-44
MERGED from [androidx.navigation:navigation-runtime:2.7.7] C:\Users\<USER>\.gradle\caches\transforms-4\fa98777c5e63d6fc6f03b354d7681c04\transformed\navigation-runtime-2.7.7\AndroidManifest.xml:20:5-44
MERGED from [androidx.navigation:navigation-runtime:2.7.7] C:\Users\<USER>\.gradle\caches\transforms-4\fa98777c5e63d6fc6f03b354d7681c04\transformed\navigation-runtime-2.7.7\AndroidManifest.xml:20:5-44
MERGED from [androidx.navigation:navigation-runtime-ktx:2.7.7] C:\Users\<USER>\.gradle\caches\transforms-4\7618122027ee7987d9e084eb02a8fd03\transformed\navigation-runtime-ktx-2.7.7\AndroidManifest.xml:5:5-44
MERGED from [androidx.navigation:navigation-runtime-ktx:2.7.7] C:\Users\<USER>\.gradle\caches\transforms-4\7618122027ee7987d9e084eb02a8fd03\transformed\navigation-runtime-ktx-2.7.7\AndroidManifest.xml:5:5-44
MERGED from [androidx.navigation:navigation-fragment:2.7.7] C:\Users\<USER>\.gradle\caches\transforms-4\fbe55d87e179147488beb39081aed127\transformed\navigation-fragment-2.7.7\AndroidManifest.xml:20:5-44
MERGED from [androidx.navigation:navigation-fragment:2.7.7] C:\Users\<USER>\.gradle\caches\transforms-4\fbe55d87e179147488beb39081aed127\transformed\navigation-fragment-2.7.7\AndroidManifest.xml:20:5-44
MERGED from [androidx.navigation:navigation-fragment-ktx:2.7.7] C:\Users\<USER>\.gradle\caches\transforms-4\e667f901dc6d0ef00df3175d7bd61c1c\transformed\navigation-fragment-ktx-2.7.7\AndroidManifest.xml:5:5-44
MERGED from [androidx.navigation:navigation-fragment-ktx:2.7.7] C:\Users\<USER>\.gradle\caches\transforms-4\e667f901dc6d0ef00df3175d7bd61c1c\transformed\navigation-fragment-ktx-2.7.7\AndroidManifest.xml:5:5-44
MERGED from [androidx.navigation:navigation-ui-ktx:2.7.7] C:\Users\<USER>\.gradle\caches\transforms-4\919786330728eddfb7b27495d4da6d5a\transformed\navigation-ui-ktx-2.7.7\AndroidManifest.xml:5:5-44
MERGED from [androidx.navigation:navigation-ui-ktx:2.7.7] C:\Users\<USER>\.gradle\caches\transforms-4\919786330728eddfb7b27495d4da6d5a\transformed\navigation-ui-ktx-2.7.7\AndroidManifest.xml:5:5-44
MERGED from [androidx.navigation:navigation-ui:2.7.7] C:\Users\<USER>\.gradle\caches\transforms-4\50962530c2d5164d93d0160cbaa0b0f7\transformed\navigation-ui-2.7.7\AndroidManifest.xml:20:5-44
MERGED from [androidx.navigation:navigation-ui:2.7.7] C:\Users\<USER>\.gradle\caches\transforms-4\50962530c2d5164d93d0160cbaa0b0f7\transformed\navigation-ui-2.7.7\AndroidManifest.xml:20:5-44
MERGED from [com.google.android.material:material:1.12.0] C:\Users\<USER>\.gradle\caches\transforms-4\b81f8393eeb9e6264a611fd1f0fbf2f8\transformed\material-1.12.0\AndroidManifest.xml:20:5-44
MERGED from [com.google.android.material:material:1.12.0] C:\Users\<USER>\.gradle\caches\transforms-4\b81f8393eeb9e6264a611fd1f0fbf2f8\transformed\material-1.12.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.constraintlayout:constraintlayout:2.2.1] C:\Users\<USER>\.gradle\caches\transforms-4\b6817f59b67a51b427529b951685c92b\transformed\constraintlayout-2.2.1\AndroidManifest.xml:5:5-44
MERGED from [androidx.constraintlayout:constraintlayout:2.2.1] C:\Users\<USER>\.gradle\caches\transforms-4\b6817f59b67a51b427529b951685c92b\transformed\constraintlayout-2.2.1\AndroidManifest.xml:5:5-44
MERGED from [com.airbnb.android:lottie:6.4.0] C:\Users\<USER>\.gradle\caches\transforms-4\d57108f86e3241494e1cbcb6a06b0629\transformed\lottie-6.4.0\AndroidManifest.xml:5:5-44
MERGED from [com.airbnb.android:lottie:6.4.0] C:\Users\<USER>\.gradle\caches\transforms-4\d57108f86e3241494e1cbcb6a06b0629\transformed\lottie-6.4.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.appcompat:appcompat-resources:1.7.1] C:\Users\<USER>\.gradle\caches\transforms-4\3cdecb75963f4d9e3bbfef1630c35e85\transformed\appcompat-resources-1.7.1\AndroidManifest.xml:5:5-44
MERGED from [androidx.appcompat:appcompat-resources:1.7.1] C:\Users\<USER>\.gradle\caches\transforms-4\3cdecb75963f4d9e3bbfef1630c35e85\transformed\appcompat-resources-1.7.1\AndroidManifest.xml:5:5-44
MERGED from [androidx.appcompat:appcompat:1.7.1] C:\Users\<USER>\.gradle\caches\transforms-4\d40d1bbec92ff79897ca07a5833574ee\transformed\appcompat-1.7.1\AndroidManifest.xml:5:5-44
MERGED from [androidx.appcompat:appcompat:1.7.1] C:\Users\<USER>\.gradle\caches\transforms-4\d40d1bbec92ff79897ca07a5833574ee\transformed\appcompat-1.7.1\AndroidManifest.xml:5:5-44
MERGED from [androidx.recyclerview:recyclerview:1.3.2] C:\Users\<USER>\.gradle\caches\transforms-4\ec58bc64d225e090cd79e1363b35b5d0\transformed\recyclerview-1.3.2\AndroidManifest.xml:20:5-44
MERGED from [androidx.recyclerview:recyclerview:1.3.2] C:\Users\<USER>\.gradle\caches\transforms-4\ec58bc64d225e090cd79e1363b35b5d0\transformed\recyclerview-1.3.2\AndroidManifest.xml:20:5-44
MERGED from [androidx.viewpager2:viewpager2:1.1.0] C:\Users\<USER>\.gradle\caches\transforms-4\2457362645e078b0b754d97b2546c477\transformed\viewpager2-1.1.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.viewpager2:viewpager2:1.1.0] C:\Users\<USER>\.gradle\caches\transforms-4\2457362645e078b0b754d97b2546c477\transformed\viewpager2-1.1.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.fragment:fragment-ktx:1.6.2] C:\Users\<USER>\.gradle\caches\transforms-4\193251ecbc4f3f03af0b4c7d9eecba70\transformed\fragment-ktx-1.6.2\AndroidManifest.xml:20:5-44
MERGED from [androidx.fragment:fragment-ktx:1.6.2] C:\Users\<USER>\.gradle\caches\transforms-4\193251ecbc4f3f03af0b4c7d9eecba70\transformed\fragment-ktx-1.6.2\AndroidManifest.xml:20:5-44
MERGED from [androidx.fragment:fragment:1.6.2] C:\Users\<USER>\.gradle\caches\transforms-4\329f45c7e06125c196bff6af2d1dd567\transformed\fragment-1.6.2\AndroidManifest.xml:20:5-44
MERGED from [androidx.fragment:fragment:1.6.2] C:\Users\<USER>\.gradle\caches\transforms-4\329f45c7e06125c196bff6af2d1dd567\transformed\fragment-1.6.2\AndroidManifest.xml:20:5-44
MERGED from [androidx.activity:activity-ktx:1.8.0] C:\Users\<USER>\.gradle\caches\transforms-4\40f1e8118dad553bd22b5f0d28abf835\transformed\activity-ktx-1.8.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.activity:activity-ktx:1.8.0] C:\Users\<USER>\.gradle\caches\transforms-4\40f1e8118dad553bd22b5f0d28abf835\transformed\activity-ktx-1.8.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.activity:activity:1.8.0] C:\Users\<USER>\.gradle\caches\transforms-4\91e615aff14331f2f65f934995c0d81a\transformed\activity-1.8.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.activity:activity:1.8.0] C:\Users\<USER>\.gradle\caches\transforms-4\91e615aff14331f2f65f934995c0d81a\transformed\activity-1.8.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.customview:customview-poolingcontainer:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\0aeb273b9b43e305b9c6c367c84074f4\transformed\customview-poolingcontainer-1.0.0\AndroidManifest.xml:20:5-21:38
MERGED from [androidx.customview:customview-poolingcontainer:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\0aeb273b9b43e305b9c6c367c84074f4\transformed\customview-poolingcontainer-1.0.0\AndroidManifest.xml:20:5-21:38
MERGED from [androidx.core:core-ktx:1.13.1] C:\Users\<USER>\.gradle\caches\transforms-4\69383c41f5e16d2764e7c21f619f40d6\transformed\core-ktx-1.13.1\AndroidManifest.xml:5:5-44
MERGED from [androidx.core:core-ktx:1.13.1] C:\Users\<USER>\.gradle\caches\transforms-4\69383c41f5e16d2764e7c21f619f40d6\transformed\core-ktx-1.13.1\AndroidManifest.xml:5:5-44
MERGED from [androidx.emoji2:emoji2-views-helper:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-4\b084cc6ffcb0d3d3ba6d2c72f15b8e39\transformed\emoji2-views-helper-1.3.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.emoji2:emoji2-views-helper:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-4\b084cc6ffcb0d3d3ba6d2c72f15b8e39\transformed\emoji2-views-helper-1.3.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-4\07782488d9d333cff68182712e97e36a\transformed\emoji2-1.3.0\AndroidManifest.xml:21:5-44
MERGED from [androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-4\07782488d9d333cff68182712e97e36a\transformed\emoji2-1.3.0\AndroidManifest.xml:21:5-44
MERGED from [androidx.drawerlayout:drawerlayout:1.1.1] C:\Users\<USER>\.gradle\caches\transforms-4\e6cba0cecec11f0eede4754373dfa12a\transformed\drawerlayout-1.1.1\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.drawerlayout:drawerlayout:1.1.1] C:\Users\<USER>\.gradle\caches\transforms-4\e6cba0cecec11f0eede4754373dfa12a\transformed\drawerlayout-1.1.1\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.coordinatorlayout:coordinatorlayout:1.1.0] C:\Users\<USER>\.gradle\caches\transforms-4\74c541ccc28566c9e6bb4390d8e7e6ff\transformed\coordinatorlayout-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.coordinatorlayout:coordinatorlayout:1.1.0] C:\Users\<USER>\.gradle\caches\transforms-4\74c541ccc28566c9e6bb4390d8e7e6ff\transformed\coordinatorlayout-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.slidingpanelayout:slidingpanelayout:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-4\007d08bde7a630c073b58646d668bd31\transformed\slidingpanelayout-1.2.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.slidingpanelayout:slidingpanelayout:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-4\007d08bde7a630c073b58646d668bd31\transformed\slidingpanelayout-1.2.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.transition:transition:1.5.0] C:\Users\<USER>\.gradle\caches\transforms-4\299d2c9394b73422e85cd4b2aeca2813\transformed\transition-1.5.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.transition:transition:1.5.0] C:\Users\<USER>\.gradle\caches\transforms-4\299d2c9394b73422e85cd4b2aeca2813\transformed\transition-1.5.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.dynamicanimation:dynamicanimation:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\9d5a2b0ad9b4fd0a35f51ce7a7f0a53a\transformed\dynamicanimation-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.dynamicanimation:dynamicanimation:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\9d5a2b0ad9b4fd0a35f51ce7a7f0a53a\transformed\dynamicanimation-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.vectordrawable:vectordrawable-animated:1.1.0] C:\Users\<USER>\.gradle\caches\transforms-4\e003cab6ae88c33e85d9f287f64fb6ed\transformed\vectordrawable-animated-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.vectordrawable:vectordrawable-animated:1.1.0] C:\Users\<USER>\.gradle\caches\transforms-4\e003cab6ae88c33e85d9f287f64fb6ed\transformed\vectordrawable-animated-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.vectordrawable:vectordrawable:1.1.0] C:\Users\<USER>\.gradle\caches\transforms-4\481fe64bccbb7071d98905fa6e8ee285\transformed\vectordrawable-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.vectordrawable:vectordrawable:1.1.0] C:\Users\<USER>\.gradle\caches\transforms-4\481fe64bccbb7071d98905fa6e8ee285\transformed\vectordrawable-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.legacy:legacy-support-core-utils:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\c11c56d4bd287aa5b6c6bc9f9e644bd1\transformed\legacy-support-core-utils-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.legacy:legacy-support-core-utils:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\c11c56d4bd287aa5b6c6bc9f9e644bd1\transformed\legacy-support-core-utils-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.loader:loader:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\cc8cf2866a793a86bd728b9f2d10e5cc\transformed\loader-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.loader:loader:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\cc8cf2866a793a86bd728b9f2d10e5cc\transformed\loader-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.viewpager:viewpager:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\13a48dd3856ba8cf9467e0497c3a0a05\transformed\viewpager-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.viewpager:viewpager:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\13a48dd3856ba8cf9467e0497c3a0a05\transformed\viewpager-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.customview:customview:1.1.0] C:\Users\<USER>\.gradle\caches\transforms-4\0223d1c8c13423018e0f00492479cab7\transformed\customview-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.customview:customview:1.1.0] C:\Users\<USER>\.gradle\caches\transforms-4\0223d1c8c13423018e0f00492479cab7\transformed\customview-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.window:window:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\8fb39156b81ea7e0af57d2eaba813d1e\transformed\window-1.0.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.window:window:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\8fb39156b81ea7e0af57d2eaba813d1e\transformed\window-1.0.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\transforms-4\3e36922ed6e480cd35722d6ef2c25ef7\transformed\core-1.13.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\transforms-4\3e36922ed6e480cd35722d6ef2c25ef7\transformed\core-1.13.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata-core:2.6.2] C:\Users\<USER>\.gradle\caches\transforms-4\cc8253833a2fc5744c6487e9fba7615d\transformed\lifecycle-livedata-core-2.6.2\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata-core:2.6.2] C:\Users\<USER>\.gradle\caches\transforms-4\cc8253833a2fc5744c6487e9fba7615d\transformed\lifecycle-livedata-core-2.6.2\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-runtime-ktx:2.6.2] C:\Users\<USER>\.gradle\caches\transforms-4\cb790eac636509db58d76c589a4b1b3d\transformed\lifecycle-runtime-ktx-2.6.2\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-runtime-ktx:2.6.2] C:\Users\<USER>\.gradle\caches\transforms-4\cb790eac636509db58d76c589a4b1b3d\transformed\lifecycle-runtime-ktx-2.6.2\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel-ktx:2.6.2] C:\Users\<USER>\.gradle\caches\transforms-4\be4331682e506b3e9528268a165b06e7\transformed\lifecycle-viewmodel-ktx-2.6.2\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel-ktx:2.6.2] C:\Users\<USER>\.gradle\caches\transforms-4\be4331682e506b3e9528268a165b06e7\transformed\lifecycle-viewmodel-ktx-2.6.2\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata-core-ktx:2.6.2] C:\Users\<USER>\.gradle\caches\transforms-4\a4f066446be511c1dfd6d0da6eccb5e8\transformed\lifecycle-livedata-core-ktx-2.6.2\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata-core-ktx:2.6.2] C:\Users\<USER>\.gradle\caches\transforms-4\a4f066446be511c1dfd6d0da6eccb5e8\transformed\lifecycle-livedata-core-ktx-2.6.2\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata:2.6.2] C:\Users\<USER>\.gradle\caches\transforms-4\48563ef32932d130e2c8d482eb1c0af4\transformed\lifecycle-livedata-2.6.2\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata:2.6.2] C:\Users\<USER>\.gradle\caches\transforms-4\48563ef32932d130e2c8d482eb1c0af4\transformed\lifecycle-livedata-2.6.2\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel:2.6.2] C:\Users\<USER>\.gradle\caches\transforms-4\5a6cc01c00ef72a224ab77106f0a983d\transformed\lifecycle-viewmodel-2.6.2\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel:2.6.2] C:\Users\<USER>\.gradle\caches\transforms-4\5a6cc01c00ef72a224ab77106f0a983d\transformed\lifecycle-viewmodel-2.6.2\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-process:2.6.2] C:\Users\<USER>\.gradle\caches\transforms-4\a81848633018603888e9437eeee1e7a0\transformed\lifecycle-process-2.6.2\AndroidManifest.xml:21:5-44
MERGED from [androidx.lifecycle:lifecycle-process:2.6.2] C:\Users\<USER>\.gradle\caches\transforms-4\a81848633018603888e9437eeee1e7a0\transformed\lifecycle-process-2.6.2\AndroidManifest.xml:21:5-44
MERGED from [androidx.lifecycle:lifecycle-runtime:2.6.2] C:\Users\<USER>\.gradle\caches\transforms-4\d35bffe38b019ebd0d57ee6a1e9c62cb\transformed\lifecycle-runtime-2.6.2\AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-runtime:2.6.2] C:\Users\<USER>\.gradle\caches\transforms-4\d35bffe38b019ebd0d57ee6a1e9c62cb\transformed\lifecycle-runtime-2.6.2\AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel-savedstate:2.6.2] C:\Users\<USER>\.gradle\caches\transforms-4\1e8243ed7d11c0ef2f22be82b2b3016f\transformed\lifecycle-viewmodel-savedstate-2.6.2\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel-savedstate:2.6.2] C:\Users\<USER>\.gradle\caches\transforms-4\1e8243ed7d11c0ef2f22be82b2b3016f\transformed\lifecycle-viewmodel-savedstate-2.6.2\AndroidManifest.xml:20:5-44
MERGED from [androidx.savedstate:savedstate-ktx:1.2.1] C:\Users\<USER>\.gradle\caches\transforms-4\93735b939af6f758a2260579cbad569e\transformed\savedstate-ktx-1.2.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.savedstate:savedstate-ktx:1.2.1] C:\Users\<USER>\.gradle\caches\transforms-4\93735b939af6f758a2260579cbad569e\transformed\savedstate-ktx-1.2.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.savedstate:savedstate:1.2.1] C:\Users\<USER>\.gradle\caches\transforms-4\c455d48269e70dbdc6c937206bc85283\transformed\savedstate-1.2.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.savedstate:savedstate:1.2.1] C:\Users\<USER>\.gradle\caches\transforms-4\c455d48269e70dbdc6c937206bc85283\transformed\savedstate-1.2.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.cardview:cardview:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\5bfac2802553748cc92db19a3f21b6bf\transformed\cardview-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.cardview:cardview:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\5bfac2802553748cc92db19a3f21b6bf\transformed\cardview-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.cursoradapter:cursoradapter:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\2538a85b76fda9b085a69e7c235b0940\transformed\cursoradapter-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.cursoradapter:cursoradapter:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\2538a85b76fda9b085a69e7c235b0940\transformed\cursoradapter-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\transforms-4\e1aab3cc3064f8754a3c1233a3dc1ffc\transformed\profileinstaller-1.4.0\AndroidManifest.xml:21:5-44
MERGED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\transforms-4\e1aab3cc3064f8754a3c1233a3dc1ffc\transformed\profileinstaller-1.4.0\AndroidManifest.xml:21:5-44
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.1] C:\Users\<USER>\.gradle\caches\transforms-4\c375047ba81d00c01d5f9bac2dc2d373\transformed\versionedparcelable-1.1.1\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.1] C:\Users\<USER>\.gradle\caches\transforms-4\c375047ba81d00c01d5f9bac2dc2d373\transformed\versionedparcelable-1.1.1\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.interpolator:interpolator:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\23d36ddf2969498845872f4cd0b43ddb\transformed\interpolator-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.interpolator:interpolator:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\23d36ddf2969498845872f4cd0b43ddb\transformed\interpolator-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\transforms-4\7efada4acf2a3dcbf00161eda206d01c\transformed\startup-runtime-1.1.1\AndroidManifest.xml:21:5-23:41
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\transforms-4\7efada4acf2a3dcbf00161eda206d01c\transformed\startup-runtime-1.1.1\AndroidManifest.xml:21:5-23:41
MERGED from [androidx.tracing:tracing:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\7b15d87a2b514a5aca6faf36440d1630\transformed\tracing-1.0.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.tracing:tracing:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\7b15d87a2b514a5aca6faf36440d1630\transformed\tracing-1.0.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.arch.core:core-runtime:2.2.0] C:\Users\<USER>\.gradle\caches\transforms-4\7f3a69a693f7b9255153cb4b951c3719\transformed\core-runtime-2.2.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.arch.core:core-runtime:2.2.0] C:\Users\<USER>\.gradle\caches\transforms-4\7f3a69a693f7b9255153cb4b951c3719\transformed\core-runtime-2.2.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.documentfile:documentfile:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\18532dd40684edb3bc01f2430cf7f9c3\transformed\documentfile-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.documentfile:documentfile:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\18532dd40684edb3bc01f2430cf7f9c3\transformed\documentfile-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.localbroadcastmanager:localbroadcastmanager:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\1841ce48141661c5b7ae70cf83224341\transformed\localbroadcastmanager-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.localbroadcastmanager:localbroadcastmanager:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\1841ce48141661c5b7ae70cf83224341\transformed\localbroadcastmanager-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.print:print:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\e13dcd9b8f0fc12a8052a0d295468c42\transformed\print-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.print:print:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\e13dcd9b8f0fc12a8052a0d295468c42\transformed\print-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.annotation:annotation-experimental:1.4.0] C:\Users\<USER>\.gradle\caches\transforms-4\97e072479b2278be3920f441b01ae50a\transformed\annotation-experimental-1.4.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.annotation:annotation-experimental:1.4.0] C:\Users\<USER>\.gradle\caches\transforms-4\97e072479b2278be3920f441b01ae50a\transformed\annotation-experimental-1.4.0\AndroidManifest.xml:5:5-44
	android:targetSdkVersion
		INJECTED from D:\Kasumi\Kasumi\app\src\main\AndroidManifest.xml
	android:minSdkVersion
		INJECTED from D:\Kasumi\Kasumi\app\src\main\AndroidManifest.xml
provider#androidx.startup.InitializationProvider
ADDED from [androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-4\07782488d9d333cff68182712e97e36a\transformed\emoji2-1.3.0\AndroidManifest.xml:24:9-32:20
MERGED from [androidx.lifecycle:lifecycle-process:2.6.2] C:\Users\<USER>\.gradle\caches\transforms-4\a81848633018603888e9437eeee1e7a0\transformed\lifecycle-process-2.6.2\AndroidManifest.xml:24:9-32:20
MERGED from [androidx.lifecycle:lifecycle-process:2.6.2] C:\Users\<USER>\.gradle\caches\transforms-4\a81848633018603888e9437eeee1e7a0\transformed\lifecycle-process-2.6.2\AndroidManifest.xml:24:9-32:20
MERGED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\transforms-4\e1aab3cc3064f8754a3c1233a3dc1ffc\transformed\profileinstaller-1.4.0\AndroidManifest.xml:24:9-32:20
MERGED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\transforms-4\e1aab3cc3064f8754a3c1233a3dc1ffc\transformed\profileinstaller-1.4.0\AndroidManifest.xml:24:9-32:20
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\transforms-4\7efada4acf2a3dcbf00161eda206d01c\transformed\startup-runtime-1.1.1\AndroidManifest.xml:26:9-30:34
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\transforms-4\7efada4acf2a3dcbf00161eda206d01c\transformed\startup-runtime-1.1.1\AndroidManifest.xml:26:9-30:34
	tools:node
		ADDED from [androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-4\07782488d9d333cff68182712e97e36a\transformed\emoji2-1.3.0\AndroidManifest.xml:28:13-31
	android:authorities
		ADDED from [androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-4\07782488d9d333cff68182712e97e36a\transformed\emoji2-1.3.0\AndroidManifest.xml:26:13-68
	android:exported
		ADDED from [androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-4\07782488d9d333cff68182712e97e36a\transformed\emoji2-1.3.0\AndroidManifest.xml:27:13-37
	android:name
		ADDED from [androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-4\07782488d9d333cff68182712e97e36a\transformed\emoji2-1.3.0\AndroidManifest.xml:25:13-67
meta-data#androidx.emoji2.text.EmojiCompatInitializer
ADDED from [androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-4\07782488d9d333cff68182712e97e36a\transformed\emoji2-1.3.0\AndroidManifest.xml:29:13-31:52
	android:value
		ADDED from [androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-4\07782488d9d333cff68182712e97e36a\transformed\emoji2-1.3.0\AndroidManifest.xml:31:17-49
	android:name
		ADDED from [androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-4\07782488d9d333cff68182712e97e36a\transformed\emoji2-1.3.0\AndroidManifest.xml:30:17-75
uses-library#androidx.window.extensions
ADDED from [androidx.window:window:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\8fb39156b81ea7e0af57d2eaba813d1e\transformed\window-1.0.0\AndroidManifest.xml:25:9-27:40
	android:required
		ADDED from [androidx.window:window:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\8fb39156b81ea7e0af57d2eaba813d1e\transformed\window-1.0.0\AndroidManifest.xml:27:13-37
	android:name
		ADDED from [androidx.window:window:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\8fb39156b81ea7e0af57d2eaba813d1e\transformed\window-1.0.0\AndroidManifest.xml:26:13-54
uses-library#androidx.window.sidecar
ADDED from [androidx.window:window:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\8fb39156b81ea7e0af57d2eaba813d1e\transformed\window-1.0.0\AndroidManifest.xml:28:9-30:40
	android:required
		ADDED from [androidx.window:window:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\8fb39156b81ea7e0af57d2eaba813d1e\transformed\window-1.0.0\AndroidManifest.xml:30:13-37
	android:name
		ADDED from [androidx.window:window:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\8fb39156b81ea7e0af57d2eaba813d1e\transformed\window-1.0.0\AndroidManifest.xml:29:13-51
permission#${applicationId}.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION
ADDED from [androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\transforms-4\3e36922ed6e480cd35722d6ef2c25ef7\transformed\core-1.13.1\AndroidManifest.xml:22:5-24:47
	android:protectionLevel
		ADDED from [androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\transforms-4\3e36922ed6e480cd35722d6ef2c25ef7\transformed\core-1.13.1\AndroidManifest.xml:24:9-44
	android:name
		ADDED from [androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\transforms-4\3e36922ed6e480cd35722d6ef2c25ef7\transformed\core-1.13.1\AndroidManifest.xml:23:9-81
permission#mc.meson.kasumi.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION
ADDED from [androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\transforms-4\3e36922ed6e480cd35722d6ef2c25ef7\transformed\core-1.13.1\AndroidManifest.xml:22:5-24:47
	android:protectionLevel
		ADDED from [androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\transforms-4\3e36922ed6e480cd35722d6ef2c25ef7\transformed\core-1.13.1\AndroidManifest.xml:24:9-44
	android:name
		ADDED from [androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\transforms-4\3e36922ed6e480cd35722d6ef2c25ef7\transformed\core-1.13.1\AndroidManifest.xml:23:9-81
uses-permission#${applicationId}.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION
ADDED from [androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\transforms-4\3e36922ed6e480cd35722d6ef2c25ef7\transformed\core-1.13.1\AndroidManifest.xml:26:5-97
	android:name
		ADDED from [androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\transforms-4\3e36922ed6e480cd35722d6ef2c25ef7\transformed\core-1.13.1\AndroidManifest.xml:26:22-94
uses-permission#mc.meson.kasumi.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION
ADDED from [androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\transforms-4\3e36922ed6e480cd35722d6ef2c25ef7\transformed\core-1.13.1\AndroidManifest.xml:26:5-97
	android:name
		ADDED from [androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\transforms-4\3e36922ed6e480cd35722d6ef2c25ef7\transformed\core-1.13.1\AndroidManifest.xml:26:22-94
meta-data#androidx.lifecycle.ProcessLifecycleInitializer
ADDED from [androidx.lifecycle:lifecycle-process:2.6.2] C:\Users\<USER>\.gradle\caches\transforms-4\a81848633018603888e9437eeee1e7a0\transformed\lifecycle-process-2.6.2\AndroidManifest.xml:29:13-31:52
	android:value
		ADDED from [androidx.lifecycle:lifecycle-process:2.6.2] C:\Users\<USER>\.gradle\caches\transforms-4\a81848633018603888e9437eeee1e7a0\transformed\lifecycle-process-2.6.2\AndroidManifest.xml:31:17-49
	android:name
		ADDED from [androidx.lifecycle:lifecycle-process:2.6.2] C:\Users\<USER>\.gradle\caches\transforms-4\a81848633018603888e9437eeee1e7a0\transformed\lifecycle-process-2.6.2\AndroidManifest.xml:30:17-78
meta-data#androidx.profileinstaller.ProfileInstallerInitializer
ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\transforms-4\e1aab3cc3064f8754a3c1233a3dc1ffc\transformed\profileinstaller-1.4.0\AndroidManifest.xml:29:13-31:52
	android:value
		ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\transforms-4\e1aab3cc3064f8754a3c1233a3dc1ffc\transformed\profileinstaller-1.4.0\AndroidManifest.xml:31:17-49
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\transforms-4\e1aab3cc3064f8754a3c1233a3dc1ffc\transformed\profileinstaller-1.4.0\AndroidManifest.xml:30:17-85
receiver#androidx.profileinstaller.ProfileInstallReceiver
ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\transforms-4\e1aab3cc3064f8754a3c1233a3dc1ffc\transformed\profileinstaller-1.4.0\AndroidManifest.xml:34:9-52:20
	android:enabled
		ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\transforms-4\e1aab3cc3064f8754a3c1233a3dc1ffc\transformed\profileinstaller-1.4.0\AndroidManifest.xml:37:13-35
	android:exported
		ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\transforms-4\e1aab3cc3064f8754a3c1233a3dc1ffc\transformed\profileinstaller-1.4.0\AndroidManifest.xml:38:13-36
	android:permission
		ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\transforms-4\e1aab3cc3064f8754a3c1233a3dc1ffc\transformed\profileinstaller-1.4.0\AndroidManifest.xml:39:13-57
	android:directBootAware
		ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\transforms-4\e1aab3cc3064f8754a3c1233a3dc1ffc\transformed\profileinstaller-1.4.0\AndroidManifest.xml:36:13-44
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\transforms-4\e1aab3cc3064f8754a3c1233a3dc1ffc\transformed\profileinstaller-1.4.0\AndroidManifest.xml:35:13-76
intent-filter#action:name:androidx.profileinstaller.action.INSTALL_PROFILE
ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\transforms-4\e1aab3cc3064f8754a3c1233a3dc1ffc\transformed\profileinstaller-1.4.0\AndroidManifest.xml:40:13-42:29
action#androidx.profileinstaller.action.INSTALL_PROFILE
ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\transforms-4\e1aab3cc3064f8754a3c1233a3dc1ffc\transformed\profileinstaller-1.4.0\AndroidManifest.xml:41:17-91
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\transforms-4\e1aab3cc3064f8754a3c1233a3dc1ffc\transformed\profileinstaller-1.4.0\AndroidManifest.xml:41:25-88
intent-filter#action:name:androidx.profileinstaller.action.SKIP_FILE
ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\transforms-4\e1aab3cc3064f8754a3c1233a3dc1ffc\transformed\profileinstaller-1.4.0\AndroidManifest.xml:43:13-45:29
action#androidx.profileinstaller.action.SKIP_FILE
ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\transforms-4\e1aab3cc3064f8754a3c1233a3dc1ffc\transformed\profileinstaller-1.4.0\AndroidManifest.xml:44:17-85
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\transforms-4\e1aab3cc3064f8754a3c1233a3dc1ffc\transformed\profileinstaller-1.4.0\AndroidManifest.xml:44:25-82
intent-filter#action:name:androidx.profileinstaller.action.SAVE_PROFILE
ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\transforms-4\e1aab3cc3064f8754a3c1233a3dc1ffc\transformed\profileinstaller-1.4.0\AndroidManifest.xml:46:13-48:29
action#androidx.profileinstaller.action.SAVE_PROFILE
ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\transforms-4\e1aab3cc3064f8754a3c1233a3dc1ffc\transformed\profileinstaller-1.4.0\AndroidManifest.xml:47:17-88
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\transforms-4\e1aab3cc3064f8754a3c1233a3dc1ffc\transformed\profileinstaller-1.4.0\AndroidManifest.xml:47:25-85
intent-filter#action:name:androidx.profileinstaller.action.BENCHMARK_OPERATION
ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\transforms-4\e1aab3cc3064f8754a3c1233a3dc1ffc\transformed\profileinstaller-1.4.0\AndroidManifest.xml:49:13-51:29
action#androidx.profileinstaller.action.BENCHMARK_OPERATION
ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\transforms-4\e1aab3cc3064f8754a3c1233a3dc1ffc\transformed\profileinstaller-1.4.0\AndroidManifest.xml:50:17-95
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\transforms-4\e1aab3cc3064f8754a3c1233a3dc1ffc\transformed\profileinstaller-1.4.0\AndroidManifest.xml:50:25-92
