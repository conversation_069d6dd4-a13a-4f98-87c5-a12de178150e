-- Merging decision tree log ---
manifest
ADDED from D:\Kasumi\Kasumi\app\src\main\AndroidManifest.xml:2:1-48:12
INJECTED from D:\Kasumi\Kasumi\app\src\main\AndroidManifest.xml:2:1-48:12
INJECTED from D:\Kasumi\Kasumi\app\src\main\AndroidManifest.xml:2:1-48:12
INJECTED from D:\Kasumi\Kasumi\app\src\main\AndroidManifest.xml:2:1-48:12
MERGED from [androidx.databinding:viewbinding:8.5.1] C:\Users\<USER>\.gradle\caches\transforms-4\0b90e6dbf4fc53eab8bd500402f7bfd8\transformed\viewbinding-8.5.1\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.navigation:navigation-common:2.7.7] C:\Users\<USER>\.gradle\caches\transforms-4\3e3af742b4e0d50c37ea7a15219c1e11\transformed\navigation-common-2.7.7\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.navigation:navigation-common-ktx:2.7.7] C:\Users\<USER>\.gradle\caches\transforms-4\94b55576434fcba7f0ad310c31857b54\transformed\navigation-common-ktx-2.7.7\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.navigation:navigation-runtime:2.7.7] C:\Users\<USER>\.gradle\caches\transforms-4\01c0d5f050cfba4f54e075980312b21c\transformed\navigation-runtime-2.7.7\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.navigation:navigation-runtime-ktx:2.7.7] C:\Users\<USER>\.gradle\caches\transforms-4\e6ea45a0e5383ed3fd62c80f934293fa\transformed\navigation-runtime-ktx-2.7.7\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.navigation:navigation-fragment:2.7.7] C:\Users\<USER>\.gradle\caches\transforms-4\8139a02941db3bfde4266b373674502a\transformed\navigation-fragment-2.7.7\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.navigation:navigation-fragment-ktx:2.7.7] C:\Users\<USER>\.gradle\caches\transforms-4\a2ad1316d955421d884125defd617359\transformed\navigation-fragment-ktx-2.7.7\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.navigation:navigation-ui-ktx:2.7.7] C:\Users\<USER>\.gradle\caches\transforms-4\90b08ba35753dbfcdb620a6f3237c3d1\transformed\navigation-ui-ktx-2.7.7\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.navigation:navigation-ui:2.7.7] C:\Users\<USER>\.gradle\caches\transforms-4\a89c5b30c1d828c7966a5a2ed8a50c44\transformed\navigation-ui-2.7.7\AndroidManifest.xml:17:1-22:12
MERGED from [com.google.android.material:material:1.12.0] C:\Users\<USER>\.gradle\caches\transforms-4\136170213714cacfd7ecace89b580fe0\transformed\material-1.12.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.constraintlayout:constraintlayout:2.2.1] C:\Users\<USER>\.gradle\caches\transforms-4\7e3bfb1d38459672af89d97dcec49433\transformed\constraintlayout-2.2.1\AndroidManifest.xml:2:1-9:12
MERGED from [com.airbnb.android:lottie:6.4.0] C:\Users\<USER>\.gradle\caches\transforms-4\13fef2b40277ddde02c28769ea2d6868\transformed\lottie-6.4.0\AndroidManifest.xml:2:1-9:12
MERGED from [androidx.appcompat:appcompat-resources:1.7.1] C:\Users\<USER>\.gradle\caches\transforms-4\b1ddcb34c25c823d9814d6fa034a8d30\transformed\appcompat-resources-1.7.1\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.appcompat:appcompat:1.7.1] C:\Users\<USER>\.gradle\caches\transforms-4\08f039399b33dd00d18329b13ddd7a9b\transformed\appcompat-1.7.1\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.recyclerview:recyclerview:1.3.2] C:\Users\<USER>\.gradle\caches\transforms-4\5b03bd4c5dea1000e2f3f572911c67a5\transformed\recyclerview-1.3.2\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.viewpager2:viewpager2:1.1.0] C:\Users\<USER>\.gradle\caches\transforms-4\61aee3c830f56e5d697641d2f23f6525\transformed\viewpager2-1.1.0\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.fragment:fragment-ktx:1.6.2] C:\Users\<USER>\.gradle\caches\transforms-4\2195dd68d06dbb6adfbdc42996fd7942\transformed\fragment-ktx-1.6.2\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.fragment:fragment:1.6.2] C:\Users\<USER>\.gradle\caches\transforms-4\0121e956dc72cb39363f398cf6f0453c\transformed\fragment-1.6.2\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.activity:activity-ktx:1.8.0] C:\Users\<USER>\.gradle\caches\transforms-4\466faa36ccc979c5357677534d660367\transformed\activity-ktx-1.8.0\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.activity:activity:1.8.0] C:\Users\<USER>\.gradle\caches\transforms-4\29fec72801506d65a5e4a66ec884f101\transformed\activity-1.8.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.customview:customview-poolingcontainer:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\bdde67129c7495a20fdd8434ad07c65f\transformed\customview-poolingcontainer-1.0.0\AndroidManifest.xml:17:1-23:12
MERGED from [androidx.core:core-ktx:1.13.1] C:\Users\<USER>\.gradle\caches\transforms-4\d5ee7d04fdd6d6e8638ba867569c1212\transformed\core-ktx-1.13.1\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.emoji2:emoji2-views-helper:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-4\f3bc5f98da5c085b667576164185acf9\transformed\emoji2-views-helper-1.3.0\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-4\c310bda4973a4914bb88a2351a33a468\transformed\emoji2-1.3.0\AndroidManifest.xml:17:1-35:12
MERGED from [androidx.drawerlayout:drawerlayout:1.1.1] C:\Users\<USER>\.gradle\caches\transforms-4\13f291d3c6740fb5c0ec804dfccfe212\transformed\drawerlayout-1.1.1\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.coordinatorlayout:coordinatorlayout:1.1.0] C:\Users\<USER>\.gradle\caches\transforms-4\efdfbb074f7eab61472cac97eee57146\transformed\coordinatorlayout-1.1.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.slidingpanelayout:slidingpanelayout:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-4\009fe9ecd9decfdeb89262f63e633c99\transformed\slidingpanelayout-1.2.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.transition:transition:1.5.0] C:\Users\<USER>\.gradle\caches\transforms-4\0ecce52ad8539d8deb1e69788bd4e4f5\transformed\transition-1.5.0\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.dynamicanimation:dynamicanimation:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\c32b7a3d5963d736f5b131b894a83cc8\transformed\dynamicanimation-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.vectordrawable:vectordrawable-animated:1.1.0] C:\Users\<USER>\.gradle\caches\transforms-4\1019c80b0521dab6a772c4d3f4036b3f\transformed\vectordrawable-animated-1.1.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.vectordrawable:vectordrawable:1.1.0] C:\Users\<USER>\.gradle\caches\transforms-4\3de60a81d028d4da5c362cf9afa7d65f\transformed\vectordrawable-1.1.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.legacy:legacy-support-core-utils:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\8a7c97f1680e0ae40873b11904155c2f\transformed\legacy-support-core-utils-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.loader:loader:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\7fff4d047143f568f02ddf57bef2f7aa\transformed\loader-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.viewpager:viewpager:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\2bfad8ab37f492119a71f01935ed0e1c\transformed\viewpager-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.customview:customview:1.1.0] C:\Users\<USER>\.gradle\caches\transforms-4\db08b7ff43d1a4e616c547b6d81c5734\transformed\customview-1.1.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.window:window:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\a0307c1c436af89ef06094848403c6b0\transformed\window-1.0.0\AndroidManifest.xml:17:1-33:12
MERGED from [androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\transforms-4\e250c14d88b0412b0bb8e6f5e61db53c\transformed\core-1.13.1\AndroidManifest.xml:17:1-30:12
MERGED from [androidx.lifecycle:lifecycle-livedata-core:2.6.2] C:\Users\<USER>\.gradle\caches\transforms-4\d61a8afcc1f51589dbc672340a746b7b\transformed\lifecycle-livedata-core-2.6.2\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.lifecycle:lifecycle-runtime-ktx:2.6.2] C:\Users\<USER>\.gradle\caches\transforms-4\158726a0d1b80c693f7f7649b20c97f8\transformed\lifecycle-runtime-ktx-2.6.2\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.lifecycle:lifecycle-viewmodel-ktx:2.6.2] C:\Users\<USER>\.gradle\caches\transforms-4\2638af9abc8556fd3e2c8f61163f64ed\transformed\lifecycle-viewmodel-ktx-2.6.2\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.lifecycle:lifecycle-livedata-core-ktx:2.6.2] C:\Users\<USER>\.gradle\caches\transforms-4\bf6b0a4c93afcba9508e144e88d89e7b\transformed\lifecycle-livedata-core-ktx-2.6.2\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.lifecycle:lifecycle-livedata:2.6.2] C:\Users\<USER>\.gradle\caches\transforms-4\1b0f140c4c19e127cbc10f5ac6c788a8\transformed\lifecycle-livedata-2.6.2\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.lifecycle:lifecycle-viewmodel:2.6.2] C:\Users\<USER>\.gradle\caches\transforms-4\9db44c7f48aa0c59d252e0ec60da0e1f\transformed\lifecycle-viewmodel-2.6.2\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.lifecycle:lifecycle-process:2.6.2] C:\Users\<USER>\.gradle\caches\transforms-4\e3e332e1b9f1e898ad45bc679d5d5849\transformed\lifecycle-process-2.6.2\AndroidManifest.xml:17:1-35:12
MERGED from [androidx.lifecycle:lifecycle-runtime:2.6.2] C:\Users\<USER>\.gradle\caches\transforms-4\ef6cc62fb447ce0e5ef5c919efe14cf4\transformed\lifecycle-runtime-2.6.2\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.lifecycle:lifecycle-viewmodel-savedstate:2.6.2] C:\Users\<USER>\.gradle\caches\transforms-4\1f0e1c1a9370dda5870e7f00101d5095\transformed\lifecycle-viewmodel-savedstate-2.6.2\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.savedstate:savedstate-ktx:1.2.1] C:\Users\<USER>\.gradle\caches\transforms-4\23961715d40addfc9e75cb715c7c330d\transformed\savedstate-ktx-1.2.1\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.savedstate:savedstate:1.2.1] C:\Users\<USER>\.gradle\caches\transforms-4\c09b2560939c697bd5c373599fad3aa5\transformed\savedstate-1.2.1\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.cardview:cardview:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\39b3b39344da7d5923cfa7a4285e0aa7\transformed\cardview-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.cursoradapter:cursoradapter:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\93a8fd68cf7f1548450d61539082e74f\transformed\cursoradapter-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\transforms-4\2d0c6c6c966d89531a3d7f8f6b718acd\transformed\profileinstaller-1.4.0\AndroidManifest.xml:17:1-55:12
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.1] C:\Users\<USER>\.gradle\caches\transforms-4\446d7a482b549e931bc7b3bf38376869\transformed\versionedparcelable-1.1.1\AndroidManifest.xml:17:1-27:12
MERGED from [androidx.interpolator:interpolator:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\4b755c4a5a64f8f5c8640f2f162f9d15\transformed\interpolator-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\transforms-4\f4e094cf3b18a97c7b2e4784f560d2e0\transformed\startup-runtime-1.1.1\AndroidManifest.xml:17:1-33:12
MERGED from [androidx.tracing:tracing:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\ae4f0f3cb8f1f1bbfd7f4361a9a90a5c\transformed\tracing-1.0.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.arch.core:core-runtime:2.2.0] C:\Users\<USER>\.gradle\caches\transforms-4\86be0c1e603a7654aec4a3a40c005105\transformed\core-runtime-2.2.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.documentfile:documentfile:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\a1f9588d2a935c67365b82396b76cfab\transformed\documentfile-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.localbroadcastmanager:localbroadcastmanager:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\f854ec6d5d9c00bbd4a993fc6e33ec31\transformed\localbroadcastmanager-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.print:print:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\cd99f583726ae035563bb0b8ab5d2576\transformed\print-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.annotation:annotation-experimental:1.4.0] C:\Users\<USER>\.gradle\caches\transforms-4\395e0fb9e0fa9cd931e74ef2c5b7c283\transformed\annotation-experimental-1.4.0\AndroidManifest.xml:2:1-7:12
	package
		INJECTED from D:\Kasumi\Kasumi\app\src\main\AndroidManifest.xml
	android:versionName
		INJECTED from D:\Kasumi\Kasumi\app\src\main\AndroidManifest.xml
	xmlns:tools
		ADDED from D:\Kasumi\Kasumi\app\src\main\AndroidManifest.xml:3:5-51
	android:versionCode
		INJECTED from D:\Kasumi\Kasumi\app\src\main\AndroidManifest.xml
	xmlns:android
		ADDED from D:\Kasumi\Kasumi\app\src\main\AndroidManifest.xml:2:11-69
uses-permission#android.permission.SYSTEM_ALERT_WINDOW
ADDED from D:\Kasumi\Kasumi\app\src\main\AndroidManifest.xml:6:5-78
	android:name
		ADDED from D:\Kasumi\Kasumi\app\src\main\AndroidManifest.xml:6:22-75
uses-permission#android.permission.FOREGROUND_SERVICE
ADDED from D:\Kasumi\Kasumi\app\src\main\AndroidManifest.xml:7:5-77
	android:name
		ADDED from D:\Kasumi\Kasumi\app\src\main\AndroidManifest.xml:7:22-74
uses-permission#android.permission.FOREGROUND_SERVICE_SPECIAL_USE
ADDED from D:\Kasumi\Kasumi\app\src\main\AndroidManifest.xml:8:5-89
	android:name
		ADDED from D:\Kasumi\Kasumi\app\src\main\AndroidManifest.xml:8:22-86
application
ADDED from D:\Kasumi\Kasumi\app\src\main\AndroidManifest.xml:10:5-46:19
INJECTED from D:\Kasumi\Kasumi\app\src\main\AndroidManifest.xml:10:5-46:19
MERGED from [com.google.android.material:material:1.12.0] C:\Users\<USER>\.gradle\caches\transforms-4\136170213714cacfd7ecace89b580fe0\transformed\material-1.12.0\AndroidManifest.xml:22:5-20
MERGED from [com.google.android.material:material:1.12.0] C:\Users\<USER>\.gradle\caches\transforms-4\136170213714cacfd7ecace89b580fe0\transformed\material-1.12.0\AndroidManifest.xml:22:5-20
MERGED from [androidx.constraintlayout:constraintlayout:2.2.1] C:\Users\<USER>\.gradle\caches\transforms-4\7e3bfb1d38459672af89d97dcec49433\transformed\constraintlayout-2.2.1\AndroidManifest.xml:7:5-20
MERGED from [androidx.constraintlayout:constraintlayout:2.2.1] C:\Users\<USER>\.gradle\caches\transforms-4\7e3bfb1d38459672af89d97dcec49433\transformed\constraintlayout-2.2.1\AndroidManifest.xml:7:5-20
MERGED from [com.airbnb.android:lottie:6.4.0] C:\Users\<USER>\.gradle\caches\transforms-4\13fef2b40277ddde02c28769ea2d6868\transformed\lottie-6.4.0\AndroidManifest.xml:7:5-20
MERGED from [com.airbnb.android:lottie:6.4.0] C:\Users\<USER>\.gradle\caches\transforms-4\13fef2b40277ddde02c28769ea2d6868\transformed\lottie-6.4.0\AndroidManifest.xml:7:5-20
MERGED from [androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-4\c310bda4973a4914bb88a2351a33a468\transformed\emoji2-1.3.0\AndroidManifest.xml:23:5-33:19
MERGED from [androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-4\c310bda4973a4914bb88a2351a33a468\transformed\emoji2-1.3.0\AndroidManifest.xml:23:5-33:19
MERGED from [androidx.window:window:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\a0307c1c436af89ef06094848403c6b0\transformed\window-1.0.0\AndroidManifest.xml:24:5-31:19
MERGED from [androidx.window:window:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\a0307c1c436af89ef06094848403c6b0\transformed\window-1.0.0\AndroidManifest.xml:24:5-31:19
MERGED from [androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\transforms-4\e250c14d88b0412b0bb8e6f5e61db53c\transformed\core-1.13.1\AndroidManifest.xml:28:5-89
MERGED from [androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\transforms-4\e250c14d88b0412b0bb8e6f5e61db53c\transformed\core-1.13.1\AndroidManifest.xml:28:5-89
MERGED from [androidx.lifecycle:lifecycle-process:2.6.2] C:\Users\<USER>\.gradle\caches\transforms-4\e3e332e1b9f1e898ad45bc679d5d5849\transformed\lifecycle-process-2.6.2\AndroidManifest.xml:23:5-33:19
MERGED from [androidx.lifecycle:lifecycle-process:2.6.2] C:\Users\<USER>\.gradle\caches\transforms-4\e3e332e1b9f1e898ad45bc679d5d5849\transformed\lifecycle-process-2.6.2\AndroidManifest.xml:23:5-33:19
MERGED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\transforms-4\2d0c6c6c966d89531a3d7f8f6b718acd\transformed\profileinstaller-1.4.0\AndroidManifest.xml:23:5-53:19
MERGED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\transforms-4\2d0c6c6c966d89531a3d7f8f6b718acd\transformed\profileinstaller-1.4.0\AndroidManifest.xml:23:5-53:19
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.1] C:\Users\<USER>\.gradle\caches\transforms-4\446d7a482b549e931bc7b3bf38376869\transformed\versionedparcelable-1.1.1\AndroidManifest.xml:24:5-25:19
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.1] C:\Users\<USER>\.gradle\caches\transforms-4\446d7a482b549e931bc7b3bf38376869\transformed\versionedparcelable-1.1.1\AndroidManifest.xml:24:5-25:19
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\transforms-4\f4e094cf3b18a97c7b2e4784f560d2e0\transformed\startup-runtime-1.1.1\AndroidManifest.xml:25:5-31:19
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\transforms-4\f4e094cf3b18a97c7b2e4784f560d2e0\transformed\startup-runtime-1.1.1\AndroidManifest.xml:25:5-31:19
	android:extractNativeLibs
		INJECTED from D:\Kasumi\Kasumi\app\src\main\AndroidManifest.xml
	android:appComponentFactory
		ADDED from [androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\transforms-4\e250c14d88b0412b0bb8e6f5e61db53c\transformed\core-1.13.1\AndroidManifest.xml:28:18-86
	android:supportsRtl
		ADDED from D:\Kasumi\Kasumi\app\src\main\AndroidManifest.xml:17:9-35
	android:label
		ADDED from D:\Kasumi\Kasumi\app\src\main\AndroidManifest.xml:15:9-41
	android:fullBackupContent
		ADDED from D:\Kasumi\Kasumi\app\src\main\AndroidManifest.xml:13:9-54
	android:roundIcon
		ADDED from D:\Kasumi\Kasumi\app\src\main\AndroidManifest.xml:16:9-54
	tools:targetApi
		ADDED from D:\Kasumi\Kasumi\app\src\main\AndroidManifest.xml:19:9-29
	android:icon
		ADDED from D:\Kasumi\Kasumi\app\src\main\AndroidManifest.xml:14:9-43
	android:allowBackup
		ADDED from D:\Kasumi\Kasumi\app\src\main\AndroidManifest.xml:11:9-35
	android:theme
		ADDED from D:\Kasumi\Kasumi\app\src\main\AndroidManifest.xml:18:9-44
	android:dataExtractionRules
		ADDED from D:\Kasumi\Kasumi\app\src\main\AndroidManifest.xml:12:9-65
activity#mc.meson.kasumi.SplashActivity
ADDED from D:\Kasumi\Kasumi\app\src\main\AndroidManifest.xml:20:9-29:20
	android:exported
		ADDED from D:\Kasumi\Kasumi\app\src\main\AndroidManifest.xml:22:13-36
	android:theme
		ADDED from D:\Kasumi\Kasumi\app\src\main\AndroidManifest.xml:23:13-48
	android:name
		ADDED from D:\Kasumi\Kasumi\app\src\main\AndroidManifest.xml:21:13-43
intent-filter#action:name:android.intent.action.MAIN+category:name:android.intent.category.LAUNCHER
ADDED from D:\Kasumi\Kasumi\app\src\main\AndroidManifest.xml:24:13-28:29
action#android.intent.action.MAIN
ADDED from D:\Kasumi\Kasumi\app\src\main\AndroidManifest.xml:25:17-69
	android:name
		ADDED from D:\Kasumi\Kasumi\app\src\main\AndroidManifest.xml:25:25-66
category#android.intent.category.LAUNCHER
ADDED from D:\Kasumi\Kasumi\app\src\main\AndroidManifest.xml:27:17-77
	android:name
		ADDED from D:\Kasumi\Kasumi\app\src\main\AndroidManifest.xml:27:27-74
activity#mc.meson.kasumi.MainActivity
ADDED from D:\Kasumi\Kasumi\app\src\main\AndroidManifest.xml:31:9-34:51
	android:exported
		ADDED from D:\Kasumi\Kasumi\app\src\main\AndroidManifest.xml:33:13-37
	android:theme
		ADDED from D:\Kasumi\Kasumi\app\src\main\AndroidManifest.xml:34:13-48
	android:name
		ADDED from D:\Kasumi\Kasumi\app\src\main\AndroidManifest.xml:32:13-41
service#mc.meson.kasumi.overlay.FloatingWindowService
ADDED from D:\Kasumi\Kasumi\app\src\main\AndroidManifest.xml:37:9-45:19
	android:enabled
		ADDED from D:\Kasumi\Kasumi\app\src\main\AndroidManifest.xml:39:13-35
	android:exported
		ADDED from D:\Kasumi\Kasumi\app\src\main\AndroidManifest.xml:40:13-37
	android:foregroundServiceType
		ADDED from D:\Kasumi\Kasumi\app\src\main\AndroidManifest.xml:41:13-55
	android:name
		ADDED from D:\Kasumi\Kasumi\app\src\main\AndroidManifest.xml:38:13-58
property#android.app.PROPERTY_SPECIAL_USE_FGS_SUBTYPE
ADDED from D:\Kasumi\Kasumi\app\src\main\AndroidManifest.xml:42:13-44:43
	android:value
		ADDED from D:\Kasumi\Kasumi\app\src\main\AndroidManifest.xml:44:17-40
	android:name
		ADDED from D:\Kasumi\Kasumi\app\src\main\AndroidManifest.xml:43:17-76
uses-sdk
INJECTED from D:\Kasumi\Kasumi\app\src\main\AndroidManifest.xml reason: use-sdk injection requested
INJECTED from D:\Kasumi\Kasumi\app\src\main\AndroidManifest.xml
INJECTED from D:\Kasumi\Kasumi\app\src\main\AndroidManifest.xml
MERGED from [androidx.databinding:viewbinding:8.5.1] C:\Users\<USER>\.gradle\caches\transforms-4\0b90e6dbf4fc53eab8bd500402f7bfd8\transformed\viewbinding-8.5.1\AndroidManifest.xml:5:5-44
MERGED from [androidx.databinding:viewbinding:8.5.1] C:\Users\<USER>\.gradle\caches\transforms-4\0b90e6dbf4fc53eab8bd500402f7bfd8\transformed\viewbinding-8.5.1\AndroidManifest.xml:5:5-44
MERGED from [androidx.navigation:navigation-common:2.7.7] C:\Users\<USER>\.gradle\caches\transforms-4\3e3af742b4e0d50c37ea7a15219c1e11\transformed\navigation-common-2.7.7\AndroidManifest.xml:20:5-44
MERGED from [androidx.navigation:navigation-common:2.7.7] C:\Users\<USER>\.gradle\caches\transforms-4\3e3af742b4e0d50c37ea7a15219c1e11\transformed\navigation-common-2.7.7\AndroidManifest.xml:20:5-44
MERGED from [androidx.navigation:navigation-common-ktx:2.7.7] C:\Users\<USER>\.gradle\caches\transforms-4\94b55576434fcba7f0ad310c31857b54\transformed\navigation-common-ktx-2.7.7\AndroidManifest.xml:5:5-44
MERGED from [androidx.navigation:navigation-common-ktx:2.7.7] C:\Users\<USER>\.gradle\caches\transforms-4\94b55576434fcba7f0ad310c31857b54\transformed\navigation-common-ktx-2.7.7\AndroidManifest.xml:5:5-44
MERGED from [androidx.navigation:navigation-runtime:2.7.7] C:\Users\<USER>\.gradle\caches\transforms-4\01c0d5f050cfba4f54e075980312b21c\transformed\navigation-runtime-2.7.7\AndroidManifest.xml:20:5-44
MERGED from [androidx.navigation:navigation-runtime:2.7.7] C:\Users\<USER>\.gradle\caches\transforms-4\01c0d5f050cfba4f54e075980312b21c\transformed\navigation-runtime-2.7.7\AndroidManifest.xml:20:5-44
MERGED from [androidx.navigation:navigation-runtime-ktx:2.7.7] C:\Users\<USER>\.gradle\caches\transforms-4\e6ea45a0e5383ed3fd62c80f934293fa\transformed\navigation-runtime-ktx-2.7.7\AndroidManifest.xml:5:5-44
MERGED from [androidx.navigation:navigation-runtime-ktx:2.7.7] C:\Users\<USER>\.gradle\caches\transforms-4\e6ea45a0e5383ed3fd62c80f934293fa\transformed\navigation-runtime-ktx-2.7.7\AndroidManifest.xml:5:5-44
MERGED from [androidx.navigation:navigation-fragment:2.7.7] C:\Users\<USER>\.gradle\caches\transforms-4\8139a02941db3bfde4266b373674502a\transformed\navigation-fragment-2.7.7\AndroidManifest.xml:20:5-44
MERGED from [androidx.navigation:navigation-fragment:2.7.7] C:\Users\<USER>\.gradle\caches\transforms-4\8139a02941db3bfde4266b373674502a\transformed\navigation-fragment-2.7.7\AndroidManifest.xml:20:5-44
MERGED from [androidx.navigation:navigation-fragment-ktx:2.7.7] C:\Users\<USER>\.gradle\caches\transforms-4\a2ad1316d955421d884125defd617359\transformed\navigation-fragment-ktx-2.7.7\AndroidManifest.xml:5:5-44
MERGED from [androidx.navigation:navigation-fragment-ktx:2.7.7] C:\Users\<USER>\.gradle\caches\transforms-4\a2ad1316d955421d884125defd617359\transformed\navigation-fragment-ktx-2.7.7\AndroidManifest.xml:5:5-44
MERGED from [androidx.navigation:navigation-ui-ktx:2.7.7] C:\Users\<USER>\.gradle\caches\transforms-4\90b08ba35753dbfcdb620a6f3237c3d1\transformed\navigation-ui-ktx-2.7.7\AndroidManifest.xml:5:5-44
MERGED from [androidx.navigation:navigation-ui-ktx:2.7.7] C:\Users\<USER>\.gradle\caches\transforms-4\90b08ba35753dbfcdb620a6f3237c3d1\transformed\navigation-ui-ktx-2.7.7\AndroidManifest.xml:5:5-44
MERGED from [androidx.navigation:navigation-ui:2.7.7] C:\Users\<USER>\.gradle\caches\transforms-4\a89c5b30c1d828c7966a5a2ed8a50c44\transformed\navigation-ui-2.7.7\AndroidManifest.xml:20:5-44
MERGED from [androidx.navigation:navigation-ui:2.7.7] C:\Users\<USER>\.gradle\caches\transforms-4\a89c5b30c1d828c7966a5a2ed8a50c44\transformed\navigation-ui-2.7.7\AndroidManifest.xml:20:5-44
MERGED from [com.google.android.material:material:1.12.0] C:\Users\<USER>\.gradle\caches\transforms-4\136170213714cacfd7ecace89b580fe0\transformed\material-1.12.0\AndroidManifest.xml:20:5-44
MERGED from [com.google.android.material:material:1.12.0] C:\Users\<USER>\.gradle\caches\transforms-4\136170213714cacfd7ecace89b580fe0\transformed\material-1.12.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.constraintlayout:constraintlayout:2.2.1] C:\Users\<USER>\.gradle\caches\transforms-4\7e3bfb1d38459672af89d97dcec49433\transformed\constraintlayout-2.2.1\AndroidManifest.xml:5:5-44
MERGED from [androidx.constraintlayout:constraintlayout:2.2.1] C:\Users\<USER>\.gradle\caches\transforms-4\7e3bfb1d38459672af89d97dcec49433\transformed\constraintlayout-2.2.1\AndroidManifest.xml:5:5-44
MERGED from [com.airbnb.android:lottie:6.4.0] C:\Users\<USER>\.gradle\caches\transforms-4\13fef2b40277ddde02c28769ea2d6868\transformed\lottie-6.4.0\AndroidManifest.xml:5:5-44
MERGED from [com.airbnb.android:lottie:6.4.0] C:\Users\<USER>\.gradle\caches\transforms-4\13fef2b40277ddde02c28769ea2d6868\transformed\lottie-6.4.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.appcompat:appcompat-resources:1.7.1] C:\Users\<USER>\.gradle\caches\transforms-4\b1ddcb34c25c823d9814d6fa034a8d30\transformed\appcompat-resources-1.7.1\AndroidManifest.xml:5:5-44
MERGED from [androidx.appcompat:appcompat-resources:1.7.1] C:\Users\<USER>\.gradle\caches\transforms-4\b1ddcb34c25c823d9814d6fa034a8d30\transformed\appcompat-resources-1.7.1\AndroidManifest.xml:5:5-44
MERGED from [androidx.appcompat:appcompat:1.7.1] C:\Users\<USER>\.gradle\caches\transforms-4\08f039399b33dd00d18329b13ddd7a9b\transformed\appcompat-1.7.1\AndroidManifest.xml:5:5-44
MERGED from [androidx.appcompat:appcompat:1.7.1] C:\Users\<USER>\.gradle\caches\transforms-4\08f039399b33dd00d18329b13ddd7a9b\transformed\appcompat-1.7.1\AndroidManifest.xml:5:5-44
MERGED from [androidx.recyclerview:recyclerview:1.3.2] C:\Users\<USER>\.gradle\caches\transforms-4\5b03bd4c5dea1000e2f3f572911c67a5\transformed\recyclerview-1.3.2\AndroidManifest.xml:20:5-44
MERGED from [androidx.recyclerview:recyclerview:1.3.2] C:\Users\<USER>\.gradle\caches\transforms-4\5b03bd4c5dea1000e2f3f572911c67a5\transformed\recyclerview-1.3.2\AndroidManifest.xml:20:5-44
MERGED from [androidx.viewpager2:viewpager2:1.1.0] C:\Users\<USER>\.gradle\caches\transforms-4\61aee3c830f56e5d697641d2f23f6525\transformed\viewpager2-1.1.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.viewpager2:viewpager2:1.1.0] C:\Users\<USER>\.gradle\caches\transforms-4\61aee3c830f56e5d697641d2f23f6525\transformed\viewpager2-1.1.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.fragment:fragment-ktx:1.6.2] C:\Users\<USER>\.gradle\caches\transforms-4\2195dd68d06dbb6adfbdc42996fd7942\transformed\fragment-ktx-1.6.2\AndroidManifest.xml:20:5-44
MERGED from [androidx.fragment:fragment-ktx:1.6.2] C:\Users\<USER>\.gradle\caches\transforms-4\2195dd68d06dbb6adfbdc42996fd7942\transformed\fragment-ktx-1.6.2\AndroidManifest.xml:20:5-44
MERGED from [androidx.fragment:fragment:1.6.2] C:\Users\<USER>\.gradle\caches\transforms-4\0121e956dc72cb39363f398cf6f0453c\transformed\fragment-1.6.2\AndroidManifest.xml:20:5-44
MERGED from [androidx.fragment:fragment:1.6.2] C:\Users\<USER>\.gradle\caches\transforms-4\0121e956dc72cb39363f398cf6f0453c\transformed\fragment-1.6.2\AndroidManifest.xml:20:5-44
MERGED from [androidx.activity:activity-ktx:1.8.0] C:\Users\<USER>\.gradle\caches\transforms-4\466faa36ccc979c5357677534d660367\transformed\activity-ktx-1.8.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.activity:activity-ktx:1.8.0] C:\Users\<USER>\.gradle\caches\transforms-4\466faa36ccc979c5357677534d660367\transformed\activity-ktx-1.8.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.activity:activity:1.8.0] C:\Users\<USER>\.gradle\caches\transforms-4\29fec72801506d65a5e4a66ec884f101\transformed\activity-1.8.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.activity:activity:1.8.0] C:\Users\<USER>\.gradle\caches\transforms-4\29fec72801506d65a5e4a66ec884f101\transformed\activity-1.8.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.customview:customview-poolingcontainer:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\bdde67129c7495a20fdd8434ad07c65f\transformed\customview-poolingcontainer-1.0.0\AndroidManifest.xml:20:5-21:38
MERGED from [androidx.customview:customview-poolingcontainer:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\bdde67129c7495a20fdd8434ad07c65f\transformed\customview-poolingcontainer-1.0.0\AndroidManifest.xml:20:5-21:38
MERGED from [androidx.core:core-ktx:1.13.1] C:\Users\<USER>\.gradle\caches\transforms-4\d5ee7d04fdd6d6e8638ba867569c1212\transformed\core-ktx-1.13.1\AndroidManifest.xml:5:5-44
MERGED from [androidx.core:core-ktx:1.13.1] C:\Users\<USER>\.gradle\caches\transforms-4\d5ee7d04fdd6d6e8638ba867569c1212\transformed\core-ktx-1.13.1\AndroidManifest.xml:5:5-44
MERGED from [androidx.emoji2:emoji2-views-helper:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-4\f3bc5f98da5c085b667576164185acf9\transformed\emoji2-views-helper-1.3.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.emoji2:emoji2-views-helper:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-4\f3bc5f98da5c085b667576164185acf9\transformed\emoji2-views-helper-1.3.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-4\c310bda4973a4914bb88a2351a33a468\transformed\emoji2-1.3.0\AndroidManifest.xml:21:5-44
MERGED from [androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-4\c310bda4973a4914bb88a2351a33a468\transformed\emoji2-1.3.0\AndroidManifest.xml:21:5-44
MERGED from [androidx.drawerlayout:drawerlayout:1.1.1] C:\Users\<USER>\.gradle\caches\transforms-4\13f291d3c6740fb5c0ec804dfccfe212\transformed\drawerlayout-1.1.1\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.drawerlayout:drawerlayout:1.1.1] C:\Users\<USER>\.gradle\caches\transforms-4\13f291d3c6740fb5c0ec804dfccfe212\transformed\drawerlayout-1.1.1\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.coordinatorlayout:coordinatorlayout:1.1.0] C:\Users\<USER>\.gradle\caches\transforms-4\efdfbb074f7eab61472cac97eee57146\transformed\coordinatorlayout-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.coordinatorlayout:coordinatorlayout:1.1.0] C:\Users\<USER>\.gradle\caches\transforms-4\efdfbb074f7eab61472cac97eee57146\transformed\coordinatorlayout-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.slidingpanelayout:slidingpanelayout:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-4\009fe9ecd9decfdeb89262f63e633c99\transformed\slidingpanelayout-1.2.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.slidingpanelayout:slidingpanelayout:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-4\009fe9ecd9decfdeb89262f63e633c99\transformed\slidingpanelayout-1.2.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.transition:transition:1.5.0] C:\Users\<USER>\.gradle\caches\transforms-4\0ecce52ad8539d8deb1e69788bd4e4f5\transformed\transition-1.5.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.transition:transition:1.5.0] C:\Users\<USER>\.gradle\caches\transforms-4\0ecce52ad8539d8deb1e69788bd4e4f5\transformed\transition-1.5.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.dynamicanimation:dynamicanimation:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\c32b7a3d5963d736f5b131b894a83cc8\transformed\dynamicanimation-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.dynamicanimation:dynamicanimation:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\c32b7a3d5963d736f5b131b894a83cc8\transformed\dynamicanimation-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.vectordrawable:vectordrawable-animated:1.1.0] C:\Users\<USER>\.gradle\caches\transforms-4\1019c80b0521dab6a772c4d3f4036b3f\transformed\vectordrawable-animated-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.vectordrawable:vectordrawable-animated:1.1.0] C:\Users\<USER>\.gradle\caches\transforms-4\1019c80b0521dab6a772c4d3f4036b3f\transformed\vectordrawable-animated-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.vectordrawable:vectordrawable:1.1.0] C:\Users\<USER>\.gradle\caches\transforms-4\3de60a81d028d4da5c362cf9afa7d65f\transformed\vectordrawable-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.vectordrawable:vectordrawable:1.1.0] C:\Users\<USER>\.gradle\caches\transforms-4\3de60a81d028d4da5c362cf9afa7d65f\transformed\vectordrawable-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.legacy:legacy-support-core-utils:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\8a7c97f1680e0ae40873b11904155c2f\transformed\legacy-support-core-utils-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.legacy:legacy-support-core-utils:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\8a7c97f1680e0ae40873b11904155c2f\transformed\legacy-support-core-utils-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.loader:loader:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\7fff4d047143f568f02ddf57bef2f7aa\transformed\loader-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.loader:loader:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\7fff4d047143f568f02ddf57bef2f7aa\transformed\loader-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.viewpager:viewpager:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\2bfad8ab37f492119a71f01935ed0e1c\transformed\viewpager-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.viewpager:viewpager:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\2bfad8ab37f492119a71f01935ed0e1c\transformed\viewpager-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.customview:customview:1.1.0] C:\Users\<USER>\.gradle\caches\transforms-4\db08b7ff43d1a4e616c547b6d81c5734\transformed\customview-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.customview:customview:1.1.0] C:\Users\<USER>\.gradle\caches\transforms-4\db08b7ff43d1a4e616c547b6d81c5734\transformed\customview-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.window:window:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\a0307c1c436af89ef06094848403c6b0\transformed\window-1.0.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.window:window:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\a0307c1c436af89ef06094848403c6b0\transformed\window-1.0.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\transforms-4\e250c14d88b0412b0bb8e6f5e61db53c\transformed\core-1.13.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\transforms-4\e250c14d88b0412b0bb8e6f5e61db53c\transformed\core-1.13.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata-core:2.6.2] C:\Users\<USER>\.gradle\caches\transforms-4\d61a8afcc1f51589dbc672340a746b7b\transformed\lifecycle-livedata-core-2.6.2\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata-core:2.6.2] C:\Users\<USER>\.gradle\caches\transforms-4\d61a8afcc1f51589dbc672340a746b7b\transformed\lifecycle-livedata-core-2.6.2\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-runtime-ktx:2.6.2] C:\Users\<USER>\.gradle\caches\transforms-4\158726a0d1b80c693f7f7649b20c97f8\transformed\lifecycle-runtime-ktx-2.6.2\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-runtime-ktx:2.6.2] C:\Users\<USER>\.gradle\caches\transforms-4\158726a0d1b80c693f7f7649b20c97f8\transformed\lifecycle-runtime-ktx-2.6.2\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel-ktx:2.6.2] C:\Users\<USER>\.gradle\caches\transforms-4\2638af9abc8556fd3e2c8f61163f64ed\transformed\lifecycle-viewmodel-ktx-2.6.2\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel-ktx:2.6.2] C:\Users\<USER>\.gradle\caches\transforms-4\2638af9abc8556fd3e2c8f61163f64ed\transformed\lifecycle-viewmodel-ktx-2.6.2\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata-core-ktx:2.6.2] C:\Users\<USER>\.gradle\caches\transforms-4\bf6b0a4c93afcba9508e144e88d89e7b\transformed\lifecycle-livedata-core-ktx-2.6.2\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata-core-ktx:2.6.2] C:\Users\<USER>\.gradle\caches\transforms-4\bf6b0a4c93afcba9508e144e88d89e7b\transformed\lifecycle-livedata-core-ktx-2.6.2\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata:2.6.2] C:\Users\<USER>\.gradle\caches\transforms-4\1b0f140c4c19e127cbc10f5ac6c788a8\transformed\lifecycle-livedata-2.6.2\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata:2.6.2] C:\Users\<USER>\.gradle\caches\transforms-4\1b0f140c4c19e127cbc10f5ac6c788a8\transformed\lifecycle-livedata-2.6.2\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel:2.6.2] C:\Users\<USER>\.gradle\caches\transforms-4\9db44c7f48aa0c59d252e0ec60da0e1f\transformed\lifecycle-viewmodel-2.6.2\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel:2.6.2] C:\Users\<USER>\.gradle\caches\transforms-4\9db44c7f48aa0c59d252e0ec60da0e1f\transformed\lifecycle-viewmodel-2.6.2\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-process:2.6.2] C:\Users\<USER>\.gradle\caches\transforms-4\e3e332e1b9f1e898ad45bc679d5d5849\transformed\lifecycle-process-2.6.2\AndroidManifest.xml:21:5-44
MERGED from [androidx.lifecycle:lifecycle-process:2.6.2] C:\Users\<USER>\.gradle\caches\transforms-4\e3e332e1b9f1e898ad45bc679d5d5849\transformed\lifecycle-process-2.6.2\AndroidManifest.xml:21:5-44
MERGED from [androidx.lifecycle:lifecycle-runtime:2.6.2] C:\Users\<USER>\.gradle\caches\transforms-4\ef6cc62fb447ce0e5ef5c919efe14cf4\transformed\lifecycle-runtime-2.6.2\AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-runtime:2.6.2] C:\Users\<USER>\.gradle\caches\transforms-4\ef6cc62fb447ce0e5ef5c919efe14cf4\transformed\lifecycle-runtime-2.6.2\AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel-savedstate:2.6.2] C:\Users\<USER>\.gradle\caches\transforms-4\1f0e1c1a9370dda5870e7f00101d5095\transformed\lifecycle-viewmodel-savedstate-2.6.2\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel-savedstate:2.6.2] C:\Users\<USER>\.gradle\caches\transforms-4\1f0e1c1a9370dda5870e7f00101d5095\transformed\lifecycle-viewmodel-savedstate-2.6.2\AndroidManifest.xml:20:5-44
MERGED from [androidx.savedstate:savedstate-ktx:1.2.1] C:\Users\<USER>\.gradle\caches\transforms-4\23961715d40addfc9e75cb715c7c330d\transformed\savedstate-ktx-1.2.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.savedstate:savedstate-ktx:1.2.1] C:\Users\<USER>\.gradle\caches\transforms-4\23961715d40addfc9e75cb715c7c330d\transformed\savedstate-ktx-1.2.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.savedstate:savedstate:1.2.1] C:\Users\<USER>\.gradle\caches\transforms-4\c09b2560939c697bd5c373599fad3aa5\transformed\savedstate-1.2.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.savedstate:savedstate:1.2.1] C:\Users\<USER>\.gradle\caches\transforms-4\c09b2560939c697bd5c373599fad3aa5\transformed\savedstate-1.2.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.cardview:cardview:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\39b3b39344da7d5923cfa7a4285e0aa7\transformed\cardview-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.cardview:cardview:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\39b3b39344da7d5923cfa7a4285e0aa7\transformed\cardview-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.cursoradapter:cursoradapter:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\93a8fd68cf7f1548450d61539082e74f\transformed\cursoradapter-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.cursoradapter:cursoradapter:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\93a8fd68cf7f1548450d61539082e74f\transformed\cursoradapter-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\transforms-4\2d0c6c6c966d89531a3d7f8f6b718acd\transformed\profileinstaller-1.4.0\AndroidManifest.xml:21:5-44
MERGED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\transforms-4\2d0c6c6c966d89531a3d7f8f6b718acd\transformed\profileinstaller-1.4.0\AndroidManifest.xml:21:5-44
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.1] C:\Users\<USER>\.gradle\caches\transforms-4\446d7a482b549e931bc7b3bf38376869\transformed\versionedparcelable-1.1.1\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.1] C:\Users\<USER>\.gradle\caches\transforms-4\446d7a482b549e931bc7b3bf38376869\transformed\versionedparcelable-1.1.1\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.interpolator:interpolator:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\4b755c4a5a64f8f5c8640f2f162f9d15\transformed\interpolator-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.interpolator:interpolator:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\4b755c4a5a64f8f5c8640f2f162f9d15\transformed\interpolator-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\transforms-4\f4e094cf3b18a97c7b2e4784f560d2e0\transformed\startup-runtime-1.1.1\AndroidManifest.xml:21:5-23:41
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\transforms-4\f4e094cf3b18a97c7b2e4784f560d2e0\transformed\startup-runtime-1.1.1\AndroidManifest.xml:21:5-23:41
MERGED from [androidx.tracing:tracing:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\ae4f0f3cb8f1f1bbfd7f4361a9a90a5c\transformed\tracing-1.0.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.tracing:tracing:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\ae4f0f3cb8f1f1bbfd7f4361a9a90a5c\transformed\tracing-1.0.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.arch.core:core-runtime:2.2.0] C:\Users\<USER>\.gradle\caches\transforms-4\86be0c1e603a7654aec4a3a40c005105\transformed\core-runtime-2.2.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.arch.core:core-runtime:2.2.0] C:\Users\<USER>\.gradle\caches\transforms-4\86be0c1e603a7654aec4a3a40c005105\transformed\core-runtime-2.2.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.documentfile:documentfile:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\a1f9588d2a935c67365b82396b76cfab\transformed\documentfile-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.documentfile:documentfile:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\a1f9588d2a935c67365b82396b76cfab\transformed\documentfile-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.localbroadcastmanager:localbroadcastmanager:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\f854ec6d5d9c00bbd4a993fc6e33ec31\transformed\localbroadcastmanager-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.localbroadcastmanager:localbroadcastmanager:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\f854ec6d5d9c00bbd4a993fc6e33ec31\transformed\localbroadcastmanager-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.print:print:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\cd99f583726ae035563bb0b8ab5d2576\transformed\print-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.print:print:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\cd99f583726ae035563bb0b8ab5d2576\transformed\print-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.annotation:annotation-experimental:1.4.0] C:\Users\<USER>\.gradle\caches\transforms-4\395e0fb9e0fa9cd931e74ef2c5b7c283\transformed\annotation-experimental-1.4.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.annotation:annotation-experimental:1.4.0] C:\Users\<USER>\.gradle\caches\transforms-4\395e0fb9e0fa9cd931e74ef2c5b7c283\transformed\annotation-experimental-1.4.0\AndroidManifest.xml:5:5-44
	android:targetSdkVersion
		INJECTED from D:\Kasumi\Kasumi\app\src\main\AndroidManifest.xml
	android:minSdkVersion
		INJECTED from D:\Kasumi\Kasumi\app\src\main\AndroidManifest.xml
provider#androidx.startup.InitializationProvider
ADDED from [androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-4\c310bda4973a4914bb88a2351a33a468\transformed\emoji2-1.3.0\AndroidManifest.xml:24:9-32:20
MERGED from [androidx.lifecycle:lifecycle-process:2.6.2] C:\Users\<USER>\.gradle\caches\transforms-4\e3e332e1b9f1e898ad45bc679d5d5849\transformed\lifecycle-process-2.6.2\AndroidManifest.xml:24:9-32:20
MERGED from [androidx.lifecycle:lifecycle-process:2.6.2] C:\Users\<USER>\.gradle\caches\transforms-4\e3e332e1b9f1e898ad45bc679d5d5849\transformed\lifecycle-process-2.6.2\AndroidManifest.xml:24:9-32:20
MERGED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\transforms-4\2d0c6c6c966d89531a3d7f8f6b718acd\transformed\profileinstaller-1.4.0\AndroidManifest.xml:24:9-32:20
MERGED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\transforms-4\2d0c6c6c966d89531a3d7f8f6b718acd\transformed\profileinstaller-1.4.0\AndroidManifest.xml:24:9-32:20
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\transforms-4\f4e094cf3b18a97c7b2e4784f560d2e0\transformed\startup-runtime-1.1.1\AndroidManifest.xml:26:9-30:34
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\transforms-4\f4e094cf3b18a97c7b2e4784f560d2e0\transformed\startup-runtime-1.1.1\AndroidManifest.xml:26:9-30:34
	tools:node
		ADDED from [androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-4\c310bda4973a4914bb88a2351a33a468\transformed\emoji2-1.3.0\AndroidManifest.xml:28:13-31
	android:authorities
		ADDED from [androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-4\c310bda4973a4914bb88a2351a33a468\transformed\emoji2-1.3.0\AndroidManifest.xml:26:13-68
	android:exported
		ADDED from [androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-4\c310bda4973a4914bb88a2351a33a468\transformed\emoji2-1.3.0\AndroidManifest.xml:27:13-37
	android:name
		ADDED from [androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-4\c310bda4973a4914bb88a2351a33a468\transformed\emoji2-1.3.0\AndroidManifest.xml:25:13-67
meta-data#androidx.emoji2.text.EmojiCompatInitializer
ADDED from [androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-4\c310bda4973a4914bb88a2351a33a468\transformed\emoji2-1.3.0\AndroidManifest.xml:29:13-31:52
	android:value
		ADDED from [androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-4\c310bda4973a4914bb88a2351a33a468\transformed\emoji2-1.3.0\AndroidManifest.xml:31:17-49
	android:name
		ADDED from [androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-4\c310bda4973a4914bb88a2351a33a468\transformed\emoji2-1.3.0\AndroidManifest.xml:30:17-75
uses-library#androidx.window.extensions
ADDED from [androidx.window:window:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\a0307c1c436af89ef06094848403c6b0\transformed\window-1.0.0\AndroidManifest.xml:25:9-27:40
	android:required
		ADDED from [androidx.window:window:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\a0307c1c436af89ef06094848403c6b0\transformed\window-1.0.0\AndroidManifest.xml:27:13-37
	android:name
		ADDED from [androidx.window:window:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\a0307c1c436af89ef06094848403c6b0\transformed\window-1.0.0\AndroidManifest.xml:26:13-54
uses-library#androidx.window.sidecar
ADDED from [androidx.window:window:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\a0307c1c436af89ef06094848403c6b0\transformed\window-1.0.0\AndroidManifest.xml:28:9-30:40
	android:required
		ADDED from [androidx.window:window:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\a0307c1c436af89ef06094848403c6b0\transformed\window-1.0.0\AndroidManifest.xml:30:13-37
	android:name
		ADDED from [androidx.window:window:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\a0307c1c436af89ef06094848403c6b0\transformed\window-1.0.0\AndroidManifest.xml:29:13-51
permission#${applicationId}.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION
ADDED from [androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\transforms-4\e250c14d88b0412b0bb8e6f5e61db53c\transformed\core-1.13.1\AndroidManifest.xml:22:5-24:47
	android:protectionLevel
		ADDED from [androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\transforms-4\e250c14d88b0412b0bb8e6f5e61db53c\transformed\core-1.13.1\AndroidManifest.xml:24:9-44
	android:name
		ADDED from [androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\transforms-4\e250c14d88b0412b0bb8e6f5e61db53c\transformed\core-1.13.1\AndroidManifest.xml:23:9-81
permission#mc.meson.kasumi.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION
ADDED from [androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\transforms-4\e250c14d88b0412b0bb8e6f5e61db53c\transformed\core-1.13.1\AndroidManifest.xml:22:5-24:47
	android:protectionLevel
		ADDED from [androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\transforms-4\e250c14d88b0412b0bb8e6f5e61db53c\transformed\core-1.13.1\AndroidManifest.xml:24:9-44
	android:name
		ADDED from [androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\transforms-4\e250c14d88b0412b0bb8e6f5e61db53c\transformed\core-1.13.1\AndroidManifest.xml:23:9-81
uses-permission#${applicationId}.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION
ADDED from [androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\transforms-4\e250c14d88b0412b0bb8e6f5e61db53c\transformed\core-1.13.1\AndroidManifest.xml:26:5-97
	android:name
		ADDED from [androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\transforms-4\e250c14d88b0412b0bb8e6f5e61db53c\transformed\core-1.13.1\AndroidManifest.xml:26:22-94
uses-permission#mc.meson.kasumi.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION
ADDED from [androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\transforms-4\e250c14d88b0412b0bb8e6f5e61db53c\transformed\core-1.13.1\AndroidManifest.xml:26:5-97
	android:name
		ADDED from [androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\transforms-4\e250c14d88b0412b0bb8e6f5e61db53c\transformed\core-1.13.1\AndroidManifest.xml:26:22-94
meta-data#androidx.lifecycle.ProcessLifecycleInitializer
ADDED from [androidx.lifecycle:lifecycle-process:2.6.2] C:\Users\<USER>\.gradle\caches\transforms-4\e3e332e1b9f1e898ad45bc679d5d5849\transformed\lifecycle-process-2.6.2\AndroidManifest.xml:29:13-31:52
	android:value
		ADDED from [androidx.lifecycle:lifecycle-process:2.6.2] C:\Users\<USER>\.gradle\caches\transforms-4\e3e332e1b9f1e898ad45bc679d5d5849\transformed\lifecycle-process-2.6.2\AndroidManifest.xml:31:17-49
	android:name
		ADDED from [androidx.lifecycle:lifecycle-process:2.6.2] C:\Users\<USER>\.gradle\caches\transforms-4\e3e332e1b9f1e898ad45bc679d5d5849\transformed\lifecycle-process-2.6.2\AndroidManifest.xml:30:17-78
meta-data#androidx.profileinstaller.ProfileInstallerInitializer
ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\transforms-4\2d0c6c6c966d89531a3d7f8f6b718acd\transformed\profileinstaller-1.4.0\AndroidManifest.xml:29:13-31:52
	android:value
		ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\transforms-4\2d0c6c6c966d89531a3d7f8f6b718acd\transformed\profileinstaller-1.4.0\AndroidManifest.xml:31:17-49
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\transforms-4\2d0c6c6c966d89531a3d7f8f6b718acd\transformed\profileinstaller-1.4.0\AndroidManifest.xml:30:17-85
receiver#androidx.profileinstaller.ProfileInstallReceiver
ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\transforms-4\2d0c6c6c966d89531a3d7f8f6b718acd\transformed\profileinstaller-1.4.0\AndroidManifest.xml:34:9-52:20
	android:enabled
		ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\transforms-4\2d0c6c6c966d89531a3d7f8f6b718acd\transformed\profileinstaller-1.4.0\AndroidManifest.xml:37:13-35
	android:exported
		ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\transforms-4\2d0c6c6c966d89531a3d7f8f6b718acd\transformed\profileinstaller-1.4.0\AndroidManifest.xml:38:13-36
	android:permission
		ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\transforms-4\2d0c6c6c966d89531a3d7f8f6b718acd\transformed\profileinstaller-1.4.0\AndroidManifest.xml:39:13-57
	android:directBootAware
		ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\transforms-4\2d0c6c6c966d89531a3d7f8f6b718acd\transformed\profileinstaller-1.4.0\AndroidManifest.xml:36:13-44
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\transforms-4\2d0c6c6c966d89531a3d7f8f6b718acd\transformed\profileinstaller-1.4.0\AndroidManifest.xml:35:13-76
intent-filter#action:name:androidx.profileinstaller.action.INSTALL_PROFILE
ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\transforms-4\2d0c6c6c966d89531a3d7f8f6b718acd\transformed\profileinstaller-1.4.0\AndroidManifest.xml:40:13-42:29
action#androidx.profileinstaller.action.INSTALL_PROFILE
ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\transforms-4\2d0c6c6c966d89531a3d7f8f6b718acd\transformed\profileinstaller-1.4.0\AndroidManifest.xml:41:17-91
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\transforms-4\2d0c6c6c966d89531a3d7f8f6b718acd\transformed\profileinstaller-1.4.0\AndroidManifest.xml:41:25-88
intent-filter#action:name:androidx.profileinstaller.action.SKIP_FILE
ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\transforms-4\2d0c6c6c966d89531a3d7f8f6b718acd\transformed\profileinstaller-1.4.0\AndroidManifest.xml:43:13-45:29
action#androidx.profileinstaller.action.SKIP_FILE
ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\transforms-4\2d0c6c6c966d89531a3d7f8f6b718acd\transformed\profileinstaller-1.4.0\AndroidManifest.xml:44:17-85
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\transforms-4\2d0c6c6c966d89531a3d7f8f6b718acd\transformed\profileinstaller-1.4.0\AndroidManifest.xml:44:25-82
intent-filter#action:name:androidx.profileinstaller.action.SAVE_PROFILE
ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\transforms-4\2d0c6c6c966d89531a3d7f8f6b718acd\transformed\profileinstaller-1.4.0\AndroidManifest.xml:46:13-48:29
action#androidx.profileinstaller.action.SAVE_PROFILE
ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\transforms-4\2d0c6c6c966d89531a3d7f8f6b718acd\transformed\profileinstaller-1.4.0\AndroidManifest.xml:47:17-88
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\transforms-4\2d0c6c6c966d89531a3d7f8f6b718acd\transformed\profileinstaller-1.4.0\AndroidManifest.xml:47:25-85
intent-filter#action:name:androidx.profileinstaller.action.BENCHMARK_OPERATION
ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\transforms-4\2d0c6c6c966d89531a3d7f8f6b718acd\transformed\profileinstaller-1.4.0\AndroidManifest.xml:49:13-51:29
action#androidx.profileinstaller.action.BENCHMARK_OPERATION
ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\transforms-4\2d0c6c6c966d89531a3d7f8f6b718acd\transformed\profileinstaller-1.4.0\AndroidManifest.xml:50:17-95
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\transforms-4\2d0c6c6c966d89531a3d7f8f6b718acd\transformed\profileinstaller-1.4.0\AndroidManifest.xml:50:25-92
