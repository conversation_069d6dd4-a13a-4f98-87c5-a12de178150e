-- Merging decision tree log ---
manifest
ADDED from D:\Kasumi\Kasumi\app\src\main\AndroidManifest.xml:2:1-48:12
INJECTED from D:\Kasumi\Kasumi\app\src\main\AndroidManifest.xml:2:1-48:12
INJECTED from D:\Kasumi\Kasumi\app\src\main\AndroidManifest.xml:2:1-48:12
INJECTED from D:\Kasumi\Kasumi\app\src\main\AndroidManifest.xml:2:1-48:12
MERGED from [androidx.databinding:viewbinding:8.5.1] C:\Users\<USER>\.gradle\caches\transforms-4\aa7080cbda2ff5d11287367dd8f68dac\transformed\viewbinding-8.5.1\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.navigation:navigation-common:2.7.7] C:\Users\<USER>\.gradle\caches\transforms-4\336596ec5c5dcb2677d966befead2bc6\transformed\navigation-common-2.7.7\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.navigation:navigation-common-ktx:2.7.7] C:\Users\<USER>\.gradle\caches\transforms-4\51fc843405451511a490dbb2b38704c3\transformed\navigation-common-ktx-2.7.7\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.navigation:navigation-runtime:2.7.7] C:\Users\<USER>\.gradle\caches\transforms-4\e7894dea0f9dcb1745b54e97c92cd70f\transformed\navigation-runtime-2.7.7\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.navigation:navigation-runtime-ktx:2.7.7] C:\Users\<USER>\.gradle\caches\transforms-4\2df91b3a20ae83b4662d1a63c07ec35b\transformed\navigation-runtime-ktx-2.7.7\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.navigation:navigation-fragment:2.7.7] C:\Users\<USER>\.gradle\caches\transforms-4\b3625fe41a785a8c2bd44b66308ff5dd\transformed\navigation-fragment-2.7.7\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.navigation:navigation-fragment-ktx:2.7.7] C:\Users\<USER>\.gradle\caches\transforms-4\3cca7ff51090028be8487e06fb7a7eeb\transformed\navigation-fragment-ktx-2.7.7\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.navigation:navigation-ui-ktx:2.7.7] C:\Users\<USER>\.gradle\caches\transforms-4\8cd550ee1f24c7904721807737096df1\transformed\navigation-ui-ktx-2.7.7\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.navigation:navigation-ui:2.7.7] C:\Users\<USER>\.gradle\caches\transforms-4\c6de447ab1a7b632fe56ac0b139a6541\transformed\navigation-ui-2.7.7\AndroidManifest.xml:17:1-22:12
MERGED from [com.google.android.material:material:1.12.0] C:\Users\<USER>\.gradle\caches\transforms-4\c1f1d66677a2054cc4b3d0f2744c5c9f\transformed\material-1.12.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.constraintlayout:constraintlayout:2.2.1] C:\Users\<USER>\.gradle\caches\transforms-4\c24a58053d5d09da3afc3522253a19b5\transformed\constraintlayout-2.2.1\AndroidManifest.xml:2:1-9:12
MERGED from [com.airbnb.android:lottie:6.4.0] C:\Users\<USER>\.gradle\caches\transforms-4\2ea84c82d93551d6c13140db0008e7fd\transformed\lottie-6.4.0\AndroidManifest.xml:2:1-9:12
MERGED from [androidx.appcompat:appcompat-resources:1.7.1] C:\Users\<USER>\.gradle\caches\transforms-4\12874f758f432c8d5694e1413102185e\transformed\appcompat-resources-1.7.1\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.appcompat:appcompat:1.7.1] C:\Users\<USER>\.gradle\caches\transforms-4\f3bee05a55d0be5267577c93938c9880\transformed\appcompat-1.7.1\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.recyclerview:recyclerview:1.3.2] C:\Users\<USER>\.gradle\caches\transforms-4\369f8c6eac057101ba958901649f86ca\transformed\recyclerview-1.3.2\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.viewpager2:viewpager2:1.1.0] C:\Users\<USER>\.gradle\caches\transforms-4\3aef22f12cf03a6c3799fdabf2163791\transformed\viewpager2-1.1.0\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.fragment:fragment-ktx:1.6.2] C:\Users\<USER>\.gradle\caches\transforms-4\c551e6278ff129bfc5b67d3987dedd10\transformed\fragment-ktx-1.6.2\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.fragment:fragment:1.6.2] C:\Users\<USER>\.gradle\caches\transforms-4\c1a0e85072235d213bc65e804d1c146f\transformed\fragment-1.6.2\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.activity:activity-ktx:1.8.0] C:\Users\<USER>\.gradle\caches\transforms-4\26c3f5209cf69815edab932b5f41dd71\transformed\activity-ktx-1.8.0\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.activity:activity:1.8.0] C:\Users\<USER>\.gradle\caches\transforms-4\e75871789a879478eda6d8dd5707ae11\transformed\activity-1.8.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.customview:customview-poolingcontainer:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\3405a944bc698c6b04c32cd940887f1d\transformed\customview-poolingcontainer-1.0.0\AndroidManifest.xml:17:1-23:12
MERGED from [androidx.core:core-ktx:1.13.1] C:\Users\<USER>\.gradle\caches\transforms-4\6d48539be5e605b1d934f3eadeea76a5\transformed\core-ktx-1.13.1\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.emoji2:emoji2-views-helper:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-4\35da01f5dcfa6228becf35fd1a4115f7\transformed\emoji2-views-helper-1.3.0\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-4\be0a8fd6c01076fcc5403b3a0b8ab700\transformed\emoji2-1.3.0\AndroidManifest.xml:17:1-35:12
MERGED from [androidx.drawerlayout:drawerlayout:1.1.1] C:\Users\<USER>\.gradle\caches\transforms-4\1a22033c201867fe084fd613d4483723\transformed\drawerlayout-1.1.1\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.coordinatorlayout:coordinatorlayout:1.1.0] C:\Users\<USER>\.gradle\caches\transforms-4\5dd9ddbb67264fe86bfc742cc37dc0e7\transformed\coordinatorlayout-1.1.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.slidingpanelayout:slidingpanelayout:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-4\4701225a6d30fee964f09e0d502a0ae0\transformed\slidingpanelayout-1.2.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.transition:transition:1.5.0] C:\Users\<USER>\.gradle\caches\transforms-4\25cc82c0aeae6473e36aa1a2dc3d40a7\transformed\transition-1.5.0\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.dynamicanimation:dynamicanimation:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\a06b374e16c65f3ba78dfe002d052060\transformed\dynamicanimation-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.vectordrawable:vectordrawable-animated:1.1.0] C:\Users\<USER>\.gradle\caches\transforms-4\be634a971dc50f91932abb543611b0db\transformed\vectordrawable-animated-1.1.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.vectordrawable:vectordrawable:1.1.0] C:\Users\<USER>\.gradle\caches\transforms-4\dfdf0436297ee09a515edc5789c202da\transformed\vectordrawable-1.1.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.legacy:legacy-support-core-utils:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\4a2d18e356c264a1ff77cc15bcca0b74\transformed\legacy-support-core-utils-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.loader:loader:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\660144c69bf5211c2b2b8660d7618536\transformed\loader-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.viewpager:viewpager:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\d2e1e8c0e1050b25dbaff3a0bc4b44b9\transformed\viewpager-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.customview:customview:1.1.0] C:\Users\<USER>\.gradle\caches\transforms-4\9b938c699bb030df307b2ffc00bda927\transformed\customview-1.1.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.window:window:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\0be200f22399ac7a36ad735597027495\transformed\window-1.0.0\AndroidManifest.xml:17:1-33:12
MERGED from [androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\transforms-4\588763d874820f4a3b3fefea157e8181\transformed\core-1.13.1\AndroidManifest.xml:17:1-30:12
MERGED from [androidx.lifecycle:lifecycle-livedata-core:2.6.2] C:\Users\<USER>\.gradle\caches\transforms-4\b9c567cb2b9e52fd75c69e99390963a0\transformed\lifecycle-livedata-core-2.6.2\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.lifecycle:lifecycle-runtime-ktx:2.6.2] C:\Users\<USER>\.gradle\caches\transforms-4\8d5da7951b9539aabc53839e122b875a\transformed\lifecycle-runtime-ktx-2.6.2\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.lifecycle:lifecycle-viewmodel-ktx:2.6.2] C:\Users\<USER>\.gradle\caches\transforms-4\97574e01696cf4eb0cf0055194602df2\transformed\lifecycle-viewmodel-ktx-2.6.2\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.lifecycle:lifecycle-livedata-core-ktx:2.6.2] C:\Users\<USER>\.gradle\caches\transforms-4\f6dd289f036f4ab4dbec129b5e49bd38\transformed\lifecycle-livedata-core-ktx-2.6.2\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.lifecycle:lifecycle-livedata:2.6.2] C:\Users\<USER>\.gradle\caches\transforms-4\ed131b05ef9b1ad66b2b24c1b81692f1\transformed\lifecycle-livedata-2.6.2\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.lifecycle:lifecycle-viewmodel:2.6.2] C:\Users\<USER>\.gradle\caches\transforms-4\8b760e1d862ee37b94e4afd4b5884fc1\transformed\lifecycle-viewmodel-2.6.2\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.lifecycle:lifecycle-process:2.6.2] C:\Users\<USER>\.gradle\caches\transforms-4\a202cbde570ebedf44c4ec7e65cf9b89\transformed\lifecycle-process-2.6.2\AndroidManifest.xml:17:1-35:12
MERGED from [androidx.lifecycle:lifecycle-runtime:2.6.2] C:\Users\<USER>\.gradle\caches\transforms-4\60a789acd2f29d5b2cc905bf9b3a2501\transformed\lifecycle-runtime-2.6.2\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.lifecycle:lifecycle-viewmodel-savedstate:2.6.2] C:\Users\<USER>\.gradle\caches\transforms-4\0d588f68cdd47aa9b026344109019d1c\transformed\lifecycle-viewmodel-savedstate-2.6.2\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.savedstate:savedstate-ktx:1.2.1] C:\Users\<USER>\.gradle\caches\transforms-4\d9308ef1b43a9be5afd9d0afb273f146\transformed\savedstate-ktx-1.2.1\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.savedstate:savedstate:1.2.1] C:\Users\<USER>\.gradle\caches\transforms-4\c1da5683f7728b07a50563e413f66c87\transformed\savedstate-1.2.1\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.cardview:cardview:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\3b89c78c5aa498acac9d3c7df0d85149\transformed\cardview-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.cursoradapter:cursoradapter:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\997b6bdad68a2c0e81d3346e107c7b9b\transformed\cursoradapter-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\transforms-4\456a6afb8e83658be746204d46bd7eb6\transformed\profileinstaller-1.4.0\AndroidManifest.xml:17:1-55:12
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.1] C:\Users\<USER>\.gradle\caches\transforms-4\ca6e083847cb527638e07a6db32b607f\transformed\versionedparcelable-1.1.1\AndroidManifest.xml:17:1-27:12
MERGED from [androidx.interpolator:interpolator:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\cda9978217b1fcc90ba332ea8b971964\transformed\interpolator-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\transforms-4\0a672da8f21a0f52a1f0c5787a74294b\transformed\startup-runtime-1.1.1\AndroidManifest.xml:17:1-33:12
MERGED from [androidx.tracing:tracing:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\b36c1c6c41bc88d61995a83b566380c5\transformed\tracing-1.0.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.arch.core:core-runtime:2.2.0] C:\Users\<USER>\.gradle\caches\transforms-4\7689e3efafa6ce1c13819b62821899f6\transformed\core-runtime-2.2.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.documentfile:documentfile:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\eec183e275a5450fc800a20c87cfbb12\transformed\documentfile-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.localbroadcastmanager:localbroadcastmanager:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\017a9b6588942956f4ef7b7f26d1c89b\transformed\localbroadcastmanager-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.print:print:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\af0008f643ef7f85d3dd523d305f4f72\transformed\print-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.annotation:annotation-experimental:1.4.0] C:\Users\<USER>\.gradle\caches\transforms-4\b95e967fa1314df7f856a92535017c90\transformed\annotation-experimental-1.4.0\AndroidManifest.xml:2:1-7:12
	package
		INJECTED from D:\Kasumi\Kasumi\app\src\main\AndroidManifest.xml
	android:versionName
		INJECTED from D:\Kasumi\Kasumi\app\src\main\AndroidManifest.xml
	xmlns:tools
		ADDED from D:\Kasumi\Kasumi\app\src\main\AndroidManifest.xml:3:5-51
	android:versionCode
		INJECTED from D:\Kasumi\Kasumi\app\src\main\AndroidManifest.xml
	xmlns:android
		ADDED from D:\Kasumi\Kasumi\app\src\main\AndroidManifest.xml:2:11-69
uses-permission#android.permission.SYSTEM_ALERT_WINDOW
ADDED from D:\Kasumi\Kasumi\app\src\main\AndroidManifest.xml:6:5-78
	android:name
		ADDED from D:\Kasumi\Kasumi\app\src\main\AndroidManifest.xml:6:22-75
uses-permission#android.permission.FOREGROUND_SERVICE
ADDED from D:\Kasumi\Kasumi\app\src\main\AndroidManifest.xml:7:5-77
	android:name
		ADDED from D:\Kasumi\Kasumi\app\src\main\AndroidManifest.xml:7:22-74
uses-permission#android.permission.FOREGROUND_SERVICE_SPECIAL_USE
ADDED from D:\Kasumi\Kasumi\app\src\main\AndroidManifest.xml:8:5-89
	android:name
		ADDED from D:\Kasumi\Kasumi\app\src\main\AndroidManifest.xml:8:22-86
application
ADDED from D:\Kasumi\Kasumi\app\src\main\AndroidManifest.xml:10:5-46:19
INJECTED from D:\Kasumi\Kasumi\app\src\main\AndroidManifest.xml:10:5-46:19
MERGED from [com.google.android.material:material:1.12.0] C:\Users\<USER>\.gradle\caches\transforms-4\c1f1d66677a2054cc4b3d0f2744c5c9f\transformed\material-1.12.0\AndroidManifest.xml:22:5-20
MERGED from [com.google.android.material:material:1.12.0] C:\Users\<USER>\.gradle\caches\transforms-4\c1f1d66677a2054cc4b3d0f2744c5c9f\transformed\material-1.12.0\AndroidManifest.xml:22:5-20
MERGED from [androidx.constraintlayout:constraintlayout:2.2.1] C:\Users\<USER>\.gradle\caches\transforms-4\c24a58053d5d09da3afc3522253a19b5\transformed\constraintlayout-2.2.1\AndroidManifest.xml:7:5-20
MERGED from [androidx.constraintlayout:constraintlayout:2.2.1] C:\Users\<USER>\.gradle\caches\transforms-4\c24a58053d5d09da3afc3522253a19b5\transformed\constraintlayout-2.2.1\AndroidManifest.xml:7:5-20
MERGED from [com.airbnb.android:lottie:6.4.0] C:\Users\<USER>\.gradle\caches\transforms-4\2ea84c82d93551d6c13140db0008e7fd\transformed\lottie-6.4.0\AndroidManifest.xml:7:5-20
MERGED from [com.airbnb.android:lottie:6.4.0] C:\Users\<USER>\.gradle\caches\transforms-4\2ea84c82d93551d6c13140db0008e7fd\transformed\lottie-6.4.0\AndroidManifest.xml:7:5-20
MERGED from [androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-4\be0a8fd6c01076fcc5403b3a0b8ab700\transformed\emoji2-1.3.0\AndroidManifest.xml:23:5-33:19
MERGED from [androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-4\be0a8fd6c01076fcc5403b3a0b8ab700\transformed\emoji2-1.3.0\AndroidManifest.xml:23:5-33:19
MERGED from [androidx.window:window:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\0be200f22399ac7a36ad735597027495\transformed\window-1.0.0\AndroidManifest.xml:24:5-31:19
MERGED from [androidx.window:window:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\0be200f22399ac7a36ad735597027495\transformed\window-1.0.0\AndroidManifest.xml:24:5-31:19
MERGED from [androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\transforms-4\588763d874820f4a3b3fefea157e8181\transformed\core-1.13.1\AndroidManifest.xml:28:5-89
MERGED from [androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\transforms-4\588763d874820f4a3b3fefea157e8181\transformed\core-1.13.1\AndroidManifest.xml:28:5-89
MERGED from [androidx.lifecycle:lifecycle-process:2.6.2] C:\Users\<USER>\.gradle\caches\transforms-4\a202cbde570ebedf44c4ec7e65cf9b89\transformed\lifecycle-process-2.6.2\AndroidManifest.xml:23:5-33:19
MERGED from [androidx.lifecycle:lifecycle-process:2.6.2] C:\Users\<USER>\.gradle\caches\transforms-4\a202cbde570ebedf44c4ec7e65cf9b89\transformed\lifecycle-process-2.6.2\AndroidManifest.xml:23:5-33:19
MERGED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\transforms-4\456a6afb8e83658be746204d46bd7eb6\transformed\profileinstaller-1.4.0\AndroidManifest.xml:23:5-53:19
MERGED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\transforms-4\456a6afb8e83658be746204d46bd7eb6\transformed\profileinstaller-1.4.0\AndroidManifest.xml:23:5-53:19
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.1] C:\Users\<USER>\.gradle\caches\transforms-4\ca6e083847cb527638e07a6db32b607f\transformed\versionedparcelable-1.1.1\AndroidManifest.xml:24:5-25:19
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.1] C:\Users\<USER>\.gradle\caches\transforms-4\ca6e083847cb527638e07a6db32b607f\transformed\versionedparcelable-1.1.1\AndroidManifest.xml:24:5-25:19
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\transforms-4\0a672da8f21a0f52a1f0c5787a74294b\transformed\startup-runtime-1.1.1\AndroidManifest.xml:25:5-31:19
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\transforms-4\0a672da8f21a0f52a1f0c5787a74294b\transformed\startup-runtime-1.1.1\AndroidManifest.xml:25:5-31:19
	android:extractNativeLibs
		INJECTED from D:\Kasumi\Kasumi\app\src\main\AndroidManifest.xml
	android:appComponentFactory
		ADDED from [androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\transforms-4\588763d874820f4a3b3fefea157e8181\transformed\core-1.13.1\AndroidManifest.xml:28:18-86
	android:supportsRtl
		ADDED from D:\Kasumi\Kasumi\app\src\main\AndroidManifest.xml:17:9-35
	android:label
		ADDED from D:\Kasumi\Kasumi\app\src\main\AndroidManifest.xml:15:9-41
	android:fullBackupContent
		ADDED from D:\Kasumi\Kasumi\app\src\main\AndroidManifest.xml:13:9-54
	android:roundIcon
		ADDED from D:\Kasumi\Kasumi\app\src\main\AndroidManifest.xml:16:9-54
	tools:targetApi
		ADDED from D:\Kasumi\Kasumi\app\src\main\AndroidManifest.xml:19:9-29
	android:icon
		ADDED from D:\Kasumi\Kasumi\app\src\main\AndroidManifest.xml:14:9-43
	android:allowBackup
		ADDED from D:\Kasumi\Kasumi\app\src\main\AndroidManifest.xml:11:9-35
	android:theme
		ADDED from D:\Kasumi\Kasumi\app\src\main\AndroidManifest.xml:18:9-44
	android:dataExtractionRules
		ADDED from D:\Kasumi\Kasumi\app\src\main\AndroidManifest.xml:12:9-65
activity#mc.meson.kasumi.SplashActivity
ADDED from D:\Kasumi\Kasumi\app\src\main\AndroidManifest.xml:20:9-29:20
	android:exported
		ADDED from D:\Kasumi\Kasumi\app\src\main\AndroidManifest.xml:22:13-36
	android:theme
		ADDED from D:\Kasumi\Kasumi\app\src\main\AndroidManifest.xml:23:13-48
	android:name
		ADDED from D:\Kasumi\Kasumi\app\src\main\AndroidManifest.xml:21:13-43
intent-filter#action:name:android.intent.action.MAIN+category:name:android.intent.category.LAUNCHER
ADDED from D:\Kasumi\Kasumi\app\src\main\AndroidManifest.xml:24:13-28:29
action#android.intent.action.MAIN
ADDED from D:\Kasumi\Kasumi\app\src\main\AndroidManifest.xml:25:17-69
	android:name
		ADDED from D:\Kasumi\Kasumi\app\src\main\AndroidManifest.xml:25:25-66
category#android.intent.category.LAUNCHER
ADDED from D:\Kasumi\Kasumi\app\src\main\AndroidManifest.xml:27:17-77
	android:name
		ADDED from D:\Kasumi\Kasumi\app\src\main\AndroidManifest.xml:27:27-74
activity#mc.meson.kasumi.MainActivity
ADDED from D:\Kasumi\Kasumi\app\src\main\AndroidManifest.xml:31:9-34:51
	android:exported
		ADDED from D:\Kasumi\Kasumi\app\src\main\AndroidManifest.xml:33:13-37
	android:theme
		ADDED from D:\Kasumi\Kasumi\app\src\main\AndroidManifest.xml:34:13-48
	android:name
		ADDED from D:\Kasumi\Kasumi\app\src\main\AndroidManifest.xml:32:13-41
service#mc.meson.kasumi.overlay.FloatingWindowService
ADDED from D:\Kasumi\Kasumi\app\src\main\AndroidManifest.xml:37:9-45:19
	android:enabled
		ADDED from D:\Kasumi\Kasumi\app\src\main\AndroidManifest.xml:39:13-35
	android:exported
		ADDED from D:\Kasumi\Kasumi\app\src\main\AndroidManifest.xml:40:13-37
	android:foregroundServiceType
		ADDED from D:\Kasumi\Kasumi\app\src\main\AndroidManifest.xml:41:13-55
	android:name
		ADDED from D:\Kasumi\Kasumi\app\src\main\AndroidManifest.xml:38:13-58
property#android.app.PROPERTY_SPECIAL_USE_FGS_SUBTYPE
ADDED from D:\Kasumi\Kasumi\app\src\main\AndroidManifest.xml:42:13-44:43
	android:value
		ADDED from D:\Kasumi\Kasumi\app\src\main\AndroidManifest.xml:44:17-40
	android:name
		ADDED from D:\Kasumi\Kasumi\app\src\main\AndroidManifest.xml:43:17-76
uses-sdk
INJECTED from D:\Kasumi\Kasumi\app\src\main\AndroidManifest.xml reason: use-sdk injection requested
INJECTED from D:\Kasumi\Kasumi\app\src\main\AndroidManifest.xml
INJECTED from D:\Kasumi\Kasumi\app\src\main\AndroidManifest.xml
MERGED from [androidx.databinding:viewbinding:8.5.1] C:\Users\<USER>\.gradle\caches\transforms-4\aa7080cbda2ff5d11287367dd8f68dac\transformed\viewbinding-8.5.1\AndroidManifest.xml:5:5-44
MERGED from [androidx.databinding:viewbinding:8.5.1] C:\Users\<USER>\.gradle\caches\transforms-4\aa7080cbda2ff5d11287367dd8f68dac\transformed\viewbinding-8.5.1\AndroidManifest.xml:5:5-44
MERGED from [androidx.navigation:navigation-common:2.7.7] C:\Users\<USER>\.gradle\caches\transforms-4\336596ec5c5dcb2677d966befead2bc6\transformed\navigation-common-2.7.7\AndroidManifest.xml:20:5-44
MERGED from [androidx.navigation:navigation-common:2.7.7] C:\Users\<USER>\.gradle\caches\transforms-4\336596ec5c5dcb2677d966befead2bc6\transformed\navigation-common-2.7.7\AndroidManifest.xml:20:5-44
MERGED from [androidx.navigation:navigation-common-ktx:2.7.7] C:\Users\<USER>\.gradle\caches\transforms-4\51fc843405451511a490dbb2b38704c3\transformed\navigation-common-ktx-2.7.7\AndroidManifest.xml:5:5-44
MERGED from [androidx.navigation:navigation-common-ktx:2.7.7] C:\Users\<USER>\.gradle\caches\transforms-4\51fc843405451511a490dbb2b38704c3\transformed\navigation-common-ktx-2.7.7\AndroidManifest.xml:5:5-44
MERGED from [androidx.navigation:navigation-runtime:2.7.7] C:\Users\<USER>\.gradle\caches\transforms-4\e7894dea0f9dcb1745b54e97c92cd70f\transformed\navigation-runtime-2.7.7\AndroidManifest.xml:20:5-44
MERGED from [androidx.navigation:navigation-runtime:2.7.7] C:\Users\<USER>\.gradle\caches\transforms-4\e7894dea0f9dcb1745b54e97c92cd70f\transformed\navigation-runtime-2.7.7\AndroidManifest.xml:20:5-44
MERGED from [androidx.navigation:navigation-runtime-ktx:2.7.7] C:\Users\<USER>\.gradle\caches\transforms-4\2df91b3a20ae83b4662d1a63c07ec35b\transformed\navigation-runtime-ktx-2.7.7\AndroidManifest.xml:5:5-44
MERGED from [androidx.navigation:navigation-runtime-ktx:2.7.7] C:\Users\<USER>\.gradle\caches\transforms-4\2df91b3a20ae83b4662d1a63c07ec35b\transformed\navigation-runtime-ktx-2.7.7\AndroidManifest.xml:5:5-44
MERGED from [androidx.navigation:navigation-fragment:2.7.7] C:\Users\<USER>\.gradle\caches\transforms-4\b3625fe41a785a8c2bd44b66308ff5dd\transformed\navigation-fragment-2.7.7\AndroidManifest.xml:20:5-44
MERGED from [androidx.navigation:navigation-fragment:2.7.7] C:\Users\<USER>\.gradle\caches\transforms-4\b3625fe41a785a8c2bd44b66308ff5dd\transformed\navigation-fragment-2.7.7\AndroidManifest.xml:20:5-44
MERGED from [androidx.navigation:navigation-fragment-ktx:2.7.7] C:\Users\<USER>\.gradle\caches\transforms-4\3cca7ff51090028be8487e06fb7a7eeb\transformed\navigation-fragment-ktx-2.7.7\AndroidManifest.xml:5:5-44
MERGED from [androidx.navigation:navigation-fragment-ktx:2.7.7] C:\Users\<USER>\.gradle\caches\transforms-4\3cca7ff51090028be8487e06fb7a7eeb\transformed\navigation-fragment-ktx-2.7.7\AndroidManifest.xml:5:5-44
MERGED from [androidx.navigation:navigation-ui-ktx:2.7.7] C:\Users\<USER>\.gradle\caches\transforms-4\8cd550ee1f24c7904721807737096df1\transformed\navigation-ui-ktx-2.7.7\AndroidManifest.xml:5:5-44
MERGED from [androidx.navigation:navigation-ui-ktx:2.7.7] C:\Users\<USER>\.gradle\caches\transforms-4\8cd550ee1f24c7904721807737096df1\transformed\navigation-ui-ktx-2.7.7\AndroidManifest.xml:5:5-44
MERGED from [androidx.navigation:navigation-ui:2.7.7] C:\Users\<USER>\.gradle\caches\transforms-4\c6de447ab1a7b632fe56ac0b139a6541\transformed\navigation-ui-2.7.7\AndroidManifest.xml:20:5-44
MERGED from [androidx.navigation:navigation-ui:2.7.7] C:\Users\<USER>\.gradle\caches\transforms-4\c6de447ab1a7b632fe56ac0b139a6541\transformed\navigation-ui-2.7.7\AndroidManifest.xml:20:5-44
MERGED from [com.google.android.material:material:1.12.0] C:\Users\<USER>\.gradle\caches\transforms-4\c1f1d66677a2054cc4b3d0f2744c5c9f\transformed\material-1.12.0\AndroidManifest.xml:20:5-44
MERGED from [com.google.android.material:material:1.12.0] C:\Users\<USER>\.gradle\caches\transforms-4\c1f1d66677a2054cc4b3d0f2744c5c9f\transformed\material-1.12.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.constraintlayout:constraintlayout:2.2.1] C:\Users\<USER>\.gradle\caches\transforms-4\c24a58053d5d09da3afc3522253a19b5\transformed\constraintlayout-2.2.1\AndroidManifest.xml:5:5-44
MERGED from [androidx.constraintlayout:constraintlayout:2.2.1] C:\Users\<USER>\.gradle\caches\transforms-4\c24a58053d5d09da3afc3522253a19b5\transformed\constraintlayout-2.2.1\AndroidManifest.xml:5:5-44
MERGED from [com.airbnb.android:lottie:6.4.0] C:\Users\<USER>\.gradle\caches\transforms-4\2ea84c82d93551d6c13140db0008e7fd\transformed\lottie-6.4.0\AndroidManifest.xml:5:5-44
MERGED from [com.airbnb.android:lottie:6.4.0] C:\Users\<USER>\.gradle\caches\transforms-4\2ea84c82d93551d6c13140db0008e7fd\transformed\lottie-6.4.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.appcompat:appcompat-resources:1.7.1] C:\Users\<USER>\.gradle\caches\transforms-4\12874f758f432c8d5694e1413102185e\transformed\appcompat-resources-1.7.1\AndroidManifest.xml:5:5-44
MERGED from [androidx.appcompat:appcompat-resources:1.7.1] C:\Users\<USER>\.gradle\caches\transforms-4\12874f758f432c8d5694e1413102185e\transformed\appcompat-resources-1.7.1\AndroidManifest.xml:5:5-44
MERGED from [androidx.appcompat:appcompat:1.7.1] C:\Users\<USER>\.gradle\caches\transforms-4\f3bee05a55d0be5267577c93938c9880\transformed\appcompat-1.7.1\AndroidManifest.xml:5:5-44
MERGED from [androidx.appcompat:appcompat:1.7.1] C:\Users\<USER>\.gradle\caches\transforms-4\f3bee05a55d0be5267577c93938c9880\transformed\appcompat-1.7.1\AndroidManifest.xml:5:5-44
MERGED from [androidx.recyclerview:recyclerview:1.3.2] C:\Users\<USER>\.gradle\caches\transforms-4\369f8c6eac057101ba958901649f86ca\transformed\recyclerview-1.3.2\AndroidManifest.xml:20:5-44
MERGED from [androidx.recyclerview:recyclerview:1.3.2] C:\Users\<USER>\.gradle\caches\transforms-4\369f8c6eac057101ba958901649f86ca\transformed\recyclerview-1.3.2\AndroidManifest.xml:20:5-44
MERGED from [androidx.viewpager2:viewpager2:1.1.0] C:\Users\<USER>\.gradle\caches\transforms-4\3aef22f12cf03a6c3799fdabf2163791\transformed\viewpager2-1.1.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.viewpager2:viewpager2:1.1.0] C:\Users\<USER>\.gradle\caches\transforms-4\3aef22f12cf03a6c3799fdabf2163791\transformed\viewpager2-1.1.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.fragment:fragment-ktx:1.6.2] C:\Users\<USER>\.gradle\caches\transforms-4\c551e6278ff129bfc5b67d3987dedd10\transformed\fragment-ktx-1.6.2\AndroidManifest.xml:20:5-44
MERGED from [androidx.fragment:fragment-ktx:1.6.2] C:\Users\<USER>\.gradle\caches\transforms-4\c551e6278ff129bfc5b67d3987dedd10\transformed\fragment-ktx-1.6.2\AndroidManifest.xml:20:5-44
MERGED from [androidx.fragment:fragment:1.6.2] C:\Users\<USER>\.gradle\caches\transforms-4\c1a0e85072235d213bc65e804d1c146f\transformed\fragment-1.6.2\AndroidManifest.xml:20:5-44
MERGED from [androidx.fragment:fragment:1.6.2] C:\Users\<USER>\.gradle\caches\transforms-4\c1a0e85072235d213bc65e804d1c146f\transformed\fragment-1.6.2\AndroidManifest.xml:20:5-44
MERGED from [androidx.activity:activity-ktx:1.8.0] C:\Users\<USER>\.gradle\caches\transforms-4\26c3f5209cf69815edab932b5f41dd71\transformed\activity-ktx-1.8.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.activity:activity-ktx:1.8.0] C:\Users\<USER>\.gradle\caches\transforms-4\26c3f5209cf69815edab932b5f41dd71\transformed\activity-ktx-1.8.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.activity:activity:1.8.0] C:\Users\<USER>\.gradle\caches\transforms-4\e75871789a879478eda6d8dd5707ae11\transformed\activity-1.8.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.activity:activity:1.8.0] C:\Users\<USER>\.gradle\caches\transforms-4\e75871789a879478eda6d8dd5707ae11\transformed\activity-1.8.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.customview:customview-poolingcontainer:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\3405a944bc698c6b04c32cd940887f1d\transformed\customview-poolingcontainer-1.0.0\AndroidManifest.xml:20:5-21:38
MERGED from [androidx.customview:customview-poolingcontainer:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\3405a944bc698c6b04c32cd940887f1d\transformed\customview-poolingcontainer-1.0.0\AndroidManifest.xml:20:5-21:38
MERGED from [androidx.core:core-ktx:1.13.1] C:\Users\<USER>\.gradle\caches\transforms-4\6d48539be5e605b1d934f3eadeea76a5\transformed\core-ktx-1.13.1\AndroidManifest.xml:5:5-44
MERGED from [androidx.core:core-ktx:1.13.1] C:\Users\<USER>\.gradle\caches\transforms-4\6d48539be5e605b1d934f3eadeea76a5\transformed\core-ktx-1.13.1\AndroidManifest.xml:5:5-44
MERGED from [androidx.emoji2:emoji2-views-helper:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-4\35da01f5dcfa6228becf35fd1a4115f7\transformed\emoji2-views-helper-1.3.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.emoji2:emoji2-views-helper:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-4\35da01f5dcfa6228becf35fd1a4115f7\transformed\emoji2-views-helper-1.3.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-4\be0a8fd6c01076fcc5403b3a0b8ab700\transformed\emoji2-1.3.0\AndroidManifest.xml:21:5-44
MERGED from [androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-4\be0a8fd6c01076fcc5403b3a0b8ab700\transformed\emoji2-1.3.0\AndroidManifest.xml:21:5-44
MERGED from [androidx.drawerlayout:drawerlayout:1.1.1] C:\Users\<USER>\.gradle\caches\transforms-4\1a22033c201867fe084fd613d4483723\transformed\drawerlayout-1.1.1\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.drawerlayout:drawerlayout:1.1.1] C:\Users\<USER>\.gradle\caches\transforms-4\1a22033c201867fe084fd613d4483723\transformed\drawerlayout-1.1.1\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.coordinatorlayout:coordinatorlayout:1.1.0] C:\Users\<USER>\.gradle\caches\transforms-4\5dd9ddbb67264fe86bfc742cc37dc0e7\transformed\coordinatorlayout-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.coordinatorlayout:coordinatorlayout:1.1.0] C:\Users\<USER>\.gradle\caches\transforms-4\5dd9ddbb67264fe86bfc742cc37dc0e7\transformed\coordinatorlayout-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.slidingpanelayout:slidingpanelayout:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-4\4701225a6d30fee964f09e0d502a0ae0\transformed\slidingpanelayout-1.2.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.slidingpanelayout:slidingpanelayout:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-4\4701225a6d30fee964f09e0d502a0ae0\transformed\slidingpanelayout-1.2.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.transition:transition:1.5.0] C:\Users\<USER>\.gradle\caches\transforms-4\25cc82c0aeae6473e36aa1a2dc3d40a7\transformed\transition-1.5.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.transition:transition:1.5.0] C:\Users\<USER>\.gradle\caches\transforms-4\25cc82c0aeae6473e36aa1a2dc3d40a7\transformed\transition-1.5.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.dynamicanimation:dynamicanimation:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\a06b374e16c65f3ba78dfe002d052060\transformed\dynamicanimation-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.dynamicanimation:dynamicanimation:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\a06b374e16c65f3ba78dfe002d052060\transformed\dynamicanimation-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.vectordrawable:vectordrawable-animated:1.1.0] C:\Users\<USER>\.gradle\caches\transforms-4\be634a971dc50f91932abb543611b0db\transformed\vectordrawable-animated-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.vectordrawable:vectordrawable-animated:1.1.0] C:\Users\<USER>\.gradle\caches\transforms-4\be634a971dc50f91932abb543611b0db\transformed\vectordrawable-animated-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.vectordrawable:vectordrawable:1.1.0] C:\Users\<USER>\.gradle\caches\transforms-4\dfdf0436297ee09a515edc5789c202da\transformed\vectordrawable-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.vectordrawable:vectordrawable:1.1.0] C:\Users\<USER>\.gradle\caches\transforms-4\dfdf0436297ee09a515edc5789c202da\transformed\vectordrawable-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.legacy:legacy-support-core-utils:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\4a2d18e356c264a1ff77cc15bcca0b74\transformed\legacy-support-core-utils-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.legacy:legacy-support-core-utils:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\4a2d18e356c264a1ff77cc15bcca0b74\transformed\legacy-support-core-utils-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.loader:loader:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\660144c69bf5211c2b2b8660d7618536\transformed\loader-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.loader:loader:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\660144c69bf5211c2b2b8660d7618536\transformed\loader-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.viewpager:viewpager:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\d2e1e8c0e1050b25dbaff3a0bc4b44b9\transformed\viewpager-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.viewpager:viewpager:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\d2e1e8c0e1050b25dbaff3a0bc4b44b9\transformed\viewpager-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.customview:customview:1.1.0] C:\Users\<USER>\.gradle\caches\transforms-4\9b938c699bb030df307b2ffc00bda927\transformed\customview-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.customview:customview:1.1.0] C:\Users\<USER>\.gradle\caches\transforms-4\9b938c699bb030df307b2ffc00bda927\transformed\customview-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.window:window:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\0be200f22399ac7a36ad735597027495\transformed\window-1.0.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.window:window:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\0be200f22399ac7a36ad735597027495\transformed\window-1.0.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\transforms-4\588763d874820f4a3b3fefea157e8181\transformed\core-1.13.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\transforms-4\588763d874820f4a3b3fefea157e8181\transformed\core-1.13.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata-core:2.6.2] C:\Users\<USER>\.gradle\caches\transforms-4\b9c567cb2b9e52fd75c69e99390963a0\transformed\lifecycle-livedata-core-2.6.2\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata-core:2.6.2] C:\Users\<USER>\.gradle\caches\transforms-4\b9c567cb2b9e52fd75c69e99390963a0\transformed\lifecycle-livedata-core-2.6.2\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-runtime-ktx:2.6.2] C:\Users\<USER>\.gradle\caches\transforms-4\8d5da7951b9539aabc53839e122b875a\transformed\lifecycle-runtime-ktx-2.6.2\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-runtime-ktx:2.6.2] C:\Users\<USER>\.gradle\caches\transforms-4\8d5da7951b9539aabc53839e122b875a\transformed\lifecycle-runtime-ktx-2.6.2\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel-ktx:2.6.2] C:\Users\<USER>\.gradle\caches\transforms-4\97574e01696cf4eb0cf0055194602df2\transformed\lifecycle-viewmodel-ktx-2.6.2\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel-ktx:2.6.2] C:\Users\<USER>\.gradle\caches\transforms-4\97574e01696cf4eb0cf0055194602df2\transformed\lifecycle-viewmodel-ktx-2.6.2\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata-core-ktx:2.6.2] C:\Users\<USER>\.gradle\caches\transforms-4\f6dd289f036f4ab4dbec129b5e49bd38\transformed\lifecycle-livedata-core-ktx-2.6.2\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata-core-ktx:2.6.2] C:\Users\<USER>\.gradle\caches\transforms-4\f6dd289f036f4ab4dbec129b5e49bd38\transformed\lifecycle-livedata-core-ktx-2.6.2\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata:2.6.2] C:\Users\<USER>\.gradle\caches\transforms-4\ed131b05ef9b1ad66b2b24c1b81692f1\transformed\lifecycle-livedata-2.6.2\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata:2.6.2] C:\Users\<USER>\.gradle\caches\transforms-4\ed131b05ef9b1ad66b2b24c1b81692f1\transformed\lifecycle-livedata-2.6.2\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel:2.6.2] C:\Users\<USER>\.gradle\caches\transforms-4\8b760e1d862ee37b94e4afd4b5884fc1\transformed\lifecycle-viewmodel-2.6.2\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel:2.6.2] C:\Users\<USER>\.gradle\caches\transforms-4\8b760e1d862ee37b94e4afd4b5884fc1\transformed\lifecycle-viewmodel-2.6.2\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-process:2.6.2] C:\Users\<USER>\.gradle\caches\transforms-4\a202cbde570ebedf44c4ec7e65cf9b89\transformed\lifecycle-process-2.6.2\AndroidManifest.xml:21:5-44
MERGED from [androidx.lifecycle:lifecycle-process:2.6.2] C:\Users\<USER>\.gradle\caches\transforms-4\a202cbde570ebedf44c4ec7e65cf9b89\transformed\lifecycle-process-2.6.2\AndroidManifest.xml:21:5-44
MERGED from [androidx.lifecycle:lifecycle-runtime:2.6.2] C:\Users\<USER>\.gradle\caches\transforms-4\60a789acd2f29d5b2cc905bf9b3a2501\transformed\lifecycle-runtime-2.6.2\AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-runtime:2.6.2] C:\Users\<USER>\.gradle\caches\transforms-4\60a789acd2f29d5b2cc905bf9b3a2501\transformed\lifecycle-runtime-2.6.2\AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel-savedstate:2.6.2] C:\Users\<USER>\.gradle\caches\transforms-4\0d588f68cdd47aa9b026344109019d1c\transformed\lifecycle-viewmodel-savedstate-2.6.2\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel-savedstate:2.6.2] C:\Users\<USER>\.gradle\caches\transforms-4\0d588f68cdd47aa9b026344109019d1c\transformed\lifecycle-viewmodel-savedstate-2.6.2\AndroidManifest.xml:20:5-44
MERGED from [androidx.savedstate:savedstate-ktx:1.2.1] C:\Users\<USER>\.gradle\caches\transforms-4\d9308ef1b43a9be5afd9d0afb273f146\transformed\savedstate-ktx-1.2.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.savedstate:savedstate-ktx:1.2.1] C:\Users\<USER>\.gradle\caches\transforms-4\d9308ef1b43a9be5afd9d0afb273f146\transformed\savedstate-ktx-1.2.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.savedstate:savedstate:1.2.1] C:\Users\<USER>\.gradle\caches\transforms-4\c1da5683f7728b07a50563e413f66c87\transformed\savedstate-1.2.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.savedstate:savedstate:1.2.1] C:\Users\<USER>\.gradle\caches\transforms-4\c1da5683f7728b07a50563e413f66c87\transformed\savedstate-1.2.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.cardview:cardview:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\3b89c78c5aa498acac9d3c7df0d85149\transformed\cardview-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.cardview:cardview:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\3b89c78c5aa498acac9d3c7df0d85149\transformed\cardview-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.cursoradapter:cursoradapter:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\997b6bdad68a2c0e81d3346e107c7b9b\transformed\cursoradapter-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.cursoradapter:cursoradapter:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\997b6bdad68a2c0e81d3346e107c7b9b\transformed\cursoradapter-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\transforms-4\456a6afb8e83658be746204d46bd7eb6\transformed\profileinstaller-1.4.0\AndroidManifest.xml:21:5-44
MERGED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\transforms-4\456a6afb8e83658be746204d46bd7eb6\transformed\profileinstaller-1.4.0\AndroidManifest.xml:21:5-44
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.1] C:\Users\<USER>\.gradle\caches\transforms-4\ca6e083847cb527638e07a6db32b607f\transformed\versionedparcelable-1.1.1\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.1] C:\Users\<USER>\.gradle\caches\transforms-4\ca6e083847cb527638e07a6db32b607f\transformed\versionedparcelable-1.1.1\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.interpolator:interpolator:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\cda9978217b1fcc90ba332ea8b971964\transformed\interpolator-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.interpolator:interpolator:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\cda9978217b1fcc90ba332ea8b971964\transformed\interpolator-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\transforms-4\0a672da8f21a0f52a1f0c5787a74294b\transformed\startup-runtime-1.1.1\AndroidManifest.xml:21:5-23:41
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\transforms-4\0a672da8f21a0f52a1f0c5787a74294b\transformed\startup-runtime-1.1.1\AndroidManifest.xml:21:5-23:41
MERGED from [androidx.tracing:tracing:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\b36c1c6c41bc88d61995a83b566380c5\transformed\tracing-1.0.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.tracing:tracing:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\b36c1c6c41bc88d61995a83b566380c5\transformed\tracing-1.0.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.arch.core:core-runtime:2.2.0] C:\Users\<USER>\.gradle\caches\transforms-4\7689e3efafa6ce1c13819b62821899f6\transformed\core-runtime-2.2.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.arch.core:core-runtime:2.2.0] C:\Users\<USER>\.gradle\caches\transforms-4\7689e3efafa6ce1c13819b62821899f6\transformed\core-runtime-2.2.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.documentfile:documentfile:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\eec183e275a5450fc800a20c87cfbb12\transformed\documentfile-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.documentfile:documentfile:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\eec183e275a5450fc800a20c87cfbb12\transformed\documentfile-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.localbroadcastmanager:localbroadcastmanager:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\017a9b6588942956f4ef7b7f26d1c89b\transformed\localbroadcastmanager-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.localbroadcastmanager:localbroadcastmanager:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\017a9b6588942956f4ef7b7f26d1c89b\transformed\localbroadcastmanager-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.print:print:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\af0008f643ef7f85d3dd523d305f4f72\transformed\print-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.print:print:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\af0008f643ef7f85d3dd523d305f4f72\transformed\print-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.annotation:annotation-experimental:1.4.0] C:\Users\<USER>\.gradle\caches\transforms-4\b95e967fa1314df7f856a92535017c90\transformed\annotation-experimental-1.4.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.annotation:annotation-experimental:1.4.0] C:\Users\<USER>\.gradle\caches\transforms-4\b95e967fa1314df7f856a92535017c90\transformed\annotation-experimental-1.4.0\AndroidManifest.xml:5:5-44
	android:targetSdkVersion
		INJECTED from D:\Kasumi\Kasumi\app\src\main\AndroidManifest.xml
	android:minSdkVersion
		INJECTED from D:\Kasumi\Kasumi\app\src\main\AndroidManifest.xml
provider#androidx.startup.InitializationProvider
ADDED from [androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-4\be0a8fd6c01076fcc5403b3a0b8ab700\transformed\emoji2-1.3.0\AndroidManifest.xml:24:9-32:20
MERGED from [androidx.lifecycle:lifecycle-process:2.6.2] C:\Users\<USER>\.gradle\caches\transforms-4\a202cbde570ebedf44c4ec7e65cf9b89\transformed\lifecycle-process-2.6.2\AndroidManifest.xml:24:9-32:20
MERGED from [androidx.lifecycle:lifecycle-process:2.6.2] C:\Users\<USER>\.gradle\caches\transforms-4\a202cbde570ebedf44c4ec7e65cf9b89\transformed\lifecycle-process-2.6.2\AndroidManifest.xml:24:9-32:20
MERGED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\transforms-4\456a6afb8e83658be746204d46bd7eb6\transformed\profileinstaller-1.4.0\AndroidManifest.xml:24:9-32:20
MERGED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\transforms-4\456a6afb8e83658be746204d46bd7eb6\transformed\profileinstaller-1.4.0\AndroidManifest.xml:24:9-32:20
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\transforms-4\0a672da8f21a0f52a1f0c5787a74294b\transformed\startup-runtime-1.1.1\AndroidManifest.xml:26:9-30:34
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\transforms-4\0a672da8f21a0f52a1f0c5787a74294b\transformed\startup-runtime-1.1.1\AndroidManifest.xml:26:9-30:34
	tools:node
		ADDED from [androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-4\be0a8fd6c01076fcc5403b3a0b8ab700\transformed\emoji2-1.3.0\AndroidManifest.xml:28:13-31
	android:authorities
		ADDED from [androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-4\be0a8fd6c01076fcc5403b3a0b8ab700\transformed\emoji2-1.3.0\AndroidManifest.xml:26:13-68
	android:exported
		ADDED from [androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-4\be0a8fd6c01076fcc5403b3a0b8ab700\transformed\emoji2-1.3.0\AndroidManifest.xml:27:13-37
	android:name
		ADDED from [androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-4\be0a8fd6c01076fcc5403b3a0b8ab700\transformed\emoji2-1.3.0\AndroidManifest.xml:25:13-67
meta-data#androidx.emoji2.text.EmojiCompatInitializer
ADDED from [androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-4\be0a8fd6c01076fcc5403b3a0b8ab700\transformed\emoji2-1.3.0\AndroidManifest.xml:29:13-31:52
	android:value
		ADDED from [androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-4\be0a8fd6c01076fcc5403b3a0b8ab700\transformed\emoji2-1.3.0\AndroidManifest.xml:31:17-49
	android:name
		ADDED from [androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-4\be0a8fd6c01076fcc5403b3a0b8ab700\transformed\emoji2-1.3.0\AndroidManifest.xml:30:17-75
uses-library#androidx.window.extensions
ADDED from [androidx.window:window:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\0be200f22399ac7a36ad735597027495\transformed\window-1.0.0\AndroidManifest.xml:25:9-27:40
	android:required
		ADDED from [androidx.window:window:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\0be200f22399ac7a36ad735597027495\transformed\window-1.0.0\AndroidManifest.xml:27:13-37
	android:name
		ADDED from [androidx.window:window:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\0be200f22399ac7a36ad735597027495\transformed\window-1.0.0\AndroidManifest.xml:26:13-54
uses-library#androidx.window.sidecar
ADDED from [androidx.window:window:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\0be200f22399ac7a36ad735597027495\transformed\window-1.0.0\AndroidManifest.xml:28:9-30:40
	android:required
		ADDED from [androidx.window:window:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\0be200f22399ac7a36ad735597027495\transformed\window-1.0.0\AndroidManifest.xml:30:13-37
	android:name
		ADDED from [androidx.window:window:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\0be200f22399ac7a36ad735597027495\transformed\window-1.0.0\AndroidManifest.xml:29:13-51
permission#${applicationId}.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION
ADDED from [androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\transforms-4\588763d874820f4a3b3fefea157e8181\transformed\core-1.13.1\AndroidManifest.xml:22:5-24:47
	android:protectionLevel
		ADDED from [androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\transforms-4\588763d874820f4a3b3fefea157e8181\transformed\core-1.13.1\AndroidManifest.xml:24:9-44
	android:name
		ADDED from [androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\transforms-4\588763d874820f4a3b3fefea157e8181\transformed\core-1.13.1\AndroidManifest.xml:23:9-81
permission#mc.meson.kasumi.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION
ADDED from [androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\transforms-4\588763d874820f4a3b3fefea157e8181\transformed\core-1.13.1\AndroidManifest.xml:22:5-24:47
	android:protectionLevel
		ADDED from [androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\transforms-4\588763d874820f4a3b3fefea157e8181\transformed\core-1.13.1\AndroidManifest.xml:24:9-44
	android:name
		ADDED from [androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\transforms-4\588763d874820f4a3b3fefea157e8181\transformed\core-1.13.1\AndroidManifest.xml:23:9-81
uses-permission#${applicationId}.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION
ADDED from [androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\transforms-4\588763d874820f4a3b3fefea157e8181\transformed\core-1.13.1\AndroidManifest.xml:26:5-97
	android:name
		ADDED from [androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\transforms-4\588763d874820f4a3b3fefea157e8181\transformed\core-1.13.1\AndroidManifest.xml:26:22-94
uses-permission#mc.meson.kasumi.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION
ADDED from [androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\transforms-4\588763d874820f4a3b3fefea157e8181\transformed\core-1.13.1\AndroidManifest.xml:26:5-97
	android:name
		ADDED from [androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\transforms-4\588763d874820f4a3b3fefea157e8181\transformed\core-1.13.1\AndroidManifest.xml:26:22-94
meta-data#androidx.lifecycle.ProcessLifecycleInitializer
ADDED from [androidx.lifecycle:lifecycle-process:2.6.2] C:\Users\<USER>\.gradle\caches\transforms-4\a202cbde570ebedf44c4ec7e65cf9b89\transformed\lifecycle-process-2.6.2\AndroidManifest.xml:29:13-31:52
	android:value
		ADDED from [androidx.lifecycle:lifecycle-process:2.6.2] C:\Users\<USER>\.gradle\caches\transforms-4\a202cbde570ebedf44c4ec7e65cf9b89\transformed\lifecycle-process-2.6.2\AndroidManifest.xml:31:17-49
	android:name
		ADDED from [androidx.lifecycle:lifecycle-process:2.6.2] C:\Users\<USER>\.gradle\caches\transforms-4\a202cbde570ebedf44c4ec7e65cf9b89\transformed\lifecycle-process-2.6.2\AndroidManifest.xml:30:17-78
meta-data#androidx.profileinstaller.ProfileInstallerInitializer
ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\transforms-4\456a6afb8e83658be746204d46bd7eb6\transformed\profileinstaller-1.4.0\AndroidManifest.xml:29:13-31:52
	android:value
		ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\transforms-4\456a6afb8e83658be746204d46bd7eb6\transformed\profileinstaller-1.4.0\AndroidManifest.xml:31:17-49
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\transforms-4\456a6afb8e83658be746204d46bd7eb6\transformed\profileinstaller-1.4.0\AndroidManifest.xml:30:17-85
receiver#androidx.profileinstaller.ProfileInstallReceiver
ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\transforms-4\456a6afb8e83658be746204d46bd7eb6\transformed\profileinstaller-1.4.0\AndroidManifest.xml:34:9-52:20
	android:enabled
		ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\transforms-4\456a6afb8e83658be746204d46bd7eb6\transformed\profileinstaller-1.4.0\AndroidManifest.xml:37:13-35
	android:exported
		ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\transforms-4\456a6afb8e83658be746204d46bd7eb6\transformed\profileinstaller-1.4.0\AndroidManifest.xml:38:13-36
	android:permission
		ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\transforms-4\456a6afb8e83658be746204d46bd7eb6\transformed\profileinstaller-1.4.0\AndroidManifest.xml:39:13-57
	android:directBootAware
		ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\transforms-4\456a6afb8e83658be746204d46bd7eb6\transformed\profileinstaller-1.4.0\AndroidManifest.xml:36:13-44
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\transforms-4\456a6afb8e83658be746204d46bd7eb6\transformed\profileinstaller-1.4.0\AndroidManifest.xml:35:13-76
intent-filter#action:name:androidx.profileinstaller.action.INSTALL_PROFILE
ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\transforms-4\456a6afb8e83658be746204d46bd7eb6\transformed\profileinstaller-1.4.0\AndroidManifest.xml:40:13-42:29
action#androidx.profileinstaller.action.INSTALL_PROFILE
ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\transforms-4\456a6afb8e83658be746204d46bd7eb6\transformed\profileinstaller-1.4.0\AndroidManifest.xml:41:17-91
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\transforms-4\456a6afb8e83658be746204d46bd7eb6\transformed\profileinstaller-1.4.0\AndroidManifest.xml:41:25-88
intent-filter#action:name:androidx.profileinstaller.action.SKIP_FILE
ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\transforms-4\456a6afb8e83658be746204d46bd7eb6\transformed\profileinstaller-1.4.0\AndroidManifest.xml:43:13-45:29
action#androidx.profileinstaller.action.SKIP_FILE
ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\transforms-4\456a6afb8e83658be746204d46bd7eb6\transformed\profileinstaller-1.4.0\AndroidManifest.xml:44:17-85
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\transforms-4\456a6afb8e83658be746204d46bd7eb6\transformed\profileinstaller-1.4.0\AndroidManifest.xml:44:25-82
intent-filter#action:name:androidx.profileinstaller.action.SAVE_PROFILE
ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\transforms-4\456a6afb8e83658be746204d46bd7eb6\transformed\profileinstaller-1.4.0\AndroidManifest.xml:46:13-48:29
action#androidx.profileinstaller.action.SAVE_PROFILE
ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\transforms-4\456a6afb8e83658be746204d46bd7eb6\transformed\profileinstaller-1.4.0\AndroidManifest.xml:47:17-88
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\transforms-4\456a6afb8e83658be746204d46bd7eb6\transformed\profileinstaller-1.4.0\AndroidManifest.xml:47:25-85
intent-filter#action:name:androidx.profileinstaller.action.BENCHMARK_OPERATION
ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\transforms-4\456a6afb8e83658be746204d46bd7eb6\transformed\profileinstaller-1.4.0\AndroidManifest.xml:49:13-51:29
action#androidx.profileinstaller.action.BENCHMARK_OPERATION
ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\transforms-4\456a6afb8e83658be746204d46bd7eb6\transformed\profileinstaller-1.4.0\AndroidManifest.xml:50:17-95
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\transforms-4\456a6afb8e83658be746204d46bd7eb6\transformed\profileinstaller-1.4.0\AndroidManifest.xml:50:25-92
