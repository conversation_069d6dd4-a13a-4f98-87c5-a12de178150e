<?xml version="1.0" encoding="utf-8" standalone="yes"?><Layout layout="fragment_config_management" modulePackage="mc.meson.kasumi" filePath="app\src\main\res\layout\fragment_config_management.xml" directory="layout" isMerge="false" isBindingData="false" rootNodeType="androidx.coordinatorlayout.widget.CoordinatorLayout"><Targets><Target tag="layout/fragment_config_management_0" view="androidx.coordinatorlayout.widget.CoordinatorLayout"><Expressions/><location startLine="1" startOffset="0" endLine="207" endOffset="53"/></Target><Target id="@+id/btnBack" view="ImageButton"><Expressions/><location startLine="27" startOffset="16" endLine="34" endOffset="53"/></Target><Target id="@+id/btnImportConfig" view="ImageButton"><Expressions/><location startLine="46" startOffset="16" endLine="53" endOffset="53"/></Target><Target id="@+id/btnExportAll" view="ImageButton"><Expressions/><location startLine="55" startOffset="16" endLine="62" endOffset="53"/></Target><Target id="@+id/tvCurrentConfigName" view="TextView"><Expressions/><location startLine="91" startOffset="16" endLine="98" endOffset="52"/></Target><Target id="@+id/tvCurrentConfigDescription" view="TextView"><Expressions/><location startLine="100" startOffset="16" endLine="107" endOffset="52"/></Target><Target id="@+id/tvCurrentConfigStats" view="TextView"><Expressions/><location startLine="109" startOffset="16" endLine="116" endOffset="52"/></Target><Target id="@+id/recyclerViewConfigs" view="androidx.recyclerview.widget.RecyclerView"><Expressions/><location startLine="146" startOffset="16" endLine="152" endOffset="51"/></Target><Target id="@+id/layoutEmptyState" view="LinearLayout"><Expressions/><location startLine="155" startOffset="16" endLine="188" endOffset="30"/></Target><Target id="@+id/fabCreateConfig" view="com.google.android.material.floatingactionbutton.FloatingActionButton"><Expressions/><location startLine="197" startOffset="4" endLine="205" endOffset="41"/></Target></Targets></Layout>