package mc.meson.kasumi.module

import android.content.Context
import android.content.SharedPreferences
import android.util.Log
import androidx.annotation.DrawableRes

/**
 * Kasumi模块基类
 * 统一管理所有hack功能的开启、关闭、状态保存等
 */
abstract class KasumiModule(
    val id: String,
    val name: String,
    val description: String,
    val category: ModuleCategory,
    @DrawableRes val iconRes: Int
) {
    
    /**
     * 模块的设置项列表
     */
    open val settings: List<ModuleSetting> = emptyList()
    
    /**
     * 是否有设置项
     */
    val hasSettings: Boolean
        get() = settings.isNotEmpty()
    
    companion object {
        private const val TAG = "KasumiModule"
        private const val PREFS_NAME = "kasumi_modules"
        private var prefs: SharedPreferences? = null
        private val listeners = mutableSetOf<ModuleStateListener>()

        fun init(context: Context) {
            prefs = context.getSharedPreferences(PREFS_NAME, Context.MODE_PRIVATE)
            // 初始化模块设置管理器
            ModuleSettingsManager.init(context)
            // 初始化配置管理器
            mc.meson.kasumi.config.ConfigManager.init(context)

            // 加载当前配置
            val currentConfig = mc.meson.kasumi.config.ConfigManager.getCurrentConfig()
            if (currentConfig != null) {
                Log.d(TAG, "Loading current config: ${currentConfig.name}")
                // 配置会在ConfigManager.init中自动应用
            }

            Log.d(TAG, "Module system initialized")
        }
        
        fun addListener(listener: ModuleStateListener) {
            listeners.add(listener)
        }
        
        fun removeListener(listener: ModuleStateListener) {
            listeners.remove(listener)
        }
        
        private fun notifyListeners(module: KasumiModule, enabled: Boolean) {
            listeners.forEach { listener ->
                try {
                    listener.onModuleStateChanged(module, enabled)
                } catch (e: Exception) {
                    Log.e(TAG, "Error notifying listener", e)
                }
            }

            // 自动保存当前状态到配置
            try {
                mc.meson.kasumi.config.ConfigManager.updateCurrentConfigState()
            } catch (e: Exception) {
                Log.e(TAG, "Error updating config state", e)
            }
        }
    }
    
    /**
     * 模块是否启用
     */
    var isEnabled: Boolean
        get() = prefs?.getBoolean(id, false) ?: false
        set(value) {
            prefs?.edit()?.putBoolean(id, value)?.apply()
            if (value) {
                onEnable()
            } else {
                onDisable()
            }
            notifyListeners(this, value)
            Log.d(TAG, "Module $name ${if (value) "enabled" else "disabled"}")
        }
    
    /**
     * 切换模块状态
     */
    fun toggle(): Boolean {
        isEnabled = !isEnabled
        return isEnabled
    }
    
    /**
     * 模块启用时调用
     */
    protected abstract fun onEnable()
    
    /**
     * 模块禁用时调用
     */
    protected abstract fun onDisable()
    
    /**
     * 获取模块详细信息
     */
    open fun getDetails(): String = description
    
    /**
     * 模块是否可用（某些模块可能需要特定条件）
     */
    open fun isAvailable(): Boolean = true
    
    /**
     * 获取设置值 - Boolean
     */
    protected fun getBooleanSetting(key: String, defaultValue: Boolean = false): Boolean {
        return ModuleSettingsManager.getBooleanSetting(id, key, defaultValue)
    }
    
    /**
     * 设置值 - Boolean
     */
    protected fun setBooleanSetting(key: String, value: Boolean) {
        ModuleSettingsManager.setBooleanSetting(id, key, value)
    }
    
    /**
     * 获取设置值 - Int
     */
    protected fun getIntSetting(key: String, defaultValue: Int = 0): Int {
        return ModuleSettingsManager.getIntSetting(id, key, defaultValue)
    }
    
    /**
     * 设置值 - Int
     */
    protected fun setIntSetting(key: String, value: Int) {
        ModuleSettingsManager.setIntSetting(id, key, value)
    }
    
    /**
     * 获取设置值 - Float
     */
    protected fun getFloatSetting(key: String, defaultValue: Float = 0f): Float {
        return ModuleSettingsManager.getFloatSetting(id, key, defaultValue)
    }
    
    /**
     * 设置值 - Float
     */
    protected fun setFloatSetting(key: String, value: Float) {
        ModuleSettingsManager.setFloatSetting(id, key, value)
    }
}

/**
 * 模块分类
 */
enum class ModuleCategory(val displayName: String, val color: Int) {
    COMBAT("战斗", 0xFFE57373.toInt()),
    MOVEMENT("移动", 0xFF81C784.toInt()),
    WORLD("世界", 0xFF64B5F6.toInt()),
    PLAYER("玩家", 0xFFFFB74D.toInt()),
    VISUAL("视觉", 0xFFBA68C8.toInt())
}

/**
 * 模块状态监听器
 */
interface ModuleStateListener {
    fun onModuleStateChanged(module: KasumiModule, enabled: Boolean)
}
