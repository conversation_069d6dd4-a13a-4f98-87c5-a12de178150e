<?xml version="1.0" encoding="utf-8" standalone="yes"?><Layout layout="item_module_grid" modulePackage="mc.meson.kasumi" filePath="app\src\main\res\layout\item_module_grid.xml" directory="layout" isMerge="false" isBindingData="false" rootNodeType="androidx.cardview.widget.CardView" rootNodeViewId="@+id/cardModule"><Targets><Target id="@+id/cardModule" tag="layout/item_module_grid_0" view="androidx.cardview.widget.CardView"><Expressions/><location startLine="1" startOffset="0" endLine="70" endOffset="35"/></Target><Target id="@+id/ivModuleIcon" view="ImageView"><Expressions/><location startLine="17" startOffset="8" endLine="24" endOffset="60"/></Target><Target id="@+id/switchModule" view="Switch"><Expressions/><location startLine="30" startOffset="8" endLine="37" endOffset="45"/></Target><Target id="@+id/tvModuleName" view="TextView"><Expressions/><location startLine="41" startOffset="8" endLine="53" endOffset="38"/></Target><Target id="@+id/tvModuleDescription" view="TextView"><Expressions/><location startLine="55" startOffset="8" endLine="66" endOffset="37"/></Target></Targets></Layout>