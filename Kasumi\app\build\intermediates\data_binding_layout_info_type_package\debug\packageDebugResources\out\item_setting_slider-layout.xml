<?xml version="1.0" encoding="utf-8" standalone="yes"?><Layout layout="item_setting_slider" modulePackage="mc.meson.kasumi" filePath="app\src\main\res\layout\item_setting_slider.xml" directory="layout" isMerge="false" isBindingData="false" rootNodeType="android.widget.LinearLayout"><Targets><Target tag="layout/item_setting_slider_0" view="LinearLayout"><Expressions/><location startLine="2" startOffset="0" endLine="62" endOffset="14"/></Target><Target id="@+id/tvSettingName" view="TextView"><Expressions/><location startLine="22" startOffset="12" endLine="29" endOffset="78"/></Target><Target id="@+id/tvSettingDescription" view="TextView"><Expressions/><location startLine="31" startOffset="12" endLine="38" endOffset="48"/></Target><Target id="@+id/tvCurrentValue" view="TextView"><Expressions/><location startLine="42" startOffset="8" endLine="51" endOffset="38"/></Target><Target id="@+id/seekBar" view="SeekBar"><Expressions/><location startLine="55" startOffset="4" endLine="60" endOffset="59"/></Target></Targets></Layout>