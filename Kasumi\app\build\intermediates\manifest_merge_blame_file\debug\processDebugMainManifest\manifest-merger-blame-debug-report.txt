1<?xml version="1.0" encoding="utf-8"?>
2<manifest xmlns:android="http://schemas.android.com/apk/res/android"
3    package="mc.meson.kasumi"
4    android:versionCode="1"
5    android:versionName="1.0" >
6
7    <uses-sdk
8        android:minSdkVersion="29"
9        android:targetSdkVersion="34" />
10
11    <!-- 悬浮窗权限 -->
12    <uses-permission android:name="android.permission.SYSTEM_ALERT_WINDOW" />
12-->D:\Kasumi\Kasumi\app\src\main\AndroidManifest.xml:6:5-78
12-->D:\<PERSON><PERSON><PERSON>\Kasumi\app\src\main\AndroidManifest.xml:6:22-75
13    <uses-permission android:name="android.permission.FOREGROUND_SERVICE" />
13-->D:\Kasumi\Kasumi\app\src\main\AndroidManifest.xml:7:5-77
13-->D:\Kasumi\Kasumi\app\src\main\AndroidManifest.xml:7:22-74
14    <uses-permission android:name="android.permission.FOREGROUND_SERVICE_SPECIAL_USE" />
14-->D:\Kasumi\Kasumi\app\src\main\AndroidManifest.xml:8:5-89
14-->D:\Kasumi\Kasumi\app\src\main\AndroidManifest.xml:8:22-86
15
16    <!-- 存储权限 -->
17    <uses-permission android:name="android.permission.READ_EXTERNAL_STORAGE" />
17-->D:\Kasumi\Kasumi\app\src\main\AndroidManifest.xml:11:5-80
17-->D:\Kasumi\Kasumi\app\src\main\AndroidManifest.xml:11:22-77
18    <uses-permission android:name="android.permission.WRITE_EXTERNAL_STORAGE" />
18-->D:\Kasumi\Kasumi\app\src\main\AndroidManifest.xml:12:5-81
18-->D:\Kasumi\Kasumi\app\src\main\AndroidManifest.xml:12:22-78
19
20    <permission
20-->[androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\transforms-4\3e36922ed6e480cd35722d6ef2c25ef7\transformed\core-1.13.1\AndroidManifest.xml:22:5-24:47
21        android:name="mc.meson.kasumi.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION"
21-->[androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\transforms-4\3e36922ed6e480cd35722d6ef2c25ef7\transformed\core-1.13.1\AndroidManifest.xml:23:9-81
22        android:protectionLevel="signature" />
22-->[androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\transforms-4\3e36922ed6e480cd35722d6ef2c25ef7\transformed\core-1.13.1\AndroidManifest.xml:24:9-44
23
24    <uses-permission android:name="mc.meson.kasumi.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION" />
24-->[androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\transforms-4\3e36922ed6e480cd35722d6ef2c25ef7\transformed\core-1.13.1\AndroidManifest.xml:26:5-97
24-->[androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\transforms-4\3e36922ed6e480cd35722d6ef2c25ef7\transformed\core-1.13.1\AndroidManifest.xml:26:22-94
25
26    <application
26-->D:\Kasumi\Kasumi\app\src\main\AndroidManifest.xml:14:5-50:19
27        android:allowBackup="true"
27-->D:\Kasumi\Kasumi\app\src\main\AndroidManifest.xml:15:9-35
28        android:appComponentFactory="androidx.core.app.CoreComponentFactory"
28-->[androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\transforms-4\3e36922ed6e480cd35722d6ef2c25ef7\transformed\core-1.13.1\AndroidManifest.xml:28:18-86
29        android:dataExtractionRules="@xml/data_extraction_rules"
29-->D:\Kasumi\Kasumi\app\src\main\AndroidManifest.xml:16:9-65
30        android:debuggable="true"
31        android:extractNativeLibs="false"
32        android:fullBackupContent="@xml/backup_rules"
32-->D:\Kasumi\Kasumi\app\src\main\AndroidManifest.xml:17:9-54
33        android:icon="@mipmap/ic_launcher"
33-->D:\Kasumi\Kasumi\app\src\main\AndroidManifest.xml:18:9-43
34        android:label="@string/app_name"
34-->D:\Kasumi\Kasumi\app\src\main\AndroidManifest.xml:19:9-41
35        android:roundIcon="@mipmap/ic_launcher_round"
35-->D:\Kasumi\Kasumi\app\src\main\AndroidManifest.xml:20:9-54
36        android:supportsRtl="true"
36-->D:\Kasumi\Kasumi\app\src\main\AndroidManifest.xml:21:9-35
37        android:testOnly="true"
38        android:theme="@style/Theme.Kasumi" >
38-->D:\Kasumi\Kasumi\app\src\main\AndroidManifest.xml:22:9-44
39        <activity
39-->D:\Kasumi\Kasumi\app\src\main\AndroidManifest.xml:24:9-33:20
40            android:name="mc.meson.kasumi.SplashActivity"
40-->D:\Kasumi\Kasumi\app\src\main\AndroidManifest.xml:25:13-43
41            android:exported="true"
41-->D:\Kasumi\Kasumi\app\src\main\AndroidManifest.xml:26:13-36
42            android:theme="@style/Theme.Kasumi" >
42-->D:\Kasumi\Kasumi\app\src\main\AndroidManifest.xml:27:13-48
43            <intent-filter>
43-->D:\Kasumi\Kasumi\app\src\main\AndroidManifest.xml:28:13-32:29
44                <action android:name="android.intent.action.MAIN" />
44-->D:\Kasumi\Kasumi\app\src\main\AndroidManifest.xml:29:17-69
44-->D:\Kasumi\Kasumi\app\src\main\AndroidManifest.xml:29:25-66
45
46                <category android:name="android.intent.category.LAUNCHER" />
46-->D:\Kasumi\Kasumi\app\src\main\AndroidManifest.xml:31:17-77
46-->D:\Kasumi\Kasumi\app\src\main\AndroidManifest.xml:31:27-74
47            </intent-filter>
48        </activity>
49        <activity
49-->D:\Kasumi\Kasumi\app\src\main\AndroidManifest.xml:35:9-38:51
50            android:name="mc.meson.kasumi.MainActivity"
50-->D:\Kasumi\Kasumi\app\src\main\AndroidManifest.xml:36:13-41
51            android:exported="false"
51-->D:\Kasumi\Kasumi\app\src\main\AndroidManifest.xml:37:13-37
52            android:theme="@style/Theme.Kasumi" />
52-->D:\Kasumi\Kasumi\app\src\main\AndroidManifest.xml:38:13-48
53
54        <!-- 悬浮窗服务 -->
55        <service
55-->D:\Kasumi\Kasumi\app\src\main\AndroidManifest.xml:41:9-49:19
56            android:name="mc.meson.kasumi.overlay.FloatingWindowService"
56-->D:\Kasumi\Kasumi\app\src\main\AndroidManifest.xml:42:13-58
57            android:enabled="true"
57-->D:\Kasumi\Kasumi\app\src\main\AndroidManifest.xml:43:13-35
58            android:exported="false"
58-->D:\Kasumi\Kasumi\app\src\main\AndroidManifest.xml:44:13-37
59            android:foregroundServiceType="specialUse" >
59-->D:\Kasumi\Kasumi\app\src\main\AndroidManifest.xml:45:13-55
60            <property
60-->D:\Kasumi\Kasumi\app\src\main\AndroidManifest.xml:46:13-48:43
61                android:name="android.app.PROPERTY_SPECIAL_USE_FGS_SUBTYPE"
61-->D:\Kasumi\Kasumi\app\src\main\AndroidManifest.xml:47:17-76
62                android:value="overlay" />
62-->D:\Kasumi\Kasumi\app\src\main\AndroidManifest.xml:48:17-40
63        </service>
64
65        <provider
65-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-4\07782488d9d333cff68182712e97e36a\transformed\emoji2-1.3.0\AndroidManifest.xml:24:9-32:20
66            android:name="androidx.startup.InitializationProvider"
66-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-4\07782488d9d333cff68182712e97e36a\transformed\emoji2-1.3.0\AndroidManifest.xml:25:13-67
67            android:authorities="mc.meson.kasumi.androidx-startup"
67-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-4\07782488d9d333cff68182712e97e36a\transformed\emoji2-1.3.0\AndroidManifest.xml:26:13-68
68            android:exported="false" >
68-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-4\07782488d9d333cff68182712e97e36a\transformed\emoji2-1.3.0\AndroidManifest.xml:27:13-37
69            <meta-data
69-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-4\07782488d9d333cff68182712e97e36a\transformed\emoji2-1.3.0\AndroidManifest.xml:29:13-31:52
70                android:name="androidx.emoji2.text.EmojiCompatInitializer"
70-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-4\07782488d9d333cff68182712e97e36a\transformed\emoji2-1.3.0\AndroidManifest.xml:30:17-75
71                android:value="androidx.startup" />
71-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-4\07782488d9d333cff68182712e97e36a\transformed\emoji2-1.3.0\AndroidManifest.xml:31:17-49
72            <meta-data
72-->[androidx.lifecycle:lifecycle-process:2.6.2] C:\Users\<USER>\.gradle\caches\transforms-4\a81848633018603888e9437eeee1e7a0\transformed\lifecycle-process-2.6.2\AndroidManifest.xml:29:13-31:52
73                android:name="androidx.lifecycle.ProcessLifecycleInitializer"
73-->[androidx.lifecycle:lifecycle-process:2.6.2] C:\Users\<USER>\.gradle\caches\transforms-4\a81848633018603888e9437eeee1e7a0\transformed\lifecycle-process-2.6.2\AndroidManifest.xml:30:17-78
74                android:value="androidx.startup" />
74-->[androidx.lifecycle:lifecycle-process:2.6.2] C:\Users\<USER>\.gradle\caches\transforms-4\a81848633018603888e9437eeee1e7a0\transformed\lifecycle-process-2.6.2\AndroidManifest.xml:31:17-49
75            <meta-data
75-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\transforms-4\e1aab3cc3064f8754a3c1233a3dc1ffc\transformed\profileinstaller-1.4.0\AndroidManifest.xml:29:13-31:52
76                android:name="androidx.profileinstaller.ProfileInstallerInitializer"
76-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\transforms-4\e1aab3cc3064f8754a3c1233a3dc1ffc\transformed\profileinstaller-1.4.0\AndroidManifest.xml:30:17-85
77                android:value="androidx.startup" />
77-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\transforms-4\e1aab3cc3064f8754a3c1233a3dc1ffc\transformed\profileinstaller-1.4.0\AndroidManifest.xml:31:17-49
78        </provider>
79
80        <uses-library
80-->[androidx.window:window:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\8fb39156b81ea7e0af57d2eaba813d1e\transformed\window-1.0.0\AndroidManifest.xml:25:9-27:40
81            android:name="androidx.window.extensions"
81-->[androidx.window:window:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\8fb39156b81ea7e0af57d2eaba813d1e\transformed\window-1.0.0\AndroidManifest.xml:26:13-54
82            android:required="false" />
82-->[androidx.window:window:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\8fb39156b81ea7e0af57d2eaba813d1e\transformed\window-1.0.0\AndroidManifest.xml:27:13-37
83        <uses-library
83-->[androidx.window:window:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\8fb39156b81ea7e0af57d2eaba813d1e\transformed\window-1.0.0\AndroidManifest.xml:28:9-30:40
84            android:name="androidx.window.sidecar"
84-->[androidx.window:window:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\8fb39156b81ea7e0af57d2eaba813d1e\transformed\window-1.0.0\AndroidManifest.xml:29:13-51
85            android:required="false" />
85-->[androidx.window:window:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\8fb39156b81ea7e0af57d2eaba813d1e\transformed\window-1.0.0\AndroidManifest.xml:30:13-37
86
87        <receiver
87-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\transforms-4\e1aab3cc3064f8754a3c1233a3dc1ffc\transformed\profileinstaller-1.4.0\AndroidManifest.xml:34:9-52:20
88            android:name="androidx.profileinstaller.ProfileInstallReceiver"
88-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\transforms-4\e1aab3cc3064f8754a3c1233a3dc1ffc\transformed\profileinstaller-1.4.0\AndroidManifest.xml:35:13-76
89            android:directBootAware="false"
89-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\transforms-4\e1aab3cc3064f8754a3c1233a3dc1ffc\transformed\profileinstaller-1.4.0\AndroidManifest.xml:36:13-44
90            android:enabled="true"
90-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\transforms-4\e1aab3cc3064f8754a3c1233a3dc1ffc\transformed\profileinstaller-1.4.0\AndroidManifest.xml:37:13-35
91            android:exported="true"
91-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\transforms-4\e1aab3cc3064f8754a3c1233a3dc1ffc\transformed\profileinstaller-1.4.0\AndroidManifest.xml:38:13-36
92            android:permission="android.permission.DUMP" >
92-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\transforms-4\e1aab3cc3064f8754a3c1233a3dc1ffc\transformed\profileinstaller-1.4.0\AndroidManifest.xml:39:13-57
93            <intent-filter>
93-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\transforms-4\e1aab3cc3064f8754a3c1233a3dc1ffc\transformed\profileinstaller-1.4.0\AndroidManifest.xml:40:13-42:29
94                <action android:name="androidx.profileinstaller.action.INSTALL_PROFILE" />
94-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\transforms-4\e1aab3cc3064f8754a3c1233a3dc1ffc\transformed\profileinstaller-1.4.0\AndroidManifest.xml:41:17-91
94-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\transforms-4\e1aab3cc3064f8754a3c1233a3dc1ffc\transformed\profileinstaller-1.4.0\AndroidManifest.xml:41:25-88
95            </intent-filter>
96            <intent-filter>
96-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\transforms-4\e1aab3cc3064f8754a3c1233a3dc1ffc\transformed\profileinstaller-1.4.0\AndroidManifest.xml:43:13-45:29
97                <action android:name="androidx.profileinstaller.action.SKIP_FILE" />
97-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\transforms-4\e1aab3cc3064f8754a3c1233a3dc1ffc\transformed\profileinstaller-1.4.0\AndroidManifest.xml:44:17-85
97-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\transforms-4\e1aab3cc3064f8754a3c1233a3dc1ffc\transformed\profileinstaller-1.4.0\AndroidManifest.xml:44:25-82
98            </intent-filter>
99            <intent-filter>
99-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\transforms-4\e1aab3cc3064f8754a3c1233a3dc1ffc\transformed\profileinstaller-1.4.0\AndroidManifest.xml:46:13-48:29
100                <action android:name="androidx.profileinstaller.action.SAVE_PROFILE" />
100-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\transforms-4\e1aab3cc3064f8754a3c1233a3dc1ffc\transformed\profileinstaller-1.4.0\AndroidManifest.xml:47:17-88
100-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\transforms-4\e1aab3cc3064f8754a3c1233a3dc1ffc\transformed\profileinstaller-1.4.0\AndroidManifest.xml:47:25-85
101            </intent-filter>
102            <intent-filter>
102-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\transforms-4\e1aab3cc3064f8754a3c1233a3dc1ffc\transformed\profileinstaller-1.4.0\AndroidManifest.xml:49:13-51:29
103                <action android:name="androidx.profileinstaller.action.BENCHMARK_OPERATION" />
103-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\transforms-4\e1aab3cc3064f8754a3c1233a3dc1ffc\transformed\profileinstaller-1.4.0\AndroidManifest.xml:50:17-95
103-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\transforms-4\e1aab3cc3064f8754a3c1233a3dc1ffc\transformed\profileinstaller-1.4.0\AndroidManifest.xml:50:25-92
104            </intent-filter>
105        </receiver>
106    </application>
107
108</manifest>
