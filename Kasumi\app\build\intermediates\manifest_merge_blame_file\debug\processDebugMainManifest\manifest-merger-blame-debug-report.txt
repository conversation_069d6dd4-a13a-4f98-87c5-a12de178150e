1<?xml version="1.0" encoding="utf-8"?>
2<manifest xmlns:android="http://schemas.android.com/apk/res/android"
3    package="mc.meson.kasumi"
4    android:versionCode="1"
5    android:versionName="1.0" >
6
7    <uses-sdk
8        android:minSdkVersion="29"
9        android:targetSdkVersion="34" />
10
11    <!-- 悬浮窗权限 -->
12    <uses-permission android:name="android.permission.SYSTEM_ALERT_WINDOW" />
12-->D:\Kasumi\Kasumi\app\src\main\AndroidManifest.xml:6:5-78
12-->D:\<PERSON><PERSON><PERSON>\Kasumi\app\src\main\AndroidManifest.xml:6:22-75
13    <uses-permission android:name="android.permission.FOREGROUND_SERVICE" />
13-->D:\Kasumi\Kasumi\app\src\main\AndroidManifest.xml:7:5-77
13-->D:\Ka<PERSON>mi\Kasumi\app\src\main\AndroidManifest.xml:7:22-74
14    <uses-permission android:name="android.permission.FOREGROUND_SERVICE_SPECIAL_USE" />
14-->D:\Kasumi\Kasumi\app\src\main\AndroidManifest.xml:8:5-89
14-->D:\Kasumi\Kasumi\app\src\main\AndroidManifest.xml:8:22-86
15
16    <permission
16-->[androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\transforms-4\e250c14d88b0412b0bb8e6f5e61db53c\transformed\core-1.13.1\AndroidManifest.xml:22:5-24:47
17        android:name="mc.meson.kasumi.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION"
17-->[androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\transforms-4\e250c14d88b0412b0bb8e6f5e61db53c\transformed\core-1.13.1\AndroidManifest.xml:23:9-81
18        android:protectionLevel="signature" />
18-->[androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\transforms-4\e250c14d88b0412b0bb8e6f5e61db53c\transformed\core-1.13.1\AndroidManifest.xml:24:9-44
19
20    <uses-permission android:name="mc.meson.kasumi.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION" />
20-->[androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\transforms-4\e250c14d88b0412b0bb8e6f5e61db53c\transformed\core-1.13.1\AndroidManifest.xml:26:5-97
20-->[androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\transforms-4\e250c14d88b0412b0bb8e6f5e61db53c\transformed\core-1.13.1\AndroidManifest.xml:26:22-94
21
22    <application
22-->D:\Kasumi\Kasumi\app\src\main\AndroidManifest.xml:10:5-46:19
23        android:allowBackup="true"
23-->D:\Kasumi\Kasumi\app\src\main\AndroidManifest.xml:11:9-35
24        android:appComponentFactory="androidx.core.app.CoreComponentFactory"
24-->[androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\transforms-4\e250c14d88b0412b0bb8e6f5e61db53c\transformed\core-1.13.1\AndroidManifest.xml:28:18-86
25        android:dataExtractionRules="@xml/data_extraction_rules"
25-->D:\Kasumi\Kasumi\app\src\main\AndroidManifest.xml:12:9-65
26        android:debuggable="true"
27        android:extractNativeLibs="false"
28        android:fullBackupContent="@xml/backup_rules"
28-->D:\Kasumi\Kasumi\app\src\main\AndroidManifest.xml:13:9-54
29        android:icon="@mipmap/ic_launcher"
29-->D:\Kasumi\Kasumi\app\src\main\AndroidManifest.xml:14:9-43
30        android:label="@string/app_name"
30-->D:\Kasumi\Kasumi\app\src\main\AndroidManifest.xml:15:9-41
31        android:roundIcon="@mipmap/ic_launcher_round"
31-->D:\Kasumi\Kasumi\app\src\main\AndroidManifest.xml:16:9-54
32        android:supportsRtl="true"
32-->D:\Kasumi\Kasumi\app\src\main\AndroidManifest.xml:17:9-35
33        android:testOnly="true"
34        android:theme="@style/Theme.Kasumi" >
34-->D:\Kasumi\Kasumi\app\src\main\AndroidManifest.xml:18:9-44
35        <activity
35-->D:\Kasumi\Kasumi\app\src\main\AndroidManifest.xml:20:9-29:20
36            android:name="mc.meson.kasumi.SplashActivity"
36-->D:\Kasumi\Kasumi\app\src\main\AndroidManifest.xml:21:13-43
37            android:exported="true"
37-->D:\Kasumi\Kasumi\app\src\main\AndroidManifest.xml:22:13-36
38            android:theme="@style/Theme.Kasumi" >
38-->D:\Kasumi\Kasumi\app\src\main\AndroidManifest.xml:23:13-48
39            <intent-filter>
39-->D:\Kasumi\Kasumi\app\src\main\AndroidManifest.xml:24:13-28:29
40                <action android:name="android.intent.action.MAIN" />
40-->D:\Kasumi\Kasumi\app\src\main\AndroidManifest.xml:25:17-69
40-->D:\Kasumi\Kasumi\app\src\main\AndroidManifest.xml:25:25-66
41
42                <category android:name="android.intent.category.LAUNCHER" />
42-->D:\Kasumi\Kasumi\app\src\main\AndroidManifest.xml:27:17-77
42-->D:\Kasumi\Kasumi\app\src\main\AndroidManifest.xml:27:27-74
43            </intent-filter>
44        </activity>
45        <activity
45-->D:\Kasumi\Kasumi\app\src\main\AndroidManifest.xml:31:9-34:51
46            android:name="mc.meson.kasumi.MainActivity"
46-->D:\Kasumi\Kasumi\app\src\main\AndroidManifest.xml:32:13-41
47            android:exported="false"
47-->D:\Kasumi\Kasumi\app\src\main\AndroidManifest.xml:33:13-37
48            android:theme="@style/Theme.Kasumi" />
48-->D:\Kasumi\Kasumi\app\src\main\AndroidManifest.xml:34:13-48
49
50        <!-- 悬浮窗服务 -->
51        <service
51-->D:\Kasumi\Kasumi\app\src\main\AndroidManifest.xml:37:9-45:19
52            android:name="mc.meson.kasumi.overlay.FloatingWindowService"
52-->D:\Kasumi\Kasumi\app\src\main\AndroidManifest.xml:38:13-58
53            android:enabled="true"
53-->D:\Kasumi\Kasumi\app\src\main\AndroidManifest.xml:39:13-35
54            android:exported="false"
54-->D:\Kasumi\Kasumi\app\src\main\AndroidManifest.xml:40:13-37
55            android:foregroundServiceType="specialUse" >
55-->D:\Kasumi\Kasumi\app\src\main\AndroidManifest.xml:41:13-55
56            <property
56-->D:\Kasumi\Kasumi\app\src\main\AndroidManifest.xml:42:13-44:43
57                android:name="android.app.PROPERTY_SPECIAL_USE_FGS_SUBTYPE"
57-->D:\Kasumi\Kasumi\app\src\main\AndroidManifest.xml:43:17-76
58                android:value="overlay" />
58-->D:\Kasumi\Kasumi\app\src\main\AndroidManifest.xml:44:17-40
59        </service>
60
61        <provider
61-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-4\c310bda4973a4914bb88a2351a33a468\transformed\emoji2-1.3.0\AndroidManifest.xml:24:9-32:20
62            android:name="androidx.startup.InitializationProvider"
62-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-4\c310bda4973a4914bb88a2351a33a468\transformed\emoji2-1.3.0\AndroidManifest.xml:25:13-67
63            android:authorities="mc.meson.kasumi.androidx-startup"
63-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-4\c310bda4973a4914bb88a2351a33a468\transformed\emoji2-1.3.0\AndroidManifest.xml:26:13-68
64            android:exported="false" >
64-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-4\c310bda4973a4914bb88a2351a33a468\transformed\emoji2-1.3.0\AndroidManifest.xml:27:13-37
65            <meta-data
65-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-4\c310bda4973a4914bb88a2351a33a468\transformed\emoji2-1.3.0\AndroidManifest.xml:29:13-31:52
66                android:name="androidx.emoji2.text.EmojiCompatInitializer"
66-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-4\c310bda4973a4914bb88a2351a33a468\transformed\emoji2-1.3.0\AndroidManifest.xml:30:17-75
67                android:value="androidx.startup" />
67-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-4\c310bda4973a4914bb88a2351a33a468\transformed\emoji2-1.3.0\AndroidManifest.xml:31:17-49
68            <meta-data
68-->[androidx.lifecycle:lifecycle-process:2.6.2] C:\Users\<USER>\.gradle\caches\transforms-4\e3e332e1b9f1e898ad45bc679d5d5849\transformed\lifecycle-process-2.6.2\AndroidManifest.xml:29:13-31:52
69                android:name="androidx.lifecycle.ProcessLifecycleInitializer"
69-->[androidx.lifecycle:lifecycle-process:2.6.2] C:\Users\<USER>\.gradle\caches\transforms-4\e3e332e1b9f1e898ad45bc679d5d5849\transformed\lifecycle-process-2.6.2\AndroidManifest.xml:30:17-78
70                android:value="androidx.startup" />
70-->[androidx.lifecycle:lifecycle-process:2.6.2] C:\Users\<USER>\.gradle\caches\transforms-4\e3e332e1b9f1e898ad45bc679d5d5849\transformed\lifecycle-process-2.6.2\AndroidManifest.xml:31:17-49
71            <meta-data
71-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\transforms-4\2d0c6c6c966d89531a3d7f8f6b718acd\transformed\profileinstaller-1.4.0\AndroidManifest.xml:29:13-31:52
72                android:name="androidx.profileinstaller.ProfileInstallerInitializer"
72-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\transforms-4\2d0c6c6c966d89531a3d7f8f6b718acd\transformed\profileinstaller-1.4.0\AndroidManifest.xml:30:17-85
73                android:value="androidx.startup" />
73-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\transforms-4\2d0c6c6c966d89531a3d7f8f6b718acd\transformed\profileinstaller-1.4.0\AndroidManifest.xml:31:17-49
74        </provider>
75
76        <uses-library
76-->[androidx.window:window:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\a0307c1c436af89ef06094848403c6b0\transformed\window-1.0.0\AndroidManifest.xml:25:9-27:40
77            android:name="androidx.window.extensions"
77-->[androidx.window:window:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\a0307c1c436af89ef06094848403c6b0\transformed\window-1.0.0\AndroidManifest.xml:26:13-54
78            android:required="false" />
78-->[androidx.window:window:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\a0307c1c436af89ef06094848403c6b0\transformed\window-1.0.0\AndroidManifest.xml:27:13-37
79        <uses-library
79-->[androidx.window:window:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\a0307c1c436af89ef06094848403c6b0\transformed\window-1.0.0\AndroidManifest.xml:28:9-30:40
80            android:name="androidx.window.sidecar"
80-->[androidx.window:window:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\a0307c1c436af89ef06094848403c6b0\transformed\window-1.0.0\AndroidManifest.xml:29:13-51
81            android:required="false" />
81-->[androidx.window:window:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\a0307c1c436af89ef06094848403c6b0\transformed\window-1.0.0\AndroidManifest.xml:30:13-37
82
83        <receiver
83-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\transforms-4\2d0c6c6c966d89531a3d7f8f6b718acd\transformed\profileinstaller-1.4.0\AndroidManifest.xml:34:9-52:20
84            android:name="androidx.profileinstaller.ProfileInstallReceiver"
84-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\transforms-4\2d0c6c6c966d89531a3d7f8f6b718acd\transformed\profileinstaller-1.4.0\AndroidManifest.xml:35:13-76
85            android:directBootAware="false"
85-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\transforms-4\2d0c6c6c966d89531a3d7f8f6b718acd\transformed\profileinstaller-1.4.0\AndroidManifest.xml:36:13-44
86            android:enabled="true"
86-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\transforms-4\2d0c6c6c966d89531a3d7f8f6b718acd\transformed\profileinstaller-1.4.0\AndroidManifest.xml:37:13-35
87            android:exported="true"
87-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\transforms-4\2d0c6c6c966d89531a3d7f8f6b718acd\transformed\profileinstaller-1.4.0\AndroidManifest.xml:38:13-36
88            android:permission="android.permission.DUMP" >
88-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\transforms-4\2d0c6c6c966d89531a3d7f8f6b718acd\transformed\profileinstaller-1.4.0\AndroidManifest.xml:39:13-57
89            <intent-filter>
89-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\transforms-4\2d0c6c6c966d89531a3d7f8f6b718acd\transformed\profileinstaller-1.4.0\AndroidManifest.xml:40:13-42:29
90                <action android:name="androidx.profileinstaller.action.INSTALL_PROFILE" />
90-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\transforms-4\2d0c6c6c966d89531a3d7f8f6b718acd\transformed\profileinstaller-1.4.0\AndroidManifest.xml:41:17-91
90-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\transforms-4\2d0c6c6c966d89531a3d7f8f6b718acd\transformed\profileinstaller-1.4.0\AndroidManifest.xml:41:25-88
91            </intent-filter>
92            <intent-filter>
92-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\transforms-4\2d0c6c6c966d89531a3d7f8f6b718acd\transformed\profileinstaller-1.4.0\AndroidManifest.xml:43:13-45:29
93                <action android:name="androidx.profileinstaller.action.SKIP_FILE" />
93-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\transforms-4\2d0c6c6c966d89531a3d7f8f6b718acd\transformed\profileinstaller-1.4.0\AndroidManifest.xml:44:17-85
93-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\transforms-4\2d0c6c6c966d89531a3d7f8f6b718acd\transformed\profileinstaller-1.4.0\AndroidManifest.xml:44:25-82
94            </intent-filter>
95            <intent-filter>
95-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\transforms-4\2d0c6c6c966d89531a3d7f8f6b718acd\transformed\profileinstaller-1.4.0\AndroidManifest.xml:46:13-48:29
96                <action android:name="androidx.profileinstaller.action.SAVE_PROFILE" />
96-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\transforms-4\2d0c6c6c966d89531a3d7f8f6b718acd\transformed\profileinstaller-1.4.0\AndroidManifest.xml:47:17-88
96-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\transforms-4\2d0c6c6c966d89531a3d7f8f6b718acd\transformed\profileinstaller-1.4.0\AndroidManifest.xml:47:25-85
97            </intent-filter>
98            <intent-filter>
98-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\transforms-4\2d0c6c6c966d89531a3d7f8f6b718acd\transformed\profileinstaller-1.4.0\AndroidManifest.xml:49:13-51:29
99                <action android:name="androidx.profileinstaller.action.BENCHMARK_OPERATION" />
99-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\transforms-4\2d0c6c6c966d89531a3d7f8f6b718acd\transformed\profileinstaller-1.4.0\AndroidManifest.xml:50:17-95
99-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\transforms-4\2d0c6c6c966d89531a3d7f8f6b718acd\transformed\profileinstaller-1.4.0\AndroidManifest.xml:50:25-92
100            </intent-filter>
101        </receiver>
102    </application>
103
104</manifest>
