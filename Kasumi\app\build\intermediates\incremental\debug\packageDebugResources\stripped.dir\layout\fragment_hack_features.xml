<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:orientation="vertical"
    android:padding="16dp">

    <!-- 配置快速切换工具栏 -->
    <com.google.android.material.card.MaterialCardView
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginBottom="16dp"
        app:cardElevation="2dp"
        app:cardCornerRadius="12dp"
        app:strokeWidth="1dp"
        app:strokeColor="?attr/colorOutline"
        style="@style/Widget.Material3.CardView.Outlined">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="horizontal"
            android:padding="12dp"
            android:gravity="center_vertical">

            <TextView
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:text="当前配置:"
                android:textAppearance="?attr/textAppearanceBodyMedium"
                android:textColor="?attr/colorOnSurface" />

            <TextView
                android:id="@+id/tvCurrentConfigName"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="默认配置"
                android:textAppearance="?attr/textAppearanceBodyMedium"
                android:textColor="?attr/colorPrimary"
                android:layout_marginEnd="8dp" />

            <com.google.android.material.button.MaterialButton
                android:id="@+id/btnQuickSwitchConfig"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="切换"
                style="@style/Widget.Material3.Button.TextButton" />

        </LinearLayout>

    </com.google.android.material.card.MaterialCardView>

    <!-- 模块网格 -->
    <androidx.recyclerview.widget.RecyclerView
        android:id="@+id/recyclerViewModules"
        android:layout_width="match_parent"
        android:layout_height="match_parent" />

</LinearLayout>
