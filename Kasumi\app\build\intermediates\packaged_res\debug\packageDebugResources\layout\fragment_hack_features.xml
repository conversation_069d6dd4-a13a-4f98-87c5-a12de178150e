<?xml version="1.0" encoding="utf-8"?>
<androidx.coordinatorlayout.widget.CoordinatorLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="match_parent">

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:orientation="vertical">

        <!-- 配置管理按钮 -->
        <com.google.android.material.card.MaterialCardView
            android:id="@+id/cardConfigManagement"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_margin="16dp"
            app:cardElevation="2dp"
            app:cardCornerRadius="12dp"
            app:cardBackgroundColor="?attr/colorPrimaryContainer"
            android:foreground="?attr/selectableItemBackground">

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="horizontal"
                android:padding="16dp"
                android:gravity="center_vertical">

                <ImageView
                    android:layout_width="32dp"
                    android:layout_height="32dp"
                    android:src="@drawable/ic_settings_24"
                    app:tint="?attr/colorOnPrimaryContainer" />

                <LinearLayout
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_weight="1"
                    android:orientation="vertical"
                    android:layout_marginStart="12dp">

                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="配置管理"
                        android:textAppearance="?attr/textAppearanceTitleMedium"
                        android:textColor="?attr/colorOnPrimaryContainer"
                        android:textStyle="bold" />

                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="创建、切换、导入导出配置"
                        android:textAppearance="?attr/textAppearanceBodyMedium"
                        android:textColor="?attr/colorOnPrimaryContainer"
                        android:layout_marginTop="2dp" />

                </LinearLayout>

                <ImageView
                    android:layout_width="24dp"
                    android:layout_height="24dp"
                    android:src="@drawable/ic_arrow_back_24"
                    android:rotation="180"
                    app:tint="?attr/colorOnPrimaryContainer" />

            </LinearLayout>

        </com.google.android.material.card.MaterialCardView>

        <!-- 模块网格 -->
        <androidx.recyclerview.widget.RecyclerView
            android:id="@+id/recyclerViewModules"
            android:layout_width="match_parent"
            android:layout_height="0dp"
            android:layout_weight="1"
            android:paddingHorizontal="16dp"
            android:clipToPadding="false" />

    </LinearLayout>

</androidx.coordinatorlayout.widget.CoordinatorLayout>
