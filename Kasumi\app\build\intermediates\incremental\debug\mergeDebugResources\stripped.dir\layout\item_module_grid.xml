<?xml version="1.0" encoding="utf-8"?>
<androidx.cardview.widget.CardView xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:id="@+id/cardModule"
    android:layout_width="match_parent"
    android:layout_height="100dp"
    android:layout_margin="4dp"
    app:cardCornerRadius="16dp"
    app:cardElevation="2dp"
    android:background="?attr/selectableItemBackground">

    <RelativeLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:padding="12dp">

        <!-- 左上角图标 -->
        <ImageView
            android:id="@+id/ivModuleIcon"
            android:layout_width="24dp"
            android:layout_height="24dp"
            android:layout_alignParentStart="true"
            android:layout_alignParentTop="true"
            android:src="@drawable/ic_close"
            android:tint="@color/md_theme_light_onSurface" />

        <!-- 右上角开关 -->

        <!-- 模块名称 -->

        <Switch
            android:id="@+id/switchModule"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_alignParentTop="true"
            android:layout_alignParentEnd="true"
            android:layout_marginTop="-2dp"
            android:layout_marginEnd="-2dp" />

        <!-- 模块描述 -->

        <TextView
            android:id="@+id/tvModuleName"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_below="@id/ivModuleIcon"
            android:layout_marginTop="15dp"
            android:layout_marginEnd="10dp"
            android:ellipsize="end"
            android:maxLines="1"
            android:text="模块名称"
            android:textColor="@color/md_theme_light_onSurface"
            android:textSize="14sp"
            android:textStyle="bold" />

        <TextView
            android:id="@+id/tvModuleDescription"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_below="@id/tvModuleName"
            android:layout_marginTop="2dp"
            android:layout_marginEnd="15dp"
            android:ellipsize="end"
            android:maxLines="2"
            android:text="模块描述"
            android:textColor="@color/md_theme_light_onSurfaceVariant"
            android:textSize="11sp" />

    </RelativeLayout>

</androidx.cardview.widget.CardView>
