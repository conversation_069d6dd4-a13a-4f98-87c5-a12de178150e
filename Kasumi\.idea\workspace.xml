<?xml version="1.0" encoding="UTF-8"?>
<project version="4">
  <component name="AndroidLayouts">
    <shared>
      <config />
    </shared>
    <layouts>
      <layout url="file://$PROJECT_DIR$/app/src/main/res/layout/activity_main.xml">
        <config>
          <theme>@style/Theme.Kasumi</theme>
        </config>
      </layout>
      <layout url="file://$PROJECT_DIR$/app/src/main/res/layout/activity_main_with_navigation.xml">
        <config>
          <theme>@style/Theme.Kasumi</theme>
        </config>
      </layout>
      <layout url="file://$PROJECT_DIR$/app/src/main/res/layout/activity_splash.xml">
        <config>
          <theme>@style/Theme.Kasumi</theme>
        </config>
      </layout>
      <layout url="file://$PROJECT_DIR$/app/src/main/res/layout/compact_category_page.xml">
        <config>
          <theme>@style/Theme.Kasumi</theme>
        </config>
      </layout>
      <layout url="file://$PROJECT_DIR$/app/src/main/res/layout/floating_window_collapsed.xml">
        <config>
          <theme>@style/Theme.Kasumi</theme>
        </config>
      </layout>
      <layout url="file://$PROJECT_DIR$/app/src/main/res/layout/floating_window_compact.xml">
        <config>
          <theme>@style/Theme.Kasumi</theme>
        </config>
      </layout>
      <layout url="file://$PROJECT_DIR$/app/src/main/res/layout/floating_window_compact_v2.xml">
        <config>
          <theme>@style/Theme.Kasumi</theme>
        </config>
      </layout>
      <layout url="file://$PROJECT_DIR$/app/src/main/res/layout/floating_window_expanded_new.xml">
        <config>
          <theme>@style/Theme.Kasumi</theme>
        </config>
      </layout>
      <layout url="file://$PROJECT_DIR$/app/src/main/res/layout/floating_window_expanded_v2.xml">
        <config>
          <theme>@style/Theme.Kasumi</theme>
        </config>
      </layout>
      <layout url="file://$PROJECT_DIR$/app/src/main/res/layout/fragment_hack_features.xml">
        <config>
          <theme>@style/Theme.Kasumi</theme>
        </config>
      </layout>
      <layout url="file://$PROJECT_DIR$/app/src/main/res/layout/fragment_home.xml">
        <config>
          <theme>@style/Theme.Kasumi</theme>
        </config>
      </layout>
      <layout url="file://$PROJECT_DIR$/app/src/main/res/layout/fragment_settings.xml">
        <config>
          <theme>@style/Theme.Kasumi</theme>
        </config>
      </layout>
      <layout url="file://$PROJECT_DIR$/app/src/main/res/layout/item_compact_module.xml">
        <config>
          <theme>@style/Theme.Kasumi</theme>
        </config>
      </layout>
      <layout url="file://$PROJECT_DIR$/app/src/main/res/layout/item_expanded_module.xml">
        <config>
          <theme>@style/Theme.Kasumi</theme>
        </config>
      </layout>
      <layout url="file://$PROJECT_DIR$/app/src/main/res/layout/item_mode_option.xml">
        <config>
          <theme>@style/Theme.Kasumi</theme>
        </config>
      </layout>
      <layout url="file://$PROJECT_DIR$/app/src/main/res/layout/item_module_grid.xml">
        <config>
          <theme>@style/Theme.Kasumi</theme>
        </config>
      </layout>
      <layout url="file://$PROJECT_DIR$/app/src/main/res/layout/item_module_list.xml">
        <config>
          <theme>@style/Theme.Kasumi</theme>
        </config>
      </layout>
      <layout url="file://$PROJECT_DIR$/app/src/main/res/layout/item_setting_mode.xml">
        <config>
          <theme>@style/Theme.Kasumi</theme>
        </config>
      </layout>
      <layout url="file://$PROJECT_DIR$/app/src/main/res/layout/item_setting_slider.xml">
        <config>
          <theme>@style/Theme.Kasumi</theme>
        </config>
      </layout>
    </layouts>
  </component>
  <component name="AutoImportSettings">
    <option name="autoReloadType" value="NONE" />
  </component>
  <component name="ChangeListManager">
    <list default="true" id="601993bc-4af3-4f8b-85ab-8a295399269f" name="更改" comment="">
      <change beforePath="$PROJECT_DIR$/app/build.gradle.kts" beforeDir="false" afterPath="$PROJECT_DIR$/app/build.gradle.kts" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/src/main/java/mc/meson/kasumi/HackFeaturesFragment.kt" beforeDir="false" afterPath="$PROJECT_DIR$/app/src/main/java/mc/meson/kasumi/HackFeaturesFragment.kt" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/src/main/java/mc/meson/kasumi/module/KasumiModule.kt" beforeDir="false" afterPath="$PROJECT_DIR$/app/src/main/java/mc/meson/kasumi/module/KasumiModule.kt" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/src/main/java/mc/meson/kasumi/module/ModuleAdapter.kt" beforeDir="false" afterPath="$PROJECT_DIR$/app/src/main/java/mc/meson/kasumi/module/ModuleAdapter.kt" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/src/main/java/mc/meson/kasumi/module/ModuleSetting.kt" beforeDir="false" afterPath="$PROJECT_DIR$/app/src/main/java/mc/meson/kasumi/module/ModuleSetting.kt" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/src/main/res/layout/fragment_hack_features.xml" beforeDir="false" afterPath="$PROJECT_DIR$/app/src/main/res/layout/fragment_hack_features.xml" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/gradle/libs.versions.toml" beforeDir="false" afterPath="$PROJECT_DIR$/gradle/libs.versions.toml" afterDir="false" />
    </list>
    <option name="SHOW_DIALOG" value="false" />
    <option name="HIGHLIGHT_CONFLICTS" value="true" />
    <option name="HIGHLIGHT_NON_ACTIVE_CHANGELIST" value="false" />
    <option name="LAST_RESOLUTION" value="IGNORE" />
  </component>
  <component name="ClangdSettings">
    <option name="formatViaClangd" value="false" />
  </component>
  <component name="ExecutionTargetManager" SELECTED_TARGET="device_and_snapshot_combo_box_target[DeviceId(pluginId=PhysicalDevice, isTemplate=false, identifier=serial=D12313310615)]" />
  <component name="ExternalProjectsData">
    <projectState path="$PROJECT_DIR$">
      <ProjectState />
    </projectState>
  </component>
  <component name="ExternalProjectsManager">
    <system id="GRADLE">
      <state>
        <task path="$PROJECT_DIR$/app">
          <activation />
        </task>
        <projects_view>
          <tree_state>
            <expand>
              <path>
                <item name="" type="6a2764b6:ExternalProjectsStructure$RootNode" />
                <item name="Kasumi" type="f1a62948:ProjectNode" />
              </path>
              <path>
                <item name="" type="6a2764b6:ExternalProjectsStructure$RootNode" />
                <item name="Kasumi" type="f1a62948:ProjectNode" />
                <item name="app" type="2d1252cf:ModuleNode" />
              </path>
              <path>
                <item name="" type="6a2764b6:ExternalProjectsStructure$RootNode" />
                <item name="Kasumi" type="f1a62948:ProjectNode" />
                <item name="app" type="2d1252cf:ModuleNode" />
                <item name="Tasks" type="e4a08cd1:TasksNode" />
              </path>
              <path>
                <item name="" type="6a2764b6:ExternalProjectsStructure$RootNode" />
                <item name="Kasumi" type="f1a62948:ProjectNode" />
                <item name="app" type="2d1252cf:ModuleNode" />
                <item name="Tasks" type="e4a08cd1:TasksNode" />
                <item name="other" type="c8890929:TasksNode$1" />
              </path>
            </expand>
            <select />
          </tree_state>
        </projects_view>
      </state>
    </system>
  </component>
  <component name="Git.Settings">
    <option name="RECENT_GIT_ROOT_PATH" value="$PROJECT_DIR$/.." />
  </component>
  <component name="ProjectColorInfo">{
  &quot;customColor&quot;: &quot;&quot;,
  &quot;associatedIndex&quot;: 2
}</component>
  <component name="ProjectId" id="30OejIhoRicLVVLUNzovVZYS9Gw" />
  <component name="ProjectViewState">
    <option name="hideEmptyMiddlePackages" value="true" />
    <option name="showLibraryContents" value="true" />
  </component>
  <component name="PropertiesComponent">{
  &quot;keyToString&quot;: {
    &quot;Android App.app.executor&quot;: &quot;Run&quot;,
    &quot;RunOnceActivity.ShowReadmeOnStart&quot;: &quot;true&quot;,
    &quot;RunOnceActivity.cidr.known.project.marker&quot;: &quot;true&quot;,
    &quot;RunOnceActivity.readMode.enableVisualFormatting&quot;: &quot;true&quot;,
    &quot;cf.first.check.clang-format&quot;: &quot;false&quot;,
    &quot;cidr.known.project.marker&quot;: &quot;true&quot;,
    &quot;git-widget-placeholder&quot;: &quot;main&quot;,
    &quot;kotlin-language-version-configured&quot;: &quot;true&quot;,
    &quot;last_opened_file_path&quot;: &quot;D:/Kasumi/Kasumi&quot;,
    &quot;show.do.not.copy.http.proxy.settings.to.gradle&quot;: &quot;true&quot;
  },
  &quot;keyToStringList&quot;: {
    &quot;kotlin-gradle-user-dirs&quot;: [
      &quot;C:\\Users\\<USER>\\.gradle&quot;
    ]
  }
}</component>
  <component name="RunManager">
    <configuration name="app" type="AndroidRunConfigurationType" factoryName="Android App">
      <module name="Kasumi.app.main" />
      <option name="DEPLOY" value="true" />
      <option name="DEPLOY_APK_FROM_BUNDLE" value="false" />
      <option name="DEPLOY_AS_INSTANT" value="false" />
      <option name="ARTIFACT_NAME" value="" />
      <option name="PM_INSTALL_OPTIONS" value="" />
      <option name="ALL_USERS" value="false" />
      <option name="ALWAYS_INSTALL_WITH_PM" value="false" />
      <option name="CLEAR_APP_STORAGE" value="false" />
      <option name="DYNAMIC_FEATURES_DISABLED_LIST" value="" />
      <option name="ACTIVITY_EXTRA_FLAGS" value="" />
      <option name="MODE" value="default_activity" />
      <option name="CLEAR_LOGCAT" value="false" />
      <option name="SHOW_LOGCAT_AUTOMATICALLY" value="false" />
      <option name="TARGET_SELECTION_MODE" value="DEVICE_AND_SNAPSHOT_COMBO_BOX" />
      <option name="SELECTED_CLOUD_MATRIX_CONFIGURATION_ID" value="-1" />
      <option name="SELECTED_CLOUD_MATRIX_PROJECT_ID" value="" />
      <option name="DEBUGGER_TYPE" value="Auto" />
      <Auto>
        <option name="USE_JAVA_AWARE_DEBUGGER" value="false" />
        <option name="SHOW_STATIC_VARS" value="true" />
        <option name="WORKING_DIR" value="" />
        <option name="TARGET_LOGGING_CHANNELS" value="lldb process:gdb-remote packets" />
        <option name="SHOW_OPTIMIZED_WARNING" value="true" />
        <option name="ATTACH_ON_WAIT_FOR_DEBUGGER" value="false" />
        <option name="DEBUG_SANDBOX_SDK" value="false" />
      </Auto>
      <Hybrid>
        <option name="USE_JAVA_AWARE_DEBUGGER" value="false" />
        <option name="SHOW_STATIC_VARS" value="true" />
        <option name="WORKING_DIR" value="" />
        <option name="TARGET_LOGGING_CHANNELS" value="lldb process:gdb-remote packets" />
        <option name="SHOW_OPTIMIZED_WARNING" value="true" />
        <option name="ATTACH_ON_WAIT_FOR_DEBUGGER" value="false" />
        <option name="DEBUG_SANDBOX_SDK" value="false" />
      </Hybrid>
      <Java>
        <option name="ATTACH_ON_WAIT_FOR_DEBUGGER" value="false" />
        <option name="DEBUG_SANDBOX_SDK" value="false" />
      </Java>
      <Native>
        <option name="USE_JAVA_AWARE_DEBUGGER" value="false" />
        <option name="SHOW_STATIC_VARS" value="true" />
        <option name="WORKING_DIR" value="" />
        <option name="TARGET_LOGGING_CHANNELS" value="lldb process:gdb-remote packets" />
        <option name="SHOW_OPTIMIZED_WARNING" value="true" />
        <option name="ATTACH_ON_WAIT_FOR_DEBUGGER" value="false" />
        <option name="DEBUG_SANDBOX_SDK" value="false" />
      </Native>
      <Profilers>
        <option name="ADVANCED_PROFILING_ENABLED" value="false" />
        <option name="STARTUP_PROFILING_ENABLED" value="false" />
        <option name="STARTUP_CPU_PROFILING_ENABLED" value="false" />
        <option name="STARTUP_CPU_PROFILING_CONFIGURATION_NAME" value="Java/Kotlin Method Sample (legacy)" />
        <option name="STARTUP_NATIVE_MEMORY_PROFILING_ENABLED" value="false" />
        <option name="NATIVE_MEMORY_SAMPLE_RATE_BYTES" value="2048" />
      </Profilers>
      <option name="DEEP_LINK" value="" />
      <option name="ACTIVITY_CLASS" value="" />
      <option name="SEARCH_ACTIVITY_IN_GLOBAL_SCOPE" value="false" />
      <option name="SKIP_ACTIVITY_VALIDATION" value="false" />
      <method v="2">
        <option name="Android.Gradle.BeforeRunTask" enabled="true" />
      </method>
    </configuration>
  </component>
  <component name="SpellCheckerSettings" RuntimeDictionaries="0" Folders="0" CustomDictionaries="0" DefaultDictionary="应用程序级" UseSingleDictionary="true" transferred="true" />
  <component name="TaskManager">
    <task active="true" id="Default" summary="默认任务">
      <changelist id="601993bc-4af3-4f8b-85ab-8a295399269f" name="更改" comment="" />
      <created>1753503179742</created>
      <option name="number" value="Default" />
      <option name="presentableId" value="Default" />
      <updated>1753503179742</updated>
    </task>
    <servers />
  </component>
</project>