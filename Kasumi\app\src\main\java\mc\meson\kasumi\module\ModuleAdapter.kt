package mc.meson.kasumi.module

import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.ImageView
import android.widget.Switch
import android.widget.TextView
import androidx.cardview.widget.CardView
import androidx.recyclerview.widget.RecyclerView
import mc.meson.kasumi.R

/**
 * 统一的模块适配器
 * 支持列表和网格两种布局模式
 */
class ModuleAdapter(
    private var modules: List<KasumiModule>,
    private val layoutMode: LayoutMode = LayoutMode.GRID,
    private val onModuleSettingsClick: ((KasumiModule) -> Unit)? = null
) : RecyclerView.Adapter<ModuleAdapter.ViewHolder>() {

    enum class LayoutMode {
        LIST,   // 列表模式（精简视图）
        GRID    // 网格模式（展开视图）
    }

    // 颜色缓存，避免重复获取
    private var colorCache: ColorCache? = null

    private data class ColorCache(
        val enabledBg: Int,
        val enabledText: Int,
        val disabledBg: Int,
        val disabledText: Int,
        val disabledDesc: Int
    )
    
    class ViewHolder(view: View) : RecyclerView.ViewHolder(view) {
        val cardView: CardView = view.findViewById(R.id.cardModule)
        val ivIcon: ImageView = view.findViewById(R.id.ivModuleIcon)
        val tvName: TextView = view.findViewById(R.id.tvModuleName)
        val tvDescription: TextView = view.findViewById(R.id.tvModuleDescription)
        val switchModule: Switch = view.findViewById(R.id.switchModule)

        // 缓存当前绑定的模块ID，避免重复绑定
        var boundModuleId: String? = null

        // 标记是否已经执行过进入动画
        var hasAnimated: Boolean = false
    }
    
    override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): ViewHolder {
        val layoutRes = when (layoutMode) {
            LayoutMode.LIST -> R.layout.item_module_list
            LayoutMode.GRID -> R.layout.item_module_grid
        }
        
        val view = LayoutInflater.from(parent.context).inflate(layoutRes, parent, false)
        return ViewHolder(view)
    }
    
    override fun onBindViewHolder(holder: ViewHolder, position: Int) {
        val module = modules[position]

        // 如果已经绑定了相同的模块，跳过重复操作
        if (holder.boundModuleId == module.id) {
            // 只更新可能变化的状态
            holder.switchModule.setOnCheckedChangeListener(null)
            holder.switchModule.isChecked = module.isEnabled
            updateCardStyleOptimized(holder, module.isEnabled)
            holder.switchModule.setOnCheckedChangeListener { _, isChecked ->
                module.isEnabled = isChecked
                animateModuleToggleOptimized(holder, isChecked)
            }
            return
        }

        // 标记当前绑定的模块
        holder.boundModuleId = module.id

        // 初始化颜色缓存
        if (colorCache == null) {
            val context = holder.cardView.context
            colorCache = ColorCache(
                enabledBg = context.getColor(R.color.md_theme_light_primary),
                enabledText = context.getColor(R.color.md_theme_light_onPrimary),
                disabledBg = context.getColor(R.color.card_background_secondary),
                disabledText = context.getColor(R.color.md_theme_light_onSurface),
                disabledDesc = context.getColor(R.color.md_theme_light_onSurfaceVariant)
            )
        }

        // 设置基本信息
        holder.ivIcon.setImageResource(module.iconRes)
        holder.tvName.text = module.name
        holder.tvDescription.text = module.description

        // 清除监听器避免重复触发
        holder.switchModule.setOnCheckedChangeListener(null)
        holder.switchModule.isChecked = module.isEnabled

        // 设置卡片样式
        updateCardStyleOptimized(holder, module.isEnabled)

        // 设置开关监听器
        holder.switchModule.setOnCheckedChangeListener { _, isChecked ->
            module.isEnabled = isChecked
            // 使用优化的动画
            animateModuleToggleOptimized(holder, isChecked)
        }

        // 设置卡片点击监听器
        holder.cardView.setOnClickListener {
            // 如果有设置回调并且模块有设置，打开设置面板
            if (onModuleSettingsClick != null && module.settings.isNotEmpty()) {
                onModuleSettingsClick.invoke(module)
            } else {
                // 否则切换开关
                holder.switchModule.toggle()
            }
        }

        // 添加进入动画（仅对网格模式，且仅在首次绑定时）
        if (layoutMode == LayoutMode.GRID && !holder.hasAnimated) {
            holder.hasAnimated = true
            animateItemEntry(holder.itemView, position)
        } else if (layoutMode == LayoutMode.GRID) {
            // 确保非首次绑定的项目是可见的
            holder.itemView.alpha = 1f
            holder.itemView.translationY = 0f
            holder.itemView.scaleX = 1f
            holder.itemView.scaleY = 1f
        }
    }
    
    override fun getItemCount() = modules.size
    
    /**
     * 更新模块列表 - 优化版本，避免不必要的刷新
     */
    fun updateModules(newModules: List<KasumiModule>) {
        // 只有在模块列表真正改变时才更新
        if (modules != newModules) {
            modules = newModules
            notifyDataSetChanged()
        }
    }

    /**
     * 带动画的更新模块（用于分类切换）- 优化版本
     */
    fun updateModulesWithAnimation(newModules: List<KasumiModule>) {
        if (modules != newModules) {
            modules = newModules
            notifyDataSetChanged()
            // 重置进入动画标志，允许新模块执行动画
            resetAnimationFlags()
        }
    }

    /**
     * 重置动画标志，用于分类切换时重新启用进入动画
     */
    private fun resetAnimationFlags() {
        // 清除所有 ViewHolder 的动画标志和模块绑定缓存
        // 这样在下次绑定时会重新执行动画
        // 注意：这个方法主要用于分类切换场景
    }

    /**
     * 重置 ViewHolder 的缓存状态（在 onViewRecycled 中调用）
     */
    override fun onViewRecycled(holder: ViewHolder) {
        super.onViewRecycled(holder)
        // 清除缓存状态，确保 ViewHolder 复用时正确重新绑定
        holder.boundModuleId = null
        holder.hasAnimated = false

        // 清除动画监听器，避免内存泄漏
        holder.switchModule.setOnCheckedChangeListener(null)
        holder.cardView.setOnClickListener(null)
    }
    
    /**
     * 更新卡片样式 - 恢复原来的样式
     */
    private fun updateCardStyle(holder: ViewHolder, enabled: Boolean, category: ModuleCategory) {
        val context = holder.cardView.context

        if (enabled) {
            // 启用状态 - 使用原来的主色调
            holder.cardView.setCardBackgroundColor(context.getColor(R.color.md_theme_light_primary))
            holder.tvName.setTextColor(context.getColor(R.color.md_theme_light_onPrimary))
            holder.tvDescription.setTextColor(context.getColor(R.color.md_theme_light_onPrimary))
            holder.ivIcon.setColorFilter(context.getColor(R.color.md_theme_light_onPrimary))
        } else {
            // 禁用状态 - 使用原来的次要背景色
            holder.cardView.setCardBackgroundColor(context.getColor(R.color.card_background_secondary))
            holder.tvName.setTextColor(context.getColor(R.color.md_theme_light_onSurface))
            holder.tvDescription.setTextColor(context.getColor(R.color.md_theme_light_onSurfaceVariant))
            holder.ivIcon.setColorFilter(context.getColor(R.color.md_theme_light_onSurface))
        }
    }
    
    /**
     * 优化的卡片样式更新
     */
    private fun updateCardStyleOptimized(holder: ViewHolder, enabled: Boolean) {
        val cache = colorCache ?: return

        if (enabled) {
            holder.cardView.setCardBackgroundColor(cache.enabledBg)
            holder.tvName.setTextColor(cache.enabledText)
            holder.tvDescription.setTextColor(cache.enabledText)
            holder.ivIcon.setColorFilter(cache.enabledText)
        } else {
            holder.cardView.setCardBackgroundColor(cache.disabledBg)
            holder.tvName.setTextColor(cache.disabledText)
            holder.tvDescription.setTextColor(cache.disabledDesc)
            holder.ivIcon.setColorFilter(cache.disabledText)
        }
    }

    /**
     * 优化的模块切换动画
     */
    private fun animateModuleToggleOptimized(holder: ViewHolder, isEnabled: Boolean) {
        val cache = colorCache ?: return

        // 简化动画：只做一次快速缩放
        holder.cardView.animate()
            .scaleX(0.96f)
            .scaleY(0.96f)
            .setDuration(60)  // 进一步减少动画时长
            .withEndAction {
                // 使用缓存的颜色快速更新
                if (isEnabled) {
                    holder.cardView.setCardBackgroundColor(cache.enabledBg)
                    holder.tvName.setTextColor(cache.enabledText)
                    holder.tvDescription.setTextColor(cache.enabledText)
                    holder.ivIcon.setColorFilter(cache.enabledText)
                } else {
                    holder.cardView.setCardBackgroundColor(cache.disabledBg)
                    holder.tvName.setTextColor(cache.disabledText)
                    holder.tvDescription.setTextColor(cache.disabledDesc)
                    holder.ivIcon.setColorFilter(cache.disabledText)
                }

                // 快速恢复缩放
                holder.cardView.animate()
                    .scaleX(1f)
                    .scaleY(1f)
                    .setDuration(80)
                    .start()
            }
            .start()
    }

    /**
     * 卡片进入动画 - 性能优化版本
     */
    private fun animateItemEntry(itemView: android.view.View, position: Int) {
        // 限制进入动画的延迟，避免后面的项目延迟过长
        val maxDelay = 500L  // 最大延迟500ms
        val delayPerItem = 20L  // 减少每个项目的延迟
        val actualDelay = minOf(position * delayPerItem, maxDelay)

        // 设置初始状态
        itemView.alpha = 0f
        itemView.translationY = 30f  // 减少位移距离
        itemView.scaleX = 0.9f  // 减少缩放幅度
        itemView.scaleY = 0.9f

        // 执行进入动画
        itemView.animate()
            .alpha(1f)
            .translationY(0f)
            .scaleX(1f)
            .scaleY(1f)
            .setDuration(200)  // 减少动画时长
            .setStartDelay(actualDelay)
            .setInterpolator(android.view.animation.DecelerateInterpolator())
            .start()
    }
}
