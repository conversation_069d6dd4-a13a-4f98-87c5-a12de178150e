mc.meson.kasumi:style/TextAppearance.AppCompat.Widget.ActionMode.Subtitle.Inverse = 0x7f1201c3
mc.meson.kasumi:styleable/AlertDialog = 0x7f130009
mc.meson.kasumi:style/ShapeAppearance.M3.Comp.Switch.Handle.Shape = 0x7f120170
mc.meson.kasumi:macro/m3_comp_checkbox_selected_error_icon_color = 0x7f0c000a
mc.meson.kasumi:style/Widget.MaterialComponents.ProgressIndicator = 0x7f12044f
mc.meson.kasumi:integer/m3_sys_motion_duration_long4 = 0x7f090017
mc.meson.kasumi:style/Widget.Material3.Button.TextButton.Dialog = 0x7f120368
mc.meson.kasumi:style/Widget.AppCompat.Light.PopupMenu = 0x7f120321
mc.meson.kasumi:style/RtlOverlay.Widget.AppCompat.PopupMenuItem.Text = 0x7f120158
mc.meson.kasumi:macro/m3_comp_time_picker_period_selector_outline_color = 0x7f0c0154
mc.meson.kasumi:style/Widget.MaterialComponents.Snackbar.FullWidth = 0x7f120453
mc.meson.kasumi:style/Widget.MaterialComponents.Button.UnelevatedButton.Icon = 0x7f120416
mc.meson.kasumi:styleable/BottomNavigationView = 0x7f130019
mc.meson.kasumi:macro/m3_comp_switch_selected_focus_state_layer_color = 0x7f0c0122
mc.meson.kasumi:style/Widget.MaterialComponents.BottomSheet = 0x7f120409
mc.meson.kasumi:id/switchModule = 0x7f080208
mc.meson.kasumi:style/Widget.Material3.MaterialCalendar.Year.Selected = 0x7f1203bb
mc.meson.kasumi:style/Widget.Material3.Button.IconButton.Filled.Tonal = 0x7f120363
mc.meson.kasumi:style/TextAppearance.AppCompat.Display4 = 0x7f1201a8
mc.meson.kasumi:string/androidx_startup = 0x7f11001d
mc.meson.kasumi:styleable/Grid = 0x7f130042
mc.meson.kasumi:macro/m3_comp_time_picker_period_selector_unselected_focus_state_layer_color = 0x7f0c015a
mc.meson.kasumi:style/Widget.Compat.NotificationActionContainer = 0x7f12033e
mc.meson.kasumi:style/Widget.Material3.Badge = 0x7f120352
mc.meson.kasumi:style/Widget.Material3.Chip.Suggestion.Elevated = 0x7f12037d
mc.meson.kasumi:styleable/MaterialAlertDialogTheme = 0x7f130054
mc.meson.kasumi:style/Widget.Material3.MaterialCalendar.HeaderCancelButton = 0x7f1203af
mc.meson.kasumi:style/Base.Widget.Material3.ExtendedFloatingActionButton.Icon = 0x7f120114
mc.meson.kasumi:style/Widget.AppCompat.ProgressBar = 0x7f12032d
mc.meson.kasumi:style/Widget.AppCompat.EditText = 0x7f12030d
mc.meson.kasumi:style/Widget.MaterialComponents.Toolbar.Surface = 0x7f120472
mc.meson.kasumi:style/Widget.Material3.CircularProgressIndicator.Legacy.ExtraSmall = 0x7f120382
mc.meson.kasumi:id/mtrl_calendar_text_input_frame = 0x7f08015c
mc.meson.kasumi:style/Theme.MaterialComponents.DayNight.NoActionBar.Bridge = 0x7f120267
mc.meson.kasumi:color/material_personalized_color_on_error = 0x7f050285
mc.meson.kasumi:style/Widget.Material3.Snackbar.TextView = 0x7f1203e2
mc.meson.kasumi:style/Widget.Design.BottomNavigationView = 0x7f120341
mc.meson.kasumi:style/ThemeOverlay.Material3.MaterialAlertDialog.Centered = 0x7f1202b9
mc.meson.kasumi:string/abc_menu_enter_shortcut_label = 0x7f11000b
mc.meson.kasumi:dimen/m3_comp_radio_button_unselected_focus_state_layer_opacity = 0x7f06016a
mc.meson.kasumi:macro/m3_comp_snackbar_container_shape = 0x7f0c0114
mc.meson.kasumi:styleable/PopupWindow = 0x7f13007e
mc.meson.kasumi:id/layoutNoSettings = 0x7f080273
mc.meson.kasumi:style/Widget.Design.BottomSheet.Modal = 0x7f120342
mc.meson.kasumi:style/Base.V26.Theme.AppCompat.Light = 0x7f1200c3
mc.meson.kasumi:style/Theme.Material3.DayNight.Dialog.Alert = 0x7f120241
mc.meson.kasumi:style/Widget.MaterialComponents.CompoundButton.CheckBox = 0x7f120423
mc.meson.kasumi:style/ShapeAppearance.Material3.Corner.Small = 0x7f120181
mc.meson.kasumi:string/m3_ref_typeface_brand_medium = 0x7f110056
mc.meson.kasumi:style/ThemeOverlay.Material3.TextInputEditText.FilledBox = 0x7f1202c7
mc.meson.kasumi:style/ThemeOverlay.MaterialComponents.BottomAppBar.Primary = 0x7f1202d6
mc.meson.kasumi:styleable/KeyTimeCycle = 0x7f13004b
mc.meson.kasumi:id/transition_position = 0x7f08023d
mc.meson.kasumi:id/split_action_bar = 0x7f0801f0
mc.meson.kasumi:styleable/Transform = 0x7f1300a2
mc.meson.kasumi:style/ThemeOverlay.MaterialComponents.ActionBar.Surface = 0x7f1202d0
mc.meson.kasumi:style/Theme.MaterialComponents.Dialog.Alert = 0x7f120269
mc.meson.kasumi:style/TextAppearance.M3.Sys.Typescale.BodyMedium = 0x7f1201e1
mc.meson.kasumi:style/Widget.MaterialComponents.Snackbar = 0x7f120452
mc.meson.kasumi:style/Widget.MaterialComponents.Button = 0x7f12040b
mc.meson.kasumi:style/Widget.MaterialComponents.Button.TextButton.Snackbar = 0x7f120414
mc.meson.kasumi:string/item_view_role_description = 0x7f110051
mc.meson.kasumi:style/Widget.MaterialComponents.Light.ActionBar.Solid = 0x7f120429
mc.meson.kasumi:style/ThemeOverlay.Material3.Light.Dialog.Alert.Framework = 0x7f1202b7
mc.meson.kasumi:drawable/$mtrl_checkbox_button_checked_unchecked__2 = 0x7f07000f
mc.meson.kasumi:style/Base.V21.Theme.AppCompat.Light.Dialog = 0x7f1200b1
mc.meson.kasumi:macro/m3_comp_radio_button_selected_icon_color = 0x7f0c00dc
mc.meson.kasumi:dimen/m3_comp_radio_button_selected_focus_state_layer_opacity = 0x7f060167
mc.meson.kasumi:style/Base.TextAppearance.AppCompat.Widget.PopupMenu.Header = 0x7f12004b
mc.meson.kasumi:color/secondary_text_default_material_light = 0x7f05034a
mc.meson.kasumi:style/ThemeOverlay.AppCompat.ActionBar = 0x7f120284
mc.meson.kasumi:style/Widget.AppCompat.Light.ActionBar.Solid = 0x7f120310
mc.meson.kasumi:style/Widget.AppCompat.SearchView = 0x7f120332
mc.meson.kasumi:style/Widget.Material3.FloatingActionButton.Large.Primary = 0x7f120397
mc.meson.kasumi:id/nav_host_fragment = 0x7f080172
mc.meson.kasumi:style/Base.V14.Theme.Material3.Dark = 0x7f120096
mc.meson.kasumi:drawable/mtrl_checkbox_button_icon_checked_unchecked = 0x7f0700d1
mc.meson.kasumi:style/ThemeOverlay.MaterialComponents.Dark.ActionBar = 0x7f1202da
mc.meson.kasumi:style/Widget.Material3.TextInputLayout.FilledBox.ExposedDropdownMenu = 0x7f1203ed
mc.meson.kasumi:style/Platform.MaterialComponents.Dialog = 0x7f120146
mc.meson.kasumi:style/ShapeAppearance.M3.Sys.Shape.Corner.Medium = 0x7f120178
mc.meson.kasumi:style/Theme.AppCompat.CompactMenu = 0x7f120219
mc.meson.kasumi:style/Widget.Material3.MaterialTimePicker.Display.HelperText = 0x7f1203c5
mc.meson.kasumi:style/ThemeOverlay.Material3.Light = 0x7f1202b6
mc.meson.kasumi:style/Widget.Material3.Button.TextButton.Icon = 0x7f12036b
mc.meson.kasumi:id/async = 0x7f080062
mc.meson.kasumi:style/Widget.AppCompat.Toolbar = 0x7f12033c
mc.meson.kasumi:style/Base.Widget.AppCompat.ProgressBar = 0x7f1200fa
mc.meson.kasumi:attr/state_collapsible = 0x7f030431
mc.meson.kasumi:dimen/abc_text_size_menu_header_material = 0x7f06004a
mc.meson.kasumi:style/Base.Theme.AppCompat = 0x7f120058
mc.meson.kasumi:id/software = 0x7f0801eb
mc.meson.kasumi:style/RtlUnderlay.Widget.AppCompat.ActionButton = 0x7f120160
mc.meson.kasumi:style/ThemeOverlay.Material3.Dialog = 0x7f1202a6
mc.meson.kasumi:style/Widget.Material3.DrawerLayout = 0x7f12038e
mc.meson.kasumi:id/vpCompactCategories = 0x7f080263
mc.meson.kasumi:style/Widget.AppCompat.Light.AutoCompleteTextView = 0x7f12031d
mc.meson.kasumi:style/Base.Widget.AppCompat.ProgressBar.Horizontal = 0x7f1200fb
mc.meson.kasumi:style/MaterialAlertDialog.Material3.Title.Icon.CenterStacked = 0x7f120134
mc.meson.kasumi:style/Theme.Design.Light = 0x7f120230
mc.meson.kasumi:style/Widget.AppCompat.Light.SearchView = 0x7f120323
mc.meson.kasumi:drawable/ic_collapse_24 = 0x7f0700a0
mc.meson.kasumi:macro/m3_comp_date_picker_modal_weekdays_label_text_color = 0x7f0c001d
mc.meson.kasumi:style/TextAppearance.MaterialComponents.Body1 = 0x7f120205
mc.meson.kasumi:style/Widget.Material3.Badge.AdjustToBounds = 0x7f120353
mc.meson.kasumi:styleable/MaterialCheckBoxStates = 0x7f13005c
mc.meson.kasumi:style/Theme.AppCompat.Light.DarkActionBar = 0x7f120227
mc.meson.kasumi:macro/m3_comp_switch_unselected_focus_icon_color = 0x7f0c0130
mc.meson.kasumi:id/tag_accessibility_pane_title = 0x7f080211
mc.meson.kasumi:style/Widget.Material3.Chip.Input.Icon = 0x7f12037a
mc.meson.kasumi:color/material_personalized_color_text_secondary_and_tertiary_inverse_disabled = 0x7f0502ab
mc.meson.kasumi:drawable/mtrl_popupmenu_background = 0x7f0700e2
mc.meson.kasumi:id/tvCategoryName = 0x7f080241
mc.meson.kasumi:style/ShapeAppearance.M3.Comp.Badge.Large.Shape = 0x7f120162
mc.meson.kasumi:styleable/FragmentNavigator = 0x7f13003f
mc.meson.kasumi:style/Base.ThemeOverlay.Material3.SideSheetDialog = 0x7f12008f
mc.meson.kasumi:style/Widget.MaterialComponents.MaterialCalendar.DayOfWeekLabel = 0x7f120431
mc.meson.kasumi:style/Widget.Material3.BottomAppBar.Button.Navigation = 0x7f120355
mc.meson.kasumi:id/off = 0x7f080188
mc.meson.kasumi:style/Widget.MaterialComponents.TextInputEditText.FilledBox.Dense = 0x7f120459
mc.meson.kasumi:string/no_fall = 0x7f1100bf
mc.meson.kasumi:dimen/m3_comp_switch_track_height = 0x7f060197
mc.meson.kasumi:dimen/m3_comp_input_chip_with_leading_icon_leading_icon_size = 0x7f060134
mc.meson.kasumi:macro/m3_comp_text_button_focus_state_layer_color = 0x7f0c0142
mc.meson.kasumi:style/TextAppearance.Design.Prefix = 0x7f1201dc
mc.meson.kasumi:style/Base.TextAppearance.Widget.AppCompat.ExpandedMenu.Item = 0x7f120055
mc.meson.kasumi:style/Widget.Design.FloatingActionButton = 0x7f120344
mc.meson.kasumi:style/Widget.Material3.CompoundButton.MaterialSwitch = 0x7f12038b
mc.meson.kasumi:style/TextAppearance.Material3.LabelMedium = 0x7f1201fb
mc.meson.kasumi:styleable/KeyFrame = 0x7f130047
mc.meson.kasumi:dimen/mtrl_progress_circular_inset_extra_small = 0x7f0602d5
mc.meson.kasumi:styleable/AppBarLayoutStates = 0x7f13000e
mc.meson.kasumi:style/Widget.Material3.FloatingActionButton.Small.Tertiary = 0x7f1203a0
mc.meson.kasumi:id/center_horizontal = 0x7f08008f
mc.meson.kasumi:drawable/status_indicator = 0x7f070102
mc.meson.kasumi:style/ThemeOverlay.Material3.Button.TextButton.Snackbar = 0x7f12029e
mc.meson.kasumi:id/disjoint = 0x7f0800c4
mc.meson.kasumi:style/TextAppearance.AppCompat.Caption = 0x7f1201a4
mc.meson.kasumi:id/tvNoSettings = 0x7f08024e
mc.meson.kasumi:id/rounded = 0x7f0801bc
mc.meson.kasumi:style/Base.Theme.MaterialComponents.Dialog.Alert = 0x7f120076
mc.meson.kasumi:style/Base.TextAppearance.AppCompat.Display4 = 0x7f12002b
mc.meson.kasumi:drawable/btn_radio_on_mtrl = 0x7f070080
mc.meson.kasumi:style/TextAppearance.AppCompat.Widget.Switch = 0x7f1201ce
mc.meson.kasumi:dimen/mtrl_calendar_navigation_top_padding = 0x7f06028d
mc.meson.kasumi:style/ThemeOverlay.Material3.NavigationView = 0x7f1202c0
mc.meson.kasumi:style/Theme.Material3.DynamicColors.Light.NoActionBar = 0x7f12024b
mc.meson.kasumi:styleable/Motion = 0x7f130069
mc.meson.kasumi:styleable/ClockFaceView = 0x7f130023
mc.meson.kasumi:style/TextAppearance.MaterialComponents.Subtitle1 = 0x7f120211
mc.meson.kasumi:dimen/material_textinput_max_width = 0x7f060242
mc.meson.kasumi:style/Widget.MaterialComponents.NavigationRailView.Colored = 0x7f120446
mc.meson.kasumi:color/mtrl_tabs_colored_ripple_color = 0x7f05032f
mc.meson.kasumi:style/Widget.MaterialComponents.CardView = 0x7f120417
mc.meson.kasumi:style/RtlOverlay.Widget.AppCompat.PopupMenuItem.InternalGroup = 0x7f120155
mc.meson.kasumi:style/Theme.Material3.DayNight.Dialog = 0x7f120240
mc.meson.kasumi:dimen/mtrl_btn_corner_radius = 0x7f060259
mc.meson.kasumi:color/m3_textfield_label_color = 0x7f05020a
mc.meson.kasumi:style/Widget.AppCompat.PopupWindow = 0x7f12032c
mc.meson.kasumi:style/TextAppearance.M3.Sys.Typescale.LabelMedium = 0x7f1201ea
mc.meson.kasumi:drawable/ic_arrow_back_black_24 = 0x7f070093
mc.meson.kasumi:id/cradle = 0x7f0800ab
mc.meson.kasumi:macro/m3_comp_navigation_bar_label_text_type = 0x7f0c0077
mc.meson.kasumi:id/touch_outside = 0x7f080235
mc.meson.kasumi:style/Theme.MaterialComponents.DialogWhenLarge = 0x7f120270
mc.meson.kasumi:style/Widget.Material3.PopupMenu.ListPopupWindow = 0x7f1203cf
mc.meson.kasumi:color/m3_sys_color_dynamic_dark_primary_container = 0x7f050194
mc.meson.kasumi:macro/m3_comp_fab_primary_large_container_shape = 0x7f0c0039
mc.meson.kasumi:style/Widget.Material3.TabLayout = 0x7f1203e3
mc.meson.kasumi:styleable/Insets = 0x7f130044
mc.meson.kasumi:style/ThemeOverlay.Material3.TextInputEditText.OutlinedBox = 0x7f1202c9
mc.meson.kasumi:string/abc_menu_function_shortcut_label = 0x7f11000c
mc.meson.kasumi:dimen/mtrl_transition_shared_axis_slide_distance = 0x7f06030b
mc.meson.kasumi:string/bottom_sheet_behavior = 0x7f110024
mc.meson.kasumi:dimen/material_clock_display_padding = 0x7f060223
mc.meson.kasumi:color/material_dynamic_neutral_variant50 = 0x7f050239
mc.meson.kasumi:style/Widget.AppCompat.Toolbar.Button.Navigation = 0x7f12033d
mc.meson.kasumi:styleable/PropertySet = 0x7f130080
mc.meson.kasumi:styleable/ActivityRule = 0x7f130008
mc.meson.kasumi:styleable/SwitchCompat = 0x7f130097
mc.meson.kasumi:style/TextAppearance.Material3.SearchView.Prefix = 0x7f120200
mc.meson.kasumi:dimen/m3_ripple_focused_alpha = 0x7f0601d6
mc.meson.kasumi:macro/m3_comp_date_picker_modal_range_selection_month_subhead_type = 0x7f0c001c
mc.meson.kasumi:macro/m3_comp_switch_disabled_unselected_track_outline_color = 0x7f0c011f
mc.meson.kasumi:style/Widget.MaterialComponents.AutoCompleteTextView.FilledBox.Dense = 0x7f1203ff
mc.meson.kasumi:style/TextAppearance.MaterialComponents.Subtitle2 = 0x7f120212
mc.meson.kasumi:styleable/ExtendedFloatingActionButton_Behavior_Layout = 0x7f130036
mc.meson.kasumi:macro/m3_comp_search_bar_input_text_type = 0x7f0c00ea
mc.meson.kasumi:string/material_timepicker_hour = 0x7f110076
mc.meson.kasumi:style/Widget.Material3.TextInputLayout.OutlinedBox.Dense = 0x7f1203ef
mc.meson.kasumi:style/Widget.Material3.BottomNavigationView.ActiveIndicator = 0x7f120359
mc.meson.kasumi:styleable/NavAction = 0x7f130070
mc.meson.kasumi:styleable/ThemeEnforcement = 0x7f13009f
mc.meson.kasumi:style/App.Widget.BottomNavigationView.ActiveIndicator = 0x7f120017
mc.meson.kasumi:color/mtrl_calendar_selected_range = 0x7f05030c
mc.meson.kasumi:style/TextAppearance.Design.Placeholder = 0x7f1201db
mc.meson.kasumi:color/m3_sys_color_dynamic_dark_on_error_container = 0x7f050188
mc.meson.kasumi:style/Widget.Material3.SideSheet.Modal.Detached = 0x7f1203db
mc.meson.kasumi:style/ThemeOverlay.MaterialComponents.BottomAppBar.Surface = 0x7f1202d7
mc.meson.kasumi:id/month_navigation_previous = 0x7f080152
mc.meson.kasumi:drawable/$m3_avd_show_password__0 = 0x7f07000a
mc.meson.kasumi:styleable/MaterialTextAppearance = 0x7f130061
mc.meson.kasumi:drawable/abc_list_divider_mtrl_alpha = 0x7f07004e
mc.meson.kasumi:macro/m3_comp_navigation_rail_inactive_pressed_state_layer_color = 0x7f0c009e
mc.meson.kasumi:style/Widget.Material3.MaterialTimePicker.Display.TextInputLayout = 0x7f1203c7
mc.meson.kasumi:color/md_theme_dark_errorContainer = 0x7f0502bd
mc.meson.kasumi:style/Widget.MaterialComponents.Button.OutlinedButton.Icon = 0x7f12040e
mc.meson.kasumi:dimen/m3_card_disabled_z = 0x7f0600e4
mc.meson.kasumi:dimen/m3_comp_navigation_bar_active_indicator_height = 0x7f060136
mc.meson.kasumi:layout/activity_main = 0x7f0b001c
mc.meson.kasumi:dimen/abc_action_bar_icon_vertical_padding_material = 0x7f060006
mc.meson.kasumi:style/Base.Theme.AppCompat.Light = 0x7f12005f
mc.meson.kasumi:style/Theme.Design.BottomSheetDialog = 0x7f12022f
mc.meson.kasumi:styleable/ViewBackgroundHelper = 0x7f1300a6
mc.meson.kasumi:style/Theme.Material3.Dark.DialogWhenLarge = 0x7f12023b
mc.meson.kasumi:color/material_personalized_color_control_highlight = 0x7f050280
mc.meson.kasumi:style/ThemeOverlay.Material3.DayNight.BottomSheetDialog = 0x7f1202a4
mc.meson.kasumi:styleable/Spinner = 0x7f13008f
mc.meson.kasumi:styleable/DrawerArrowToggle = 0x7f130033
mc.meson.kasumi:id/graph = 0x7f0800f8
mc.meson.kasumi:style/Base.V26.Theme.AppCompat = 0x7f1200c2
mc.meson.kasumi:macro/m3_comp_primary_navigation_tab_with_label_text_inactive_label_text_color = 0x7f0c00d2
mc.meson.kasumi:style/TextAppearance.AppCompat.Widget.ActionMode.Subtitle = 0x7f1201c2
mc.meson.kasumi:style/Base.V14.Theme.Material3.Light.SideSheetDialog = 0x7f12009d
mc.meson.kasumi:id/fitEnd = 0x7f0800ea
mc.meson.kasumi:dimen/design_bottom_navigation_label_padding = 0x7f060067
mc.meson.kasumi:style/Widget.Material3.MaterialDivider.Heavy = 0x7f1203bf
mc.meson.kasumi:style/ThemeOverlay.Material3.DynamicColors.Light = 0x7f1202ab
mc.meson.kasumi:styleable/MaterialButton = 0x7f130056
mc.meson.kasumi:dimen/abc_action_bar_elevation_material = 0x7f060005
mc.meson.kasumi:style/Widget.MaterialComponents.CheckedTextView = 0x7f120418
mc.meson.kasumi:id/skipCollapsed = 0x7f0801e2
mc.meson.kasumi:style/TextAppearance.Widget.AppCompat.Toolbar.Title = 0x7f120217
mc.meson.kasumi:style/RtlOverlay.Widget.AppCompat.Search.DropDown.Icon2 = 0x7f12015c
mc.meson.kasumi:id/accessibility_custom_action_13 = 0x7f080016
mc.meson.kasumi:style/ThemeOverlay.MaterialAlertDialog.Material3.Title.Icon = 0x7f1202cc
mc.meson.kasumi:style/Widget.Material3.FloatingActionButton.Small.Primary = 0x7f12039d
mc.meson.kasumi:macro/m3_comp_badge_large_label_text_type = 0x7f0c0004
mc.meson.kasumi:style/Theme.Material3.DayNight.DialogWhenLarge = 0x7f120243
mc.meson.kasumi:style/Base.Widget.AppCompat.Light.ActionBar.TabText = 0x7f1200ed
mc.meson.kasumi:style/TextAppearance.Material3.TitleMedium = 0x7f120202
mc.meson.kasumi:layout/notification_action = 0x7f0b0077
mc.meson.kasumi:style/Theme.AppCompat.DayNight.DialogWhenLarge = 0x7f12021f
mc.meson.kasumi:color/material_dynamic_tertiary99 = 0x7f050266
mc.meson.kasumi:macro/m3_comp_time_picker_time_selector_separator_type = 0x7f0c0166
mc.meson.kasumi:macro/m3_comp_primary_navigation_tab_with_label_text_active_label_text_color = 0x7f0c00d1
mc.meson.kasumi:style/Widget.AppCompat.ActionBar.TabText = 0x7f1202f8
mc.meson.kasumi:style/ShapeAppearance.M3.Sys.Shape.Corner.ExtraLarge = 0x7f120174
mc.meson.kasumi:style/TextAppearance.Material3.HeadlineSmall = 0x7f1201f9
mc.meson.kasumi:macro/m3_comp_switch_selected_focus_track_color = 0x7f0c0123
mc.meson.kasumi:macro/m3_comp_assist_chip_container_shape = 0x7f0c0000
mc.meson.kasumi:id/action_features_to_module_settings = 0x7f080041
mc.meson.kasumi:style/Widget.Material3.MaterialCalendar.HeaderSelection.Fullscreen = 0x7f1203b4
mc.meson.kasumi:macro/m3_comp_time_input_time_input_field_supporting_text_type = 0x7f0c014b
mc.meson.kasumi:string/auto_connect_desc = 0x7f110023
mc.meson.kasumi:style/Widget.Material3.CircularProgressIndicator.Medium = 0x7f120385
mc.meson.kasumi:dimen/abc_dialog_title_divider_material = 0x7f060026
mc.meson.kasumi:style/Widget.Material3.MaterialTimePicker.Button = 0x7f1203c1
mc.meson.kasumi:style/ThemeOverlay.AppCompat.Dialog.Alert = 0x7f12028a
mc.meson.kasumi:color/material_personalized_color_on_secondary_container = 0x7f05028a
mc.meson.kasumi:dimen/mtrl_textinput_end_icon_margin_start = 0x7f060302
mc.meson.kasumi:style/Widget.MaterialComponents.Toolbar = 0x7f12046f
mc.meson.kasumi:style/Widget.AppCompat.Light.ActionBar.TabText.Inverse = 0x7f120315
mc.meson.kasumi:string/disconnected = 0x7f11003e
mc.meson.kasumi:style/Base.Widget.AppCompat.ListMenuView = 0x7f1200f2
mc.meson.kasumi:styleable/MaterialAlertDialog = 0x7f130053
mc.meson.kasumi:style/Base.Widget.MaterialComponents.PopupMenu.ListPopupWindow = 0x7f120125
mc.meson.kasumi:style/Theme.Design.Light.BottomSheetDialog = 0x7f120231
mc.meson.kasumi:layout/abc_screen_simple = 0x7f0b0015
mc.meson.kasumi:style/MaterialAlertDialog.Material3 = 0x7f12012f
mc.meson.kasumi:style/Widget.Material3.CollapsingToolbar.Large = 0x7f120388
mc.meson.kasumi:dimen/abc_star_small = 0x7f06003d
mc.meson.kasumi:style/Widget.MaterialComponents.Snackbar.TextView = 0x7f120454
mc.meson.kasumi:style/TextAppearance.Material3.MaterialTimePicker.Title = 0x7f1201fd
mc.meson.kasumi:layout/support_simple_spinner_dropdown_item = 0x7f0b0080
mc.meson.kasumi:style/TextAppearance.M3.Sys.Typescale.LabelLarge = 0x7f1201e9
mc.meson.kasumi:style/ThemeOverlay.Material3.Dark.ActionBar = 0x7f1202a3
mc.meson.kasumi:style/Theme.MaterialComponents.BottomSheetDialog = 0x7f120255
mc.meson.kasumi:styleable/LinearProgressIndicator = 0x7f130050
mc.meson.kasumi:style/Widget.MaterialComponents.BottomAppBar = 0x7f120403
mc.meson.kasumi:color/m3_sys_color_dynamic_light_error = 0x7f0501a3
mc.meson.kasumi:style/TextAppearance.AppCompat.Widget.PopupMenu.Small = 0x7f1201cd
mc.meson.kasumi:style/Theme.MaterialComponents.Light = 0x7f120271
mc.meson.kasumi:drawable/ic_arrow_back_24 = 0x7f070092
mc.meson.kasumi:color/m3_sys_color_light_surface_variant = 0x7f0501ef
mc.meson.kasumi:style/Widget.Material3.BottomSheet = 0x7f12035a
mc.meson.kasumi:style/Widget.Material3.CircularProgressIndicator.Legacy.Medium = 0x7f120383
mc.meson.kasumi:style/Base.Theme.AppCompat.Dialog.Alert = 0x7f12005b
mc.meson.kasumi:style/ThemeOverlay.Material3.TextInputEditText = 0x7f1202c6
mc.meson.kasumi:color/material_dynamic_neutral100 = 0x7f050228
mc.meson.kasumi:macro/m3_comp_navigation_rail_active_icon_color = 0x7f0c0095
mc.meson.kasumi:dimen/mtrl_btn_padding_top = 0x7f060268
mc.meson.kasumi:id/constraint = 0x7f0800a2
mc.meson.kasumi:id/listMode = 0x7f080127
mc.meson.kasumi:id/tvModuleName = 0x7f08024c
mc.meson.kasumi:layout/mtrl_calendar_month = 0x7f0b0062
mc.meson.kasumi:style/Widget.Material3.BottomAppBar.Legacy = 0x7f120356
mc.meson.kasumi:style/Widget.AppCompat.SearchView.ActionBar = 0x7f120333
mc.meson.kasumi:styleable/RecyclerView = 0x7f130084
mc.meson.kasumi:dimen/m3_comp_outlined_card_outline_width = 0x7f060153
mc.meson.kasumi:color/md_theme_dark_onError = 0x7f0502c2
mc.meson.kasumi:dimen/mtrl_snackbar_message_margin_horizontal = 0x7f0602f4
mc.meson.kasumi:style/Widget.Material3.MaterialCalendar.Day.Today = 0x7f1203ab
mc.meson.kasumi:drawable/ic_features_24 = 0x7f0700a3
mc.meson.kasumi:drawable/abc_dialog_material_background = 0x7f07003c
mc.meson.kasumi:style/Widget.AppCompat.ActionBar.TabBar = 0x7f1202f7
mc.meson.kasumi:style/Widget.Material3.MaterialTimePicker.Display.Divider = 0x7f1203c4
mc.meson.kasumi:style/TextAppearance.AppCompat.Widget.ActionBar.Title.Inverse = 0x7f1201c1
mc.meson.kasumi:style/TextAppearance.AppCompat.Widget.ActionBar.Menu = 0x7f1201bd
mc.meson.kasumi:style/ThemeOverlay.MaterialComponents.AutoCompleteTextView = 0x7f1202d1
mc.meson.kasumi:style/Base.V24.Theme.Material3.Light.Dialog = 0x7f1200c1
mc.meson.kasumi:integer/material_motion_duration_long_2 = 0x7f090028
mc.meson.kasumi:id/flip = 0x7f0800ef
mc.meson.kasumi:layout/mtrl_picker_text_input_date = 0x7f0b0073
mc.meson.kasumi:style/App.Card.Outlined = 0x7f12000e
mc.meson.kasumi:style/TextAppearance.AppCompat.SearchResult.Title = 0x7f1201b5
mc.meson.kasumi:style/Base.TextAppearance.AppCompat.Small.Inverse = 0x7f120039
mc.meson.kasumi:style/TextAppearance.Material3.DisplaySmall = 0x7f1201f6
mc.meson.kasumi:macro/m3_comp_navigation_bar_active_focus_label_text_color = 0x7f0c0060
mc.meson.kasumi:style/Widget.Material3.AutoCompleteTextView.OutlinedBox.Dense = 0x7f120351
mc.meson.kasumi:styleable/CollapsingToolbarLayout = 0x7f130025
mc.meson.kasumi:layout/design_text_input_start_icon = 0x7f0b002f
mc.meson.kasumi:color/m3_sys_color_dynamic_on_secondary_fixed_variant = 0x7f0501c7
mc.meson.kasumi:styleable/NavGraphNavigator = 0x7f130073
mc.meson.kasumi:dimen/m3_comp_date_picker_modal_date_today_container_outline_width = 0x7f060104
mc.meson.kasumi:styleable/ConstraintLayout_ReactiveGuide = 0x7f13002b
mc.meson.kasumi:style/TextAppearance.AppCompat.Display2 = 0x7f1201a6
mc.meson.kasumi:color/m3_sys_color_dynamic_dark_tertiary_container = 0x7f0501a1
mc.meson.kasumi:style/ThemeOverlay.MaterialComponents.MaterialAlertDialog = 0x7f1202e1
mc.meson.kasumi:style/ShapeAppearanceOverlay.MaterialComponents.FloatingActionButton = 0x7f12019b
mc.meson.kasumi:style/ThemeOverlay.Material3.Button.ElevatedButton = 0x7f120299
mc.meson.kasumi:style/Widget.MaterialComponents.MaterialCalendar.HeaderSelection = 0x7f120439
mc.meson.kasumi:macro/m3_comp_switch_unselected_hover_handle_color = 0x7f0c0135
mc.meson.kasumi:style/Widget.MaterialComponents.LinearProgressIndicator = 0x7f12042a
mc.meson.kasumi:id/report_drawn = 0x7f0801b4
mc.meson.kasumi:string/mtrl_checkbox_button_path_unchecked = 0x7f110084
mc.meson.kasumi:style/ThemeOverlay.Material3.ActionBar = 0x7f12028e
mc.meson.kasumi:style/Widget.Material3.MaterialTimePicker.Display = 0x7f1203c3
mc.meson.kasumi:drawable/abc_list_longpressed_holo = 0x7f070050
mc.meson.kasumi:style/Base.Widget.Material3.ExtendedFloatingActionButton = 0x7f120113
mc.meson.kasumi:macro/m3_comp_slider_label_container_color = 0x7f0c0111
mc.meson.kasumi:style/Theme.MaterialComponents.Light.DialogWhenLarge = 0x7f12027e
mc.meson.kasumi:style/Widget.MaterialComponents.FloatingActionButton = 0x7f120428
mc.meson.kasumi:drawable/m3_tabs_transparent_background = 0x7f0700c0
mc.meson.kasumi:integer/mtrl_switch_thumb_post_morphing_duration = 0x7f090038
mc.meson.kasumi:style/Base.MaterialAlertDialog.MaterialComponents.Title.Icon = 0x7f120020
mc.meson.kasumi:style/Theme.Material3.Light.NoActionBar = 0x7f120252
mc.meson.kasumi:style/TextAppearance.Design.Counter.Overflow = 0x7f1201d7
mc.meson.kasumi:layout/mtrl_alert_dialog = 0x7f0b0057
mc.meson.kasumi:layout/abc_alert_dialog_material = 0x7f0b0009
mc.meson.kasumi:dimen/mtrl_progress_circular_radius = 0x7f0602d8
mc.meson.kasumi:macro/m3_comp_outlined_text_field_input_text_color = 0x7f0c00c0
mc.meson.kasumi:style/Widget.MaterialComponents.MaterialCalendar.YearNavigationButton = 0x7f120443
mc.meson.kasumi:string/mtrl_checkbox_button_icon_path_checked = 0x7f11007d
mc.meson.kasumi:macro/m3_comp_outlined_button_disabled_outline_color = 0x7f0c00a3
mc.meson.kasumi:macro/m3_comp_search_view_divider_color = 0x7f0c00f2
mc.meson.kasumi:integer/mtrl_badge_max_character_count = 0x7f09002e
mc.meson.kasumi:style/Widget.Material3.PopupMenu.ContextMenu = 0x7f1203ce
mc.meson.kasumi:style/ThemeOverlay.Material3.DynamicColors.Dark = 0x7f1202a9
mc.meson.kasumi:style/Theme.MaterialComponents = 0x7f120254
mc.meson.kasumi:style/TextAppearance.Material3.BodyLarge = 0x7f1201f1
mc.meson.kasumi:color/m3_sys_color_dynamic_dark_on_primary_container = 0x7f05018a
mc.meson.kasumi:layout/design_navigation_item = 0x7f0b0028
mc.meson.kasumi:style/ShapeAppearance.M3.Comp.TextButton.Container.Shape = 0x7f120173
mc.meson.kasumi:style/Widget.MaterialComponents.Chip.Choice = 0x7f12041a
mc.meson.kasumi:id/home = 0x7f080101
mc.meson.kasumi:color/material_personalized_color_surface_container_lowest = 0x7f0502a1
mc.meson.kasumi:style/Platform.ThemeOverlay.AppCompat.Light = 0x7f12014b
mc.meson.kasumi:style/Base.Widget.AppCompat.Button.Borderless.Colored = 0x7f1200dc
mc.meson.kasumi:dimen/design_fab_border_width = 0x7f06006e
mc.meson.kasumi:style/Widget.Material3.TextInputEditText.OutlinedBox = 0x7f1203e8
mc.meson.kasumi:style/Widget.AppCompat.RatingBar.Indicator = 0x7f120330
mc.meson.kasumi:style/TextAppearance.Design.Counter = 0x7f1201d6
mc.meson.kasumi:id/rvSettings = 0x7f0801c1
mc.meson.kasumi:macro/m3_comp_time_picker_period_selector_unselected_pressed_state_layer_color = 0x7f0c015d
mc.meson.kasumi:style/Base.Widget.AppCompat.CompoundButton.Switch = 0x7f1200e4
mc.meson.kasumi:styleable/NavigationRailView = 0x7f130079
mc.meson.kasumi:id/ignoreRequest = 0x7f08010a
mc.meson.kasumi:macro/m3_comp_navigation_rail_active_pressed_state_layer_color = 0x7f0c0098
mc.meson.kasumi:id/action_home_to_settings = 0x7f080044
mc.meson.kasumi:macro/m3_comp_secondary_navigation_tab_hover_state_layer_color = 0x7f0c00fe
mc.meson.kasumi:style/Widget.Material3.PopupMenu = 0x7f1203cd
mc.meson.kasumi:style/Theme.Kasumi.FloatingWindow = 0x7f120235
mc.meson.kasumi:style/TextAppearance.Material3.SearchView = 0x7f1201ff
mc.meson.kasumi:layout/design_menu_item_action_area = 0x7f0b0027
mc.meson.kasumi:style/Base.Widget.AppCompat.TextView.SpinnerItem = 0x7f120106
mc.meson.kasumi:style/TextAppearance.M3.Sys.Typescale.HeadlineMedium = 0x7f1201e7
mc.meson.kasumi:color/m3_sys_color_dynamic_light_background = 0x7f0501a2
mc.meson.kasumi:style/Widget.AppCompat.Light.ActionBar.Solid.Inverse = 0x7f120311
mc.meson.kasumi:string/speed_desc = 0x7f1100d5
mc.meson.kasumi:id/tag_unhandled_key_event_manager = 0x7f080218
mc.meson.kasumi:macro/m3_comp_time_picker_period_selector_selected_hover_state_layer_color = 0x7f0c0157
mc.meson.kasumi:styleable/TextAppearance = 0x7f13009b
mc.meson.kasumi:style/Base.Theme.AppCompat.Light.DarkActionBar = 0x7f120060
mc.meson.kasumi:id/search_src_text = 0x7f0801d4
mc.meson.kasumi:style/Widget.MaterialComponents.TimePicker.Display.TextInputEditText = 0x7f12046b
mc.meson.kasumi:style/Widget.MaterialComponents.MaterialCalendar.MonthNavigationButton = 0x7f12043e
mc.meson.kasumi:style/ShapeAppearanceOverlay.Material3.Corner.Left = 0x7f120190
mc.meson.kasumi:color/material_slider_thumb_color = 0x7f0502b5
mc.meson.kasumi:style/Widget.Material3.CircularProgressIndicator.Legacy = 0x7f120381
mc.meson.kasumi:style/TextAppearance.AppCompat.Widget.PopupMenu.Large = 0x7f1201cc
mc.meson.kasumi:macro/m3_comp_navigation_drawer_active_hover_label_text_color = 0x7f0c007c
mc.meson.kasumi:style/Theme.Material3.Light.BottomSheetDialog = 0x7f12024d
mc.meson.kasumi:style/Widget.MaterialComponents.CircularProgressIndicator = 0x7f12041e
mc.meson.kasumi:styleable/ColorStateListItem = 0x7f130027
mc.meson.kasumi:drawable/mtrl_checkbox_button_icon_indeterminate_unchecked = 0x7f0700d3
mc.meson.kasumi:style/Widget.Material3.Button.ElevatedButton.Icon = 0x7f12035f
mc.meson.kasumi:string/dark_mode_desc = 0x7f11003c
mc.meson.kasumi:styleable/OnClick = 0x7f13007c
mc.meson.kasumi:styleable/OnSwipe = 0x7f13007d
mc.meson.kasumi:style/ThemeOverlay.Material3.Button = 0x7f120298
mc.meson.kasumi:styleable/TextEffects = 0x7f13009c
mc.meson.kasumi:styleable/AnimatedStateListDrawableTransition = 0x7f13000c
mc.meson.kasumi:id/alertTitle = 0x7f080052
mc.meson.kasumi:color/material_on_primary_emphasis_medium = 0x7f050277
mc.meson.kasumi:style/Widget.MaterialComponents.Button.Icon = 0x7f12040c
mc.meson.kasumi:styleable/LinearLayoutCompat = 0x7f13004e
mc.meson.kasumi:style/ShapeAppearance.Material3.Tooltip = 0x7f120186
mc.meson.kasumi:style/Widget.MaterialComponents.MaterialButtonToggleGroup = 0x7f12042b
mc.meson.kasumi:macro/m3_comp_radio_button_unselected_pressed_icon_color = 0x7f0c00e4
mc.meson.kasumi:id/currentState = 0x7f0800ac
mc.meson.kasumi:string/mtrl_picker_cancel = 0x7f110090
mc.meson.kasumi:string/material_motion_easing_decelerated = 0x7f11006d
mc.meson.kasumi:style/Widget.Material3.TextInputLayout.FilledBox = 0x7f1203ea
mc.meson.kasumi:drawable/abc_btn_check_material = 0x7f07002c
mc.meson.kasumi:dimen/m3_comp_switch_selected_hover_state_layer_opacity = 0x7f060195
mc.meson.kasumi:style/ShapeAppearance.Material3.Corner.ExtraSmall = 0x7f12017c
mc.meson.kasumi:macro/m3_comp_outlined_text_field_disabled_outline_color = 0x7f0c00b4
mc.meson.kasumi:macro/m3_comp_date_picker_modal_date_label_text_type = 0x7f0c000f
mc.meson.kasumi:style/Base.TextAppearance.AppCompat.Widget.Button.Borderless.Colored = 0x7f120047
mc.meson.kasumi:integer/status_bar_notification_info_maxnum = 0x7f090044
mc.meson.kasumi:style/Widget.AppCompat.ActivityChooserView = 0x7f1202fe
mc.meson.kasumi:style/Theme.AppCompat.Light.Dialog.MinWidth = 0x7f12022a
mc.meson.kasumi:style/Widget.MaterialComponents.AutoCompleteTextView.FilledBox = 0x7f1203fe
mc.meson.kasumi:style/Widget.MaterialComponents.ActionBar.Solid = 0x7f1203f8
mc.meson.kasumi:style/Base.V14.Theme.MaterialComponents.Light.Dialog.Bridge = 0x7f1200a6
mc.meson.kasumi:style/Base.Widget.AppCompat.SeekBar.Discrete = 0x7f120102
mc.meson.kasumi:drawable/mtrl_checkbox_button_icon_checked_indeterminate = 0x7f0700d0
mc.meson.kasumi:style/ShapeAppearance.M3.Comp.Switch.Track.Shape = 0x7f120172
mc.meson.kasumi:id/marquee = 0x7f080131
mc.meson.kasumi:dimen/m3_btn_text_btn_padding_left = 0x7f0600e0
mc.meson.kasumi:style/ShapeAppearance.Material3.MediumComponent = 0x7f120183
mc.meson.kasumi:style/Widget.MaterialComponents.NavigationRailView.PrimarySurface = 0x7f120449
mc.meson.kasumi:style/ShapeAppearance.M3.Comp.NavigationRail.Container.Shape = 0x7f12016b
mc.meson.kasumi:macro/m3_comp_menu_list_item_selected_container_color = 0x7f0c005e
mc.meson.kasumi:style/ShapeAppearance.M3.Comp.NavigationBar.Container.Shape = 0x7f120168
mc.meson.kasumi:string/abc_menu_meta_shortcut_label = 0x7f11000d
mc.meson.kasumi:style/RtlOverlay.Widget.AppCompat.Search.DropDown.Text = 0x7f12015e
mc.meson.kasumi:id/material_timepicker_cancel_button = 0x7f080143
mc.meson.kasumi:style/RtlOverlay.Widget.AppCompat.Search.DropDown.Icon1 = 0x7f12015b
mc.meson.kasumi:id/snap = 0x7f0801e9
mc.meson.kasumi:style/Base.Theme.MaterialComponents.Light.DialogWhenLarge = 0x7f120084
mc.meson.kasumi:macro/m3_comp_dialog_headline_type = 0x7f0c0025
mc.meson.kasumi:string/check_update = 0x7f110034
mc.meson.kasumi:style/Widget.Material3.Button.ElevatedButton = 0x7f12035e
mc.meson.kasumi:macro/m3_comp_checkbox_selected_error_container_color = 0x7f0c0009
mc.meson.kasumi:layout/mtrl_calendar_month_labeled = 0x7f0b0063
mc.meson.kasumi:id/btnCheckUpdate = 0x7f080078
mc.meson.kasumi:integer/material_motion_duration_long_1 = 0x7f090027
mc.meson.kasumi:style/TextAppearance.Material3.DisplayLarge = 0x7f1201f4
mc.meson.kasumi:style/Base.Theme.MaterialComponents.Light.DarkActionBar = 0x7f12007d
mc.meson.kasumi:attr/cardBackgroundColor = 0x7f03009f
mc.meson.kasumi:color/mtrl_navigation_bar_item_tint = 0x7f050320
mc.meson.kasumi:dimen/mtrl_slider_thumb_elevation = 0x7f0602e9
mc.meson.kasumi:string/notifications_desc = 0x7f1100c2
mc.meson.kasumi:style/RtlOverlay.Widget.AppCompat.DialogTitle.Icon = 0x7f120153
mc.meson.kasumi:style/Base.Widget.Material3.CollapsingToolbar = 0x7f12010f
mc.meson.kasumi:macro/m3_comp_filled_tonal_icon_button_container_color = 0x7f0c0054
mc.meson.kasumi:macro/m3_comp_dialog_supporting_text_type = 0x7f0c0027
mc.meson.kasumi:styleable/KeyAttribute = 0x7f130045
mc.meson.kasumi:style/Platform.V25.AppCompat = 0x7f12014e
mc.meson.kasumi:style/Theme.AppCompat.DayNight = 0x7f12021a
mc.meson.kasumi:style/Platform.V21.AppCompat.Light = 0x7f12014d
mc.meson.kasumi:interpolator/btn_checkbox_checked_mtrl_animation_interpolator_1 = 0x7f0a0001
mc.meson.kasumi:style/Widget.Material3.Chip.Assist = 0x7f120374
mc.meson.kasumi:integer/mtrl_switch_thumb_motion_duration = 0x7f090037
mc.meson.kasumi:dimen/mtrl_calendar_landscape_header_width = 0x7f060287
mc.meson.kasumi:id/skipped = 0x7f0801e3
mc.meson.kasumi:color/switch_thumb_normal_material_dark = 0x7f050351
mc.meson.kasumi:style/Platform.V21.AppCompat = 0x7f12014c
mc.meson.kasumi:style/Widget.Design.TextInputLayout = 0x7f12034a
mc.meson.kasumi:macro/m3_comp_search_view_header_supporting_text_color = 0x7f0c00f7
mc.meson.kasumi:string/about_desc = 0x7f11001c
mc.meson.kasumi:style/App.Text.Headline = 0x7f120014
mc.meson.kasumi:style/TextAppearance.AppCompat.Widget.Button.Borderless.Colored = 0x7f1201c7
mc.meson.kasumi:styleable/ClockHandView = 0x7f130024
mc.meson.kasumi:style/Base.TextAppearance.AppCompat.Widget.DropDownItem = 0x7f12004a
mc.meson.kasumi:style/Platform.MaterialComponents.Light = 0x7f120147
mc.meson.kasumi:integer/material_motion_path = 0x7f09002d
mc.meson.kasumi:style/ShapeAppearance.M3.Comp.NavigationRail.ActiveIndicator.Shape = 0x7f12016a
mc.meson.kasumi:id/btnExpand = 0x7f08007d
mc.meson.kasumi:color/switch_thumb_material_light = 0x7f050350
mc.meson.kasumi:attr/viewTransitionOnCross = 0x7f03050e
mc.meson.kasumi:style/Base.V14.Theme.Material3.Light.BottomSheetDialog = 0x7f12009b
mc.meson.kasumi:style/Base.Theme.AppCompat.CompactMenu = 0x7f120059
mc.meson.kasumi:id/search_close_btn = 0x7f0801cf
mc.meson.kasumi:style/MaterialAlertDialog.MaterialComponents.Body.Text = 0x7f12013a
mc.meson.kasumi:dimen/material_cursor_width = 0x7f060231
mc.meson.kasumi:style/TextAppearance.Material3.LabelSmall = 0x7f1201fc
mc.meson.kasumi:style/MaterialAlertDialog.Material3.Title.Panel = 0x7f120135
mc.meson.kasumi:style/Widget.MaterialComponents.MaterialCalendar.Day.Selected = 0x7f12042f
mc.meson.kasumi:dimen/cardview_default_elevation = 0x7f060053
mc.meson.kasumi:id/view_tree_lifecycle_owner = 0x7f08025d
mc.meson.kasumi:style/MaterialAlertDialog.Material3.Body.Text.CenterStacked = 0x7f120132
mc.meson.kasumi:string/mtrl_switch_thumb_group_name = 0x7f1100b0
mc.meson.kasumi:layout/abc_list_menu_item_icon = 0x7f0b000f
mc.meson.kasumi:style/ShapeAppearanceOverlay.Material3.Button = 0x7f12018d
mc.meson.kasumi:integer/cancel_button_image_alpha = 0x7f090004
mc.meson.kasumi:color/minecraft_yellow_dark = 0x7f050302
mc.meson.kasumi:color/material_personalized_color_control_normal = 0x7f050281
mc.meson.kasumi:layout/mtrl_calendar_months = 0x7f0b0065
mc.meson.kasumi:style/MaterialAlertDialog.Material3.Body.Text = 0x7f120131
mc.meson.kasumi:styleable/LottieAnimationView = 0x7f130052
mc.meson.kasumi:style/MaterialAlertDialog.Material3.Animation = 0x7f120130
mc.meson.kasumi:id/triangle = 0x7f080240
mc.meson.kasumi:styleable/Fragment = 0x7f13003d
mc.meson.kasumi:layout/item_expanded_module = 0x7f0b003c
mc.meson.kasumi:style/ThemeOverlay.Material3.NavigationRailView = 0x7f1202bf
mc.meson.kasumi:style/ThemeOverlay.MaterialComponents.TimePicker.Display = 0x7f1202f0
mc.meson.kasumi:styleable/ActionBarLayout = 0x7f130001
mc.meson.kasumi:color/m3_sys_color_dynamic_dark_surface_dim = 0x7f05019e
mc.meson.kasumi:styleable/FontFamily = 0x7f13003a
mc.meson.kasumi:id/neverCompleteToEnd = 0x7f08017e
mc.meson.kasumi:string/mtrl_switch_thumb_path_checked = 0x7f1100b1
mc.meson.kasumi:style/Widget.MaterialComponents.PopupMenu.ListPopupWindow = 0x7f12044d
mc.meson.kasumi:macro/m3_comp_outlined_autocomplete_text_field_input_text_type = 0x7f0c00a2
mc.meson.kasumi:style/CardView = 0x7f12012c
mc.meson.kasumi:color/md_theme_light_primary = 0x7f0502ec
mc.meson.kasumi:style/Widget.Material3.Chip.Assist.Elevated = 0x7f120375
mc.meson.kasumi:style/Base.Widget.MaterialComponents.Snackbar = 0x7f120128
mc.meson.kasumi:macro/m3_comp_top_app_bar_small_trailing_icon_color = 0x7f0c0174
mc.meson.kasumi:id/dragStart = 0x7f0800cb
mc.meson.kasumi:style/ThemeOverlay.MaterialComponents.TextInputEditText = 0x7f1202ea
mc.meson.kasumi:style/Widget.Material3.TextInputLayout.FilledBox.Dense = 0x7f1203eb
mc.meson.kasumi:styleable/TextInputLayout = 0x7f13009e
mc.meson.kasumi:style/Theme.MaterialComponents.Light.Dialog = 0x7f120276
mc.meson.kasumi:styleable/AppBarLayout = 0x7f13000d
mc.meson.kasumi:style/TextAppearance.AppCompat.Display3 = 0x7f1201a7
mc.meson.kasumi:string/m3_sys_motion_easing_linear = 0x7f110061
mc.meson.kasumi:drawable/$mtrl_switch_thumb_unchecked_checked__0 = 0x7f070026
mc.meson.kasumi:drawable/abc_ratingbar_material = 0x7f07005c
mc.meson.kasumi:color/material_dynamic_neutral_variant100 = 0x7f050235
mc.meson.kasumi:style/Widget.AppCompat.Light.ActionMode.Inverse = 0x7f12031b
mc.meson.kasumi:style/Theme.AppCompat.DayNight.DarkActionBar = 0x7f12021b
mc.meson.kasumi:style/Widget.Material3.Light.ActionBar.Solid = 0x7f1203a3
mc.meson.kasumi:styleable/CoordinatorLayout = 0x7f13002f
mc.meson.kasumi:interpolator/btn_checkbox_unchecked_mtrl_animation_interpolator_0 = 0x7f0a0002
mc.meson.kasumi:style/Widget.Material3.ExtendedFloatingActionButton.Icon.Secondary = 0x7f120390
mc.meson.kasumi:style/Base.Widget.MaterialComponents.MaterialCalendar.HeaderToggleButton = 0x7f120121
mc.meson.kasumi:style/Widget.Material3.LinearProgressIndicator.Legacy = 0x7f1203a5
mc.meson.kasumi:style/Theme.MaterialComponents.DayNight.NoActionBar = 0x7f120266
mc.meson.kasumi:dimen/mtrl_navigation_bar_item_default_margin = 0x7f0602c4
mc.meson.kasumi:style/TextAppearance.MaterialComponents.Headline6 = 0x7f12020f
mc.meson.kasumi:dimen/m3_sys_elevation_level4 = 0x7f0601f4
mc.meson.kasumi:style/Theme.MaterialComponents.Light.NoActionBar = 0x7f12027f
mc.meson.kasumi:style/ShapeAppearance.M3.Comp.Switch.StateLayer.Shape = 0x7f120171
mc.meson.kasumi:style/Base.Widget.MaterialComponents.AutoCompleteTextView = 0x7f12011e
mc.meson.kasumi:macro/m3_comp_primary_navigation_tab_active_indicator_color = 0x7f0c00c9
mc.meson.kasumi:dimen/mtrl_textinput_outline_box_expanded_padding = 0x7f060303
mc.meson.kasumi:styleable/MaterialCalendar = 0x7f130058
mc.meson.kasumi:id/transition_image_transform = 0x7f08023a
mc.meson.kasumi:style/Base.Widget.Material3.MaterialCalendar.NavigationButton = 0x7f120119
mc.meson.kasumi:style/Widget.Material3.MaterialCalendar.HeaderLayout.Fullscreen = 0x7f1203b2
mc.meson.kasumi:id/mtrl_picker_text_input_date = 0x7f080167
mc.meson.kasumi:id/ratio = 0x7f0801af
mc.meson.kasumi:style/Widget.MaterialComponents.MaterialCalendar.MonthTextView = 0x7f12043f
mc.meson.kasumi:style/Base.Widget.Material3.BottomNavigationView = 0x7f12010c
mc.meson.kasumi:styleable/DrawerLayout = 0x7f130034
mc.meson.kasumi:dimen/m3_navigation_rail_elevation = 0x7f0601ca
mc.meson.kasumi:styleable/Capability = 0x7f13001c
mc.meson.kasumi:style/Base.Widget.AppCompat.Toolbar = 0x7f120107
mc.meson.kasumi:id/textinput_suffix_text = 0x7f08022a
mc.meson.kasumi:style/Widget.AppCompat.Button.Small = 0x7f120305
mc.meson.kasumi:style/Widget.AppCompat.Light.ActionBar.TabText = 0x7f120314
mc.meson.kasumi:macro/m3_comp_icon_button_unselected_icon_color = 0x7f0c005a
mc.meson.kasumi:style/Widget.MaterialComponents.Button.TextButton = 0x7f12040f
mc.meson.kasumi:style/Base.V21.Theme.MaterialComponents.Light = 0x7f1200b4
mc.meson.kasumi:style/Base.Widget.AppCompat.ButtonBar.AlertDialog = 0x7f1200e1
mc.meson.kasumi:style/TextAppearance.Material3.TitleLarge = 0x7f120201
mc.meson.kasumi:style/Animation.Material3.SideSheetDialog.Right = 0x7f120009
mc.meson.kasumi:styleable/View = 0x7f1300a5
mc.meson.kasumi:id/parentPanel = 0x7f08019f
mc.meson.kasumi:color/m3_text_button_background_color_selector = 0x7f050204
mc.meson.kasumi:style/Base.Widget.AppCompat.ListView.Menu = 0x7f1200f6
mc.meson.kasumi:style/MaterialAlertDialog.Material3.Title.Panel.CenterStacked = 0x7f120136
mc.meson.kasumi:style/Base.Widget.Material3.FloatingActionButton = 0x7f120115
mc.meson.kasumi:style/Widget.MaterialComponents.BottomNavigationView.PrimarySurface = 0x7f120408
mc.meson.kasumi:style/Widget.Material3.Slider.Legacy.Label = 0x7f1203df
mc.meson.kasumi:macro/m3_comp_primary_navigation_tab_active_focus_state_layer_color = 0x7f0c00c7
mc.meson.kasumi:macro/m3_comp_outlined_card_dragged_outline_color = 0x7f0c00ab
mc.meson.kasumi:style/Base.Widget.AppCompat.ListPopupWindow = 0x7f1200f3
mc.meson.kasumi:style/Base.Widget.AppCompat.Light.PopupMenu = 0x7f1200f0
mc.meson.kasumi:id/textSpacerNoButtons = 0x7f08021e
mc.meson.kasumi:dimen/m3_comp_secondary_navigation_tab_focus_state_layer_opacity = 0x7f060177
mc.meson.kasumi:string/version = 0x7f1100d8
mc.meson.kasumi:id/scrollable = 0x7f0801cb
mc.meson.kasumi:macro/m3_comp_fab_primary_icon_color = 0x7f0c0038
mc.meson.kasumi:style/Base.Widget.AppCompat.Light.ActionBar.TabBar = 0x7f1200ec
mc.meson.kasumi:layout/mtrl_picker_header_dialog = 0x7f0b006e
mc.meson.kasumi:layout/item_config = 0x7f0b003b
mc.meson.kasumi:string/nav_features = 0x7f1100bc
mc.meson.kasumi:style/Widget.MaterialComponents.Chip.Entry = 0x7f12041b
mc.meson.kasumi:style/Base.V7.Widget.AppCompat.Toolbar = 0x7f1200ce
mc.meson.kasumi:style/Base.V28.Theme.AppCompat.Light = 0x7f1200c6
mc.meson.kasumi:color/mtrl_scrim_color = 0x7f05032a
mc.meson.kasumi:style/Base.Theme.MaterialComponents.Dialog.MinWidth = 0x7f120079
mc.meson.kasumi:style/Base.V24.Theme.Material3.Dark.Dialog = 0x7f1200bf
mc.meson.kasumi:style/Widget.Material3.TextInputLayout.OutlinedBox.ExposedDropdownMenu = 0x7f1203f1
mc.meson.kasumi:macro/m3_comp_radio_button_selected_focus_state_layer_color = 0x7f0c00d9
mc.meson.kasumi:style/Base.V21.ThemeOverlay.Material3.BottomSheetDialog = 0x7f1200b7
mc.meson.kasumi:macro/m3_comp_time_picker_period_selector_container_shape = 0x7f0c0152
mc.meson.kasumi:style/TextAppearance.AppCompat.Body2 = 0x7f1201a2
mc.meson.kasumi:style/Widget.MaterialComponents.ActionBar.Surface = 0x7f1203f9
mc.meson.kasumi:style/Widget.MaterialComponents.MaterialCalendar.HeaderConfirmButton = 0x7f120435
mc.meson.kasumi:macro/m3_comp_dialog_headline_color = 0x7f0c0024
mc.meson.kasumi:style/Base.V21.Theme.MaterialComponents.Light.Dialog = 0x7f1200b5
mc.meson.kasumi:macro/m3_comp_top_app_bar_small_headline_color = 0x7f0c0170
mc.meson.kasumi:styleable/FragmentContainerView = 0x7f13003e
mc.meson.kasumi:style/Widget.Design.Snackbar = 0x7f120347
mc.meson.kasumi:style/TextAppearance.MaterialComponents.Headline1 = 0x7f12020a
mc.meson.kasumi:id/switchModuleEnabled = 0x7f080209
mc.meson.kasumi:string/mtrl_picker_announce_current_selection_none = 0x7f11008f
mc.meson.kasumi:interpolator/mtrl_fast_out_linear_in = 0x7f0a000e
mc.meson.kasumi:style/ThemeOverlay.Material3.AutoCompleteTextView.OutlinedBox = 0x7f120292
mc.meson.kasumi:string/abc_capital_off = 0x7f110006
mc.meson.kasumi:style/Widget.Material3.Toolbar = 0x7f1203f2
mc.meson.kasumi:string/abc_menu_sym_shortcut_label = 0x7f110010
mc.meson.kasumi:integer/mtrl_btn_anim_duration_ms = 0x7f090030
mc.meson.kasumi:macro/m3_comp_secondary_navigation_tab_inactive_label_text_color = 0x7f0c00ff
mc.meson.kasumi:style/Base.V14.Theme.MaterialComponents.Light.DarkActionBar.Bridge = 0x7f1200a4
mc.meson.kasumi:style/Base.V14.Theme.MaterialComponents.Light = 0x7f1200a2
mc.meson.kasumi:macro/m3_comp_search_view_docked_container_shape = 0x7f0c00f3
mc.meson.kasumi:style/Base.V14.Theme.Material3.Dark.Dialog = 0x7f120098
mc.meson.kasumi:style/Theme.MaterialComponents.Light.Dialog.FixedSize = 0x7f12027a
mc.meson.kasumi:macro/m3_comp_input_chip_label_text_type = 0x7f0c005c
mc.meson.kasumi:dimen/m3_searchbar_text_margin_start_no_navigation_icon = 0x7f0601e0
mc.meson.kasumi:styleable/Constraint = 0x7f130029
mc.meson.kasumi:string/m3_sys_motion_easing_legacy_accelerate = 0x7f11005f
mc.meson.kasumi:id/layoutEmptyState = 0x7f080120
mc.meson.kasumi:style/Widget.MaterialComponents.Button.TextButton.Dialog.Icon = 0x7f120412
mc.meson.kasumi:style/Base.ThemeOverlay.AppCompat.Dark.ActionBar = 0x7f120088
mc.meson.kasumi:style/Base.Theme.MaterialComponents.Light.Dialog.FixedSize = 0x7f120082
mc.meson.kasumi:style/Base.Theme.MaterialComponents.Light.DarkActionBar.Bridge = 0x7f12007e
mc.meson.kasumi:styleable/GradientColor = 0x7f130040
mc.meson.kasumi:style/ThemeOverlay.Material3.HarmonizedColors.Empty = 0x7f1202b5
mc.meson.kasumi:style/Base.Theme.Material3.Light = 0x7f12006c
mc.meson.kasumi:style/Base.Theme.Material3.Dark.BottomSheetDialog = 0x7f120067
mc.meson.kasumi:style/Base.Theme.AppCompat.Light.Dialog = 0x7f120061
mc.meson.kasumi:string/icon_content_description = 0x7f110050
mc.meson.kasumi:macro/m3_comp_time_input_time_input_field_supporting_text_color = 0x7f0c014a
mc.meson.kasumi:style/Base.TextAppearance.AppCompat.Widget.PopupMenu.Large = 0x7f12004c
mc.meson.kasumi:id/accessibility_custom_action_9 = 0x7f080030
mc.meson.kasumi:style/Widget.Material3.MaterialButtonToggleGroup = 0x7f1203a6
mc.meson.kasumi:style/Base.Widget.AppCompat.DrawerArrowToggle = 0x7f1200e5
mc.meson.kasumi:style/Base.Theme.AppCompat.Dialog.FixedSize = 0x7f12005c
mc.meson.kasumi:style/Widget.Material3.SideSheet = 0x7f1203d8
mc.meson.kasumi:macro/m3_comp_snackbar_container_color = 0x7f0c0113
mc.meson.kasumi:macro/m3_comp_primary_navigation_tab_active_hover_state_layer_color = 0x7f0c00c8
mc.meson.kasumi:id/grouping = 0x7f0800fb
mc.meson.kasumi:style/Platform.AppCompat = 0x7f120143
mc.meson.kasumi:string/mtrl_picker_navigate_to_current_year_description = 0x7f11009b
mc.meson.kasumi:macro/m3_comp_fab_primary_small_container_shape = 0x7f0c003a
mc.meson.kasumi:macro/m3_comp_switch_unselected_pressed_icon_color = 0x7f0c013c
mc.meson.kasumi:styleable/ExtendedFloatingActionButton = 0x7f130035
mc.meson.kasumi:style/Base.TextAppearance.AppCompat.Widget.PopupMenu.Small = 0x7f12004d
mc.meson.kasumi:style/Base.TextAppearance.AppCompat.Widget.ActionBar.Subtitle = 0x7f120040
mc.meson.kasumi:style/Base.TextAppearance.AppCompat.Widget.ActionBar.Menu = 0x7f12003f
mc.meson.kasumi:style/Base.TextAppearance.AppCompat.Tooltip = 0x7f12003e
mc.meson.kasumi:style/Widget.Material3.CircularProgressIndicator.Legacy.Small = 0x7f120384
mc.meson.kasumi:drawable/ic_mtrl_checked_circle = 0x7f0700ad
mc.meson.kasumi:style/Base.TextAppearance.AppCompat.Large = 0x7f12002e
mc.meson.kasumi:styleable/MaterialButtonToggleGroup = 0x7f130057
mc.meson.kasumi:style/Theme.MaterialComponents.DayNight.Dialog.FixedSize.Bridge = 0x7f120262
mc.meson.kasumi:style/ThemeOverlay.MaterialComponents.DayNight.BottomSheetDialog = 0x7f1202db
mc.meson.kasumi:style/Base.TextAppearance.AppCompat.Headline = 0x7f12002c
mc.meson.kasumi:style/Widget.AppCompat.Light.ActionButton.CloseMode = 0x7f120319
mc.meson.kasumi:style/Theme.Material3.DynamicColors.Dark.NoActionBar = 0x7f120247
mc.meson.kasumi:style/Base.TextAppearance.AppCompat.Button = 0x7f120026
mc.meson.kasumi:style/ThemeOverlay.Material3.Snackbar = 0x7f1202c4
mc.meson.kasumi:styleable/FontFamilyFont = 0x7f13003b
mc.meson.kasumi:style/Base.TextAppearance.AppCompat.Display2 = 0x7f120029
mc.meson.kasumi:style/Base.MaterialAlertDialog.MaterialComponents.Title.Text = 0x7f120022
mc.meson.kasumi:layout/notification_template_icon_group = 0x7f0b007a
mc.meson.kasumi:style/Base.MaterialAlertDialog.MaterialComponents.Title.Panel = 0x7f120021
mc.meson.kasumi:id/accessibility_custom_action_18 = 0x7f08001b
mc.meson.kasumi:drawable/tab_background_selector = 0x7f070103
mc.meson.kasumi:color/m3_sys_color_dynamic_dark_surface_container_low = 0x7f05019c
mc.meson.kasumi:style/TextAppearance.Compat.Notification.Time = 0x7f1201d3
mc.meson.kasumi:id/btnDeleteConfig = 0x7f08007b
mc.meson.kasumi:id/anticipate = 0x7f08005b
mc.meson.kasumi:id/SHOW_ALL = 0x7f080008
mc.meson.kasumi:style/Base.CardView = 0x7f12001d
mc.meson.kasumi:style/Base.Widget.AppCompat.Light.ActionBar.TabView = 0x7f1200ef
mc.meson.kasumi:macro/m3_comp_date_picker_modal_date_selected_label_text_color = 0x7f0c0011
mc.meson.kasumi:style/ThemeOverlay.MaterialComponents.MaterialAlertDialog.Picker.Date.Header.Text = 0x7f1202e5
mc.meson.kasumi:color/md_theme_dark_onPrimary = 0x7f0502c4
mc.meson.kasumi:macro/m3_comp_switch_unselected_focus_state_layer_color = 0x7f0c0131
mc.meson.kasumi:style/TextAppearance.AppCompat.Widget.ActionBar.Subtitle.Inverse = 0x7f1201bf
mc.meson.kasumi:dimen/mtrl_progress_circular_track_thickness_extra_small = 0x7f0602dd
mc.meson.kasumi:style/Base.TextAppearance.AppCompat.Widget.ActionMode.Subtitle = 0x7f120044
mc.meson.kasumi:style/Base.V21.ThemeOverlay.AppCompat.Dialog = 0x7f1200b6
mc.meson.kasumi:attr/springStiffness = 0x7f030421
mc.meson.kasumi:dimen/m3_navigation_rail_item_min_height = 0x7f0601cf
mc.meson.kasumi:id/open_search_bar_text_view = 0x7f08018b
mc.meson.kasumi:id/ghost_view = 0x7f0800f5
mc.meson.kasumi:style/Base.Theme.MaterialComponents.Bridge = 0x7f120073
mc.meson.kasumi:style/Base.Animation.AppCompat.Tooltip = 0x7f12001c
mc.meson.kasumi:style/ThemeOverlay.AppCompat.DayNight.ActionBar = 0x7f120288
mc.meson.kasumi:style/Platform.Widget.AppCompat.Spinner = 0x7f120150
mc.meson.kasumi:style/ThemeOverlay.Material3.SideSheetDialog = 0x7f1202c3
mc.meson.kasumi:dimen/abc_config_prefDialogWidth = 0x7f060017
mc.meson.kasumi:id/container = 0x7f0800a3
mc.meson.kasumi:id/m3_side_sheet = 0x7f08012f
mc.meson.kasumi:style/Widget.MaterialComponents.TextInputEditText.OutlinedBox = 0x7f12045a
mc.meson.kasumi:style/Theme.Kasumi = 0x7f120234
mc.meson.kasumi:style/Widget.MaterialComponents.MaterialCalendar.Year = 0x7f120440
mc.meson.kasumi:style/Base.Theme.Material3.Dark.DialogWhenLarge = 0x7f12006a
mc.meson.kasumi:integer/config_tooltipAnimTime = 0x7f090006
mc.meson.kasumi:string/status_bar_notification_info_overflow = 0x7f1100d7
mc.meson.kasumi:color/mtrl_fab_icon_text_color_selector = 0x7f050318
mc.meson.kasumi:style/ShapeAppearance.Material3.Corner.Medium = 0x7f12017f
mc.meson.kasumi:styleable/MaterialRadioButton = 0x7f13005e
mc.meson.kasumi:style/Theme.MaterialComponents.Dialog.FixedSize.Bridge = 0x7f12026d
mc.meson.kasumi:style/App.Toolbar = 0x7f120016
mc.meson.kasumi:style/Base.Widget.AppCompat.Button.ButtonBar.AlertDialog = 0x7f1200dd
mc.meson.kasumi:style/App.TabLayout.TextAppearance = 0x7f120012
mc.meson.kasumi:style/App.FloatingActionButton = 0x7f12000f
mc.meson.kasumi:dimen/mtrl_low_ripple_hovered_alpha = 0x7f0602c0
mc.meson.kasumi:style/App.Button.Secondary = 0x7f12000c
mc.meson.kasumi:style/Widget.MaterialComponents.NavigationRailView.Compact = 0x7f120448
mc.meson.kasumi:id/included = 0x7f08010d
mc.meson.kasumi:layout/material_radial_view_group = 0x7f0b004f
mc.meson.kasumi:dimen/m3_btn_text_btn_padding_right = 0x7f0600e1
mc.meson.kasumi:style/App.Button.Primary = 0x7f12000b
mc.meson.kasumi:style/Widget.MaterialComponents.MaterialCalendar.Day.Today = 0x7f120430
mc.meson.kasumi:style/TextAppearance.Design.HelperText = 0x7f1201d9
mc.meson.kasumi:dimen/tooltip_y_offset_non_touch = 0x7f060322
mc.meson.kasumi:style/TextAppearance.MaterialComponents.TimePicker.Title = 0x7f120213
mc.meson.kasumi:style/ThemeOverlay.AppCompat = 0x7f120283
mc.meson.kasumi:style/Base.Theme.MaterialComponents.Light.Dialog = 0x7f12007f
mc.meson.kasumi:color/m3_ref_palette_neutral_variant0 = 0x7f05011e
mc.meson.kasumi:dimen/m3_sys_state_dragged_state_layer_opacity = 0x7f06021a
mc.meson.kasumi:dimen/m3_sys_motion_easing_legacy_decelerate_control_x1 = 0x7f060206
mc.meson.kasumi:macro/m3_comp_snackbar_supporting_text_color = 0x7f0c0115
mc.meson.kasumi:drawable/m3_password_eye = 0x7f0700b9
mc.meson.kasumi:id/ghost_view_holder = 0x7f0800f6
mc.meson.kasumi:dimen/m3_comp_elevated_card_icon_size = 0x7f06010b
mc.meson.kasumi:id/action_config_management_back = 0x7f08003b
mc.meson.kasumi:color/material_personalized_primary_text_disable_only = 0x7f0502af
mc.meson.kasumi:string/kill_aura = 0x7f110052
mc.meson.kasumi:dimen/m3_sys_motion_easing_standard_accelerate_control_y2 = 0x7f060211
mc.meson.kasumi:attr/buttonIconDimen = 0x7f030097
mc.meson.kasumi:style/Animation.AppCompat.Dialog = 0x7f120002
mc.meson.kasumi:style/AlertDialog.AppCompat.Light = 0x7f120001
mc.meson.kasumi:style/Widget.MaterialComponents.BottomAppBar.Colored = 0x7f120404
mc.meson.kasumi:style/Widget.MaterialComponents.BottomSheet.Modal = 0x7f12040a
mc.meson.kasumi:string/side_sheet_behavior = 0x7f1100d3
mc.meson.kasumi:style/Widget.Material3.Toolbar.OnSurface = 0x7f1203f3
mc.meson.kasumi:string/settings = 0x7f1100d0
mc.meson.kasumi:macro/m3_comp_filled_button_label_text_color = 0x7f0c0044
mc.meson.kasumi:integer/m3_sys_shape_corner_small_corner_family = 0x7f090026
mc.meson.kasumi:string/floating_window_notification_title = 0x7f110048
mc.meson.kasumi:style/TextAppearance.AppCompat.Title.Inverse = 0x7f1201bb
mc.meson.kasumi:style/Widget.Material3.FloatingActionButton.Surface = 0x7f1203a1
mc.meson.kasumi:id/mtrl_picker_title_text = 0x7f08016a
mc.meson.kasumi:style/Widget.AppCompat.ImageButton = 0x7f12030e
mc.meson.kasumi:id/month_title = 0x7f080153
mc.meson.kasumi:dimen/material_emphasis_disabled = 0x7f060233
mc.meson.kasumi:dimen/mtrl_switch_thumb_elevation = 0x7f0602f7
mc.meson.kasumi:string/setting_enabled = 0x7f1100cf
mc.meson.kasumi:string/reach_desc = 0x7f1100c9
mc.meson.kasumi:style/ThemeOverlay.MaterialComponents.MaterialCalendar = 0x7f1202e8
mc.meson.kasumi:color/mtrl_tabs_icon_color_selector_colored = 0x7f050331
mc.meson.kasumi:string/path_password_eye = 0x7f1100c4
mc.meson.kasumi:color/m3_sys_color_light_inverse_on_surface = 0x7f0501d3
mc.meson.kasumi:styleable/NavArgument = 0x7f130071
mc.meson.kasumi:style/ThemeOverlay.MaterialComponents.Toolbar.Surface = 0x7f1202f4
mc.meson.kasumi:layout/abc_search_view = 0x7f0b0019
mc.meson.kasumi:layout/abc_alert_dialog_button_bar_material = 0x7f0b0008
mc.meson.kasumi:id/blocking = 0x7f08006f
mc.meson.kasumi:macro/m3_comp_text_button_pressed_state_layer_color = 0x7f0c0146
mc.meson.kasumi:style/Widget.MaterialComponents.TimePicker = 0x7f120465
mc.meson.kasumi:dimen/m3_comp_navigation_rail_container_width = 0x7f060148
mc.meson.kasumi:string/mtrl_picker_toggle_to_calendar_input_mode = 0x7f1100ac
mc.meson.kasumi:string/mtrl_picker_start_date_description = 0x7f1100a4
mc.meson.kasumi:style/Widget.MaterialComponents.TextInputEditText.FilledBox = 0x7f120458
mc.meson.kasumi:id/actionUp = 0x7f080033
mc.meson.kasumi:macro/m3_comp_navigation_bar_inactive_pressed_state_layer_color = 0x7f0c0076
mc.meson.kasumi:string/mtrl_picker_range_header_selected = 0x7f1100a0
mc.meson.kasumi:color/m3_sys_color_dynamic_light_inverse_on_surface = 0x7f0501a5
mc.meson.kasumi:integer/m3_sys_motion_duration_medium4 = 0x7f09001b
mc.meson.kasumi:style/Widget.AppCompat.ActionButton.CloseMode = 0x7f1202fb
mc.meson.kasumi:style/Base.Widget.Material3.TabLayout.Secondary = 0x7f12011d
mc.meson.kasumi:string/mtrl_checkbox_button_path_group_name = 0x7f110082
mc.meson.kasumi:dimen/m3_comp_switch_track_width = 0x7f060198
mc.meson.kasumi:style/Widget.AppCompat.Button.Colored = 0x7f120304
mc.meson.kasumi:style/Base.V14.ThemeOverlay.Material3.BottomSheetDialog = 0x7f1200a7
mc.meson.kasumi:layout/m3_alert_dialog_title = 0x7f0b0045
mc.meson.kasumi:layout/abc_list_menu_item_radio = 0x7f0b0011
mc.meson.kasumi:macro/m3_comp_outlined_text_field_container_shape = 0x7f0c00b1
mc.meson.kasumi:macro/m3_comp_filled_button_label_text_type = 0x7f0c0045
mc.meson.kasumi:color/m3_sys_color_light_secondary = 0x7f0501e5
mc.meson.kasumi:dimen/m3_comp_time_picker_time_selector_hover_state_layer_opacity = 0x7f0601a6
mc.meson.kasumi:attr/floatingActionButtonSmallTertiaryStyle = 0x7f0301f2
mc.meson.kasumi:macro/m3_comp_date_picker_modal_range_selection_header_headline_type = 0x7f0c001a
mc.meson.kasumi:id/indicatorMovement = 0x7f080110
mc.meson.kasumi:string/movement_features = 0x7f11007b
mc.meson.kasumi:style/Animation.MaterialComponents.BottomSheetDialog = 0x7f12000a
mc.meson.kasumi:string/m3_sys_motion_easing_emphasized = 0x7f11005a
mc.meson.kasumi:string/auto_clicker_desc = 0x7f110021
mc.meson.kasumi:style/ShapeAppearance.M3.Comp.SearchBar.Container.Shape = 0x7f12016d
mc.meson.kasumi:macro/m3_comp_top_app_bar_small_on_scroll_container_color = 0x7f0c0173
mc.meson.kasumi:string/no_fall_desc = 0x7f1100c0
mc.meson.kasumi:style/Base.Widget.MaterialComponents.PopupMenu.ContextMenu = 0x7f120124
mc.meson.kasumi:style/Widget.AppCompat.ButtonBar = 0x7f120306
mc.meson.kasumi:style/MaterialAlertDialog.MaterialComponents.Title.Panel = 0x7f12013f
mc.meson.kasumi:style/Base.V7.Theme.AppCompat.Light.Dialog = 0x7f1200ca
mc.meson.kasumi:style/Base.Widget.AppCompat.CompoundButton.CheckBox = 0x7f1200e2
mc.meson.kasumi:string/material_motion_easing_standard = 0x7f110070
mc.meson.kasumi:style/TextAppearance.MaterialComponents.Headline5 = 0x7f12020e
mc.meson.kasumi:id/accessibility_custom_action_26 = 0x7f080024
mc.meson.kasumi:id/text2 = 0x7f08021c
mc.meson.kasumi:string/material_motion_easing_emphasized = 0x7f11006e
mc.meson.kasumi:string/mtrl_checkbox_state_description_checked = 0x7f110085
mc.meson.kasumi:string/material_motion_easing_accelerated = 0x7f11006c
mc.meson.kasumi:style/Theme.Material3.DayNight = 0x7f12023e
mc.meson.kasumi:string/material_hour_suffix = 0x7f110069
mc.meson.kasumi:id/edit_text_id = 0x7f0800d4
mc.meson.kasumi:style/Base.TextAppearance.AppCompat.Caption = 0x7f120027
mc.meson.kasumi:style/ThemeOverlay.Material3.FloatingActionButton.Primary = 0x7f1202b0
mc.meson.kasumi:string/mtrl_picker_invalid_format_use = 0x7f110099
mc.meson.kasumi:string/material_hour_selection = 0x7f110068
mc.meson.kasumi:id/neverCompleteToStart = 0x7f08017f
mc.meson.kasumi:style/Base.V14.Theme.Material3.Light = 0x7f12009a
mc.meson.kasumi:dimen/m3_comp_search_view_container_elevation = 0x7f060173
mc.meson.kasumi:dimen/m3_comp_time_picker_period_selector_focus_state_layer_opacity = 0x7f0601a1
mc.meson.kasumi:color/m3_sys_color_light_surface = 0x7f0501e7
mc.meson.kasumi:dimen/m3_sys_motion_easing_emphasized_decelerate_control_y1 = 0x7f0601fc
mc.meson.kasumi:style/Widget.Material3.CollapsingToolbar.Medium = 0x7f120389
mc.meson.kasumi:style/App.Card.Elevated = 0x7f12000d
mc.meson.kasumi:string/m3_sys_motion_easing_standard_accelerate = 0x7f110063
mc.meson.kasumi:string/latest_version = 0x7f110054
mc.meson.kasumi:styleable/MaterialDivider = 0x7f13005d
mc.meson.kasumi:string/kill_aura_desc = 0x7f110053
mc.meson.kasumi:style/TextAppearance.Material3.ActionBar.Title = 0x7f1201f0
mc.meson.kasumi:dimen/m3_back_progress_side_container_max_scale_x_distance_shrink = 0x7f0600b1
mc.meson.kasumi:string/path_password_eye_mask_visible = 0x7f1100c6
mc.meson.kasumi:style/Base.DialogWindowTitleBackground.AppCompat = 0x7f12001f
mc.meson.kasumi:style/Widget.Support.CoordinatorLayout = 0x7f120474
mc.meson.kasumi:string/m3_sys_motion_easing_standard_decelerate = 0x7f110064
mc.meson.kasumi:string/error_icon_content_description = 0x7f110040
mc.meson.kasumi:string/error_a11y_label = 0x7f11003f
mc.meson.kasumi:string/dark_mode = 0x7f11003b
mc.meson.kasumi:id/fill_vertical = 0x7f0800e7
mc.meson.kasumi:string/call_notification_screening_text = 0x7f110030
mc.meson.kasumi:id/SHIFT = 0x7f080007
mc.meson.kasumi:style/Widget.AppCompat.AutoCompleteTextView = 0x7f1202ff
mc.meson.kasumi:string/call_notification_ongoing_text = 0x7f11002f
mc.meson.kasumi:string/material_motion_easing_linear = 0x7f11006f
mc.meson.kasumi:string/bottomsheet_drag_handle_clicked = 0x7f110028
mc.meson.kasumi:macro/m3_comp_checkbox_unselected_outline_color = 0x7f0c000c
mc.meson.kasumi:dimen/m3_comp_slider_active_handle_width = 0x7f060183
mc.meson.kasumi:color/md_theme_dark_inverseSurface = 0x7f0502c0
mc.meson.kasumi:style/ThemeOverlay.Material3.FloatingActionButton.Tertiary = 0x7f1202b3
mc.meson.kasumi:string/auto_clicker = 0x7f110020
mc.meson.kasumi:style/ThemeOverlay.MaterialComponents.MaterialAlertDialog.Centered = 0x7f1202e2
mc.meson.kasumi:styleable/MaterialTextView = 0x7f130062
mc.meson.kasumi:style/ThemeOverlay.Material3.ExtendedFloatingActionButton.Primary = 0x7f1202ac
mc.meson.kasumi:style/Widget.Design.AppBarLayout = 0x7f120340
mc.meson.kasumi:macro/m3_comp_date_picker_modal_header_supporting_text_type = 0x7f0c0018
mc.meson.kasumi:string/appbar_scrolling_view_behavior = 0x7f11001f
mc.meson.kasumi:style/Widget.Material3.MaterialTimePicker = 0x7f1203c0
mc.meson.kasumi:style/Base.ThemeOverlay.MaterialComponents.Dialog.Alert = 0x7f120092
mc.meson.kasumi:layout/design_navigation_item_separator = 0x7f0b002a
mc.meson.kasumi:style/Base.TextAppearance.Material3.Search = 0x7f120050
mc.meson.kasumi:style/ShapeAppearanceOverlay.MaterialComponents.BottomSheet = 0x7f120198
mc.meson.kasumi:style/Base.TextAppearance.AppCompat.Subhead.Inverse = 0x7f12003b
mc.meson.kasumi:dimen/m3_comp_navigation_rail_icon_size = 0x7f06014b
mc.meson.kasumi:string/abc_action_mode_done = 0x7f110003
mc.meson.kasumi:style/Widget.Material3.BottomSheet.DragHandle = 0x7f12035b
mc.meson.kasumi:id/action_features_to_home = 0x7f080040
mc.meson.kasumi:macro/m3_comp_top_app_bar_large_headline_type = 0x7f0c016c
mc.meson.kasumi:drawable/$mtrl_checkbox_button_checked_unchecked__0 = 0x7f07000d
mc.meson.kasumi:macro/m3_comp_time_picker_time_selector_unselected_hover_state_layer_color = 0x7f0c0169
mc.meson.kasumi:dimen/m3_carousel_gone_size = 0x7f0600ef
mc.meson.kasumi:dimen/mtrl_tooltip_cornerSize = 0x7f060307
mc.meson.kasumi:style/MaterialAlertDialog.Material3.Title.Text = 0x7f120137
mc.meson.kasumi:style/Platform.ThemeOverlay.AppCompat = 0x7f120149
mc.meson.kasumi:id/mtrl_picker_text_input_range_end = 0x7f080168
mc.meson.kasumi:macro/m3_comp_time_picker_time_selector_separator_color = 0x7f0c0165
mc.meson.kasumi:style/Widget.MaterialComponents.CollapsingToolbar = 0x7f120422
mc.meson.kasumi:dimen/mtrl_badge_size = 0x7f06024d
mc.meson.kasumi:styleable/SplitPairFilter = 0x7f130090
mc.meson.kasumi:style/Theme.MaterialComponents.CompactMenu = 0x7f120257
mc.meson.kasumi:style/Base.Theme.MaterialComponents.Dialog = 0x7f120075
mc.meson.kasumi:style/TextAppearance.AppCompat.Display1 = 0x7f1201a5
mc.meson.kasumi:macro/m3_comp_time_picker_time_selector_container_shape = 0x7f0c015e
mc.meson.kasumi:drawable/abc_switch_track_mtrl_alpha = 0x7f07006b
mc.meson.kasumi:macro/m3_comp_dialog_container_color = 0x7f0c0022
mc.meson.kasumi:macro/m3_comp_top_app_bar_small_leading_icon_color = 0x7f0c0172
mc.meson.kasumi:style/Base.TextAppearance.AppCompat = 0x7f120023
mc.meson.kasumi:macro/m3_comp_snackbar_supporting_text_type = 0x7f0c0116
mc.meson.kasumi:macro/m3_comp_navigation_drawer_modal_container_color = 0x7f0c0092
mc.meson.kasumi:style/Theme.AppCompat.Light = 0x7f120226
mc.meson.kasumi:macro/m3_comp_time_picker_headline_color = 0x7f0c0150
mc.meson.kasumi:string/searchview_clear_text_content_description = 0x7f1100cc
mc.meson.kasumi:dimen/notification_large_icon_width = 0x7f060311
mc.meson.kasumi:style/Widget.Material3.FloatingActionButton.Small.Surface = 0x7f12039f
mc.meson.kasumi:style/Base.Widget.AppCompat.SeekBar = 0x7f120101
mc.meson.kasumi:drawable/abc_list_divider_material = 0x7f07004d
mc.meson.kasumi:dimen/mtrl_low_ripple_pressed_alpha = 0x7f0602c1
mc.meson.kasumi:style/Base.TextAppearance.AppCompat.Medium = 0x7f120032
mc.meson.kasumi:style/Widget.AppCompat.ProgressBar.Horizontal = 0x7f12032e
mc.meson.kasumi:macro/m3_comp_time_picker_container_shape = 0x7f0c014f
mc.meson.kasumi:style/Theme.Design.Light.NoActionBar = 0x7f120232
mc.meson.kasumi:macro/m3_comp_switch_unselected_hover_state_layer_color = 0x7f0c0137
mc.meson.kasumi:macro/m3_comp_time_picker_clock_dial_color = 0x7f0c014c
mc.meson.kasumi:style/TextAppearance.AppCompat.Menu = 0x7f1201b3
mc.meson.kasumi:macro/m3_comp_time_input_time_input_field_container_shape = 0x7f0c0147
mc.meson.kasumi:drawable/$m3_avd_show_password__2 = 0x7f07000c
mc.meson.kasumi:style/Base.V14.Theme.Material3.Dark.BottomSheetDialog = 0x7f120097
mc.meson.kasumi:macro/m3_comp_sheet_side_docked_modal_container_color = 0x7f0c0108
mc.meson.kasumi:macro/m3_comp_switch_unselected_hover_track_outline_color = 0x7f0c0139
mc.meson.kasumi:style/Widget.AppCompat.ActionMode = 0x7f1202fd
mc.meson.kasumi:macro/m3_comp_switch_unselected_hover_track_color = 0x7f0c0138
mc.meson.kasumi:string/material_minute_suffix = 0x7f11006b
mc.meson.kasumi:style/ThemeOverlay.Material3.Dialog.Alert.Framework = 0x7f1202a8
mc.meson.kasumi:macro/m3_comp_switch_unselected_hover_icon_color = 0x7f0c0136
mc.meson.kasumi:string/call_notification_incoming_text = 0x7f11002e
mc.meson.kasumi:macro/m3_comp_switch_unselected_focus_track_outline_color = 0x7f0c0133
mc.meson.kasumi:style/ThemeOverlay.Material3.Dialog.Alert = 0x7f1202a7
mc.meson.kasumi:macro/m3_comp_switch_unselected_focus_track_color = 0x7f0c0132
mc.meson.kasumi:style/Theme.Material3.DayNight.NoActionBar = 0x7f120244
mc.meson.kasumi:style/Base.Theme.MaterialComponents.Light.Dialog.Alert = 0x7f120080
mc.meson.kasumi:style/Widget.Material3.Snackbar = 0x7f1203e0
mc.meson.kasumi:macro/m3_comp_fab_tertiary_container_color = 0x7f0c003f
mc.meson.kasumi:id/material_clock_level = 0x7f080139
mc.meson.kasumi:macro/m3_comp_switch_unselected_focus_handle_color = 0x7f0c012f
mc.meson.kasumi:dimen/mtrl_extended_fab_elevation = 0x7f0602a9
mc.meson.kasumi:styleable/CoordinatorLayout_Layout = 0x7f130030
mc.meson.kasumi:macro/m3_comp_switch_selected_hover_state_layer_color = 0x7f0c0127
mc.meson.kasumi:styleable/MaterialCardView = 0x7f13005a
mc.meson.kasumi:macro/m3_comp_switch_selected_hover_icon_color = 0x7f0c0126
mc.meson.kasumi:dimen/m3_comp_fab_primary_container_height = 0x7f060116
mc.meson.kasumi:dimen/design_bottom_sheet_elevation = 0x7f06006b
mc.meson.kasumi:macro/m3_comp_switch_selected_handle_color = 0x7f0c0124
mc.meson.kasumi:style/Base.Widget.Material3.ActionBar.Solid = 0x7f12010a
mc.meson.kasumi:style/Widget.Material3.ExtendedFloatingActionButton.Primary = 0x7f120393
mc.meson.kasumi:string/mtrl_picker_end_date_description = 0x7f110096
mc.meson.kasumi:macro/m3_comp_switch_selected_focus_icon_color = 0x7f0c0121
mc.meson.kasumi:color/material_dynamic_neutral_variant30 = 0x7f050237
mc.meson.kasumi:style/Base.V14.Theme.MaterialComponents.Bridge = 0x7f12009f
mc.meson.kasumi:macro/m3_comp_switch_selected_focus_handle_color = 0x7f0c0120
mc.meson.kasumi:macro/m3_comp_switch_selected_hover_track_color = 0x7f0c0128
mc.meson.kasumi:attr/alpha = 0x7f030031
mc.meson.kasumi:color/m3_sys_color_dynamic_light_surface_dim = 0x7f0501c0
mc.meson.kasumi:string/mtrl_picker_a11y_next_month = 0x7f11008b
mc.meson.kasumi:macro/m3_comp_top_app_bar_large_headline_color = 0x7f0c016b
mc.meson.kasumi:color/material_dynamic_neutral80 = 0x7f05022f
mc.meson.kasumi:style/Platform.MaterialComponents = 0x7f120145
mc.meson.kasumi:style/Widget.MaterialComponents.MaterialCalendar.HeaderToggleButton = 0x7f12043c
mc.meson.kasumi:style/Base.Theme.AppCompat.DialogWhenLarge = 0x7f12005e
mc.meson.kasumi:style/Widget.AppCompat.Light.ActionBar.TabView = 0x7f120316
mc.meson.kasumi:macro/m3_comp_search_bar_trailing_icon_color = 0x7f0c00f0
mc.meson.kasumi:macro/m3_comp_switch_disabled_selected_icon_color = 0x7f0c011a
mc.meson.kasumi:macro/m3_comp_switch_disabled_selected_handle_color = 0x7f0c0119
mc.meson.kasumi:string/mtrl_picker_range_header_unselected = 0x7f1100a2
mc.meson.kasumi:id/loadingIndicator = 0x7f08012a
mc.meson.kasumi:macro/m3_comp_primary_navigation_tab_inactive_pressed_state_layer_color = 0x7f0c00ce
mc.meson.kasumi:macro/m3_comp_time_picker_time_selector_selected_hover_state_layer_color = 0x7f0c0162
mc.meson.kasumi:string/path_password_eye_mask_strike_through = 0x7f1100c5
mc.meson.kasumi:macro/m3_comp_slider_label_label_text_color = 0x7f0c0112
mc.meson.kasumi:drawable/material_ic_edit_black_24dp = 0x7f0700c4
mc.meson.kasumi:style/Theme.MaterialComponents.Dialog = 0x7f120268
mc.meson.kasumi:style/ThemeOverlay.Material3.Dark = 0x7f1202a2
mc.meson.kasumi:macro/m3_comp_sheet_side_docked_standard_container_color = 0x7f0c010a
mc.meson.kasumi:style/Widget.AppCompat.TextView.SpinnerItem = 0x7f12033b
mc.meson.kasumi:macro/m3_comp_slider_handle_color = 0x7f0c010f
mc.meson.kasumi:macro/m3_comp_secondary_navigation_tab_with_icon_active_icon_color = 0x7f0c0102
mc.meson.kasumi:macro/m3_comp_secondary_navigation_tab_pressed_state_layer_color = 0x7f0c0101
mc.meson.kasumi:style/Base.DialogWindowTitle.AppCompat = 0x7f12001e
mc.meson.kasumi:style/Widget.MaterialComponents.MaterialCalendar.HeaderSelection.Fullscreen = 0x7f12043a
mc.meson.kasumi:style/Theme.Material3.DynamicColors.Light = 0x7f12024a
mc.meson.kasumi:style/MaterialAlertDialog.MaterialComponents.Title.Panel.CenterStacked = 0x7f120140
mc.meson.kasumi:macro/m3_comp_secondary_navigation_tab_focus_state_layer_color = 0x7f0c00fd
mc.meson.kasumi:style/TextAppearance.AppCompat.Small.Inverse = 0x7f1201b7
mc.meson.kasumi:macro/m3_comp_secondary_navigation_tab_container_color = 0x7f0c00fc
mc.meson.kasumi:styleable/RadialViewGroup = 0x7f130081
mc.meson.kasumi:style/Widget.MaterialComponents.ExtendedFloatingActionButton.Icon = 0x7f120427
mc.meson.kasumi:style/Widget.MaterialComponents.MaterialCalendar.HeaderTitle = 0x7f12043b
mc.meson.kasumi:string/mtrl_picker_announce_current_range_selection = 0x7f11008d
mc.meson.kasumi:dimen/m3_comp_badge_large_size = 0x7f0600ff
mc.meson.kasumi:id/parent_matrix = 0x7f0801a1
mc.meson.kasumi:styleable/ChipGroup = 0x7f130021
mc.meson.kasumi:macro/m3_comp_search_view_header_trailing_icon_color = 0x7f0c00f9
mc.meson.kasumi:integer/m3_sys_motion_duration_medium1 = 0x7f090018
mc.meson.kasumi:style/ShapeAppearanceOverlay.Material3.FloatingActionButton = 0x7f120193
mc.meson.kasumi:macro/m3_comp_search_bar_hover_state_layer_color = 0x7f0c00e7
mc.meson.kasumi:id/enabled = 0x7f0800d7
mc.meson.kasumi:style/Widget.MaterialComponents.MaterialCalendar.HeaderLayout = 0x7f120437
mc.meson.kasumi:style/Widget.MaterialComponents.Button.OutlinedButton = 0x7f12040d
mc.meson.kasumi:color/m3_sys_color_light_surface_dim = 0x7f0501ee
mc.meson.kasumi:style/Widget.Material3.CompoundButton.CheckBox = 0x7f12038a
mc.meson.kasumi:style/Widget.MaterialComponents.Chip.Action = 0x7f120419
mc.meson.kasumi:macro/m3_comp_radio_button_selected_pressed_state_layer_color = 0x7f0c00de
mc.meson.kasumi:dimen/m3_comp_primary_navigation_tab_active_hover_state_layer_opacity = 0x7f06015b
mc.meson.kasumi:macro/m3_comp_radio_button_selected_hover_state_layer_color = 0x7f0c00db
mc.meson.kasumi:style/Theme.MaterialComponents.DayNight.Dialog.Alert = 0x7f12025e
mc.meson.kasumi:style/ThemeOverlay.MaterialComponents.MaterialAlertDialog.Picker.Date.Header.Text.Day = 0x7f1202e6
mc.meson.kasumi:styleable/Snackbar = 0x7f13008d
mc.meson.kasumi:macro/m3_comp_primary_navigation_tab_inactive_hover_state_layer_color = 0x7f0c00cd
mc.meson.kasumi:id/search_voice_btn = 0x7f0801d5
mc.meson.kasumi:macro/m3_comp_primary_navigation_tab_inactive_focus_state_layer_color = 0x7f0c00cc
mc.meson.kasumi:macro/m3_comp_plain_tooltip_supporting_text_type = 0x7f0c00c6
mc.meson.kasumi:style/TextAppearance.MaterialComponents.Caption = 0x7f120208
mc.meson.kasumi:string/checking_updates = 0x7f110035
mc.meson.kasumi:string/material_timepicker_pm = 0x7f110078
mc.meson.kasumi:macro/m3_comp_outlined_text_field_label_text_color = 0x7f0c00c2
mc.meson.kasumi:macro/m3_comp_radio_button_unselected_pressed_state_layer_color = 0x7f0c00e5
mc.meson.kasumi:macro/m3_comp_outlined_text_field_input_text_type = 0x7f0c00c1
mc.meson.kasumi:style/TextAppearance.AppCompat.Widget.Button.Inverse = 0x7f1201c9
mc.meson.kasumi:macro/m3_comp_outlined_text_field_hover_outline_color = 0x7f0c00be
mc.meson.kasumi:style/TextAppearance.MaterialComponents.Overline = 0x7f120210
mc.meson.kasumi:dimen/abc_star_medium = 0x7f06003c
mc.meson.kasumi:attr/cornerRadius = 0x7f030157
mc.meson.kasumi:macro/m3_comp_outlined_text_field_hover_input_text_color = 0x7f0c00bd
mc.meson.kasumi:style/TextAppearance.MaterialComponents.Headline4 = 0x7f12020d
mc.meson.kasumi:style/Widget.AppCompat.SeekBar.Discrete = 0x7f120335
mc.meson.kasumi:macro/m3_comp_outlined_text_field_focus_outline_color = 0x7f0c00bb
mc.meson.kasumi:macro/m3_comp_navigation_bar_inactive_focus_icon_color = 0x7f0c006c
mc.meson.kasumi:macro/m3_comp_outlined_text_field_focus_label_text_color = 0x7f0c00ba
mc.meson.kasumi:style/Widget.MaterialComponents.TimePicker.Display = 0x7f120468
mc.meson.kasumi:dimen/m3_searchbar_elevation = 0x7f0601da
mc.meson.kasumi:id/useLogo = 0x7f080256
mc.meson.kasumi:id/textStart = 0x7f080220
mc.meson.kasumi:macro/m3_comp_outlined_text_field_disabled_label_text_color = 0x7f0c00b3
mc.meson.kasumi:style/Base.Widget.AppCompat.Button.Borderless = 0x7f1200db
mc.meson.kasumi:macro/m3_comp_outlined_text_field_caret_color = 0x7f0c00b0
mc.meson.kasumi:id/deltaRelative = 0x7f0800b5
mc.meson.kasumi:macro/m3_comp_outlined_card_pressed_outline_color = 0x7f0c00af
mc.meson.kasumi:dimen/m3_comp_extended_fab_primary_pressed_container_elevation = 0x7f060113
mc.meson.kasumi:macro/m3_comp_outlined_card_outline_color = 0x7f0c00ae
mc.meson.kasumi:macro/m3_comp_outlined_card_container_color = 0x7f0c00a8
mc.meson.kasumi:id/transitionToEnd = 0x7f080236
mc.meson.kasumi:macro/m3_comp_navigation_rail_label_text_type = 0x7f0c009f
mc.meson.kasumi:dimen/tooltip_horizontal_padding = 0x7f06031d
mc.meson.kasumi:macro/m3_comp_navigation_rail_inactive_label_text_color = 0x7f0c009d
mc.meson.kasumi:macro/m3_comp_navigation_rail_container_color = 0x7f0c0099
mc.meson.kasumi:color/md_theme_dark_onTertiary = 0x7f0502ca
mc.meson.kasumi:macro/m3_comp_navigation_rail_active_hover_state_layer_color = 0x7f0c0094
mc.meson.kasumi:color/material_dynamic_neutral50 = 0x7f05022c
mc.meson.kasumi:macro/m3_comp_navigation_drawer_inactive_focus_label_text_color = 0x7f0c0087
mc.meson.kasumi:style/Base.TextAppearance.AppCompat.Medium.Inverse = 0x7f120033
mc.meson.kasumi:dimen/m3_navigation_item_active_indicator_label_padding = 0x7f0601bf
mc.meson.kasumi:macro/m3_comp_navigation_drawer_inactive_focus_icon_color = 0x7f0c0086
mc.meson.kasumi:id/visible_removing_fragment_view_tag = 0x7f080262
mc.meson.kasumi:id/with_icon = 0x7f080267
mc.meson.kasumi:id/cardNoSettings = 0x7f08026f
mc.meson.kasumi:integer/config_navAnimTime = 0x7f090005
mc.meson.kasumi:string/nav_settings = 0x7f1100be
mc.meson.kasumi:macro/m3_comp_navigation_drawer_headline_type = 0x7f0c0085
mc.meson.kasumi:layout/abc_activity_chooser_view_list_item = 0x7f0b0007
mc.meson.kasumi:id/submit_area = 0x7f080204
mc.meson.kasumi:macro/m3_comp_navigation_drawer_inactive_hover_icon_color = 0x7f0c0089
mc.meson.kasumi:string/mtrl_picker_text_input_day_abbr = 0x7f1100a8
mc.meson.kasumi:string/mtrl_switch_thumb_path_pressed = 0x7f1100b4
mc.meson.kasumi:macro/m3_comp_time_picker_clock_dial_selector_handle_container_color = 0x7f0c014d
mc.meson.kasumi:macro/m3_comp_navigation_drawer_active_hover_state_layer_color = 0x7f0c007d
mc.meson.kasumi:styleable/StateListDrawable = 0x7f130094
mc.meson.kasumi:macro/m3_comp_navigation_drawer_active_hover_icon_color = 0x7f0c007b
mc.meson.kasumi:style/Base.Widget.Material3.CompoundButton.RadioButton = 0x7f120111
mc.meson.kasumi:macro/m3_comp_navigation_drawer_active_focus_state_layer_color = 0x7f0c007a
mc.meson.kasumi:id/fitXY = 0x7f0800ed
mc.meson.kasumi:macro/m3_comp_navigation_drawer_active_focus_label_text_color = 0x7f0c0079
mc.meson.kasumi:style/Widget.MaterialComponents.TimePicker.ImageButton = 0x7f12046d
mc.meson.kasumi:id/snackbar_text = 0x7f0801e8
mc.meson.kasumi:style/ShapeAppearance.MaterialComponents = 0x7f120187
mc.meson.kasumi:styleable/ViewTransition = 0x7f1300a9
mc.meson.kasumi:style/ShapeAppearance.Material3.SmallComponent = 0x7f120185
mc.meson.kasumi:macro/m3_comp_navigation_bar_inactive_pressed_label_text_color = 0x7f0c0075
mc.meson.kasumi:id/peekHeight = 0x7f0801a5
mc.meson.kasumi:macro/m3_comp_navigation_bar_inactive_label_text_color = 0x7f0c0073
mc.meson.kasumi:style/ThemeOverlay.Material3.AutoCompleteTextView = 0x7f12028f
mc.meson.kasumi:macro/m3_comp_navigation_bar_inactive_hover_state_layer_color = 0x7f0c0071
mc.meson.kasumi:macro/m3_comp_navigation_bar_inactive_hover_icon_color = 0x7f0c006f
mc.meson.kasumi:macro/m3_comp_navigation_bar_inactive_focus_label_text_color = 0x7f0c006d
mc.meson.kasumi:macro/m3_comp_navigation_bar_container_color = 0x7f0c006b
mc.meson.kasumi:macro/m3_comp_navigation_bar_active_pressed_label_text_color = 0x7f0c0069
mc.meson.kasumi:style/Widget.MaterialComponents.AppBarLayout.Surface = 0x7f1203fd
mc.meson.kasumi:macro/m3_comp_navigation_bar_active_pressed_icon_color = 0x7f0c0068
mc.meson.kasumi:string/mtrl_checkbox_button_icon_path_name = 0x7f110080
mc.meson.kasumi:id/NO_DEBUG = 0x7f080006
mc.meson.kasumi:macro/m3_comp_navigation_bar_active_label_text_color = 0x7f0c0067
mc.meson.kasumi:macro/m3_comp_navigation_bar_active_indicator_color = 0x7f0c0066
mc.meson.kasumi:macro/m3_comp_switch_unselected_track_color = 0x7f0c0140
mc.meson.kasumi:macro/m3_comp_navigation_bar_active_hover_icon_color = 0x7f0c0062
mc.meson.kasumi:style/Widget.Material3.ExtendedFloatingActionButton.Icon.Primary = 0x7f12038f
mc.meson.kasumi:macro/m3_comp_navigation_bar_active_focus_icon_color = 0x7f0c005f
mc.meson.kasumi:macro/m3_comp_menu_container_color = 0x7f0c005d
mc.meson.kasumi:layout/notification_template_part_time = 0x7f0b007c
mc.meson.kasumi:styleable/SplitPlaceholderRule = 0x7f130092
mc.meson.kasumi:id/uniform = 0x7f080253
mc.meson.kasumi:style/ThemeOverlay.Material3 = 0x7f12028d
mc.meson.kasumi:macro/m3_comp_filter_chip_label_text_type = 0x7f0c0058
mc.meson.kasumi:style/App.Text.Body = 0x7f120013
mc.meson.kasumi:color/m3_sys_color_light_surface_container_low = 0x7f0501ec
mc.meson.kasumi:color/material_personalized_color_on_tertiary_container = 0x7f05028f
mc.meson.kasumi:macro/m3_comp_filled_tonal_icon_button_toggle_unselected_icon_color = 0x7f0c0056
mc.meson.kasumi:macro/m3_comp_filled_tonal_button_container_color = 0x7f0c0052
mc.meson.kasumi:style/TextAppearance.Material3.BodySmall = 0x7f1201f3
mc.meson.kasumi:macro/m3_comp_filled_text_field_error_trailing_icon_color = 0x7f0c004f
mc.meson.kasumi:macro/m3_comp_filled_text_field_error_supporting_text_color = 0x7f0c004e
mc.meson.kasumi:macro/m3_comp_badge_color = 0x7f0c0002
mc.meson.kasumi:drawable/mtrl_popupmenu_background_overlay = 0x7f0700e3
mc.meson.kasumi:id/spline = 0x7f0801ef
mc.meson.kasumi:macro/m3_comp_filled_text_field_container_shape = 0x7f0c004c
mc.meson.kasumi:macro/m3_comp_fab_tertiary_icon_color = 0x7f0c0040
mc.meson.kasumi:id/on = 0x7f080189
mc.meson.kasumi:dimen/m3_navigation_item_shape_inset_start = 0x7f0601c4
mc.meson.kasumi:style/Base.Widget.AppCompat.Light.PopupMenu.Overflow = 0x7f1200f1
mc.meson.kasumi:style/Base.TextAppearance.AppCompat.Display1 = 0x7f120028
mc.meson.kasumi:color/md_theme_light_inversePrimary = 0x7f0502dd
mc.meson.kasumi:string/material_slider_value = 0x7f110073
mc.meson.kasumi:string/mtrl_picker_text_input_year_abbr = 0x7f1100aa
mc.meson.kasumi:dimen/m3_comp_primary_navigation_tab_inactive_hover_state_layer_opacity = 0x7f06015f
mc.meson.kasumi:color/material_dynamic_neutral40 = 0x7f05022b
mc.meson.kasumi:color/mtrl_textinput_disabled_color = 0x7f050336
mc.meson.kasumi:style/Base.V14.ThemeOverlay.Material3.SideSheetDialog = 0x7f1200a8
mc.meson.kasumi:macro/m3_comp_fab_secondary_container_color = 0x7f0c003b
mc.meson.kasumi:string/abc_shareactionprovider_share_with = 0x7f110018
mc.meson.kasumi:dimen/m3_nav_badge_with_text_vertical_offset = 0x7f0601bd
mc.meson.kasumi:style/Widget.Material3.ExtendedFloatingActionButton.Secondary = 0x7f120394
mc.meson.kasumi:id/search_go_btn = 0x7f0801d1
mc.meson.kasumi:layout/ime_secondary_split_test_activity = 0x7f0b0039
mc.meson.kasumi:styleable/AppCompatTheme = 0x7f130015
mc.meson.kasumi:drawable/material_cursor_drawable = 0x7f0700c1
mc.meson.kasumi:id/south = 0x7f0801ec
mc.meson.kasumi:style/Widget.Material3.NavigationRailView.Badge = 0x7f1203cb
mc.meson.kasumi:string/mtrl_chip_close_icon_content_description = 0x7f110088
mc.meson.kasumi:macro/m3_comp_fab_primary_container_shape = 0x7f0c0037
mc.meson.kasumi:macro/m3_comp_progress_indicator_active_indicator_color = 0x7f0c00d4
mc.meson.kasumi:id/tvConfigStats = 0x7f080244
mc.meson.kasumi:macro/m3_comp_extended_fab_tertiary_icon_color = 0x7f0c0035
mc.meson.kasumi:style/Base.TextAppearance.AppCompat.Widget.Switch = 0x7f12004e
mc.meson.kasumi:style/ThemeOverlay.Design.TextInputEditText = 0x7f12028c
mc.meson.kasumi:macro/m3_comp_extended_fab_surface_container_color = 0x7f0c0032
mc.meson.kasumi:macro/m3_comp_extended_fab_secondary_icon_color = 0x7f0c0031
mc.meson.kasumi:id/src_in = 0x7f0801f6
mc.meson.kasumi:style/Animation.Material3.BottomSheetDialog = 0x7f120006
mc.meson.kasumi:drawable/mtrl_switch_thumb_checked_unchecked = 0x7f0700e7
mc.meson.kasumi:macro/m3_comp_extended_fab_primary_label_text_type = 0x7f0c002f
mc.meson.kasumi:styleable/KeyFramesVelocity = 0x7f130049
mc.meson.kasumi:string/material_minute_selection = 0x7f11006a
mc.meson.kasumi:color/material_dynamic_tertiary100 = 0x7f05025c
mc.meson.kasumi:style/TextAppearance.Design.Suffix = 0x7f1201de
mc.meson.kasumi:style/Widget.MaterialComponents.Button.UnelevatedButton = 0x7f120415
mc.meson.kasumi:macro/m3_comp_elevated_card_container_color = 0x7f0c002a
mc.meson.kasumi:style/Widget.MaterialComponents.CircularProgressIndicator.Medium = 0x7f120420
mc.meson.kasumi:color/purple_200 = 0x7f050344
mc.meson.kasumi:macro/m3_comp_elevated_button_container_color = 0x7f0c0029
mc.meson.kasumi:macro/m3_comp_divider_color = 0x7f0c0028
mc.meson.kasumi:macro/m3_comp_date_picker_modal_year_selection_year_unselected_label_text_color = 0x7f0c0021
mc.meson.kasumi:style/Theme.MaterialComponents.DayNight.DialogWhenLarge = 0x7f120265
mc.meson.kasumi:layout/material_textinput_timepicker = 0x7f0b0050
mc.meson.kasumi:macro/m3_comp_date_picker_modal_range_selection_month_subhead_color = 0x7f0c001b
mc.meson.kasumi:drawable/abc_ic_menu_share_mtrl_alpha = 0x7f070048
mc.meson.kasumi:style/Widget.Material3.MaterialCalendar.MonthTextView = 0x7f1203b9
mc.meson.kasumi:macro/m3_comp_date_picker_modal_header_headline_type = 0x7f0c0016
mc.meson.kasumi:macro/m3_comp_date_picker_modal_date_selected_container_color = 0x7f0c0010
mc.meson.kasumi:macro/m3_comp_date_picker_modal_container_shape = 0x7f0c000e
mc.meson.kasumi:dimen/mtrl_slider_halo_radius = 0x7f0602e5
mc.meson.kasumi:layout/material_time_input = 0x7f0b0052
mc.meson.kasumi:style/ThemeOverlay.Material3.Chip = 0x7f1202a0
mc.meson.kasumi:string/mtrl_picker_text_input_month_abbr = 0x7f1100a9
mc.meson.kasumi:id/mtrl_view_tag_bottom_padding = 0x7f08016b
mc.meson.kasumi:style/ShapeAppearanceOverlay.Material3.SearchView = 0x7f120196
mc.meson.kasumi:macro/m3_comp_checkbox_selected_disabled_icon_color = 0x7f0c0008
mc.meson.kasumi:macro/m3_comp_checkbox_selected_disabled_container_color = 0x7f0c0007
mc.meson.kasumi:style/Widget.Material3.MaterialCalendar.Year = 0x7f1203ba
mc.meson.kasumi:style/ShapeAppearance.M3.Sys.Shape.Corner.None = 0x7f120179
mc.meson.kasumi:style/Base.V21.Theme.AppCompat.Light = 0x7f1200b0
mc.meson.kasumi:id/action_bar = 0x7f080034
mc.meson.kasumi:style/Widget.Compat.NotificationActionText = 0x7f12033f
mc.meson.kasumi:macro/m3_comp_checkbox_selected_container_color = 0x7f0c0006
mc.meson.kasumi:macro/m3_comp_radio_button_selected_pressed_icon_color = 0x7f0c00dd
mc.meson.kasumi:macro/m3_comp_bottom_app_bar_container_color = 0x7f0c0005
mc.meson.kasumi:style/TextAppearance.AppCompat.Small = 0x7f1201b6
mc.meson.kasumi:layout/notification_template_part_chronometer = 0x7f0b007b
mc.meson.kasumi:color/md_theme_dark_secondary = 0x7f0502d1
mc.meson.kasumi:layout/notification_action_tombstone = 0x7f0b0078
mc.meson.kasumi:style/Theme.Material3.Dark.Dialog.Alert = 0x7f120239
mc.meson.kasumi:style/Theme.MaterialComponents.NoActionBar.Bridge = 0x7f120282
mc.meson.kasumi:layout/mtrl_search_view = 0x7f0b0076
mc.meson.kasumi:color/mtrl_tabs_legacy_text_color_selector = 0x7f050332
mc.meson.kasumi:layout/mtrl_picker_text_input_date_range = 0x7f0b0074
mc.meson.kasumi:layout/mtrl_picker_dialog = 0x7f0b006c
mc.meson.kasumi:id/icon_group = 0x7f080107
mc.meson.kasumi:layout/mtrl_picker_actions = 0x7f0b006b
mc.meson.kasumi:macro/m3_comp_radio_button_unselected_icon_color = 0x7f0c00e3
mc.meson.kasumi:id/sliding_pane_detail_container = 0x7f0801e5
mc.meson.kasumi:id/select_dialog_listview = 0x7f0801d7
mc.meson.kasumi:style/ThemeOverlay.Material3.Chip.Assist = 0x7f1202a1
mc.meson.kasumi:layout/mtrl_calendar_month_navigation = 0x7f0b0064
mc.meson.kasumi:style/TextAppearance.AppCompat.Widget.Button.Colored = 0x7f1201c8
mc.meson.kasumi:layout/mtrl_calendar_horizontal = 0x7f0b0061
mc.meson.kasumi:style/Widget.Material3.Button = 0x7f12035d
mc.meson.kasumi:style/Base.V14.ThemeOverlay.MaterialComponents.Dialog.Alert = 0x7f1200ab
mc.meson.kasumi:layout/mtrl_calendar_day_of_week = 0x7f0b005f
mc.meson.kasumi:id/aligned = 0x7f080053
mc.meson.kasumi:integer/mtrl_view_invisible = 0x7f090041
mc.meson.kasumi:macro/m3_comp_switch_unselected_icon_color = 0x7f0c013a
mc.meson.kasumi:dimen/m3_fab_border_width = 0x7f0601b5
mc.meson.kasumi:macro/m3_comp_dialog_container_shape = 0x7f0c0023
mc.meson.kasumi:dimen/m3_comp_scrim_container_opacity = 0x7f06016d
mc.meson.kasumi:layout/mtrl_calendar_day = 0x7f0b005e
mc.meson.kasumi:macro/m3_comp_navigation_bar_inactive_icon_color = 0x7f0c0072
mc.meson.kasumi:layout/mtrl_auto_complete_simple_item = 0x7f0b005d
mc.meson.kasumi:layout/notification_template_custom_big = 0x7f0b0079
mc.meson.kasumi:style/Base.V7.Widget.AppCompat.EditText = 0x7f1200cd
mc.meson.kasumi:layout/module_settings_panel = 0x7f0b0056
mc.meson.kasumi:string/mtrl_picker_invalid_range = 0x7f11009a
mc.meson.kasumi:macro/m3_comp_slider_disabled_handle_color = 0x7f0c010d
mc.meson.kasumi:layout/material_timepicker_textinput_display = 0x7f0b0055
mc.meson.kasumi:macro/m3_comp_switch_selected_hover_handle_color = 0x7f0c0125
mc.meson.kasumi:id/add = 0x7f080051
mc.meson.kasumi:color/material_grey_900 = 0x7f05026d
mc.meson.kasumi:id/fragment_container_view_tag = 0x7f0800f2
mc.meson.kasumi:styleable/ActionMenuView = 0x7f130003
mc.meson.kasumi:id/confirm_button = 0x7f0800a1
mc.meson.kasumi:dimen/m3_extended_fab_bottom_padding = 0x7f0601af
mc.meson.kasumi:id/continuousVelocity = 0x7f0800a7
mc.meson.kasumi:style/Base.TextAppearance.AppCompat.SearchResult.Subtitle = 0x7f120036
mc.meson.kasumi:layout/material_clock_period_toggle_land = 0x7f0b004c
mc.meson.kasumi:dimen/abc_list_item_padding_horizontal_material = 0x7f060033
mc.meson.kasumi:color/m3_sys_color_light_tertiary_container = 0x7f0501f1
mc.meson.kasumi:macro/m3_comp_extended_fab_surface_icon_color = 0x7f0c0033
mc.meson.kasumi:color/m3_dynamic_default_color_secondary_text = 0x7f050086
mc.meson.kasumi:color/md_theme_light_outlineVariant = 0x7f0502eb
mc.meson.kasumi:layout/material_clock_display = 0x7f0b0049
mc.meson.kasumi:style/Widget.Material3.TextInputEditText.OutlinedBox.Dense = 0x7f1203e9
mc.meson.kasumi:style/Widget.MaterialComponents.TextInputLayout.OutlinedBox.ExposedDropdownMenu = 0x7f120463
mc.meson.kasumi:layout/material_chip_input_combo = 0x7f0b0048
mc.meson.kasumi:style/Base.Widget.Material3.TabLayout.OnSurface = 0x7f12011c
mc.meson.kasumi:string/reach = 0x7f1100c8
mc.meson.kasumi:id/ivCurrentIndicator = 0x7f08011a
mc.meson.kasumi:style/Widget.Material3.Slider.Label = 0x7f1203dd
mc.meson.kasumi:layout/m3_alert_dialog_actions = 0x7f0b0044
mc.meson.kasumi:style/Base.Widget.AppCompat.ActivityChooserView = 0x7f1200d8
mc.meson.kasumi:style/Base.Widget.Material3.Chip = 0x7f12010e
mc.meson.kasumi:layout/item_setting_slider = 0x7f0b0041
mc.meson.kasumi:layout/item_setting_mode = 0x7f0b0040
mc.meson.kasumi:style/TextAppearance.Compat.Notification = 0x7f1201d0
mc.meson.kasumi:style/ShapeAppearanceOverlay.MaterialComponents.MaterialCalendar.Year = 0x7f12019e
mc.meson.kasumi:layout/item_module_list = 0x7f0b003f
mc.meson.kasumi:style/Theme.MaterialComponents.Light.Dialog.FixedSize.Bridge = 0x7f12027b
mc.meson.kasumi:id/design_menu_item_action_area = 0x7f0800b8
mc.meson.kasumi:layout/item_mode_option = 0x7f0b003d
mc.meson.kasumi:macro/m3_comp_time_picker_period_selector_label_text_type = 0x7f0c0153
mc.meson.kasumi:id/tvSettingDescription = 0x7f080250
mc.meson.kasumi:color/material_personalized_color_text_hint_foreground_inverse = 0x7f0502a7
mc.meson.kasumi:style/ThemeOverlay.Material3.TextInputEditText.OutlinedBox.Dense = 0x7f1202ca
mc.meson.kasumi:id/activity_chooser_view_content = 0x7f080050
mc.meson.kasumi:id/compactContent = 0x7f08009f
mc.meson.kasumi:layout/ime_base_split_test_activity = 0x7f0b0038
mc.meson.kasumi:style/Base.V14.Theme.Material3.Dark.SideSheetDialog = 0x7f120099
mc.meson.kasumi:style/Widget.MaterialComponents.Badge = 0x7f120402
mc.meson.kasumi:style/ThemeOverlay.MaterialComponents.MaterialCalendar.Fullscreen = 0x7f1202e9
mc.meson.kasumi:layout/floating_window_expanded_new = 0x7f0b0032
mc.meson.kasumi:layout/floating_window_compact_v2 = 0x7f0b0031
mc.meson.kasumi:style/Widget.Material3.TextInputLayout.OutlinedBox.Dense.ExposedDropdownMenu = 0x7f1203f0
mc.meson.kasumi:id/middle = 0x7f08014b
mc.meson.kasumi:layout/design_text_input_end_icon = 0x7f0b002e
mc.meson.kasumi:color/md_theme_light_onTertiaryContainer = 0x7f0502e9
mc.meson.kasumi:macro/m3_comp_outlined_autocomplete_text_field_caret_color = 0x7f0c00a1
mc.meson.kasumi:id/action_mode_bar_stub = 0x7f080049
mc.meson.kasumi:dimen/mtrl_high_ripple_hovered_alpha = 0x7f0602bc
mc.meson.kasumi:dimen/abc_disabled_alpha_material_dark = 0x7f060027
mc.meson.kasumi:style/TextAppearance.Material3.LabelLarge = 0x7f1201fa
mc.meson.kasumi:style/Theme.MaterialComponents.DayNight.Bridge = 0x7f12025a
mc.meson.kasumi:macro/m3_comp_search_bar_supporting_text_color = 0x7f0c00ee
mc.meson.kasumi:layout/design_layout_snackbar_include = 0x7f0b0024
mc.meson.kasumi:layout/mtrl_calendar_year = 0x7f0b0067
mc.meson.kasumi:color/m3_sys_color_dynamic_tertiary_fixed_dim = 0x7f0501cf
mc.meson.kasumi:style/Widget.Design.NavigationView = 0x7f120345
mc.meson.kasumi:style/Base.V22.Theme.AppCompat = 0x7f1200ba
mc.meson.kasumi:style/ThemeOverlay.MaterialComponents.MaterialAlertDialog.Picker.Date.Spinner = 0x7f1202e7
mc.meson.kasumi:layout/design_bottom_sheet_dialog = 0x7f0b0022
mc.meson.kasumi:dimen/m3_comp_sheet_side_docked_container_width = 0x7f06017e
mc.meson.kasumi:attr/textStartPadding = 0x7f0304ae
mc.meson.kasumi:macro/m3_comp_navigation_drawer_active_pressed_icon_color = 0x7f0c0081
mc.meson.kasumi:layout/custom_dialog = 0x7f0b0020
mc.meson.kasumi:layout/design_navigation_item_header = 0x7f0b0029
mc.meson.kasumi:color/md_theme_dark_surface = 0x7f0502d4
mc.meson.kasumi:layout/abc_screen_toolbar = 0x7f0b0017
mc.meson.kasumi:style/Base.V7.ThemeOverlay.AppCompat.Dialog = 0x7f1200cb
mc.meson.kasumi:id/legacy = 0x7f080123
mc.meson.kasumi:color/material_dynamic_secondary70 = 0x7f050255
mc.meson.kasumi:layout/abc_popup_menu_item_layout = 0x7f0b0013
mc.meson.kasumi:string/m3_exceed_max_badge_text_suffix = 0x7f110055
mc.meson.kasumi:styleable/MotionHelper = 0x7f13006b
mc.meson.kasumi:layout/abc_cascading_menu_item_layout = 0x7f0b000b
mc.meson.kasumi:style/Base.TextAppearance.AppCompat.Body2 = 0x7f120025
mc.meson.kasumi:layout/abc_action_bar_title_item = 0x7f0b0000
mc.meson.kasumi:id/action_features_to_config_management = 0x7f08003f
mc.meson.kasumi:dimen/m3_comp_filter_chip_flat_unselected_outline_width = 0x7f06012e
mc.meson.kasumi:string/mtrl_picker_navigate_to_year_description = 0x7f11009c
mc.meson.kasumi:style/Widget.MaterialComponents.CircularProgressIndicator.Small = 0x7f120421
mc.meson.kasumi:interpolator/m3_sys_motion_easing_linear = 0x7f0a000a
mc.meson.kasumi:macro/m3_comp_outlined_button_outline_color = 0x7f0c00a6
mc.meson.kasumi:style/TextAppearance.Design.Snackbar.Message = 0x7f1201dd
mc.meson.kasumi:interpolator/fast_out_slow_in = 0x7f0a0006
mc.meson.kasumi:integer/mtrl_view_visible = 0x7f090042
mc.meson.kasumi:string/mtrl_picker_date_header_selected = 0x7f110092
mc.meson.kasumi:integer/mtrl_view_gone = 0x7f090040
mc.meson.kasumi:drawable/abc_ic_go_search_api_material = 0x7f070042
mc.meson.kasumi:style/ThemeOverlay.MaterialComponents.AutoCompleteTextView.OutlinedBox = 0x7f1202d4
mc.meson.kasumi:integer/mtrl_tab_indicator_anim_duration_ms = 0x7f09003f
mc.meson.kasumi:integer/mtrl_switch_track_viewport_height = 0x7f09003d
mc.meson.kasumi:style/TextAppearance.AppCompat.Tooltip = 0x7f1201bc
mc.meson.kasumi:integer/mtrl_switch_thumb_viewport_center_coordinate = 0x7f09003b
mc.meson.kasumi:integer/mtrl_calendar_selection_text_lines = 0x7f090032
mc.meson.kasumi:layout/select_dialog_item_material = 0x7f0b007d
mc.meson.kasumi:integer/mtrl_switch_thumb_pressed_duration = 0x7f09003a
mc.meson.kasumi:id/recyclerViewSettings = 0x7f0801b3
mc.meson.kasumi:macro/m3_comp_date_picker_modal_date_unselected_label_text_color = 0x7f0c0014
mc.meson.kasumi:color/m3_timepicker_button_text_color = 0x7f05020e
mc.meson.kasumi:style/Theme.Material3.DayNight.SideSheetDialog = 0x7f120245
mc.meson.kasumi:string/material_timepicker_select_time = 0x7f110079
mc.meson.kasumi:integer/mtrl_switch_thumb_pre_morphing_duration = 0x7f090039
mc.meson.kasumi:integer/mtrl_card_anim_duration_ms = 0x7f090035
mc.meson.kasumi:id/search_button = 0x7f0801ce
mc.meson.kasumi:style/Base.Widget.AppCompat.Light.ActionBar = 0x7f1200ea
mc.meson.kasumi:macro/m3_comp_input_chip_container_shape = 0x7f0c005b
mc.meson.kasumi:integer/mtrl_btn_anim_delay_ms = 0x7f09002f
mc.meson.kasumi:integer/material_motion_duration_medium_1 = 0x7f090029
mc.meson.kasumi:dimen/mtrl_card_checked_icon_size = 0x7f06029c
mc.meson.kasumi:styleable/BaseProgressIndicator = 0x7f130017
mc.meson.kasumi:color/md_theme_light_onSurfaceVariant = 0x7f0502e7
mc.meson.kasumi:id/fitStart = 0x7f0800eb
mc.meson.kasumi:styleable/MotionEffect = 0x7f13006a
mc.meson.kasumi:string/mtrl_picker_text_input_date_range_end_hint = 0x7f1100a6
mc.meson.kasumi:style/Base.V26.Widget.AppCompat.Toolbar = 0x7f1200c4
mc.meson.kasumi:integer/m3_sys_shape_corner_medium_corner_family = 0x7f090025
mc.meson.kasumi:style/ShapeAppearance.M3.Comp.SearchView.FullScreen.Container.Shape = 0x7f12016e
mc.meson.kasumi:integer/m3_sys_shape_corner_large_corner_family = 0x7f090024
mc.meson.kasumi:styleable/AppCompatTextHelper = 0x7f130013
mc.meson.kasumi:drawable/mtrl_switch_thumb_unchecked = 0x7f0700eb
mc.meson.kasumi:style/Widget.Material3.Button.Icon = 0x7f120360
mc.meson.kasumi:string/connection_status = 0x7f11003a
mc.meson.kasumi:integer/m3_sys_shape_corner_full_corner_family = 0x7f090023
mc.meson.kasumi:macro/m3_comp_time_picker_time_selector_selected_focus_state_layer_color = 0x7f0c0161
mc.meson.kasumi:style/Base.TextAppearance.AppCompat.Title = 0x7f12003c
mc.meson.kasumi:string/mtrl_picker_range_header_title = 0x7f1100a1
mc.meson.kasumi:integer/m3_sys_shape_corner_extra_large_corner_family = 0x7f090021
mc.meson.kasumi:id/never = 0x7f08017d
mc.meson.kasumi:layout/abc_search_dropdown_item_icons_2line = 0x7f0b0018
mc.meson.kasumi:drawable/abc_cab_background_top_mtrl_alpha = 0x7f07003a
mc.meson.kasumi:macro/m3_comp_navigation_bar_active_hover_state_layer_color = 0x7f0c0064
mc.meson.kasumi:id/bottom = 0x7f080070
mc.meson.kasumi:color/m3_sys_color_dynamic_light_inverse_surface = 0x7f0501a7
mc.meson.kasumi:drawable/mtrl_checkbox_button_icon_unchecked_checked = 0x7f0700d4
mc.meson.kasumi:macro/m3_comp_time_picker_period_selector_selected_container_color = 0x7f0c0155
mc.meson.kasumi:string/abc_menu_ctrl_shortcut_label = 0x7f110009
mc.meson.kasumi:style/Widget.Material3.MaterialCalendar.HeaderDivider = 0x7f1203b0
mc.meson.kasumi:string/abc_action_menu_overflow_description = 0x7f110002
mc.meson.kasumi:integer/m3_sys_motion_path = 0x7f090020
mc.meson.kasumi:style/ThemeOverlay.Material3.MaterialTimePicker.Display.TextInputEditText = 0x7f1202be
mc.meson.kasumi:id/logoContainer = 0x7f08012c
mc.meson.kasumi:integer/m3_sys_motion_duration_short3 = 0x7f09001e
mc.meson.kasumi:id/title_template = 0x7f08022f
mc.meson.kasumi:dimen/abc_button_inset_horizontal_material = 0x7f060012
mc.meson.kasumi:dimen/material_clock_hand_center_dot_radius = 0x7f060227
mc.meson.kasumi:attr/textAppearanceSearchResultSubtitle = 0x7f030490
mc.meson.kasumi:layout/mtrl_alert_dialog_title = 0x7f0b0059
mc.meson.kasumi:dimen/material_clock_period_toggle_vertical_gap = 0x7f06022d
mc.meson.kasumi:attr/minHideDelay = 0x7f030343
mc.meson.kasumi:id/statusIndicator = 0x7f0801ff
mc.meson.kasumi:id/bestChoice = 0x7f08006e
mc.meson.kasumi:style/Theme.Design.NoActionBar = 0x7f120233
mc.meson.kasumi:styleable/ViewStubCompat = 0x7f1300a8
mc.meson.kasumi:macro/m3_comp_search_bar_leading_icon_color = 0x7f0c00eb
mc.meson.kasumi:macro/m3_comp_navigation_drawer_inactive_focus_state_layer_color = 0x7f0c0088
mc.meson.kasumi:id/topBar = 0x7f080233
mc.meson.kasumi:style/ThemeOverlay.Material3.TextInputEditText.FilledBox.Dense = 0x7f1202c8
mc.meson.kasumi:id/fullscreen_header = 0x7f0800f4
mc.meson.kasumi:string/mtrl_picker_a11y_prev_month = 0x7f11008c
mc.meson.kasumi:style/Widget.MaterialComponents.TimePicker.Display.TextInputLayout = 0x7f12046c
mc.meson.kasumi:integer/m3_sys_motion_duration_medium3 = 0x7f09001a
mc.meson.kasumi:macro/m3_comp_navigation_rail_inactive_icon_color = 0x7f0c009c
mc.meson.kasumi:style/ShapeAppearanceOverlay.Material3.Chip = 0x7f12018e
mc.meson.kasumi:integer/m3_sys_motion_duration_medium2 = 0x7f090019
mc.meson.kasumi:integer/m3_sys_motion_duration_extra_long3 = 0x7f090012
mc.meson.kasumi:dimen/mtrl_badge_toolbar_action_menu_item_vertical_offset = 0x7f060251
mc.meson.kasumi:integer/m3_sys_motion_duration_extra_long2 = 0x7f090011
mc.meson.kasumi:id/stop = 0x7f080201
mc.meson.kasumi:integer/m3_chip_anim_duration = 0x7f09000f
mc.meson.kasumi:id/material_hour_tv = 0x7f08013e
mc.meson.kasumi:integer/m3_card_anim_delay_ms = 0x7f09000d
mc.meson.kasumi:macro/m3_comp_primary_navigation_tab_with_label_text_label_text_type = 0x7f0c00d3
mc.meson.kasumi:id/x_right = 0x7f08026d
mc.meson.kasumi:style/Widget.Material3.Chip.Input.Icon.Elevated = 0x7f12037b
mc.meson.kasumi:id/x_left = 0x7f08026c
mc.meson.kasumi:id/material_clock_period_pm_button = 0x7f08013b
mc.meson.kasumi:style/Widget.Material3.MaterialCalendar.HeaderToggleButton = 0x7f1203b6
mc.meson.kasumi:id/wrap_content_constrained = 0x7f08026b
mc.meson.kasumi:id/withinBounds = 0x7f080268
mc.meson.kasumi:style/Widget.AppCompat.ListView.Menu = 0x7f120329
mc.meson.kasumi:id/withText = 0x7f080266
mc.meson.kasumi:macro/m3_sys_color_light_surface_tint = 0x7f0c0176
mc.meson.kasumi:id/wrap = 0x7f080269
mc.meson.kasumi:id/visible = 0x7f080261
mc.meson.kasumi:id/tag_transition_group = 0x7f080217
mc.meson.kasumi:style/Widget.AppCompat.Light.DropDownItem.Spinner = 0x7f12031e
mc.meson.kasumi:id/showTitle = 0x7f0801e0
mc.meson.kasumi:id/view_tree_saved_state_registry_owner = 0x7f08025f
mc.meson.kasumi:macro/m3_comp_sheet_bottom_docked_container_color = 0x7f0c0104
mc.meson.kasumi:id/centerInside = 0x7f08008e
mc.meson.kasumi:style/Base.AlertDialog.AppCompat.Light = 0x7f120019
mc.meson.kasumi:id/view_offset_helper = 0x7f08025b
mc.meson.kasumi:dimen/mtrl_calendar_header_content_padding = 0x7f06027e
mc.meson.kasumi:macro/m3_comp_filled_text_field_supporting_text_type = 0x7f0c0051
mc.meson.kasumi:style/Base.Theme.AppCompat.Dialog = 0x7f12005a
mc.meson.kasumi:styleable/AppCompatTextView = 0x7f130014
mc.meson.kasumi:style/ThemeOverlay.MaterialComponents.TextInputEditText.FilledBox = 0x7f1202eb
mc.meson.kasumi:macro/m3_comp_navigation_rail_active_focus_state_layer_color = 0x7f0c0093
mc.meson.kasumi:id/navigation_bar_item_icon_view = 0x7f080178
mc.meson.kasumi:style/ShapeAppearance.M3.Sys.Shape.Corner.Small = 0x7f12017a
mc.meson.kasumi:style/Base.V14.ThemeOverlay.MaterialComponents.BottomSheetDialog = 0x7f1200a9
mc.meson.kasumi:id/vertical = 0x7f080258
mc.meson.kasumi:style/Widget.Material3.PopupMenu.Overflow = 0x7f1203d0
mc.meson.kasumi:integer/m3_sys_motion_duration_extra_long4 = 0x7f090013
mc.meson.kasumi:id/up = 0x7f080255
mc.meson.kasumi:style/Widget.Material3.Chip.Input = 0x7f120378
mc.meson.kasumi:id/unlabeled = 0x7f080254
mc.meson.kasumi:id/unchecked = 0x7f080252
mc.meson.kasumi:styleable/MenuView = 0x7f130067
mc.meson.kasumi:style/Theme.AppCompat = 0x7f120218
mc.meson.kasumi:id/tvOption = 0x7f08024f
mc.meson.kasumi:id/tvModuleTitleInTitle = 0x7f08024d
mc.meson.kasumi:style/TextAppearance.Design.CollapsingToolbar.Expanded = 0x7f1201d5
mc.meson.kasumi:id/tvConfigDescription = 0x7f080242
mc.meson.kasumi:string/fly_desc = 0x7f11004d
mc.meson.kasumi:dimen/mtrl_calendar_selection_text_baseline_to_bottom = 0x7f060290
mc.meson.kasumi:attr/progressBarStyle = 0x7f0303c2
mc.meson.kasumi:id/month_navigation_next = 0x7f080151
mc.meson.kasumi:layout/activity_main_with_navigation = 0x7f0b001d
mc.meson.kasumi:style/TextAppearance.MaterialComponents.Headline2 = 0x7f12020b
mc.meson.kasumi:id/transition_scene_layoutid_cache = 0x7f08023e
mc.meson.kasumi:id/transition_pause_alpha = 0x7f08023c
mc.meson.kasumi:id/search_mag_icon = 0x7f0801d2
mc.meson.kasumi:macro/m3_comp_switch_unselected_pressed_state_layer_color = 0x7f0c013d
mc.meson.kasumi:id/transition_current_scene = 0x7f080239
mc.meson.kasumi:string/material_clock_toggle_content_description = 0x7f110066
mc.meson.kasumi:style/Theme.AppCompat.DayNight.Dialog.MinWidth = 0x7f12021e
mc.meson.kasumi:macro/m3_comp_fab_secondary_icon_color = 0x7f0c003c
mc.meson.kasumi:integer/abc_config_activityDefaultDur = 0x7f090000
mc.meson.kasumi:id/transition_clip = 0x7f080238
mc.meson.kasumi:layout/m3_alert_dialog = 0x7f0b0043
mc.meson.kasumi:id/toolbar = 0x7f080231
mc.meson.kasumi:id/vertical_only = 0x7f080259
mc.meson.kasumi:id/time = 0x7f08022b
mc.meson.kasumi:id/text_input_end_icon = 0x7f080222
mc.meson.kasumi:id/textSpacerNoTitle = 0x7f08021f
mc.meson.kasumi:string/app_name = 0x7f11001e
mc.meson.kasumi:id/textEnd = 0x7f08021d
mc.meson.kasumi:attr/textPanY = 0x7f0304ad
mc.meson.kasumi:attr/closeIconEnabled = 0x7f0300e6
mc.meson.kasumi:dimen/design_bottom_navigation_active_text_size = 0x7f060061
mc.meson.kasumi:styleable/MaterialAutoCompleteTextView = 0x7f130055
mc.meson.kasumi:id/tag_screen_reader_focusable = 0x7f080215
mc.meson.kasumi:id/forever = 0x7f0800f1
mc.meson.kasumi:style/Widget.MaterialComponents.MaterialCalendar.DayTextView = 0x7f120432
mc.meson.kasumi:id/pooling_container_listener_holder_tag = 0x7f0801a8
mc.meson.kasumi:id/settingsPanel = 0x7f0801da
mc.meson.kasumi:id/tag_accessibility_clickable_spans = 0x7f08020f
mc.meson.kasumi:styleable/LinearLayoutCompat_Layout = 0x7f13004f
mc.meson.kasumi:id/tag_accessibility_actions = 0x7f08020e
mc.meson.kasumi:menu/bottom_navigation_menu = 0x7f0d0000
mc.meson.kasumi:id/tabCategories = 0x7f08020c
mc.meson.kasumi:id/staticLayout = 0x7f0801fd
mc.meson.kasumi:id/hide_ime_id = 0x7f0800ff
mc.meson.kasumi:id/closest = 0x7f08009c
mc.meson.kasumi:dimen/abc_text_size_headline_material = 0x7f060047
mc.meson.kasumi:style/Widget.Material3.SearchView.Toolbar = 0x7f1203d7
mc.meson.kasumi:interpolator/mtrl_linear = 0x7f0a0010
mc.meson.kasumi:id/startHorizontal = 0x7f0801fa
mc.meson.kasumi:layout/mtrl_picker_header_title_text = 0x7f0b0071
mc.meson.kasumi:dimen/m3_carousel_extra_small_item_size = 0x7f0600ee
mc.meson.kasumi:color/material_personalized_color_outline_variant = 0x7f050291
mc.meson.kasumi:color/material_slider_inactive_track_color = 0x7f0502b4
mc.meson.kasumi:style/Widget.MaterialComponents.Toolbar.Primary = 0x7f120470
mc.meson.kasumi:attr/fontFamily = 0x7f03020a
mc.meson.kasumi:id/src_over = 0x7f0801f7
mc.meson.kasumi:id/src_atop = 0x7f0801f5
mc.meson.kasumi:layout/fragment_settings = 0x7f0b0037
mc.meson.kasumi:id/square = 0x7f0801f4
mc.meson.kasumi:style/Widget.Material3.Button.TextButton.Snackbar = 0x7f12036c
mc.meson.kasumi:style/Widget.MaterialComponents.MaterialCalendar.HeaderCancelButton = 0x7f120434
mc.meson.kasumi:id/open_search_view_search_prefix = 0x7f080195
mc.meson.kasumi:dimen/m3_comp_navigation_drawer_hover_state_layer_opacity = 0x7f060140
mc.meson.kasumi:drawable/abc_edit_text_material = 0x7f07003d
mc.meson.kasumi:style/Base.Widget.AppCompat.Toolbar.Button.Navigation = 0x7f120108
mc.meson.kasumi:id/seekBar = 0x7f0801d6
mc.meson.kasumi:styleable/Layout = 0x7f13004d
mc.meson.kasumi:id/spread = 0x7f0801f1
mc.meson.kasumi:id/password_toggle = 0x7f0801a2
mc.meson.kasumi:dimen/material_input_text_to_prefix_suffix_padding = 0x7f060240
mc.meson.kasumi:style/Theme.AppCompat.Light.Dialog.Alert = 0x7f120229
mc.meson.kasumi:id/coordinator = 0x7f0800a8
mc.meson.kasumi:macro/m3_comp_outlined_text_field_supporting_text_color = 0x7f0c00c4
mc.meson.kasumi:id/spacer = 0x7f0801ed
mc.meson.kasumi:id/sliding_pane_layout = 0x7f0801e6
mc.meson.kasumi:id/nav_module_settings = 0x7f080174
mc.meson.kasumi:string/searchview_navigation_content_description = 0x7f1100cd
mc.meson.kasumi:string/mtrl_picker_text_input_date_range_start_hint = 0x7f1100a7
mc.meson.kasumi:dimen/m3_comp_outlined_autocomplete_menu_container_elevation = 0x7f06014d
mc.meson.kasumi:style/Widget.Material3.MaterialCalendar.DayOfWeekLabel = 0x7f1203ac
mc.meson.kasumi:anim/mtrl_bottom_sheet_slide_out = 0x7f01002f
mc.meson.kasumi:style/Widget.Material3.FloatingActionButton.Small.Secondary = 0x7f12039e
mc.meson.kasumi:id/slide = 0x7f0801e4
mc.meson.kasumi:id/sin = 0x7f0801e1
mc.meson.kasumi:id/rectangles = 0x7f0801b0
mc.meson.kasumi:id/customPanel = 0x7f0800ae
mc.meson.kasumi:string/mtrl_checkbox_button_icon_path_group_name = 0x7f11007e
mc.meson.kasumi:id/compress = 0x7f0800a0
mc.meson.kasumi:id/showCustom = 0x7f0801de
mc.meson.kasumi:style/Widget.MaterialComponents.TextInputLayout.FilledBox.Dense = 0x7f12045d
mc.meson.kasumi:dimen/material_emphasis_disabled_background = 0x7f060234
mc.meson.kasumi:id/sharedValueSet = 0x7f0801db
mc.meson.kasumi:styleable/Variant = 0x7f1300a4
mc.meson.kasumi:color/mtrl_navigation_item_background_color = 0x7f050322
mc.meson.kasumi:id/spring = 0x7f0801f3
mc.meson.kasumi:styleable/MaterialSwitch = 0x7f130060
mc.meson.kasumi:id/selected = 0x7f0801d8
mc.meson.kasumi:id/pin = 0x7f0801a7
mc.meson.kasumi:id/motion_base = 0x7f080154
mc.meson.kasumi:style/TextAppearance.M3.Sys.Typescale.LabelSmall = 0x7f1201eb
mc.meson.kasumi:dimen/mtrl_bottomappbar_height = 0x7f060258
mc.meson.kasumi:id/search_plate = 0x7f0801d3
mc.meson.kasumi:style/Theme.MaterialComponents.Dialog.MinWidth = 0x7f12026e
mc.meson.kasumi:id/search_edit_frame = 0x7f0801d0
mc.meson.kasumi:dimen/m3_carousel_small_item_size_min = 0x7f0600f2
mc.meson.kasumi:attr/transitionDisable = 0x7f0304fb
mc.meson.kasumi:id/scroll = 0x7f0801c7
mc.meson.kasumi:styleable/StateListDrawableItem = 0x7f130095
mc.meson.kasumi:id/startToEnd = 0x7f0801fb
mc.meson.kasumi:style/TextAppearance.AppCompat.Widget.ActionMode.Title = 0x7f1201c4
mc.meson.kasumi:style/Base.Widget.MaterialComponents.PopupMenu.Overflow = 0x7f120126
mc.meson.kasumi:id/screen = 0x7f0801c6
mc.meson.kasumi:dimen/m3_navigation_rail_item_active_indicator_height = 0x7f0601cc
mc.meson.kasumi:color/m3_sys_color_dynamic_light_surface_container_high = 0x7f0501bc
mc.meson.kasumi:dimen/mtrl_calendar_action_height = 0x7f060272
mc.meson.kasumi:style/Widget.MaterialComponents.TabLayout.Colored = 0x7f120456
mc.meson.kasumi:string/start_floating_window = 0x7f1100d6
mc.meson.kasumi:id/scale = 0x7f0801c5
mc.meson.kasumi:string/character_counter_content_description = 0x7f110031
mc.meson.kasumi:style/TextAppearance.AppCompat.Widget.Button = 0x7f1201c6
mc.meson.kasumi:style/RtlOverlay.DialogWindowTitle.AppCompat = 0x7f120151
mc.meson.kasumi:id/sawtooth = 0x7f0801c4
mc.meson.kasumi:string/mtrl_exceed_max_badge_number_content_description = 0x7f110089
mc.meson.kasumi:style/Base.Widget.Material3.FloatingActionButton.Small = 0x7f120117
mc.meson.kasumi:id/save_overlay_view = 0x7f0801c3
mc.meson.kasumi:dimen/abc_dialog_list_padding_top_no_title = 0x7f060021
mc.meson.kasumi:color/m3_sys_color_dynamic_light_surface_container_lowest = 0x7f0501bf
mc.meson.kasumi:color/teal_200 = 0x7f050353
mc.meson.kasumi:style/Widget.MaterialComponents.MaterialCalendar.HeaderDivider = 0x7f120436
mc.meson.kasumi:id/rvCategoryModules = 0x7f0801bf
mc.meson.kasumi:macro/m3_comp_outlined_text_field_outline_color = 0x7f0c00c3
mc.meson.kasumi:id/tag_on_receive_content_mime_types = 0x7f080214
mc.meson.kasumi:macro/m3_comp_outlined_text_field_disabled_supporting_text_color = 0x7f0c00b5
mc.meson.kasumi:id/rtl = 0x7f0801be
mc.meson.kasumi:id/right_side = 0x7f0801bb
mc.meson.kasumi:style/Base.Theme.Material3.Light.Dialog.FixedSize = 0x7f12006f
mc.meson.kasumi:id/progress_horizontal = 0x7f0801ad
mc.meson.kasumi:color/mtrl_popupmenu_overlay_color = 0x7f050329
mc.meson.kasumi:color/m3_ref_palette_neutral40 = 0x7f050110
mc.meson.kasumi:id/btnCollapse = 0x7f080079
mc.meson.kasumi:id/view_tree_view_model_store_owner = 0x7f080260
mc.meson.kasumi:id/material_timepicker_mode_button = 0x7f080145
mc.meson.kasumi:color/m3_sys_color_dark_on_primary_container = 0x7f050168
mc.meson.kasumi:color/m3_dark_highlighted_text = 0x7f05007b
mc.meson.kasumi:style/Widget.Material3.Slider = 0x7f1203dc
mc.meson.kasumi:id/SHOW_PATH = 0x7f080009
mc.meson.kasumi:style/ThemeOverlay.AppCompat.Dark.ActionBar = 0x7f120286
mc.meson.kasumi:integer/material_motion_duration_short_2 = 0x7f09002c
mc.meson.kasumi:id/pressed = 0x7f0801ab
mc.meson.kasumi:attr/backgroundTintMode = 0x7f030056
mc.meson.kasumi:drawable/abc_ic_clear_material = 0x7f070040
mc.meson.kasumi:string/mtrl_checkbox_button_path_name = 0x7f110083
mc.meson.kasumi:style/Theme.MaterialComponents.Dialog.Bridge = 0x7f12026b
mc.meson.kasumi:id/parentRelative = 0x7f0801a0
mc.meson.kasumi:macro/m3_comp_top_app_bar_small_headline_type = 0x7f0c0171
mc.meson.kasumi:string/feature_enabled = 0x7f110045
mc.meson.kasumi:id/parent = 0x7f08019e
mc.meson.kasumi:id/multiply = 0x7f08016c
mc.meson.kasumi:style/ThemeOverlay.Material3.Button.IconButton.Filled = 0x7f12029b
mc.meson.kasumi:id/dragDown = 0x7f0800c7
mc.meson.kasumi:style/Base.ThemeOverlay.AppCompat.Light = 0x7f12008b
mc.meson.kasumi:id/overshoot = 0x7f08019b
mc.meson.kasumi:style/Base.Widget.AppCompat.ActionMode = 0x7f1200d7
mc.meson.kasumi:style/RtlUnderlay.Widget.AppCompat.ActionButton.Overflow = 0x7f120161
mc.meson.kasumi:layout/abc_screen_content_include = 0x7f0b0014
mc.meson.kasumi:id/open_search_view_scrim = 0x7f080194
mc.meson.kasumi:id/notification_main_column_container = 0x7f080187
mc.meson.kasumi:style/Base.V14.Theme.MaterialComponents.Light.Dialog = 0x7f1200a5
mc.meson.kasumi:id/indicatorPlayer = 0x7f080111
mc.meson.kasumi:style/Widget.AppCompat.ActionButton = 0x7f1202fa
mc.meson.kasumi:attr/fabCustomSize = 0x7f0301dd
mc.meson.kasumi:dimen/material_clock_hand_stroke_width = 0x7f060229
mc.meson.kasumi:style/Widget.Material3.FloatingActionButton.Tertiary = 0x7f1203a2
mc.meson.kasumi:id/open_search_view_root = 0x7f080193
mc.meson.kasumi:color/m3_sys_color_light_on_tertiary = 0x7f0501df
mc.meson.kasumi:color/m3_sys_color_dynamic_dark_background = 0x7f050180
mc.meson.kasumi:attr/listPreferredItemHeightLarge = 0x7f0302df
mc.meson.kasumi:id/normal = 0x7f080183
mc.meson.kasumi:styleable/MaterialShape = 0x7f13005f
mc.meson.kasumi:style/Base.TextAppearance.AppCompat.Widget.Button.Colored = 0x7f120048
mc.meson.kasumi:id/centerCrop = 0x7f08008d
mc.meson.kasumi:id/navigation_bar_item_labels_group = 0x7f080179
mc.meson.kasumi:string/bottomsheet_action_expand = 0x7f110026
mc.meson.kasumi:id/navigation_bar_item_icon_container = 0x7f080177
mc.meson.kasumi:id/nav_settings = 0x7f080175
mc.meson.kasumi:style/Widget.Design.TextInputEditText = 0x7f120349
mc.meson.kasumi:id/nav_host_fragment_container = 0x7f080173
mc.meson.kasumi:style/Base.V21.Theme.MaterialComponents = 0x7f1200b2
mc.meson.kasumi:style/Base.V7.Theme.AppCompat.Dialog = 0x7f1200c8
mc.meson.kasumi:style/Base.Widget.AppCompat.EditText = 0x7f1200e8
mc.meson.kasumi:style/ThemeOverlay.Material3.Search = 0x7f1202c2
mc.meson.kasumi:dimen/notification_content_margin_start = 0x7f06030f
mc.meson.kasumi:style/Widget.AppCompat.Light.Spinner.DropDown.ActionBar = 0x7f120324
mc.meson.kasumi:id/nav_controller_view_tag = 0x7f08016e
mc.meson.kasumi:style/Theme.Material3.Light.Dialog.MinWidth = 0x7f120250
mc.meson.kasumi:style/Theme.Material3.DynamicColors.DayNight = 0x7f120248
mc.meson.kasumi:id/mtrl_picker_text_input_range_start = 0x7f080169
mc.meson.kasumi:style/ThemeOverlay.Material3.Button.TextButton = 0x7f12029d
mc.meson.kasumi:id/dragUp = 0x7f0800cc
mc.meson.kasumi:id/mtrl_picker_header_toggle = 0x7f080166
mc.meson.kasumi:macro/m3_comp_outlined_button_pressed_outline_color = 0x7f0c00a7
mc.meson.kasumi:style/Theme.Material3.Dark.SideSheetDialog = 0x7f12023d
mc.meson.kasumi:style/Base.TextAppearance.AppCompat.Widget.TextView.SpinnerItem = 0x7f12004f
mc.meson.kasumi:macro/m3_comp_search_view_header_input_text_color = 0x7f0c00f4
mc.meson.kasumi:id/mtrl_picker_header = 0x7f080163
mc.meson.kasumi:interpolator/m3_sys_motion_easing_standard = 0x7f0a000b
mc.meson.kasumi:style/Widget.Material3.MaterialTimePicker.Clock = 0x7f1203c2
mc.meson.kasumi:id/mtrl_motion_snapshot_view = 0x7f080161
mc.meson.kasumi:string/mtrl_picker_out_of_range = 0x7f11009d
mc.meson.kasumi:macro/m3_comp_switch_selected_pressed_track_color = 0x7f0c012d
mc.meson.kasumi:macro/m3_comp_navigation_drawer_inactive_hover_state_layer_color = 0x7f0c008b
mc.meson.kasumi:id/mtrl_internal_children_alpha_tag = 0x7f080160
mc.meson.kasumi:dimen/m3_comp_menu_container_elevation = 0x7f060135
mc.meson.kasumi:dimen/m3_comp_outlined_text_field_disabled_label_text_opacity = 0x7f060156
mc.meson.kasumi:anim/fragment_fade_exit = 0x7f010020
mc.meson.kasumi:id/mtrl_child_content_container = 0x7f08015f
mc.meson.kasumi:id/mtrl_card_checked_layer_id = 0x7f08015e
mc.meson.kasumi:style/Widget.Material3.BottomNavigationView = 0x7f120358
mc.meson.kasumi:id/mtrl_calendar_year_selector_frame = 0x7f08015d
mc.meson.kasumi:style/MaterialAlertDialog.MaterialComponents = 0x7f120139
mc.meson.kasumi:id/mtrl_calendar_selection_frame = 0x7f08015b
mc.meson.kasumi:id/action_image = 0x7f080045
mc.meson.kasumi:style/Base.Widget.AppCompat.Spinner = 0x7f120103
mc.meson.kasumi:id/fabCreateConfig = 0x7f0800e2
mc.meson.kasumi:id/versionText = 0x7f080257
mc.meson.kasumi:id/mtrl_anchor_parent = 0x7f080155
mc.meson.kasumi:id/month_grid = 0x7f08014e
mc.meson.kasumi:id/homeAsUp = 0x7f080102
mc.meson.kasumi:id/message = 0x7f08014a
mc.meson.kasumi:id/material_value_index = 0x7f080148
mc.meson.kasumi:styleable/ConstraintOverride = 0x7f13002d
mc.meson.kasumi:color/m3_sys_color_dynamic_light_surface_container_low = 0x7f0501be
mc.meson.kasumi:dimen/mtrl_calendar_bottom_padding = 0x7f060274
mc.meson.kasumi:id/material_timepicker_ok_button = 0x7f080146
mc.meson.kasumi:color/mtrl_switch_thumb_tint = 0x7f05032c
mc.meson.kasumi:style/Base.TextAppearance.AppCompat.Light.Widget.PopupMenu.Large = 0x7f120030
mc.meson.kasumi:id/material_label = 0x7f08013f
mc.meson.kasumi:style/Base.Theme.MaterialComponents.Light = 0x7f12007b
mc.meson.kasumi:id/material_clock_face = 0x7f080137
mc.meson.kasumi:string/bottomsheet_drag_handle_content_description = 0x7f110029
mc.meson.kasumi:id/outward = 0x7f08019a
mc.meson.kasumi:id/match_constraint = 0x7f080133
mc.meson.kasumi:id/mainContent = 0x7f080130
mc.meson.kasumi:id/tvCurrentValue = 0x7f080249
mc.meson.kasumi:string/nav_app_bar_navigate_up_description = 0x7f1100ba
mc.meson.kasumi:id/sharedValueUnset = 0x7f0801dc
mc.meson.kasumi:id/lottie_layer_name = 0x7f08012d
mc.meson.kasumi:id/clip_vertical = 0x7f08009a
mc.meson.kasumi:string/material_timepicker_minute = 0x7f110077
mc.meson.kasumi:id/btnResetSettings = 0x7f080083
mc.meson.kasumi:id/line3 = 0x7f080125
mc.meson.kasumi:macro/m3_comp_radio_button_unselected_hover_icon_color = 0x7f0c00e1
mc.meson.kasumi:styleable/SideSheetBehavior_Layout = 0x7f13008b
mc.meson.kasumi:id/accessibility_custom_action_10 = 0x7f080013
mc.meson.kasumi:style/Theme.Material3.DayNight.BottomSheetDialog = 0x7f12023f
mc.meson.kasumi:id/layout = 0x7f08011f
mc.meson.kasumi:dimen/mtrl_switch_track_width = 0x7f0602fb
mc.meson.kasumi:color/material_personalized_color_primary = 0x7f050292
mc.meson.kasumi:dimen/m3_comp_fab_primary_large_container_height = 0x7f06011b
mc.meson.kasumi:id/labeled = 0x7f08011e
mc.meson.kasumi:macro/m3_comp_progress_indicator_track_color = 0x7f0c00d5
mc.meson.kasumi:style/Widget.Material3.ActionBar.Solid = 0x7f12034b
mc.meson.kasumi:id/invisible = 0x7f080115
mc.meson.kasumi:id/chain2 = 0x7f080092
mc.meson.kasumi:styleable/ScrollingViewBehavior_Layout = 0x7f130086
mc.meson.kasumi:drawable/ic_zoom_out_map = 0x7f0700b4
mc.meson.kasumi:id/jumpToStart = 0x7f08011d
mc.meson.kasumi:macro/m3_comp_navigation_rail_active_indicator_color = 0x7f0c0096
mc.meson.kasumi:id/jumpToEnd = 0x7f08011c
mc.meson.kasumi:id/action_divider = 0x7f08003e
mc.meson.kasumi:id/tag_accessibility_heading = 0x7f080210
mc.meson.kasumi:id/italic = 0x7f080118
mc.meson.kasumi:id/shortcut = 0x7f0801dd
mc.meson.kasumi:style/Widget.Material3.MaterialCalendar = 0x7f1203a7
mc.meson.kasumi:color/material_dynamic_primary20 = 0x7f050243
mc.meson.kasumi:string/abc_action_bar_home_description = 0x7f110000
mc.meson.kasumi:id/info = 0x7f080114
mc.meson.kasumi:id/ignore = 0x7f080109
mc.meson.kasumi:id/ifRoom = 0x7f080108
mc.meson.kasumi:id/linear = 0x7f080126
mc.meson.kasumi:id/groups = 0x7f0800fc
mc.meson.kasumi:macro/m3_comp_filled_autocomplete_text_field_input_text_type = 0x7f0c0042
mc.meson.kasumi:id/open_search_view_content_container = 0x7f08018e
mc.meson.kasumi:string/call_notification_answer_video_action = 0x7f11002b
mc.meson.kasumi:layout/design_bottom_navigation_item = 0x7f0b0021
mc.meson.kasumi:id/gone = 0x7f0800f7
mc.meson.kasumi:id/frost = 0x7f0800f3
mc.meson.kasumi:id/floating = 0x7f0800f0
mc.meson.kasumi:styleable/MotionTelltales = 0x7f13006f
mc.meson.kasumi:id/fixed = 0x7f0800ee
mc.meson.kasumi:layout/mtrl_navigation_rail_item = 0x7f0b006a
mc.meson.kasumi:style/ThemeOverlay.MaterialComponents.Toolbar.Primary = 0x7f1202f3
mc.meson.kasumi:id/design_menu_item_action_area_stub = 0x7f0800b9
mc.meson.kasumi:style/Theme.Material3.DayNight.Dialog.MinWidth = 0x7f120242
mc.meson.kasumi:color/m3_ref_palette_neutral0 = 0x7f050106
mc.meson.kasumi:color/minecraft_orange = 0x7f0502fb
mc.meson.kasumi:dimen/m3_carousel_small_item_default_corner_size = 0x7f0600f0
mc.meson.kasumi:color/mtrl_chip_surface_color = 0x7f050311
mc.meson.kasumi:integer/m3_badge_max_number = 0x7f09000a
mc.meson.kasumi:id/fitCenter = 0x7f0800e9
mc.meson.kasumi:style/MaterialAlertDialog.MaterialComponents.Title.Icon = 0x7f12013d
mc.meson.kasumi:id/arc = 0x7f080060
mc.meson.kasumi:style/Base.Widget.Material3.CompoundButton.CheckBox = 0x7f120110
mc.meson.kasumi:macro/m3_comp_navigation_drawer_active_label_text_color = 0x7f0c0080
mc.meson.kasumi:style/Widget.MaterialComponents.ShapeableImageView = 0x7f120450
mc.meson.kasumi:id/fill_horizontal = 0x7f0800e6
mc.meson.kasumi:dimen/m3_comp_top_app_bar_small_on_scroll_container_elevation = 0x7f0601ac
mc.meson.kasumi:id/fade = 0x7f0800e4
mc.meson.kasumi:layout/material_timepicker = 0x7f0b0053
mc.meson.kasumi:id/cardConfigManagement = 0x7f080089
mc.meson.kasumi:styleable/ListPopupWindow = 0x7f130051
mc.meson.kasumi:id/fabSettings = 0x7f0800e3
mc.meson.kasumi:dimen/m3_btn_icon_btn_padding_left = 0x7f0600d1
mc.meson.kasumi:id/action_bar_subtitle = 0x7f080039
mc.meson.kasumi:integer/material_motion_duration_short_1 = 0x7f09002b
mc.meson.kasumi:id/expanded_menu = 0x7f0800e1
mc.meson.kasumi:style/Widget.MaterialComponents.ChipGroup = 0x7f12041d
mc.meson.kasumi:macro/m3_comp_elevated_card_container_shape = 0x7f0c002b
mc.meson.kasumi:style/ShapeAppearance.Material3.LargeComponent = 0x7f120182
mc.meson.kasumi:macro/m3_comp_sheet_side_docked_modal_container_shape = 0x7f0c0109
mc.meson.kasumi:style/Theme.Material3.Dark.Dialog.MinWidth = 0x7f12023a
mc.meson.kasumi:style/TextAppearance.AppCompat.Light.SearchResult.Subtitle = 0x7f1201ad
mc.meson.kasumi:id/expand_activities_button = 0x7f0800e0
mc.meson.kasumi:style/TextAppearance.M3.Sys.Typescale.HeadlineSmall = 0x7f1201e8
mc.meson.kasumi:dimen/m3_searchbar_margin_horizontal = 0x7f0601dc
mc.meson.kasumi:macro/m3_comp_text_button_label_text_type = 0x7f0c0145
mc.meson.kasumi:dimen/compat_button_inset_vertical_material = 0x7f060057
mc.meson.kasumi:macro/m3_comp_search_view_container_color = 0x7f0c00f1
mc.meson.kasumi:id/etConfigDescription = 0x7f0800dd
mc.meson.kasumi:id/escape = 0x7f0800dc
mc.meson.kasumi:style/Widget.AppCompat.Light.ActionButton = 0x7f120318
mc.meson.kasumi:id/enterAlways = 0x7f0800da
mc.meson.kasumi:id/endToStart = 0x7f0800d9
mc.meson.kasumi:id/edit_query = 0x7f0800d3
mc.meson.kasumi:string/abc_toolbar_collapse_description = 0x7f11001a
mc.meson.kasumi:id/accessibility_custom_action_22 = 0x7f080020
mc.meson.kasumi:id/easeIn = 0x7f0800ce
mc.meson.kasumi:style/Widget.Material3.Search.Toolbar.Button.Navigation = 0x7f1203d2
mc.meson.kasumi:style/ThemeOverlay.Material3.FloatingActionButton.Secondary = 0x7f1202b1
mc.meson.kasumi:id/dragLeft = 0x7f0800c9
mc.meson.kasumi:id/dragClockwise = 0x7f0800c6
mc.meson.kasumi:id/dragAnticlockwise = 0x7f0800c5
mc.meson.kasumi:string/password_toggle_content_description = 0x7f1100c3
mc.meson.kasumi:style/Animation.Material3.SideSheetDialog = 0x7f120007
mc.meson.kasumi:style/Base.Widget.AppCompat.ListView.DropDown = 0x7f1200f5
mc.meson.kasumi:dimen/material_divider_thickness = 0x7f060232
mc.meson.kasumi:id/disabled = 0x7f0800c3
mc.meson.kasumi:id/disablePostScroll = 0x7f0800c1
mc.meson.kasumi:style/TextAppearance.M3.Sys.Typescale.DisplayMedium = 0x7f1201e4
mc.meson.kasumi:style/Base.Theme.Material3.Dark.Dialog = 0x7f120068
mc.meson.kasumi:color/m3_sys_color_light_surface_container_highest = 0x7f0501eb
mc.meson.kasumi:xml/backup_rules = 0x7f140000
mc.meson.kasumi:id/disableHome = 0x7f0800bf
mc.meson.kasumi:id/text_input_start_icon = 0x7f080224
mc.meson.kasumi:id/dimensions = 0x7f0800bd
mc.meson.kasumi:id/tvConfigName = 0x7f080243
mc.meson.kasumi:id/dragEnd = 0x7f0800c8
mc.meson.kasumi:style/ThemeOverlay.Material3.MaterialAlertDialog = 0x7f1202b8
mc.meson.kasumi:id/mtrl_picker_fullscreen = 0x7f080162
mc.meson.kasumi:id/design_menu_item_text = 0x7f0800ba
mc.meson.kasumi:id/textinput_prefix_text = 0x7f080229
mc.meson.kasumi:layout/item_compact_module = 0x7f0b003a
mc.meson.kasumi:layout/abc_dialog_title_material = 0x7f0b000c
mc.meson.kasumi:id/design_bottom_sheet = 0x7f0800b7
mc.meson.kasumi:id/default_activity_button = 0x7f0800b4
mc.meson.kasumi:id/month_navigation_bar = 0x7f08014f
mc.meson.kasumi:macro/m3_comp_switch_disabled_selected_track_color = 0x7f0c011b
mc.meson.kasumi:drawable/mtrl_ic_error = 0x7f0700df
mc.meson.kasumi:string/combat_features = 0x7f110037
mc.meson.kasumi:drawable/abc_ic_commit_search_api_mtrl_alpha = 0x7f070041
mc.meson.kasumi:id/decelerateAndComplete = 0x7f0800b2
mc.meson.kasumi:id/decelerate = 0x7f0800b1
mc.meson.kasumi:styleable/DialogFragmentNavigator = 0x7f130032
mc.meson.kasumi:id/date_picker_actions = 0x7f0800b0
mc.meson.kasumi:id/dropdown_menu = 0x7f0800cd
mc.meson.kasumi:attr/ratingBarStyle = 0x7f0303cb
mc.meson.kasumi:dimen/m3_comp_navigation_bar_container_height = 0x7f060139
mc.meson.kasumi:id/contiguous = 0x7f0800a6
mc.meson.kasumi:string/mtrl_exceed_max_badge_number_suffix = 0x7f11008a
mc.meson.kasumi:id/collapsedDot = 0x7f08009e
mc.meson.kasumi:layout/abc_expanded_menu_layout = 0x7f0b000d
mc.meson.kasumi:id/appBarLayout = 0x7f08005c
mc.meson.kasumi:id/tvModuleDescription = 0x7f08024b
mc.meson.kasumi:id/collapseActionView = 0x7f08009d
mc.meson.kasumi:style/Base.TextAppearance.AppCompat.Inverse = 0x7f12002d
mc.meson.kasumi:id/match_parent = 0x7f080134
mc.meson.kasumi:id/clear_text = 0x7f080098
mc.meson.kasumi:style/ThemeOverlay.Material3.Button.IconButton = 0x7f12029a
mc.meson.kasumi:style/TextAppearance.AppCompat.SearchResult.Subtitle = 0x7f1201b4
mc.meson.kasumi:styleable/AnimatedStateListDrawableCompat = 0x7f13000a
mc.meson.kasumi:id/checked = 0x7f080095
mc.meson.kasumi:macro/m3_comp_text_button_label_text_color = 0x7f0c0144
mc.meson.kasumi:style/Base.Widget.AppCompat.TextView = 0x7f120105
mc.meson.kasumi:id/checkbox = 0x7f080094
mc.meson.kasumi:id/chain = 0x7f080091
mc.meson.kasumi:id/center_vertical = 0x7f080090
mc.meson.kasumi:style/Widget.MaterialComponents.Button.TextButton.Dialog.Flush = 0x7f120411
mc.meson.kasumi:layout/mtrl_picker_header_selection_text = 0x7f0b0070
mc.meson.kasumi:layout/design_layout_tab_text = 0x7f0b0026
mc.meson.kasumi:id/cardConfig = 0x7f080088
mc.meson.kasumi:color/primary_dark_material_dark = 0x7f05033c
mc.meson.kasumi:style/Theme.MaterialComponents.Bridge = 0x7f120256
mc.meson.kasumi:id/top = 0x7f080232
mc.meson.kasumi:macro/m3_comp_extended_fab_primary_container_shape = 0x7f0c002d
mc.meson.kasumi:id/btnExportConfig = 0x7f08007f
mc.meson.kasumi:string/abc_searchview_description_query = 0x7f110014
mc.meson.kasumi:id/cardModule = 0x7f08008a
mc.meson.kasumi:macro/m3_comp_navigation_drawer_inactive_pressed_icon_color = 0x7f0c008e
mc.meson.kasumi:style/Base.TextAppearance.AppCompat.Small = 0x7f120038
mc.meson.kasumi:style/Base.Widget.AppCompat.RatingBar = 0x7f1200fc
mc.meson.kasumi:color/m3_sys_color_dynamic_primary_fixed = 0x7f0501ca
mc.meson.kasumi:dimen/m3_comp_time_picker_period_selector_hover_state_layer_opacity = 0x7f0601a2
mc.meson.kasumi:id/bounceStart = 0x7f080075
mc.meson.kasumi:macro/m3_comp_navigation_rail_active_label_text_color = 0x7f0c0097
mc.meson.kasumi:id/bounceEnd = 0x7f080074
mc.meson.kasumi:styleable/MenuItem = 0x7f130066
mc.meson.kasumi:style/Widget.Material3.Tooltip = 0x7f1203f5
mc.meson.kasumi:id/bounceBoth = 0x7f080073
mc.meson.kasumi:style/Widget.Material3.MaterialTimePicker.ImageButton = 0x7f1203c8
mc.meson.kasumi:macro/m3_comp_filled_icon_button_container_color = 0x7f0c0048
mc.meson.kasumi:id/below = 0x7f08006d
mc.meson.kasumi:style/TextAppearance.AppCompat.Button = 0x7f1201a3
mc.meson.kasumi:id/beginOnFirstDraw = 0x7f08006b
mc.meson.kasumi:string/clear_text_end_icon_content_description = 0x7f110036
mc.meson.kasumi:id/barrier = 0x7f080069
mc.meson.kasumi:id/scrollView = 0x7f0801ca
mc.meson.kasumi:id/axisRelative = 0x7f080068
mc.meson.kasumi:style/MaterialAlertDialog.Material3.Title.Icon = 0x7f120133
mc.meson.kasumi:id/mtrl_calendar_main_pane = 0x7f080159
mc.meson.kasumi:id/automatic = 0x7f080067
mc.meson.kasumi:styleable/ShapeAppearance = 0x7f130089
mc.meson.kasumi:id/autoCompleteToEnd = 0x7f080065
mc.meson.kasumi:style/Widget.Material3.CheckedTextView = 0x7f120373
mc.meson.kasumi:id/asConfigured = 0x7f080061
mc.meson.kasumi:id/btnLandscape = 0x7f080082
mc.meson.kasumi:drawable/abc_ic_ab_back_material = 0x7f07003e
mc.meson.kasumi:id/appIcon = 0x7f08005d
mc.meson.kasumi:id/animateToEnd = 0x7f080058
mc.meson.kasumi:drawable/abc_ic_menu_copy_mtrl_am_alpha = 0x7f070043
mc.meson.kasumi:color/m3_sys_color_light_secondary_container = 0x7f0501e6
mc.meson.kasumi:integer/m3_card_anim_duration_ms = 0x7f09000e
mc.meson.kasumi:id/all = 0x7f080054
mc.meson.kasumi:id/action_settings_to_features = 0x7f08004c
mc.meson.kasumi:style/Widget.AppCompat.RatingBar.Small = 0x7f120331
mc.meson.kasumi:string/welcome_title = 0x7f1100da
mc.meson.kasumi:string/feature_disabled = 0x7f110044
mc.meson.kasumi:id/action_module_settings_back = 0x7f08004b
mc.meson.kasumi:macro/m3_comp_navigation_bar_inactive_focus_state_layer_color = 0x7f0c006e
mc.meson.kasumi:id/action_menu_presenter = 0x7f080047
mc.meson.kasumi:style/Base.Theme.MaterialComponents.Dialog.FixedSize = 0x7f120078
mc.meson.kasumi:id/action_context_bar = 0x7f08003d
mc.meson.kasumi:style/Base.Theme.AppCompat.Light.Dialog.Alert = 0x7f120062
mc.meson.kasumi:id/action_bar_spinner = 0x7f080038
mc.meson.kasumi:color/material_dynamic_neutral20 = 0x7f050229
mc.meson.kasumi:style/Widget.MaterialComponents.AutoCompleteTextView.OutlinedBox.Dense = 0x7f120401
mc.meson.kasumi:id/action_bar_activity_content = 0x7f080035
mc.meson.kasumi:dimen/m3_comp_input_chip_unselected_outline_width = 0x7f060132
mc.meson.kasumi:styleable/ActivityChooserView = 0x7f130005
mc.meson.kasumi:id/actionDown = 0x7f080031
mc.meson.kasumi:string/floating_window_notification_text = 0x7f110047
mc.meson.kasumi:style/Animation.Design.BottomSheetDialog = 0x7f120005
mc.meson.kasumi:style/Base.ThemeOverlay.Material3.BottomSheetDialog = 0x7f12008d
mc.meson.kasumi:id/accessibility_custom_action_7 = 0x7f08002e
mc.meson.kasumi:style/Widget.MaterialComponents.MaterialCalendar.Item = 0x7f12043d
mc.meson.kasumi:dimen/mtrl_switch_thumb_icon_size = 0x7f0602f8
mc.meson.kasumi:id/material_clock_period_am_button = 0x7f08013a
mc.meson.kasumi:id/accessibility_custom_action_5 = 0x7f08002c
mc.meson.kasumi:id/fitToContents = 0x7f0800ec
mc.meson.kasumi:styleable/TabLayout = 0x7f13009a
mc.meson.kasumi:id/accessibility_custom_action_4 = 0x7f08002b
mc.meson.kasumi:id/accessibility_custom_action_29 = 0x7f080027
mc.meson.kasumi:id/start = 0x7f0801f9
mc.meson.kasumi:style/TextAppearance.AppCompat.Subhead.Inverse = 0x7f1201b9
mc.meson.kasumi:color/m3_sys_color_dark_on_primary = 0x7f050167
mc.meson.kasumi:attr/extraMultilineHeightEnabled = 0x7f0301d5
mc.meson.kasumi:id/statusText = 0x7f080200
mc.meson.kasumi:id/accessibility_custom_action_24 = 0x7f080022
mc.meson.kasumi:macro/m3_comp_time_picker_period_selector_selected_focus_state_layer_color = 0x7f0c0156
mc.meson.kasumi:id/mtrl_calendar_day_selector_frame = 0x7f080156
mc.meson.kasumi:macro/m3_comp_extended_fab_primary_container_color = 0x7f0c002c
mc.meson.kasumi:style/Base.Widget.AppCompat.ButtonBar = 0x7f1200e0
mc.meson.kasumi:id/accessibility_custom_action_23 = 0x7f080021
mc.meson.kasumi:macro/m3_comp_navigation_drawer_inactive_hover_label_text_color = 0x7f0c008a
mc.meson.kasumi:id/accessibility_custom_action_20 = 0x7f08001e
mc.meson.kasumi:style/Widget.Material3.MaterialCalendar.Year.Today = 0x7f1203bc
mc.meson.kasumi:id/accessibility_custom_action_2 = 0x7f08001d
mc.meson.kasumi:style/Widget.Material3.BottomNavigation.Badge = 0x7f120357
mc.meson.kasumi:id/SHOW_PROGRESS = 0x7f08000a
mc.meson.kasumi:id/accessibility_custom_action_17 = 0x7f08001a
mc.meson.kasumi:id/search_bar = 0x7f0801cd
mc.meson.kasumi:id/accessibility_custom_action_16 = 0x7f080019
mc.meson.kasumi:id/accessibility_custom_action_14 = 0x7f080017
mc.meson.kasumi:id/accessibility_custom_action_12 = 0x7f080015
mc.meson.kasumi:id/scrollIndicatorDown = 0x7f0801c8
mc.meson.kasumi:string/floating_window_started = 0x7f11004a
mc.meson.kasumi:id/accessibility_custom_action_1 = 0x7f080012
mc.meson.kasumi:drawable/m3_avd_show_password = 0x7f0700b7
mc.meson.kasumi:style/Base.V21.ThemeOverlay.Material3.SideSheetDialog = 0x7f1200b8
mc.meson.kasumi:id/accessibility_custom_action_0 = 0x7f080011
mc.meson.kasumi:id/accessibility_action_clickable_span = 0x7f080010
mc.meson.kasumi:id/accelerate = 0x7f08000f
mc.meson.kasumi:macro/m3_comp_checkbox_selected_icon_color = 0x7f0c000b
mc.meson.kasumi:id/TOP_START = 0x7f08000d
mc.meson.kasumi:id/META = 0x7f080005
mc.meson.kasumi:style/Base.Widget.AppCompat.RatingBar.Small = 0x7f1200fe
mc.meson.kasumi:id/FUNCTION = 0x7f080004
mc.meson.kasumi:id/BOTTOM_START = 0x7f080002
mc.meson.kasumi:style/Widget.AppCompat.Light.ListPopupWindow = 0x7f12031f
mc.meson.kasumi:id/BOTTOM_END = 0x7f080001
mc.meson.kasumi:macro/m3_comp_search_view_header_input_text_type = 0x7f0c00f5
mc.meson.kasumi:id/ALT = 0x7f080000
mc.meson.kasumi:drawable/tooltip_frame_dark = 0x7f070108
mc.meson.kasumi:style/Widget.MaterialComponents.MaterialDivider = 0x7f120444
mc.meson.kasumi:id/line1 = 0x7f080124
mc.meson.kasumi:drawable/tab_layout_background = 0x7f070106
mc.meson.kasumi:drawable/tab_indicator_simple = 0x7f070105
mc.meson.kasumi:drawable/notification_template_icon_low_bg = 0x7f0700fc
mc.meson.kasumi:drawable/notification_template_icon_bg = 0x7f0700fb
mc.meson.kasumi:style/Theme.MaterialComponents.Light.Dialog.Alert = 0x7f120277
mc.meson.kasumi:drawable/notification_bg_low_pressed = 0x7f0700f6
mc.meson.kasumi:style/Widget.Material3.MaterialDivider = 0x7f1203be
mc.meson.kasumi:style/Widget.MaterialComponents.TimePicker.Button = 0x7f120466
mc.meson.kasumi:drawable/notification_bg_low = 0x7f0700f4
mc.meson.kasumi:drawable/notification_bg = 0x7f0700f3
mc.meson.kasumi:integer/hide_password_duration = 0x7f090009
mc.meson.kasumi:drawable/navigation_empty_icon = 0x7f0700f1
mc.meson.kasumi:dimen/m3_card_elevated_hovered_z = 0x7f0600e9
mc.meson.kasumi:xml/data_extraction_rules = 0x7f140001
mc.meson.kasumi:drawable/mtrl_switch_track_decoration = 0x7f0700ef
mc.meson.kasumi:color/material_grey_850 = 0x7f05026c
mc.meson.kasumi:drawable/mtrl_switch_thumb_pressed_checked = 0x7f0700e9
mc.meson.kasumi:drawable/mtrl_switch_thumb_checked_pressed = 0x7f0700e6
mc.meson.kasumi:drawable/mtrl_navigation_bar_item_background = 0x7f0700e1
mc.meson.kasumi:layout/fragment_hack_features = 0x7f0b0034
mc.meson.kasumi:style/Base.Widget.AppCompat.ActionBar.TabBar = 0x7f1200d1
mc.meson.kasumi:dimen/abc_search_view_preferred_width = 0x7f060037
mc.meson.kasumi:style/Base.V7.Theme.AppCompat.Light = 0x7f1200c9
mc.meson.kasumi:drawable/mtrl_ic_checkbox_unchecked = 0x7f0700de
mc.meson.kasumi:color/material_on_surface_stroke = 0x7f05027b
mc.meson.kasumi:drawable/mtrl_ic_check_mark = 0x7f0700dc
mc.meson.kasumi:id/tvCurrentConfigDescription = 0x7f080246
mc.meson.kasumi:drawable/mtrl_ic_cancel = 0x7f0700db
mc.meson.kasumi:drawable/mtrl_tabs_default_indicator = 0x7f0700f0
mc.meson.kasumi:drawable/mtrl_ic_arrow_drop_up = 0x7f0700da
mc.meson.kasumi:drawable/mtrl_dropdown_arrow = 0x7f0700d8
mc.meson.kasumi:drawable/mtrl_dialog_background = 0x7f0700d7
mc.meson.kasumi:id/auto = 0x7f080063
mc.meson.kasumi:drawable/mtrl_checkbox_button_unchecked_checked = 0x7f0700d6
mc.meson.kasumi:dimen/mtrl_tooltip_arrowSize = 0x7f060306
mc.meson.kasumi:drawable/mtrl_checkbox_button_icon_unchecked_indeterminate = 0x7f0700d5
mc.meson.kasumi:id/dependency_ordering = 0x7f0800b6
mc.meson.kasumi:drawable/mtrl_checkbox_button_icon_indeterminate_checked = 0x7f0700d2
mc.meson.kasumi:macro/m3_comp_search_bar_hover_supporting_text_color = 0x7f0c00e8
mc.meson.kasumi:drawable/mtrl_checkbox_button_icon = 0x7f0700cf
mc.meson.kasumi:drawable/material_ic_menu_arrow_down_black_24dp = 0x7f0700c9
mc.meson.kasumi:macro/m3_comp_text_button_hover_state_layer_color = 0x7f0c0143
mc.meson.kasumi:layout/m3_side_sheet_dialog = 0x7f0b0047
mc.meson.kasumi:drawable/material_ic_keyboard_arrow_right_black_24dp = 0x7f0700c8
mc.meson.kasumi:drawable/material_ic_keyboard_arrow_next_black_24dp = 0x7f0700c6
mc.meson.kasumi:drawable/material_ic_keyboard_arrow_left_black_24dp = 0x7f0700c5
mc.meson.kasumi:drawable/m3_tabs_rounded_line_indicator = 0x7f0700bf
mc.meson.kasumi:style/Widget.MaterialComponents.CircularProgressIndicator.ExtraSmall = 0x7f12041f
mc.meson.kasumi:drawable/m3_tabs_background = 0x7f0700bd
mc.meson.kasumi:drawable/m3_radiobutton_ripple = 0x7f0700bb
mc.meson.kasumi:drawable/m3_avd_hide_password = 0x7f0700b6
mc.meson.kasumi:drawable/ic_settings_24 = 0x7f0700b3
mc.meson.kasumi:drawable/ic_mtrl_chip_close_circle = 0x7f0700b0
mc.meson.kasumi:string/mtrl_picker_range_header_only_end_selected = 0x7f11009e
mc.meson.kasumi:drawable/ic_mtrl_chip_checked_circle = 0x7f0700af
mc.meson.kasumi:drawable/ic_m3_chip_checked_circle = 0x7f0700ab
mc.meson.kasumi:drawable/ic_launcher_foreground = 0x7f0700a9
mc.meson.kasumi:drawable/ic_launcher_background = 0x7f0700a8
mc.meson.kasumi:drawable/ic_keyboard_black_24dp = 0x7f0700a7
mc.meson.kasumi:drawable/ic_home_24 = 0x7f0700a4
mc.meson.kasumi:drawable/ic_compress = 0x7f0700a1
mc.meson.kasumi:style/MaterialAlertDialog.MaterialComponents.Picker.Date.Spinner = 0x7f12013c
mc.meson.kasumi:string/abc_activitychooserview_choose_application = 0x7f110005
mc.meson.kasumi:drawable/ic_close_24 = 0x7f07009f
mc.meson.kasumi:dimen/abc_action_bar_stacked_tab_max_width = 0x7f06000a
mc.meson.kasumi:style/Widget.Material3.ExtendedFloatingActionButton.Icon.Tertiary = 0x7f120392
mc.meson.kasumi:drawable/ic_close = 0x7f07009e
mc.meson.kasumi:dimen/m3_comp_navigation_drawer_container_width = 0x7f06013e
mc.meson.kasumi:style/Theme.AppCompat.Dialog.MinWidth = 0x7f120223
mc.meson.kasumi:drawable/ic_clear_black_24 = 0x7f07009c
mc.meson.kasumi:drawable/ic_call_decline_low = 0x7f07009a
mc.meson.kasumi:dimen/hint_alpha_material_light = 0x7f060097
mc.meson.kasumi:color/tooltip_background_light = 0x7f050356
mc.meson.kasumi:drawable/ic_call_answer_video_low = 0x7f070098
mc.meson.kasumi:style/TextAppearance.Compat.Notification.Info = 0x7f1201d1
mc.meson.kasumi:macro/m3_comp_search_bar_input_text_color = 0x7f0c00e9
mc.meson.kasumi:drawable/floating_divider = 0x7f070090
mc.meson.kasumi:drawable/floating_button_ripple = 0x7f07008e
mc.meson.kasumi:style/ShapeAppearance.M3.Comp.FilledButton.Container.Shape = 0x7f120166
mc.meson.kasumi:drawable/floating_button_background = 0x7f07008d
mc.meson.kasumi:color/minecraft_blue = 0x7f0502f7
mc.meson.kasumi:macro/m3_comp_radio_button_unselected_hover_state_layer_color = 0x7f0c00e2
mc.meson.kasumi:drawable/drag_indicator = 0x7f07008c
mc.meson.kasumi:drawable/design_ic_visibility_off = 0x7f070089
mc.meson.kasumi:drawable/custom_scrollbar_track = 0x7f070086
mc.meson.kasumi:drawable/button_ripple = 0x7f070082
mc.meson.kasumi:style/Base.Widget.AppCompat.PopupWindow = 0x7f1200f9
mc.meson.kasumi:drawable/btn_radio_on_to_off_mtrl_animation = 0x7f070081
mc.meson.kasumi:drawable/indeterminate_static = 0x7f0700b5
mc.meson.kasumi:drawable/btn_checkbox_checked_mtrl = 0x7f07007a
mc.meson.kasumi:string/fab_transformation_scrim_behavior = 0x7f110042
mc.meson.kasumi:attr/layout_scrollInterpolator = 0x7f0302cd
mc.meson.kasumi:drawable/abc_vector_test = 0x7f070077
mc.meson.kasumi:attr/constraintSetStart = 0x7f03013a
mc.meson.kasumi:color/m3_sys_color_light_surface_container_lowest = 0x7f0501ed
mc.meson.kasumi:string/floating_window_desc = 0x7f110046
mc.meson.kasumi:drawable/abc_textfield_search_material = 0x7f070076
mc.meson.kasumi:drawable/abc_textfield_search_activated_mtrl_alpha = 0x7f070074
mc.meson.kasumi:id/cache_measures = 0x7f080085
mc.meson.kasumi:drawable/abc_text_select_handle_left_mtrl = 0x7f07006f
mc.meson.kasumi:attr/motionEasingEmphasizedInterpolator = 0x7f030363
mc.meson.kasumi:drawable/abc_tab_indicator_material = 0x7f07006c
mc.meson.kasumi:attr/subheaderInsetEnd = 0x7f03043f
mc.meson.kasumi:style/Widget.Material3.TextInputEditText.FilledBox = 0x7f1203e6
mc.meson.kasumi:color/md_theme_dark_inverseOnSurface = 0x7f0502be
mc.meson.kasumi:drawable/abc_star_half_black_48dp = 0x7f070069
mc.meson.kasumi:attr/dragScale = 0x7f03018f
mc.meson.kasumi:drawable/abc_star_black_48dp = 0x7f070068
mc.meson.kasumi:id/chains = 0x7f080093
mc.meson.kasumi:color/m3_sys_color_dynamic_light_surface_bright = 0x7f0501ba
mc.meson.kasumi:interpolator/btn_radio_to_off_mtrl_animation_interpolator_0 = 0x7f0a0004
mc.meson.kasumi:color/md_theme_light_tertiary = 0x7f0502f5
mc.meson.kasumi:color/material_personalized_color_surface_variant = 0x7f0502a4
mc.meson.kasumi:color/m3_sys_color_dynamic_light_secondary = 0x7f0501b7
mc.meson.kasumi:drawable/abc_spinner_mtrl_am_alpha = 0x7f070066
mc.meson.kasumi:macro/m3_comp_navigation_bar_inactive_pressed_icon_color = 0x7f0c0074
mc.meson.kasumi:color/material_personalized_color_on_surface_inverse = 0x7f05028c
mc.meson.kasumi:drawable/abc_seekbar_thumb_material = 0x7f070063
mc.meson.kasumi:styleable/SnackbarLayout = 0x7f13008e
mc.meson.kasumi:attr/snackbarStyle = 0x7f030413
mc.meson.kasumi:drawable/abc_ratingbar_small_material = 0x7f07005d
mc.meson.kasumi:dimen/mtrl_calendar_year_vertical_padding = 0x7f060299
mc.meson.kasumi:drawable/abc_ratingbar_indicator_material = 0x7f07005b
mc.meson.kasumi:style/Theme.MaterialComponents.DayNight.Dialog.MinWidth = 0x7f120263
mc.meson.kasumi:drawable/abc_list_selector_holo_light = 0x7f070058
mc.meson.kasumi:dimen/material_filled_edittext_font_1_3_padding_bottom = 0x7f060237
mc.meson.kasumi:style/TextAppearance.MaterialComponents.Body2 = 0x7f120206
mc.meson.kasumi:drawable/abc_list_selector_disabled_holo_light = 0x7f070056
mc.meson.kasumi:interpolator/btn_checkbox_checked_mtrl_animation_interpolator_0 = 0x7f0a0000
mc.meson.kasumi:drawable/abc_list_selector_background_transition_holo_dark = 0x7f070053
mc.meson.kasumi:style/Theme.Material3.Light.SideSheetDialog = 0x7f120253
mc.meson.kasumi:drawable/abc_list_focused_holo = 0x7f07004f
mc.meson.kasumi:string/welcome_description = 0x7f1100d9
mc.meson.kasumi:drawable/abc_item_background_holo_dark = 0x7f07004b
mc.meson.kasumi:id/navigation_bar_item_large_label_view = 0x7f08017a
mc.meson.kasumi:drawable/abc_ic_menu_cut_mtrl_alpha = 0x7f070044
mc.meson.kasumi:attr/tabIconTint = 0x7f030453
mc.meson.kasumi:drawable/abc_cab_background_internal_bg = 0x7f070038
mc.meson.kasumi:dimen/highlight_alpha_material_colored = 0x7f060093
mc.meson.kasumi:macro/m3_comp_primary_navigation_tab_with_icon_inactive_icon_color = 0x7f0c00d0
mc.meson.kasumi:id/switchDarkMode = 0x7f080207
mc.meson.kasumi:drawable/abc_btn_default_mtrl_shape = 0x7f070031
mc.meson.kasumi:drawable/abc_btn_check_to_on_mtrl_015 = 0x7f07002f
mc.meson.kasumi:attr/startIconMinSize = 0x7f03042a
mc.meson.kasumi:attr/customColorDrawableValue = 0x7f030169
mc.meson.kasumi:drawable/$mtrl_switch_thumb_pressed_checked__0 = 0x7f070024
mc.meson.kasumi:style/TextAppearance.Compat.Notification.Line2 = 0x7f1201d2
mc.meson.kasumi:string/mtrl_picker_invalid_format = 0x7f110097
mc.meson.kasumi:macro/m3_comp_secondary_navigation_tab_active_label_text_color = 0x7f0c00fb
mc.meson.kasumi:attr/state_indeterminate = 0x7f030434
mc.meson.kasumi:drawable/$mtrl_switch_thumb_checked_unchecked__1 = 0x7f070023
mc.meson.kasumi:attr/extendMotionSpec = 0x7f0301ce
mc.meson.kasumi:style/CardView.Light = 0x7f12012e
mc.meson.kasumi:drawable/$mtrl_checkbox_button_icon_unchecked_indeterminate__2 = 0x7f07001d
mc.meson.kasumi:attr/tabIndicator = 0x7f030455
mc.meson.kasumi:style/Base.V22.Theme.AppCompat.Light = 0x7f1200bb
mc.meson.kasumi:drawable/$mtrl_checkbox_button_icon_indeterminate_unchecked__0 = 0x7f070015
mc.meson.kasumi:drawable/mtrl_ic_indeterminate = 0x7f0700e0
mc.meson.kasumi:drawable/$mtrl_checkbox_button_icon_checked_unchecked__1 = 0x7f070012
mc.meson.kasumi:style/App.Text.Title = 0x7f120015
mc.meson.kasumi:id/autoComplete = 0x7f080064
mc.meson.kasumi:color/mtrl_filled_stroke_color = 0x7f05031c
mc.meson.kasumi:drawable/$mtrl_checkbox_button_icon_checked_unchecked__0 = 0x7f070011
mc.meson.kasumi:id/action_text = 0x7f08004e
mc.meson.kasumi:drawable/$mtrl_checkbox_button_icon_checked_indeterminate__0 = 0x7f070010
mc.meson.kasumi:id/dialog_button = 0x7f0800bc
mc.meson.kasumi:drawable/$mtrl_checkbox_button_checked_unchecked__1 = 0x7f07000e
mc.meson.kasumi:dimen/m3_navigation_item_shape_inset_end = 0x7f0601c3
mc.meson.kasumi:drawable/$m3_avd_hide_password__1 = 0x7f070008
mc.meson.kasumi:drawable/$m3_avd_hide_password__0 = 0x7f070007
mc.meson.kasumi:drawable/$avd_hide_password__2 = 0x7f070002
mc.meson.kasumi:attr/textAppearanceHeadline4 = 0x7f030480
mc.meson.kasumi:drawable/$avd_hide_password__1 = 0x7f070001
mc.meson.kasumi:dimen/tooltip_vertical_padding = 0x7f060321
mc.meson.kasumi:id/inward = 0x7f080116
mc.meson.kasumi:id/tvCurrentConfigName = 0x7f080247
mc.meson.kasumi:color/m3_sys_color_dynamic_dark_on_secondary = 0x7f05018b
mc.meson.kasumi:dimen/abc_disabled_alpha_material_light = 0x7f060028
mc.meson.kasumi:dimen/notification_top_pad = 0x7f060319
mc.meson.kasumi:attr/tickMarkTintMode = 0x7f0304c9
mc.meson.kasumi:integer/m3_btn_anim_delay_ms = 0x7f09000b
mc.meson.kasumi:dimen/notification_small_icon_background_padding = 0x7f060316
mc.meson.kasumi:style/ShapeAppearance.M3.Comp.BottomAppBar.Container.Shape = 0x7f120164
mc.meson.kasumi:dimen/notification_right_side_padding_top = 0x7f060315
mc.meson.kasumi:dimen/notification_right_icon_size = 0x7f060314
mc.meson.kasumi:dimen/notification_main_column_padding_top = 0x7f060312
mc.meson.kasumi:style/ShapeAppearance.MaterialComponents.Tooltip = 0x7f12018c
mc.meson.kasumi:dimen/mtrl_toolbar_default_height = 0x7f060305
mc.meson.kasumi:dimen/mtrl_textinput_start_icon_margin_end = 0x7f060304
mc.meson.kasumi:style/Base.ThemeOverlay.AppCompat = 0x7f120085
mc.meson.kasumi:id/item_touch_helper_previous_elevation = 0x7f080119
mc.meson.kasumi:dimen/mtrl_textinput_counter_margin_start = 0x7f060301
mc.meson.kasumi:attr/contrast = 0x7f03014e
mc.meson.kasumi:dimen/mtrl_textinput_box_label_cutout_padding = 0x7f0602fe
mc.meson.kasumi:id/embed = 0x7f0800d6
mc.meson.kasumi:dimen/mtrl_textinput_box_corner_radius_small = 0x7f0602fd
mc.meson.kasumi:id/action_bar_root = 0x7f080037
mc.meson.kasumi:dimen/mtrl_snackbar_background_overlay_color_alpha = 0x7f0602f2
mc.meson.kasumi:dimen/mtrl_progress_circular_track_thickness_medium = 0x7f0602de
mc.meson.kasumi:attr/materialCalendarHeaderDivider = 0x7f030311
mc.meson.kasumi:dimen/mtrl_snackbar_action_text_color_alpha = 0x7f0602f0
mc.meson.kasumi:dimen/mtrl_slider_track_side_padding = 0x7f0602ee
mc.meson.kasumi:dimen/mtrl_textinput_box_stroke_width_default = 0x7f0602ff
mc.meson.kasumi:dimen/mtrl_slider_track_height = 0x7f0602ed
mc.meson.kasumi:style/Theme.AppCompat.Empty = 0x7f120225
mc.meson.kasumi:dimen/mtrl_slider_tick_min_spacing = 0x7f0602eb
mc.meson.kasumi:id/hideable = 0x7f080100
mc.meson.kasumi:dimen/mtrl_slider_label_square_side = 0x7f0602e8
mc.meson.kasumi:attr/behavior_overlapTop = 0x7f030073
mc.meson.kasumi:attr/panelBackground = 0x7f03039c
mc.meson.kasumi:dimen/mtrl_slider_label_padding = 0x7f0602e6
mc.meson.kasumi:string/m3_sys_motion_easing_emphasized_decelerate = 0x7f11005c
mc.meson.kasumi:dimen/mtrl_progress_circular_track_thickness_small = 0x7f0602df
mc.meson.kasumi:attr/colorOnContainer = 0x7f030103
mc.meson.kasumi:dimen/mtrl_progress_circular_size_medium = 0x7f0602db
mc.meson.kasumi:id/right = 0x7f0801b8
mc.meson.kasumi:dimen/mtrl_progress_circular_inset_small = 0x7f0602d7
mc.meson.kasumi:styleable/CircularProgressIndicator = 0x7f130022
mc.meson.kasumi:color/m3_sys_color_dynamic_secondary_fixed = 0x7f0501cc
mc.meson.kasumi:macro/m3_comp_secondary_navigation_tab_label_text_type = 0x7f0c0100
mc.meson.kasumi:drawable/btn_checkbox_checked_to_unchecked_mtrl_animation = 0x7f07007b
mc.meson.kasumi:dimen/mtrl_progress_circular_inset = 0x7f0602d4
mc.meson.kasumi:attr/guidelineUseRtl = 0x7f030228
mc.meson.kasumi:dimen/mtrl_navigation_rail_default_width = 0x7f0602cd
mc.meson.kasumi:layout/material_clockface_textview = 0x7f0b004d
mc.meson.kasumi:style/Base.Widget.Material3.Snackbar = 0x7f12011a
mc.meson.kasumi:dimen/mtrl_switch_text_padding = 0x7f0602f6
mc.meson.kasumi:style/ThemeOverlay.Material3.Button.IconButton.Filled.Tonal = 0x7f12029c
mc.meson.kasumi:dimen/mtrl_navigation_rail_active_text_size = 0x7f0602cb
mc.meson.kasumi:color/m3_sys_color_light_surface_container_high = 0x7f0501ea
mc.meson.kasumi:styleable/ScrimInsetsFrameLayout = 0x7f130085
mc.meson.kasumi:dimen/mtrl_navigation_elevation = 0x7f0602c5
mc.meson.kasumi:dimen/mtrl_low_ripple_focused_alpha = 0x7f0602bf
mc.meson.kasumi:dimen/mtrl_fab_translation_z_hovered_focused = 0x7f0602b8
mc.meson.kasumi:attr/fontVariationSettings = 0x7f030213
mc.meson.kasumi:color/m3_sys_color_dark_on_background = 0x7f050164
mc.meson.kasumi:dimen/mtrl_fab_elevation = 0x7f0602b6
mc.meson.kasumi:styleable/MockView = 0x7f130068
mc.meson.kasumi:attr/numericModifiers = 0x7f030388
mc.meson.kasumi:dimen/mtrl_extended_fab_translation_z_pressed = 0x7f0602b5
mc.meson.kasumi:dimen/mtrl_extended_fab_translation_z_base = 0x7f0602b3
mc.meson.kasumi:style/ShapeAppearance.Material3.Corner.None = 0x7f120180
mc.meson.kasumi:color/material_dynamic_tertiary20 = 0x7f05025d
mc.meson.kasumi:style/ThemeOverlay.MaterialComponents.TextInputEditText.OutlinedBox = 0x7f1202ed
mc.meson.kasumi:color/design_dark_default_color_secondary = 0x7f050040
mc.meson.kasumi:dimen/mtrl_extended_fab_icon_text_spacing = 0x7f0602ad
mc.meson.kasumi:dimen/mtrl_extended_fab_bottom_padding = 0x7f0602a6
mc.meson.kasumi:macro/m3_comp_navigation_bar_active_icon_color = 0x7f0c0065
mc.meson.kasumi:dimen/mtrl_card_spacing = 0x7f0602a0
mc.meson.kasumi:id/accessibility_custom_action_11 = 0x7f080014
mc.meson.kasumi:dimen/mtrl_card_elevation = 0x7f06029f
mc.meson.kasumi:id/mtrl_calendar_months = 0x7f08015a
mc.meson.kasumi:string/material_hour_24h_suffix = 0x7f110067
mc.meson.kasumi:dimen/mtrl_card_corner_radius = 0x7f06029d
mc.meson.kasumi:id/material_clock_display_and_toggle = 0x7f080136
mc.meson.kasumi:attr/daySelectedStyle = 0x7f030175
mc.meson.kasumi:style/Widget.MaterialComponents.Button.TextButton.Icon = 0x7f120413
mc.meson.kasumi:dimen/mtrl_calendar_year_horizontal_padding = 0x7f060298
mc.meson.kasumi:attr/listPreferredItemHeightSmall = 0x7f0302e0
mc.meson.kasumi:dimen/mtrl_calendar_title_baseline_to_top = 0x7f060294
mc.meson.kasumi:attr/floatingActionButtonSmallPrimaryStyle = 0x7f0301ee
mc.meson.kasumi:color/m3_ref_palette_neutral30 = 0x7f05010e
mc.meson.kasumi:dimen/mtrl_calendar_selection_text_baseline_to_top = 0x7f060292
mc.meson.kasumi:color/m3_sys_color_on_tertiary_fixed_variant = 0x7f0501f7
mc.meson.kasumi:dimen/mtrl_calendar_navigation_bottom_padding = 0x7f06028b
mc.meson.kasumi:attr/clickAction = 0x7f0300e0
mc.meson.kasumi:dimen/mtrl_calendar_month_vertical_padding = 0x7f06028a
mc.meson.kasumi:color/m3_sys_color_dark_on_error = 0x7f050165
mc.meson.kasumi:id/toggle = 0x7f080230
mc.meson.kasumi:attr/hideMotionSpec = 0x7f030232
mc.meson.kasumi:color/material_dynamic_secondary50 = 0x7f050253
mc.meson.kasumi:style/Widget.Material3.ChipGroup = 0x7f12037e
mc.meson.kasumi:dimen/mtrl_calendar_header_toggle_margin_top = 0x7f060286
mc.meson.kasumi:dimen/mtrl_calendar_header_text_padding = 0x7f060284
mc.meson.kasumi:dimen/mtrl_exposed_dropdown_menu_popup_vertical_offset = 0x7f0602a4
mc.meson.kasumi:color/material_dynamic_neutral10 = 0x7f050227
mc.meson.kasumi:style/Base.Widget.AppCompat.ActionButton.CloseMode = 0x7f1200d5
mc.meson.kasumi:color/minecraft_purple_dark = 0x7f0502fe
mc.meson.kasumi:dimen/mtrl_calendar_header_selection_line_height = 0x7f060283
mc.meson.kasumi:style/ThemeOverlay.MaterialComponents.Dialog.Alert = 0x7f1202dd
mc.meson.kasumi:integer/m3_sys_motion_duration_long1 = 0x7f090014
mc.meson.kasumi:dimen/mtrl_calendar_header_height_fullscreen = 0x7f060282
mc.meson.kasumi:dimen/mtrl_calendar_day_height = 0x7f060277
mc.meson.kasumi:attr/route = 0x7f0303de
mc.meson.kasumi:color/dim_foreground_material_dark = 0x7f05005c
mc.meson.kasumi:dimen/mtrl_calendar_action_confirm_button_min_width = 0x7f060271
mc.meson.kasumi:style/ThemeOverlay.Material3.BottomAppBar.Legacy = 0x7f120295
mc.meson.kasumi:style/ShapeAppearanceOverlay.Material3.SearchBar = 0x7f120195
mc.meson.kasumi:dimen/mtrl_btn_z = 0x7f060270
mc.meson.kasumi:dimen/mtrl_extended_fab_start_padding_icon = 0x7f0602b1
mc.meson.kasumi:dimen/mtrl_extended_fab_min_width = 0x7f0602af
mc.meson.kasumi:style/ShapeAppearance.M3.Comp.NavigationDrawer.ActiveIndicator.Shape = 0x7f120169
mc.meson.kasumi:attr/shapeAppearanceSmallComponent = 0x7f0303f9
mc.meson.kasumi:dimen/abc_dialog_fixed_height_minor = 0x7f06001d
mc.meson.kasumi:interpolator/m3_sys_motion_easing_standard_accelerate = 0x7f0a000c
mc.meson.kasumi:dimen/mtrl_btn_text_btn_padding_left = 0x7f06026d
mc.meson.kasumi:styleable/FloatingActionButton = 0x7f130037
mc.meson.kasumi:id/action_bar_title = 0x7f08003a
mc.meson.kasumi:dimen/mtrl_btn_text_btn_icon_padding = 0x7f06026c
mc.meson.kasumi:id/bottom_navigation = 0x7f080071
mc.meson.kasumi:color/ripple_material_dark = 0x7f050347
mc.meson.kasumi:color/m3_ref_palette_neutral24 = 0x7f05010d
mc.meson.kasumi:dimen/mtrl_btn_stroke_size = 0x7f06026b
mc.meson.kasumi:id/chronometer = 0x7f080096
mc.meson.kasumi:drawable/abc_control_background_material = 0x7f07003b
mc.meson.kasumi:attr/centerIfNoTextEnabled = 0x7f0300b2
mc.meson.kasumi:attr/motionEffect_end = 0x7f03036b
mc.meson.kasumi:id/SYM = 0x7f08000b
mc.meson.kasumi:dimen/mtrl_btn_pressed_z = 0x7f060269
mc.meson.kasumi:dimen/mtrl_btn_padding_right = 0x7f060267
mc.meson.kasumi:dimen/mtrl_btn_max_width = 0x7f060264
mc.meson.kasumi:dimen/m3_sys_motion_easing_standard_decelerate_control_x2 = 0x7f060217
mc.meson.kasumi:string/dest_title = 0x7f11003d
mc.meson.kasumi:id/is_pooling_container_tag = 0x7f080117
mc.meson.kasumi:id/reverse = 0x7f0801b6
mc.meson.kasumi:dimen/mtrl_btn_icon_padding = 0x7f060261
mc.meson.kasumi:attr/springStopThreshold = 0x7f030422
mc.meson.kasumi:dimen/mtrl_bottomappbar_fab_cradle_rounded_corner_radius = 0x7f060256
mc.meson.kasumi:dimen/mtrl_bottomappbar_fab_bottom_margin = 0x7f060254
mc.meson.kasumi:attr/windowNoTitle = 0x7f030523
mc.meson.kasumi:dimen/mtrl_bottomappbar_fabOffsetEndMode = 0x7f060253
mc.meson.kasumi:attr/colorSecondaryFixedDim = 0x7f030124
mc.meson.kasumi:dimen/mtrl_badge_with_text_size = 0x7f060252
mc.meson.kasumi:attr/arrowHeadLength = 0x7f03003e
mc.meson.kasumi:id/open_search_view_background = 0x7f08018c
mc.meson.kasumi:attr/bottomSheetDragHandleStyle = 0x7f030081
mc.meson.kasumi:dimen/mtrl_badge_toolbar_action_menu_item_horizontal_offset = 0x7f060250
mc.meson.kasumi:styleable/Badge = 0x7f130016
mc.meson.kasumi:color/material_dynamic_secondary99 = 0x7f050259
mc.meson.kasumi:dimen/m3_ripple_default_alpha = 0x7f0601d5
mc.meson.kasumi:dimen/mtrl_badge_text_horizontal_edge_offset = 0x7f06024e
mc.meson.kasumi:dimen/mtrl_alert_dialog_background_inset_start = 0x7f060248
mc.meson.kasumi:attr/motionEasingAccelerated = 0x7f03035e
mc.meson.kasumi:drawable/abc_spinner_textfield_background_material = 0x7f070067
mc.meson.kasumi:dimen/material_textinput_min_width = 0x7f060243
mc.meson.kasumi:style/Widget.AppCompat.ListPopupWindow = 0x7f120326
mc.meson.kasumi:integer/design_snackbar_text_max_lines = 0x7f090007
mc.meson.kasumi:dimen/m3_navigation_rail_item_padding_bottom_with_large_font = 0x7f0601d1
mc.meson.kasumi:color/m3_sys_color_light_on_error_container = 0x7f0501d8
mc.meson.kasumi:style/Widget.MaterialComponents.TextInputLayout.OutlinedBox.Dense = 0x7f120461
mc.meson.kasumi:attr/snackbarButtonStyle = 0x7f030412
mc.meson.kasumi:dimen/material_helper_text_default_padding_top = 0x7f06023d
mc.meson.kasumi:dimen/material_filled_edittext_font_2_0_padding_top = 0x7f06023a
mc.meson.kasumi:dimen/material_filled_edittext_font_2_0_padding_bottom = 0x7f060239
mc.meson.kasumi:macro/m3_comp_outlined_autocomplete_menu_container_color = 0x7f0c00a0
mc.meson.kasumi:dimen/material_clock_size = 0x7f06022f
mc.meson.kasumi:dimen/material_clock_period_toggle_width = 0x7f06022e
mc.meson.kasumi:id/autoCompleteToStart = 0x7f080066
mc.meson.kasumi:dimen/material_clock_period_toggle_height = 0x7f06022b
mc.meson.kasumi:drawable/$mtrl_switch_thumb_checked_unchecked__0 = 0x7f070022
mc.meson.kasumi:macro/m3_comp_time_picker_period_selector_unselected_label_text_color = 0x7f0c015c
mc.meson.kasumi:dimen/m3_alert_dialog_elevation = 0x7f0600a0
mc.meson.kasumi:dimen/material_clock_hand_padding = 0x7f060228
mc.meson.kasumi:id/honorRequest = 0x7f080103
mc.meson.kasumi:dimen/mtrl_navigation_rail_text_size = 0x7f0602d3
mc.meson.kasumi:dimen/material_clock_display_height = 0x7f060222
mc.meson.kasumi:attr/fastScrollVerticalTrackDrawable = 0x7f0301e3
mc.meson.kasumi:dimen/m3_timepicker_window_elevation = 0x7f06021f
mc.meson.kasumi:string/fab_transformation_sheet_behavior = 0x7f110043
mc.meson.kasumi:dimen/m3_navigation_rail_default_width = 0x7f0601c9
mc.meson.kasumi:attr/dropDownBackgroundTint = 0x7f03019e
mc.meson.kasumi:drawable/abc_item_background_holo_light = 0x7f07004c
mc.meson.kasumi:anim/btn_radio_to_on_mtrl_ring_outer_animation = 0x7f010017
mc.meson.kasumi:dimen/m3_sys_motion_easing_standard_decelerate_control_x1 = 0x7f060216
mc.meson.kasumi:dimen/m3_sys_motion_easing_standard_control_x1 = 0x7f060212
mc.meson.kasumi:dimen/m3_sys_motion_easing_standard_accelerate_control_x2 = 0x7f06020f
mc.meson.kasumi:attr/imageButtonStyle = 0x7f03024a
mc.meson.kasumi:dimen/mtrl_high_ripple_pressed_alpha = 0x7f0602bd
mc.meson.kasumi:dimen/m3_sys_motion_easing_standard_accelerate_control_x1 = 0x7f06020e
mc.meson.kasumi:attr/badgeGravity = 0x7f030057
mc.meson.kasumi:dimen/m3_sys_motion_easing_linear_control_y1 = 0x7f06020c
mc.meson.kasumi:dimen/m3_sys_motion_easing_linear_control_x2 = 0x7f06020b
mc.meson.kasumi:id/welcomeText = 0x7f080264
mc.meson.kasumi:attr/finishSecondaryWithPrimary = 0x7f0301e5
mc.meson.kasumi:id/snackbar_action = 0x7f0801e7
mc.meson.kasumi:dimen/m3_back_progress_bottom_container_max_scale_x_distance = 0x7f0600ac
mc.meson.kasumi:dimen/m3_sys_motion_easing_legacy_control_x1 = 0x7f060202
mc.meson.kasumi:color/material_dynamic_neutral_variant20 = 0x7f050236
mc.meson.kasumi:dimen/m3_sys_motion_easing_legacy_accelerate_control_y1 = 0x7f060200
mc.meson.kasumi:dimen/mtrl_extended_fab_min_height = 0x7f0602ae
mc.meson.kasumi:style/Base.Widget.AppCompat.Button.Colored = 0x7f1200de
mc.meson.kasumi:dimen/mtrl_calendar_dialog_background_inset = 0x7f06027d
mc.meson.kasumi:dimen/m3_sys_motion_easing_legacy_accelerate_control_x1 = 0x7f0601fe
mc.meson.kasumi:color/m3_navigation_item_ripple_color = 0x7f050098
mc.meson.kasumi:dimen/m3_appbar_scrim_height_trigger_large = 0x7f0600a7
mc.meson.kasumi:id/disableIntraAutoTransition = 0x7f0800c0
mc.meson.kasumi:dimen/m3_sys_motion_easing_emphasized_decelerate_control_y2 = 0x7f0601fd
mc.meson.kasumi:bool/abc_action_bar_embed_tabs = 0x7f040000
mc.meson.kasumi:style/ThemeOverlay.Material3.FloatingActionButton.Surface = 0x7f1202b2
mc.meson.kasumi:dimen/m3_sys_elevation_level3 = 0x7f0601f3
mc.meson.kasumi:drawable/splash_gradient = 0x7f070101
mc.meson.kasumi:attr/percentHeight = 0x7f0303a6
mc.meson.kasumi:attr/onTouchUp = 0x7f030390
mc.meson.kasumi:dimen/m3_sys_elevation_level2 = 0x7f0601f2
mc.meson.kasumi:style/Widget.MaterialComponents.Chip.Filter = 0x7f12041c
mc.meson.kasumi:dimen/m3_comp_outlined_text_field_disabled_supporting_text_opacity = 0x7f060157
mc.meson.kasumi:dimen/m3_sys_elevation_level0 = 0x7f0601f0
mc.meson.kasumi:attr/colorControlNormal = 0x7f0300ff
mc.meson.kasumi:color/material_personalized_color_error_container = 0x7f050283
mc.meson.kasumi:dimen/m3_snackbar_action_text_color_alpha = 0x7f0601ee
mc.meson.kasumi:dimen/m3_simple_item_color_selected_alpha = 0x7f0601ea
mc.meson.kasumi:style/Widget.AppCompat.TextView = 0x7f12033a
mc.meson.kasumi:color/material_dynamic_tertiary40 = 0x7f05025f
mc.meson.kasumi:macro/m3_comp_assist_chip_label_text_type = 0x7f0c0001
mc.meson.kasumi:dimen/m3_side_sheet_modal_elevation = 0x7f0601e6
mc.meson.kasumi:color/material_dynamic_secondary20 = 0x7f050250
mc.meson.kasumi:dimen/m3_searchview_height = 0x7f0601e4
mc.meson.kasumi:style/TextAppearance.MaterialComponents.Button = 0x7f120207
mc.meson.kasumi:attr/switchPadding = 0x7f03044d
mc.meson.kasumi:color/md_theme_dark_surfaceVariant = 0x7f0502d6
mc.meson.kasumi:dimen/m3_searchbar_text_size = 0x7f0601e1
mc.meson.kasumi:id/cut = 0x7f0800af
mc.meson.kasumi:attr/materialCalendarMonth = 0x7f030316
mc.meson.kasumi:dimen/m3_back_progress_main_container_max_translation_y = 0x7f0600ae
mc.meson.kasumi:styleable/ActionBar = 0x7f130000
mc.meson.kasumi:style/Theme.MaterialComponents.Light.NoActionBar.Bridge = 0x7f120280
mc.meson.kasumi:dimen/m3_searchbar_padding_start = 0x7f0601df
mc.meson.kasumi:style/Widget.Material3.NavigationRailView = 0x7f1203c9
mc.meson.kasumi:style/Base.Theme.MaterialComponents.CompactMenu = 0x7f120074
mc.meson.kasumi:dimen/m3_searchbar_outlined_stroke_width = 0x7f0601de
mc.meson.kasumi:dimen/m3_ripple_selectable_pressed_alpha = 0x7f0601d9
mc.meson.kasumi:dimen/m3_comp_primary_navigation_tab_with_icon_icon_size = 0x7f060161
mc.meson.kasumi:dimen/m3_navigation_rail_label_padding_horizontal = 0x7f0601d4
mc.meson.kasumi:dimen/m3_sys_motion_easing_standard_control_y2 = 0x7f060215
mc.meson.kasumi:dimen/m3_navigation_rail_item_padding_top_with_large_font = 0x7f0601d3
mc.meson.kasumi:style/Widget.MaterialComponents.TimePicker.Display.HelperText = 0x7f12046a
mc.meson.kasumi:dimen/m3_navigation_rail_item_padding_top = 0x7f0601d2
mc.meson.kasumi:dimen/m3_navigation_rail_item_active_indicator_width = 0x7f0601ce
mc.meson.kasumi:id/contentPanel = 0x7f0800a5
mc.meson.kasumi:color/m3_ref_palette_dynamic_neutral_variant50 = 0x7f0500c5
mc.meson.kasumi:dimen/m3_navigation_item_icon_padding = 0x7f0601c1
mc.meson.kasumi:style/TextAppearance.AppCompat.Large.Inverse = 0x7f1201ac
mc.meson.kasumi:color/button_material_light = 0x7f05002a
mc.meson.kasumi:dimen/m3_navigation_drawer_layout_corner_size = 0x7f0601be
mc.meson.kasumi:color/material_timepicker_modebutton_tint = 0x7f0502ba
mc.meson.kasumi:dimen/m3_large_text_vertical_offset_adjustment = 0x7f0601bb
mc.meson.kasumi:animator/m3_btn_state_list_anim = 0x7f02000b
mc.meson.kasumi:dimen/m3_extended_fab_top_padding = 0x7f0601b4
mc.meson.kasumi:attr/behavior_expandedOffset = 0x7f03006f
mc.meson.kasumi:dimen/m3_extended_fab_icon_padding = 0x7f0601b1
mc.meson.kasumi:dimen/m3_navigation_rail_item_active_indicator_margin_horizontal = 0x7f0601cd
mc.meson.kasumi:id/beginning = 0x7f08006c
mc.meson.kasumi:dimen/m3_btn_icon_only_icon_padding = 0x7f0600d5
mc.meson.kasumi:dimen/mtrl_calendar_year_corner = 0x7f060296
mc.meson.kasumi:dimen/m3_divider_heavy_thickness = 0x7f0601ae
mc.meson.kasumi:color/m3_sys_color_dynamic_dark_on_tertiary = 0x7f05018f
mc.meson.kasumi:style/Widget.AppCompat.DrawerArrowToggle = 0x7f12030b
mc.meson.kasumi:attr/layout_editor_absoluteY = 0x7f0302bf
mc.meson.kasumi:dimen/mtrl_alert_dialog_background_inset_top = 0x7f060249
mc.meson.kasumi:attr/errorTextColor = 0x7f0301c1
mc.meson.kasumi:dimen/m3_comp_top_app_bar_small_container_elevation = 0x7f0601aa
mc.meson.kasumi:style/ThemeOverlay.MaterialComponents.MaterialAlertDialog.Picker.Date = 0x7f1202e3
mc.meson.kasumi:style/TextAppearance.AppCompat.Title = 0x7f1201ba
mc.meson.kasumi:dimen/m3_comp_top_app_bar_medium_container_height = 0x7f0601a9
mc.meson.kasumi:dimen/m3_comp_top_app_bar_large_container_height = 0x7f0601a8
mc.meson.kasumi:color/design_fab_stroke_end_inner_color = 0x7f050054
mc.meson.kasumi:id/buttonPanel = 0x7f080084
mc.meson.kasumi:color/m3_sys_color_dynamic_light_tertiary_container = 0x7f0501c3
mc.meson.kasumi:style/Widget.Material3.FloatingActionButton.Large.Secondary = 0x7f120398
mc.meson.kasumi:dimen/design_bottom_navigation_margin = 0x7f060068
mc.meson.kasumi:style/Theme.MaterialComponents.DayNight.DarkActionBar = 0x7f12025b
mc.meson.kasumi:dimen/m3_comp_time_picker_time_selector_focus_state_layer_opacity = 0x7f0601a5
mc.meson.kasumi:dimen/m3_comp_time_picker_container_elevation = 0x7f0601a0
mc.meson.kasumi:dimen/mtrl_progress_track_thickness = 0x7f0602e1
mc.meson.kasumi:styleable/KeyCycle = 0x7f130046
mc.meson.kasumi:dimen/m3_comp_time_input_time_input_field_focus_outline_width = 0x7f06019f
mc.meson.kasumi:dimen/m3_comp_switch_unselected_hover_state_layer_opacity = 0x7f06019a
mc.meson.kasumi:dimen/m3_comp_switch_unselected_focus_state_layer_opacity = 0x7f060199
mc.meson.kasumi:dimen/m3_sys_motion_easing_emphasized_accelerate_control_x2 = 0x7f0601f7
mc.meson.kasumi:id/edge = 0x7f0800d2
mc.meson.kasumi:dimen/m3_comp_switch_disabled_unselected_icon_opacity = 0x7f060193
mc.meson.kasumi:drawable/mtrl_switch_thumb_pressed = 0x7f0700e8
mc.meson.kasumi:dimen/m3_comp_suggestion_chip_with_leading_icon_leading_icon_size = 0x7f06018e
mc.meson.kasumi:attr/floatingActionButtonSecondaryStyle = 0x7f0301ed
mc.meson.kasumi:dimen/m3_comp_suggestion_chip_flat_container_elevation = 0x7f06018c
mc.meson.kasumi:id/actionDownUp = 0x7f080032
mc.meson.kasumi:macro/m3_comp_time_picker_time_selector_selected_container_color = 0x7f0c0160
mc.meson.kasumi:id/material_clock_period_toggle = 0x7f08013c
mc.meson.kasumi:dimen/m3_comp_suggestion_chip_elevated_container_elevation = 0x7f06018b
mc.meson.kasumi:style/Widget.Material3.MaterialCalendar.HeaderLayout = 0x7f1203b1
mc.meson.kasumi:id/disableScroll = 0x7f0800c2
mc.meson.kasumi:dimen/m3_comp_suggestion_chip_container_height = 0x7f06018a
mc.meson.kasumi:attr/colorSecondaryContainer = 0x7f030122
mc.meson.kasumi:dimen/m3_comp_slider_inactive_track_height = 0x7f060187
mc.meson.kasumi:attr/strokeColor = 0x7f03043b
mc.meson.kasumi:dimen/m3_comp_sheet_side_docked_modal_container_elevation = 0x7f06017f
mc.meson.kasumi:dimen/m3_comp_sheet_bottom_docked_standard_container_elevation = 0x7f06017d
mc.meson.kasumi:style/Widget.Material3.CardView.Outlined = 0x7f120372
mc.meson.kasumi:dimen/highlight_alpha_material_light = 0x7f060095
mc.meson.kasumi:attr/passwordToggleTint = 0x7f0303a2
mc.meson.kasumi:dimen/m3_comp_sheet_bottom_docked_drag_handle_width = 0x7f06017b
mc.meson.kasumi:drawable/abc_scrubber_control_to_pressed_mtrl_000 = 0x7f07005f
mc.meson.kasumi:dimen/m3_comp_search_bar_container_elevation = 0x7f06016f
mc.meson.kasumi:dimen/abc_text_size_body_2_material = 0x7f060040
mc.meson.kasumi:id/notification_background = 0x7f080185
mc.meson.kasumi:dimen/m3_comp_radio_button_unselected_pressed_state_layer_opacity = 0x7f06016c
mc.meson.kasumi:layout/fragment_config_management = 0x7f0b0033
mc.meson.kasumi:dimen/m3_comp_radio_button_selected_pressed_state_layer_opacity = 0x7f060169
mc.meson.kasumi:dimen/m3_appbar_size_large = 0x7f0600aa
mc.meson.kasumi:dimen/mtrl_switch_track_height = 0x7f0602fa
mc.meson.kasumi:dimen/m3_comp_radio_button_selected_hover_state_layer_opacity = 0x7f060168
mc.meson.kasumi:macro/m3_comp_primary_navigation_tab_active_pressed_state_layer_color = 0x7f0c00ca
mc.meson.kasumi:dimen/m3_comp_radio_button_disabled_unselected_icon_opacity = 0x7f060166
mc.meson.kasumi:integer/m3_btn_anim_duration_ms = 0x7f09000c
mc.meson.kasumi:color/m3_sys_color_dynamic_dark_surface_container_lowest = 0x7f05019d
mc.meson.kasumi:dimen/m3_comp_radio_button_disabled_selected_icon_opacity = 0x7f060165
mc.meson.kasumi:attr/errorAccessibilityLabel = 0x7f0301b8
mc.meson.kasumi:dimen/m3_comp_progress_indicator_stop_indicator_size = 0x7f060163
mc.meson.kasumi:dimen/compat_control_corner_material = 0x7f06005a
mc.meson.kasumi:styleable/SwitchMaterial = 0x7f130098
mc.meson.kasumi:style/Widget.MaterialComponents.AutoCompleteTextView.OutlinedBox = 0x7f120400
mc.meson.kasumi:dimen/m3_comp_switch_selected_focus_state_layer_opacity = 0x7f060194
mc.meson.kasumi:dimen/m3_comp_primary_navigation_tab_inactive_focus_state_layer_opacity = 0x7f06015e
mc.meson.kasumi:dimen/m3_comp_outlined_card_disabled_outline_opacity = 0x7f060151
mc.meson.kasumi:id/always = 0x7f080056
mc.meson.kasumi:drawable/mtrl_switch_thumb_unchecked_pressed = 0x7f0700ed
mc.meson.kasumi:attr/enforceTextAppearance = 0x7f0301b5
mc.meson.kasumi:dimen/m3_comp_outlined_button_outline_width = 0x7f06014f
mc.meson.kasumi:attr/flow_verticalAlign = 0x7f030204
mc.meson.kasumi:dimen/m3_comp_outlined_button_disabled_outline_opacity = 0x7f06014e
mc.meson.kasumi:macro/m3_comp_radio_button_selected_focus_icon_color = 0x7f0c00d8
mc.meson.kasumi:dimen/mtrl_calendar_pre_l_text_clip_padding = 0x7f06028e
mc.meson.kasumi:dimen/abc_button_padding_vertical_material = 0x7f060015
mc.meson.kasumi:dimen/m3_comp_navigation_rail_active_indicator_width = 0x7f060146
mc.meson.kasumi:dimen/m3_comp_navigation_drawer_standard_container_elevation = 0x7f060144
mc.meson.kasumi:attr/itemTextColor = 0x7f030279
mc.meson.kasumi:color/m3_primary_text_disable_only = 0x7f05009e
mc.meson.kasumi:string/mtrl_picker_save = 0x7f1100a3
mc.meson.kasumi:dimen/item_touch_helper_swipe_escape_velocity = 0x7f06009c
mc.meson.kasumi:dimen/m3_comp_navigation_drawer_pressed_state_layer_opacity = 0x7f060143
mc.meson.kasumi:dimen/m3_comp_navigation_drawer_modal_container_elevation = 0x7f060142
mc.meson.kasumi:id/btnFloatingWindow = 0x7f080080
mc.meson.kasumi:attr/targetId = 0x7f03046d
mc.meson.kasumi:dimen/m3_comp_filled_card_icon_size = 0x7f060128
mc.meson.kasumi:dimen/m3_comp_slider_active_handle_leading_space = 0x7f060182
mc.meson.kasumi:color/md_theme_light_inverseOnSurface = 0x7f0502dc
mc.meson.kasumi:dimen/m3_comp_time_picker_period_selector_outline_width = 0x7f0601a3
mc.meson.kasumi:color/abc_primary_text_material_dark = 0x7f05000b
mc.meson.kasumi:dimen/m3_comp_navigation_bar_pressed_state_layer_opacity = 0x7f06013d
mc.meson.kasumi:macro/m3_comp_radio_button_disabled_unselected_icon_color = 0x7f0c00d7
mc.meson.kasumi:dimen/m3_comp_navigation_bar_focus_state_layer_opacity = 0x7f06013a
mc.meson.kasumi:macro/m3_comp_filled_tonal_icon_button_toggle_selected_icon_color = 0x7f0c0055
mc.meson.kasumi:style/Widget.MaterialComponents.CompoundButton.Switch = 0x7f120425
mc.meson.kasumi:style/Widget.Material3.MaterialCalendar.YearNavigationButton = 0x7f1203bd
mc.meson.kasumi:dimen/m3_comp_filled_card_pressed_state_layer_opacity = 0x7f060129
mc.meson.kasumi:dimen/m3_comp_filled_card_focus_state_layer_opacity = 0x7f060126
mc.meson.kasumi:dimen/m3_comp_filled_card_dragged_state_layer_opacity = 0x7f060125
mc.meson.kasumi:dimen/m3_comp_fab_primary_small_icon_size = 0x7f060120
mc.meson.kasumi:style/RtlOverlay.Widget.AppCompat.PopupMenuItem = 0x7f120154
mc.meson.kasumi:dimen/m3_comp_fab_primary_icon_size = 0x7f06011a
mc.meson.kasumi:dimen/m3_comp_extended_fab_primary_pressed_state_layer_opacity = 0x7f060114
mc.meson.kasumi:dimen/m3_comp_extended_fab_primary_hover_container_elevation = 0x7f060110
mc.meson.kasumi:drawable/ic_expand_more = 0x7f0700a2
mc.meson.kasumi:dimen/m3_comp_extended_fab_primary_focus_container_elevation = 0x7f06010e
mc.meson.kasumi:drawable/btn_checkbox_unchecked_to_checked_mtrl_animation = 0x7f07007d
mc.meson.kasumi:style/Widget.MaterialComponents.ActionBar.Primary = 0x7f1203f6
mc.meson.kasumi:style/Widget.MaterialComponents.MaterialCalendar.Year.Selected = 0x7f120441
mc.meson.kasumi:dimen/m3_comp_divider_thickness = 0x7f060107
mc.meson.kasumi:style/Base.ThemeOverlay.MaterialComponents.Dialog.Alert.Framework = 0x7f120093
mc.meson.kasumi:attr/checkedIconEnabled = 0x7f0300bb
mc.meson.kasumi:color/md_theme_dark_primary = 0x7f0502ce
mc.meson.kasumi:dimen/m3_comp_date_picker_modal_range_selection_header_container_height = 0x7f060106
mc.meson.kasumi:style/TextAppearance.Design.Error = 0x7f1201d8
mc.meson.kasumi:color/m3_sys_color_light_outline = 0x7f0501e1
mc.meson.kasumi:dimen/design_bottom_navigation_active_item_max_width = 0x7f06005f
mc.meson.kasumi:dimen/m3_comp_checkbox_selected_disabled_container_opacity = 0x7f060103
mc.meson.kasumi:dimen/m3_comp_bottom_app_bar_container_height = 0x7f060102
mc.meson.kasumi:dimen/m3_sys_motion_easing_legacy_accelerate_control_x2 = 0x7f0601ff
mc.meson.kasumi:attr/layout_constraintHeight_percent = 0x7f0302a5
mc.meson.kasumi:dimen/mtrl_progress_circular_size = 0x7f0602d9
mc.meson.kasumi:drawable/tooltip_frame_light = 0x7f070109
mc.meson.kasumi:dimen/m3_comp_bottom_app_bar_container_elevation = 0x7f060101
mc.meson.kasumi:style/Widget.MaterialComponents.TimePicker.Display.Divider = 0x7f120469
mc.meson.kasumi:style/Theme.MaterialComponents.DayNight.Dialog = 0x7f12025d
mc.meson.kasumi:style/Base.ThemeOverlay.Material3.TextInputEditText = 0x7f120090
mc.meson.kasumi:attr/thumbWidth = 0x7f0304c3
mc.meson.kasumi:dimen/m3_comp_assist_chip_with_icon_icon_size = 0x7f0600fe
mc.meson.kasumi:attr/layout_constraintVertical_bias = 0x7f0302b5
mc.meson.kasumi:dimen/m3_comp_assist_chip_elevated_container_elevation = 0x7f0600fb
mc.meson.kasumi:style/Widget.Material3.Chip.Filter.Elevated = 0x7f120377
mc.meson.kasumi:dimen/m3_comp_assist_chip_container_height = 0x7f0600fa
mc.meson.kasumi:style/Base.TextAppearance.AppCompat.SearchResult = 0x7f120035
mc.meson.kasumi:dimen/m3_comp_switch_disabled_selected_handle_opacity = 0x7f06018f
mc.meson.kasumi:dimen/m3_comp_extended_fab_primary_hover_state_layer_opacity = 0x7f060111
mc.meson.kasumi:macro/m3_comp_filled_card_container_color = 0x7f0c0046
mc.meson.kasumi:id/enterAlwaysCollapsed = 0x7f0800db
mc.meson.kasumi:dimen/m3_ripple_hovered_alpha = 0x7f0601d7
mc.meson.kasumi:color/m3_ref_palette_dynamic_neutral_variant70 = 0x7f0500c8
mc.meson.kasumi:dimen/m3_chip_hovered_translation_z = 0x7f0600f8
mc.meson.kasumi:style/Widget.Material3.CircularProgressIndicator.ExtraSmall = 0x7f120380
mc.meson.kasumi:macro/m3_comp_primary_navigation_tab_with_icon_active_icon_color = 0x7f0c00cf
mc.meson.kasumi:dimen/m3_chip_disabled_translation_z = 0x7f0600f5
mc.meson.kasumi:layout/design_navigation_menu = 0x7f0b002c
mc.meson.kasumi:dimen/m3_carousel_debug_keyline_width = 0x7f0600ed
mc.meson.kasumi:drawable/$mtrl_switch_thumb_pressed_unchecked__0 = 0x7f070025
mc.meson.kasumi:dimen/m3_card_hovered_z = 0x7f0600eb
mc.meson.kasumi:dimen/m3_carousel_small_item_size_max = 0x7f0600f1
mc.meson.kasumi:animator/mtrl_btn_state_list_anim = 0x7f020015
mc.meson.kasumi:dimen/m3_card_elevated_elevation = 0x7f0600e8
mc.meson.kasumi:style/TextAppearance.AppCompat.Widget.TextView.SpinnerItem = 0x7f1201cf
mc.meson.kasumi:dimen/design_bottom_navigation_elevation = 0x7f060062
mc.meson.kasumi:dimen/m3_card_elevated_dragged_z = 0x7f0600e7
mc.meson.kasumi:dimen/m3_card_dragged_z = 0x7f0600e5
mc.meson.kasumi:attr/trackStopIndicatorSize = 0x7f0304f6
mc.meson.kasumi:dimen/m3_btn_translation_z_base = 0x7f0600e2
mc.meson.kasumi:id/indeterminate = 0x7f08010e
mc.meson.kasumi:style/Base.Widget.MaterialComponents.PopupMenu = 0x7f120123
mc.meson.kasumi:dimen/m3_btn_padding_right = 0x7f0600db
mc.meson.kasumi:style/Animation.AppCompat.DropDownUp = 0x7f120003
mc.meson.kasumi:dimen/m3_btn_padding_bottom = 0x7f0600d9
mc.meson.kasumi:style/TextAppearance.M3.Sys.Typescale.TitleMedium = 0x7f1201ed
mc.meson.kasumi:style/Widget.Design.TabLayout = 0x7f120348
mc.meson.kasumi:anim/btn_radio_to_off_mtrl_ring_outer_animation = 0x7f010014
mc.meson.kasumi:styleable/MaterialToolbar = 0x7f130064
mc.meson.kasumi:dimen/m3_btn_icon_only_min_width = 0x7f0600d6
mc.meson.kasumi:dimen/m3_comp_suggestion_chip_flat_outline_width = 0x7f06018d
mc.meson.kasumi:dimen/m3_btn_icon_only_default_size = 0x7f0600d4
mc.meson.kasumi:style/Theme.MaterialComponents.Dialog.MinWidth.Bridge = 0x7f12026f
mc.meson.kasumi:dimen/m3_btn_icon_only_default_padding = 0x7f0600d3
mc.meson.kasumi:dimen/m3_btn_disabled_translation_z = 0x7f0600ce
mc.meson.kasumi:attr/barrierMargin = 0x7f03006b
mc.meson.kasumi:dimen/m3_btn_disabled_elevation = 0x7f0600cd
mc.meson.kasumi:attr/carousel_touchUp_dampeningFactor = 0x7f0300b0
mc.meson.kasumi:dimen/m3_btn_dialog_btn_spacing = 0x7f0600cc
mc.meson.kasumi:color/m3_ref_palette_dynamic_primary40 = 0x7f0500d7
mc.meson.kasumi:id/accessibility_custom_action_15 = 0x7f080018
mc.meson.kasumi:dimen/m3_btn_dialog_btn_min_width = 0x7f0600cb
mc.meson.kasumi:dimen/m3_bottomappbar_height = 0x7f0600c9
mc.meson.kasumi:color/m3_sys_color_dark_on_secondary = 0x7f050169
mc.meson.kasumi:dimen/m3_bottomappbar_fab_end_margin = 0x7f0600c8
mc.meson.kasumi:drawable/abc_menu_hardkey_panel_mtrl_mult = 0x7f070059
mc.meson.kasumi:dimen/m3_comp_primary_navigation_tab_active_pressed_state_layer_opacity = 0x7f06015d
mc.meson.kasumi:dimen/m3_bottomappbar_fab_cradle_margin = 0x7f0600c5
mc.meson.kasumi:styleable/CompoundButton = 0x7f130028
mc.meson.kasumi:style/Platform.ThemeOverlay.AppCompat.Dark = 0x7f12014a
mc.meson.kasumi:color/m3_sys_color_dynamic_light_on_primary_container = 0x7f0501ac
mc.meson.kasumi:dimen/notification_subtext_size = 0x7f060318
mc.meson.kasumi:dimen/mtrl_calendar_header_content_padding_fullscreen = 0x7f06027f
mc.meson.kasumi:dimen/m3_comp_extended_fab_primary_icon_size = 0x7f060112
mc.meson.kasumi:dimen/m3_navigation_menu_headline_horizontal_padding = 0x7f0601c8
mc.meson.kasumi:dimen/m3_bottom_nav_item_active_indicator_margin_horizontal = 0x7f0600bd
mc.meson.kasumi:dimen/m3_comp_navigation_rail_focus_state_layer_opacity = 0x7f060149
mc.meson.kasumi:color/md_theme_dark_tertiary = 0x7f0502d7
mc.meson.kasumi:dimen/m3_sys_motion_easing_emphasized_accelerate_control_y1 = 0x7f0601f8
mc.meson.kasumi:color/switch_thumb_disabled_material_dark = 0x7f05034d
mc.meson.kasumi:style/TextAppearance.Material3.SearchBar = 0x7f1201fe
mc.meson.kasumi:attr/chipSurfaceColor = 0x7f0300d6
mc.meson.kasumi:dimen/m3_badge_with_text_vertical_padding = 0x7f0600bb
mc.meson.kasumi:id/recyclerViewModules = 0x7f0801b2
mc.meson.kasumi:color/m3_sys_color_dynamic_light_outline_variant = 0x7f0501b4
mc.meson.kasumi:attr/flow_horizontalStyle = 0x7f0301fd
mc.meson.kasumi:dimen/m3_badge_with_text_vertical_offset = 0x7f0600ba
mc.meson.kasumi:attr/boxStrokeWidth = 0x7f03008c
mc.meson.kasumi:integer/material_motion_duration_medium_2 = 0x7f09002a
mc.meson.kasumi:dimen/m3_badge_horizontal_offset = 0x7f0600b3
mc.meson.kasumi:style/Widget.Material3.BottomAppBar = 0x7f120354
mc.meson.kasumi:dimen/mtrl_fab_min_touch_target = 0x7f0602b7
mc.meson.kasumi:drawable/m3_bottom_sheet_drag_handle = 0x7f0700b8
mc.meson.kasumi:dimen/m3_appbar_size_medium = 0x7f0600ab
mc.meson.kasumi:dimen/m3_appbar_size_compact = 0x7f0600a9
mc.meson.kasumi:string/abc_action_bar_up_description = 0x7f110001
mc.meson.kasumi:color/m3_sys_color_secondary_fixed = 0x7f0501fa
mc.meson.kasumi:style/Theme.AppCompat.Light.DialogWhenLarge = 0x7f12022b
mc.meson.kasumi:style/Widget.AppCompat.ListMenuView = 0x7f120325
mc.meson.kasumi:dimen/m3_appbar_scrim_height_trigger_medium = 0x7f0600a8
mc.meson.kasumi:dimen/material_clock_number_text_size = 0x7f06022a
mc.meson.kasumi:dimen/m3_appbar_scrim_height_trigger = 0x7f0600a6
mc.meson.kasumi:style/ShapeAppearanceOverlay.Material3.Corner.Right = 0x7f120191
mc.meson.kasumi:dimen/m3_appbar_expanded_title_margin_bottom = 0x7f0600a4
mc.meson.kasumi:style/Widget.AppCompat.Button = 0x7f120300
mc.meson.kasumi:id/matrix = 0x7f080149
mc.meson.kasumi:dimen/m3_alert_dialog_corner_size = 0x7f06009f
mc.meson.kasumi:attr/colorPrimaryDark = 0x7f03011b
mc.meson.kasumi:dimen/m3_alert_dialog_action_bottom_padding = 0x7f06009d
mc.meson.kasumi:dimen/abc_alert_dialog_button_dimen = 0x7f060011
mc.meson.kasumi:style/TextAppearance.Material3.ActionBar.Subtitle = 0x7f1201ef
mc.meson.kasumi:dimen/item_touch_helper_max_drag_scroll_per_frame = 0x7f06009a
mc.meson.kasumi:style/MaterialAlertDialog.MaterialComponents.Title.Text = 0x7f120141
mc.meson.kasumi:attr/colorSwitchThumbNormal = 0x7f030130
mc.meson.kasumi:dimen/hint_pressed_alpha_material_light = 0x7f060099
mc.meson.kasumi:style/Widget.AppCompat.Light.ActivityChooserView = 0x7f12031c
mc.meson.kasumi:dimen/hint_pressed_alpha_material_dark = 0x7f060098
mc.meson.kasumi:style/Base.Widget.Material3.CardView = 0x7f12010d
mc.meson.kasumi:dimen/m3_comp_fab_primary_small_container_height = 0x7f06011f
mc.meson.kasumi:dimen/highlight_alpha_material_dark = 0x7f060094
mc.meson.kasumi:attr/listChoiceIndicatorMultipleAnimated = 0x7f0302d7
mc.meson.kasumi:dimen/fastscroll_margin = 0x7f060091
mc.meson.kasumi:dimen/fastscroll_default_thickness = 0x7f060090
mc.meson.kasumi:style/Base.Widget.AppCompat.DropDownItem.Spinner = 0x7f1200e7
mc.meson.kasumi:id/radio = 0x7f0801ae
mc.meson.kasumi:dimen/m3_sys_motion_easing_legacy_control_y2 = 0x7f060205
mc.meson.kasumi:styleable/SearchView = 0x7f130088
mc.meson.kasumi:attr/colorOnTertiaryFixedVariant = 0x7f030116
mc.meson.kasumi:dimen/disabled_alpha_material_light = 0x7f06008f
mc.meson.kasumi:macro/m3_comp_time_picker_time_selector_unselected_container_color = 0x7f0c0167
mc.meson.kasumi:dimen/design_textinput_caption_translate_y = 0x7f06008d
mc.meson.kasumi:dimen/design_tab_text_size_2line = 0x7f06008c
mc.meson.kasumi:color/material_personalized_color_surface_container_highest = 0x7f05029f
mc.meson.kasumi:dimen/design_tab_max_width = 0x7f060089
mc.meson.kasumi:dimen/design_snackbar_padding_vertical_2lines = 0x7f060087
mc.meson.kasumi:id/restart = 0x7f0801b5
mc.meson.kasumi:dimen/design_snackbar_padding_vertical = 0x7f060086
mc.meson.kasumi:dimen/design_snackbar_action_text_color_alpha = 0x7f06007f
mc.meson.kasumi:id/standard = 0x7f0801f8
mc.meson.kasumi:dimen/design_snackbar_action_inline_max_width = 0x7f06007e
mc.meson.kasumi:attr/reactiveGuide_applyToConstraintSet = 0x7f0303d0
mc.meson.kasumi:anim/abc_tooltip_exit = 0x7f01000b
mc.meson.kasumi:style/Widget.Material3.CardView.Filled = 0x7f120371
mc.meson.kasumi:dimen/design_navigation_separator_vertical_padding = 0x7f06007d
mc.meson.kasumi:drawable/ic_check_circle_24 = 0x7f07009b
mc.meson.kasumi:dimen/design_navigation_padding_bottom = 0x7f06007c
mc.meson.kasumi:dimen/design_navigation_item_horizontal_padding = 0x7f060078
mc.meson.kasumi:styleable/NavigationBarActiveIndicator = 0x7f130077
mc.meson.kasumi:dimen/design_navigation_icon_size = 0x7f060077
mc.meson.kasumi:styleable/ImageFilterView = 0x7f130043
mc.meson.kasumi:dimen/design_fab_translation_z_pressed = 0x7f060074
mc.meson.kasumi:dimen/design_fab_size_normal = 0x7f060072
mc.meson.kasumi:dimen/design_bottom_navigation_shadow_height = 0x7f060069
mc.meson.kasumi:color/m3_sys_color_dynamic_light_secondary_container = 0x7f0501b8
mc.meson.kasumi:style/RtlOverlay.Widget.AppCompat.PopupMenuItem.Shortcut = 0x7f120156
mc.meson.kasumi:dimen/design_bottom_navigation_item_max_width = 0x7f060065
mc.meson.kasumi:dimen/notification_big_circle_margin = 0x7f06030e
mc.meson.kasumi:color/md_theme_dark_onSecondaryContainer = 0x7f0502c7
mc.meson.kasumi:dimen/mtrl_extended_fab_disabled_translation_z = 0x7f0602a8
mc.meson.kasumi:dimen/design_bottom_navigation_icon_size = 0x7f060064
mc.meson.kasumi:dimen/compat_notification_large_icon_max_width = 0x7f06005c
mc.meson.kasumi:macro/m3_comp_navigation_bar_active_pressed_state_layer_color = 0x7f0c006a
mc.meson.kasumi:dimen/m3_alert_dialog_icon_margin = 0x7f0600a1
mc.meson.kasumi:attr/layout_goneMarginTop = 0x7f0302c6
mc.meson.kasumi:dimen/compat_notification_large_icon_max_height = 0x7f06005b
mc.meson.kasumi:style/Widget.Material3.MaterialCalendar.Day.Selected = 0x7f1203aa
mc.meson.kasumi:styleable/ViewPager2 = 0x7f1300a7
mc.meson.kasumi:dimen/clock_face_margin_start = 0x7f060055
mc.meson.kasumi:dimen/cardview_compat_inset_shadow = 0x7f060052
mc.meson.kasumi:style/Base.V7.Theme.AppCompat = 0x7f1200c7
mc.meson.kasumi:dimen/appcompat_dialog_background_inset = 0x7f060051
mc.meson.kasumi:style/Widget.MaterialComponents.MaterialCalendar.Day = 0x7f12042d
mc.meson.kasumi:dimen/abc_text_size_title_material = 0x7f06004f
mc.meson.kasumi:color/m3_sys_color_dynamic_light_on_secondary = 0x7f0501ad
mc.meson.kasumi:attr/motionDurationLong2 = 0x7f030353
mc.meson.kasumi:dimen/abc_text_size_subhead_material = 0x7f06004d
mc.meson.kasumi:dimen/abc_text_size_small_material = 0x7f06004c
mc.meson.kasumi:dimen/abc_text_size_medium_material = 0x7f060049
mc.meson.kasumi:attr/shapeAppearanceCornerExtraSmall = 0x7f0303f2
mc.meson.kasumi:id/appSubtitle = 0x7f08005f
mc.meson.kasumi:dimen/abc_text_size_display_4_material = 0x7f060046
mc.meson.kasumi:id/textinput_error = 0x7f080226
mc.meson.kasumi:dimen/abc_text_size_display_3_material = 0x7f060045
mc.meson.kasumi:style/Theme.Material3.Light.DialogWhenLarge = 0x7f120251
mc.meson.kasumi:dimen/m3_sys_elevation_level1 = 0x7f0601f1
mc.meson.kasumi:attr/iconStartPadding = 0x7f030244
mc.meson.kasumi:dimen/abc_text_size_display_2_material = 0x7f060044
mc.meson.kasumi:layout/abc_list_menu_item_layout = 0x7f0b0010
mc.meson.kasumi:macro/m3_comp_sheet_bottom_docked_container_shape = 0x7f0c0105
mc.meson.kasumi:dimen/abc_text_size_display_1_material = 0x7f060043
mc.meson.kasumi:style/Widget.Material3.ExtendedFloatingActionButton.Icon.Surface = 0x7f120391
mc.meson.kasumi:id/navigation_bar_item_active_indicator_view = 0x7f080176
mc.meson.kasumi:dimen/m3_comp_fab_primary_focus_state_layer_opacity = 0x7f060117
mc.meson.kasumi:style/Widget.AppCompat.Light.PopupMenu.Overflow = 0x7f120322
mc.meson.kasumi:drawable/$mtrl_checkbox_button_icon_unchecked_checked__1 = 0x7f070019
mc.meson.kasumi:dimen/abc_text_size_button_material = 0x7f060041
mc.meson.kasumi:dimen/mtrl_badge_horizontal_edge_offset = 0x7f06024b
mc.meson.kasumi:color/m3_tabs_text_color_secondary = 0x7f050203
mc.meson.kasumi:dimen/abc_text_size_body_1_material = 0x7f06003f
mc.meson.kasumi:attr/iconifiedByDefault = 0x7f030247
mc.meson.kasumi:dimen/abc_progress_bar_height_material = 0x7f060035
mc.meson.kasumi:dimen/m3_comp_top_app_bar_small_container_height = 0x7f0601ab
mc.meson.kasumi:attr/tabIconTintMode = 0x7f030454
mc.meson.kasumi:dimen/abc_panel_menu_list_width = 0x7f060034
mc.meson.kasumi:dimen/mtrl_calendar_year_height = 0x7f060297
mc.meson.kasumi:dimen/abc_list_item_height_material = 0x7f060031
mc.meson.kasumi:attr/motionDurationShort3 = 0x7f03035c
mc.meson.kasumi:dimen/material_clock_display_width = 0x7f060224
mc.meson.kasumi:styleable/MenuGroup = 0x7f130065
mc.meson.kasumi:dimen/abc_floating_window_z = 0x7f06002f
mc.meson.kasumi:dimen/compat_button_inset_horizontal_material = 0x7f060056
mc.meson.kasumi:style/Base.Widget.AppCompat.PopupMenu.Overflow = 0x7f1200f8
mc.meson.kasumi:drawable/ic_call_answer_low = 0x7f070096
mc.meson.kasumi:color/mtrl_text_btn_text_color_selector = 0x7f050334
mc.meson.kasumi:dimen/tooltip_corner_radius = 0x7f06031c
mc.meson.kasumi:attr/textAppearanceListItemSecondary = 0x7f03048c
mc.meson.kasumi:attr/thumbColor = 0x7f0304b5
mc.meson.kasumi:dimen/abc_edit_text_inset_horizontal_material = 0x7f06002d
mc.meson.kasumi:dimen/abc_dropdownitem_text_padding_right = 0x7f06002b
mc.meson.kasumi:color/m3_ref_palette_neutral_variant50 = 0x7f050124
mc.meson.kasumi:dimen/abc_dropdownitem_text_padding_left = 0x7f06002a
mc.meson.kasumi:style/Base.V21.Theme.MaterialComponents.Dialog = 0x7f1200b3
mc.meson.kasumi:dimen/m3_bottom_nav_item_active_indicator_width = 0x7f0600be
mc.meson.kasumi:dimen/abc_dropdownitem_icon_width = 0x7f060029
mc.meson.kasumi:dimen/abc_dialog_min_width_minor = 0x7f060023
mc.meson.kasumi:dimen/abc_dialog_list_padding_bottom_no_buttons = 0x7f060020
mc.meson.kasumi:dimen/abc_dialog_fixed_width_major = 0x7f06001e
mc.meson.kasumi:attr/textPanX = 0x7f0304ac
mc.meson.kasumi:dimen/abc_dialog_corner_radius_material = 0x7f06001b
mc.meson.kasumi:dimen/abc_control_padding_material = 0x7f06001a
mc.meson.kasumi:style/Base.Theme.Material3.Dark.SideSheetDialog = 0x7f12006b
mc.meson.kasumi:id/leftToRight = 0x7f080122
mc.meson.kasumi:dimen/abc_control_corner_material = 0x7f060018
mc.meson.kasumi:drawable/avd_hide_password = 0x7f070078
mc.meson.kasumi:dimen/abc_button_padding_horizontal_material = 0x7f060014
mc.meson.kasumi:dimen/abc_alert_dialog_button_bar_height = 0x7f060010
mc.meson.kasumi:attr/layout_anchorGravity = 0x7f03028b
mc.meson.kasumi:color/m3_appbar_overlay_color = 0x7f050064
mc.meson.kasumi:dimen/abc_action_button_min_width_material = 0x7f06000e
mc.meson.kasumi:color/m3_sys_color_light_on_error = 0x7f0501d7
mc.meson.kasumi:attr/actionOverflowMenuStyle = 0x7f030023
mc.meson.kasumi:style/Base.TextAppearance.AppCompat.Light.Widget.PopupMenu.Small = 0x7f120031
mc.meson.kasumi:dimen/abc_action_bar_subtitle_top_margin_material = 0x7f06000c
mc.meson.kasumi:style/ShapeAppearance.Material3.NavigationBarView.ActiveIndicator = 0x7f120184
mc.meson.kasumi:dimen/abc_action_bar_default_padding_start_material = 0x7f060004
mc.meson.kasumi:style/TextAppearance.MaterialComponents.Tooltip = 0x7f120214
mc.meson.kasumi:id/counterclockwise = 0x7f0800aa
mc.meson.kasumi:attr/buttonIconTintMode = 0x7f030099
mc.meson.kasumi:dimen/abc_action_bar_content_inset_material = 0x7f060000
mc.meson.kasumi:color/white = 0x7f050357
mc.meson.kasumi:attr/thumbTint = 0x7f0304c0
mc.meson.kasumi:color/abc_primary_text_material_light = 0x7f05000c
mc.meson.kasumi:color/tooltip_background_dark = 0x7f050355
mc.meson.kasumi:id/tvSettingName = 0x7f080251
mc.meson.kasumi:style/ThemeOverlay.Material3.AutoCompleteTextView.FilledBox = 0x7f120290
mc.meson.kasumi:color/m3_ref_palette_neutral_variant95 = 0x7f050129
mc.meson.kasumi:dimen/mtrl_progress_circular_inset_medium = 0x7f0602d6
mc.meson.kasumi:dimen/m3_extended_fab_end_padding = 0x7f0601b0
mc.meson.kasumi:color/switch_thumb_material_dark = 0x7f05034f
mc.meson.kasumi:style/ThemeOverlay.MaterialComponents.Toolbar.Popup.Primary = 0x7f1202f2
mc.meson.kasumi:color/secondary_text_disabled_material_light = 0x7f05034c
mc.meson.kasumi:color/secondary_text_default_material_dark = 0x7f050349
mc.meson.kasumi:layout/abc_alert_dialog_title_material = 0x7f0b000a
mc.meson.kasumi:color/ripple_material_light = 0x7f050348
mc.meson.kasumi:dimen/m3_btn_inset = 0x7f0600d7
mc.meson.kasumi:color/purple_500 = 0x7f050345
mc.meson.kasumi:color/m3_ref_palette_neutral22 = 0x7f05010c
mc.meson.kasumi:string/m3_ref_typeface_plain_regular = 0x7f110059
mc.meson.kasumi:string/bottomsheet_action_expand_halfway = 0x7f110027
mc.meson.kasumi:string/mtrl_picker_announce_current_selection = 0x7f11008e
mc.meson.kasumi:dimen/m3_datepicker_elevation = 0x7f0601ad
mc.meson.kasumi:color/primary_text_disabled_material_light = 0x7f050343
mc.meson.kasumi:styleable/RangeSlider = 0x7f130082
mc.meson.kasumi:attr/forceDefaultNavigationOnClickListener = 0x7f030216
mc.meson.kasumi:color/primary_material_light = 0x7f05033f
mc.meson.kasumi:attr/itemShapeInsetStart = 0x7f030270
mc.meson.kasumi:color/primary_dark_material_light = 0x7f05033d
mc.meson.kasumi:color/notification_icon_bg_color = 0x7f05033b
mc.meson.kasumi:attr/behavior_saveFlags = 0x7f030075
mc.meson.kasumi:color/material_dynamic_primary0 = 0x7f050240
mc.meson.kasumi:color/mtrl_textinput_focused_box_stroke_color = 0x7f050338
mc.meson.kasumi:color/m3_tabs_icon_color = 0x7f0501fe
mc.meson.kasumi:color/mtrl_textinput_filled_box_default_background_color = 0x7f050337
mc.meson.kasumi:styleable/BottomSheetBehavior_Layout = 0x7f13001a
mc.meson.kasumi:attr/materialCalendarHeaderTitle = 0x7f030314
mc.meson.kasumi:color/mtrl_tabs_ripple_color = 0x7f050333
mc.meson.kasumi:macro/m3_comp_date_picker_modal_date_today_label_text_color = 0x7f0c0013
mc.meson.kasumi:dimen/m3_comp_navigation_rail_pressed_state_layer_opacity = 0x7f06014c
mc.meson.kasumi:attr/staggered = 0x7f030425
mc.meson.kasumi:attr/paddingTopNoTitle = 0x7f03039a
mc.meson.kasumi:dimen/mtrl_chip_pressed_translation_z = 0x7f0602a1
mc.meson.kasumi:style/Widget.Material3.Button.IconButton = 0x7f120361
mc.meson.kasumi:attr/displayOptions = 0x7f030185
mc.meson.kasumi:dimen/mtrl_textinput_box_corner_radius_medium = 0x7f0602fc
mc.meson.kasumi:dimen/m3_sys_motion_easing_standard_decelerate_control_y1 = 0x7f060218
mc.meson.kasumi:id/indicatorCombat = 0x7f08010f
mc.meson.kasumi:id/staticPostLayout = 0x7f0801fe
mc.meson.kasumi:color/mtrl_switch_thumb_icon_tint = 0x7f05032b
mc.meson.kasumi:color/m3_ref_palette_dynamic_primary30 = 0x7f0500d6
mc.meson.kasumi:color/mtrl_on_surface_ripple_color = 0x7f050326
mc.meson.kasumi:attr/clockNumberTextColor = 0x7f0300e4
mc.meson.kasumi:color/mtrl_navigation_item_icon_tint = 0x7f050323
mc.meson.kasumi:style/Base.TextAppearance.Widget.AppCompat.Toolbar.Title = 0x7f120057
mc.meson.kasumi:color/mtrl_navigation_bar_colored_item_tint = 0x7f05031e
mc.meson.kasumi:color/mtrl_filled_icon_tint = 0x7f05031b
mc.meson.kasumi:style/Widget.Material3.Chip.Input.Elevated = 0x7f120379
mc.meson.kasumi:dimen/mtrl_bottomappbar_fab_cradle_margin = 0x7f060255
mc.meson.kasumi:attr/listChoiceBackgroundIndicator = 0x7f0302d6
mc.meson.kasumi:color/mtrl_fab_ripple_color = 0x7f050319
mc.meson.kasumi:color/mtrl_fab_bg_color_selector = 0x7f050317
mc.meson.kasumi:style/Widget.AppCompat.CompoundButton.RadioButton = 0x7f120309
mc.meson.kasumi:color/mtrl_chip_text_color = 0x7f050312
mc.meson.kasumi:style/Base.Animation.AppCompat.DropDownUp = 0x7f12001b
mc.meson.kasumi:string/character_counter_pattern = 0x7f110033
mc.meson.kasumi:color/secondary_text_disabled_material_dark = 0x7f05034b
mc.meson.kasumi:drawable/mtrl_switch_thumb_checked = 0x7f0700e5
mc.meson.kasumi:style/Base.Widget.Material3.TabLayout = 0x7f12011b
mc.meson.kasumi:layout/material_clock_period_toggle = 0x7f0b004b
mc.meson.kasumi:color/mtrl_chip_close_icon_tint = 0x7f050310
mc.meson.kasumi:color/mtrl_chip_background_color = 0x7f05030f
mc.meson.kasumi:dimen/mtrl_btn_inset = 0x7f060262
mc.meson.kasumi:color/mtrl_calendar_item_stroke_color = 0x7f05030b
mc.meson.kasumi:dimen/mtrl_navigation_item_shape_horizontal_margin = 0x7f0602c9
mc.meson.kasumi:color/mtrl_btn_text_color_disabled = 0x7f050308
mc.meson.kasumi:dimen/m3_sys_motion_easing_legacy_control_y1 = 0x7f060204
mc.meson.kasumi:color/mtrl_btn_ripple_color = 0x7f050304
mc.meson.kasumi:style/Widget.Material3.Button.TextButton = 0x7f120367
mc.meson.kasumi:color/minecraft_purple = 0x7f0502fd
mc.meson.kasumi:dimen/mtrl_btn_text_size = 0x7f06026f
mc.meson.kasumi:attr/activityAction = 0x7f030028
mc.meson.kasumi:color/minecraft_green_dark = 0x7f0502fa
mc.meson.kasumi:dimen/mtrl_navigation_item_icon_padding = 0x7f0602c7
mc.meson.kasumi:styleable/AppBarLayout_Layout = 0x7f13000f
mc.meson.kasumi:color/minecraft_green = 0x7f0502f9
mc.meson.kasumi:style/ThemeOverlay.MaterialComponents.TimePicker = 0x7f1202ef
mc.meson.kasumi:attr/popupMenuStyle = 0x7f0303b8
mc.meson.kasumi:color/mtrl_outlined_stroke_color = 0x7f050328
mc.meson.kasumi:drawable/ic_resize = 0x7f0700b1
mc.meson.kasumi:id/textinput_helper_text = 0x7f080227
mc.meson.kasumi:color/minecraft_blue_dark = 0x7f0502f8
mc.meson.kasumi:id/west = 0x7f080265
mc.meson.kasumi:attr/grid_verticalGaps = 0x7f030227
mc.meson.kasumi:color/mtrl_choice_chip_ripple_color = 0x7f050314
mc.meson.kasumi:color/md_theme_light_surfaceTint = 0x7f0502f3
mc.meson.kasumi:color/md_theme_light_secondaryContainer = 0x7f0502f0
mc.meson.kasumi:color/m3_sys_color_dynamic_light_primary = 0x7f0501b5
mc.meson.kasumi:macro/m3_comp_navigation_drawer_active_pressed_state_layer_color = 0x7f0c0083
mc.meson.kasumi:id/actions = 0x7f08004f
mc.meson.kasumi:color/md_theme_light_scrim = 0x7f0502ee
mc.meson.kasumi:color/m3_ref_palette_dynamic_neutral96 = 0x7f0500b7
mc.meson.kasumi:attr/roundPercent = 0x7f0303dd
mc.meson.kasumi:dimen/m3_appbar_expanded_title_margin_horizontal = 0x7f0600a5
mc.meson.kasumi:color/md_theme_light_primaryContainer = 0x7f0502ed
mc.meson.kasumi:drawable/abc_ic_menu_paste_mtrl_am_alpha = 0x7f070046
mc.meson.kasumi:dimen/m3_bottom_nav_item_padding_bottom = 0x7f0600bf
mc.meson.kasumi:color/md_theme_light_onSurface = 0x7f0502e6
mc.meson.kasumi:dimen/mtrl_exposed_dropdown_menu_popup_elevation = 0x7f0602a3
mc.meson.kasumi:id/accessibility_custom_action_21 = 0x7f08001f
mc.meson.kasumi:string/m3_sys_motion_easing_legacy_decelerate = 0x7f110060
mc.meson.kasumi:color/md_theme_light_onSecondaryContainer = 0x7f0502e5
mc.meson.kasumi:macro/m3_comp_time_picker_headline_type = 0x7f0c0151
mc.meson.kasumi:color/md_theme_light_onSecondary = 0x7f0502e4
mc.meson.kasumi:color/material_slider_active_tick_marks_color = 0x7f0502b0
mc.meson.kasumi:style/Widget.MaterialComponents.BottomNavigationView = 0x7f120406
mc.meson.kasumi:color/md_theme_light_onPrimary = 0x7f0502e2
mc.meson.kasumi:color/md_theme_light_inverseSurface = 0x7f0502de
mc.meson.kasumi:color/md_theme_light_error = 0x7f0502da
mc.meson.kasumi:color/bright_foreground_material_light = 0x7f050028
mc.meson.kasumi:dimen/notification_large_icon_height = 0x7f060310
mc.meson.kasumi:id/action_mode_close_button = 0x7f08004a
mc.meson.kasumi:color/md_theme_dark_shadow = 0x7f0502d3
mc.meson.kasumi:color/md_theme_dark_secondaryContainer = 0x7f0502d2
mc.meson.kasumi:color/md_theme_dark_scrim = 0x7f0502d0
mc.meson.kasumi:color/md_theme_dark_outlineVariant = 0x7f0502cd
mc.meson.kasumi:dimen/mtrl_calendar_day_corner = 0x7f060276
mc.meson.kasumi:color/md_theme_dark_outline = 0x7f0502cc
mc.meson.kasumi:color/md_theme_dark_onSurfaceVariant = 0x7f0502c9
mc.meson.kasumi:styleable/MotionLayout = 0x7f13006d
mc.meson.kasumi:bool/mtrl_btn_textappearance_all_caps = 0x7f040002
mc.meson.kasumi:layout/abc_list_menu_item_checkbox = 0x7f0b000e
mc.meson.kasumi:color/md_theme_dark_onSecondary = 0x7f0502c6
mc.meson.kasumi:color/m3_sys_color_dark_secondary_container = 0x7f050174
mc.meson.kasumi:color/md_theme_dark_onBackground = 0x7f0502c1
mc.meson.kasumi:string/speed = 0x7f1100d4
mc.meson.kasumi:color/material_timepicker_button_background = 0x7f0502b6
mc.meson.kasumi:attr/scrimBackground = 0x7f0303e2
mc.meson.kasumi:color/material_slider_inactive_tick_marks_color = 0x7f0502b3
mc.meson.kasumi:drawable/abc_btn_check_material_anim = 0x7f07002d
mc.meson.kasumi:dimen/design_snackbar_padding_horizontal = 0x7f060085
mc.meson.kasumi:attr/contentPadding = 0x7f030146
mc.meson.kasumi:style/Theme.AppCompat.Dialog.Alert = 0x7f120222
mc.meson.kasumi:integer/m3_sys_motion_duration_long2 = 0x7f090015
mc.meson.kasumi:color/mtrl_error = 0x7f050316
mc.meson.kasumi:style/ShapeAppearance.Material3.Corner.ExtraLarge = 0x7f12017b
mc.meson.kasumi:dimen/mtrl_btn_elevation = 0x7f06025d
mc.meson.kasumi:color/material_slider_halo_color = 0x7f0502b2
mc.meson.kasumi:drawable/ic_call_answer = 0x7f070095
mc.meson.kasumi:anim/btn_checkbox_to_unchecked_check_path_merged_animation = 0x7f010011
mc.meson.kasumi:color/material_personalized_primary_inverse_text_disable_only = 0x7f0502ae
mc.meson.kasumi:dimen/abc_seekbar_track_background_height_material = 0x7f060038
mc.meson.kasumi:color/material_personalized_hint_foreground_inverse = 0x7f0502ad
mc.meson.kasumi:dimen/mtrl_calendar_day_today_stroke = 0x7f060279
mc.meson.kasumi:id/ltr = 0x7f08012e
mc.meson.kasumi:dimen/m3_sys_motion_easing_emphasized_accelerate_control_y2 = 0x7f0601f9
mc.meson.kasumi:style/Theme.MaterialComponents.Light.DarkActionBar.Bridge = 0x7f120275
mc.meson.kasumi:color/m3_sys_color_light_on_secondary_container = 0x7f0501dc
mc.meson.kasumi:id/btnCollapseExpanded = 0x7f08007a
mc.meson.kasumi:id/open_search_view_clear_button = 0x7f08018d
mc.meson.kasumi:dimen/m3_comp_radio_button_unselected_hover_state_layer_opacity = 0x7f06016b
mc.meson.kasumi:color/m3_ref_palette_tertiary90 = 0x7f05014f
mc.meson.kasumi:color/material_personalized_hint_foreground = 0x7f0502ac
mc.meson.kasumi:id/circle_center = 0x7f080097
mc.meson.kasumi:color/material_personalized_color_text_primary_inverse = 0x7f0502a8
mc.meson.kasumi:color/material_personalized_color_surface_inverse = 0x7f0502a3
mc.meson.kasumi:macro/m3_comp_switch_selected_track_color = 0x7f0c012e
mc.meson.kasumi:style/TextAppearance.Material3.DisplayMedium = 0x7f1201f5
mc.meson.kasumi:color/material_personalized_color_surface_container_low = 0x7f0502a0
mc.meson.kasumi:macro/m3_comp_navigation_drawer_active_indicator_color = 0x7f0c007f
mc.meson.kasumi:color/m3_timepicker_display_ripple_color = 0x7f050211
mc.meson.kasumi:color/material_personalized_color_surface_bright = 0x7f05029c
mc.meson.kasumi:color/m3_ref_palette_neutral4 = 0x7f05010f
mc.meson.kasumi:color/material_personalized_color_surface = 0x7f05029b
mc.meson.kasumi:attr/tickVisible = 0x7f0304cc
mc.meson.kasumi:color/material_personalized_color_primary_container = 0x7f050293
mc.meson.kasumi:color/m3_timepicker_secondary_text_button_ripple_color = 0x7f050213
mc.meson.kasumi:color/material_personalized_color_on_tertiary = 0x7f05028e
mc.meson.kasumi:dimen/abc_dialog_padding_top_material = 0x7f060025
mc.meson.kasumi:drawable/btn_radio_off_to_on_mtrl_animation = 0x7f07007f
mc.meson.kasumi:color/material_personalized_color_on_surface_variant = 0x7f05028d
mc.meson.kasumi:color/material_personalized_color_on_surface = 0x7f05028b
mc.meson.kasumi:color/material_personalized_color_on_error_container = 0x7f050286
mc.meson.kasumi:style/Theme.MaterialComponents.DayNight = 0x7f120258
mc.meson.kasumi:style/Widget.MaterialComponents.TextView = 0x7f120464
mc.meson.kasumi:string/material_slider_range_start = 0x7f110072
mc.meson.kasumi:style/Widget.Material3.SideSheet.Detached = 0x7f1203d9
mc.meson.kasumi:style/ThemeOverlay.Material3.MaterialCalendar.HeaderCancelButton = 0x7f1202bc
mc.meson.kasumi:style/Base.Widget.MaterialComponents.TextInputEditText = 0x7f120129
mc.meson.kasumi:color/material_personalized_color_on_background = 0x7f050284
mc.meson.kasumi:styleable/StateSet = 0x7f130096
mc.meson.kasumi:layout/mtrl_picker_header_toggle = 0x7f0b0072
mc.meson.kasumi:animator/design_fab_show_motion_spec = 0x7f020002
mc.meson.kasumi:color/material_personalized_color_error = 0x7f050282
mc.meson.kasumi:color/material_personalized_color_background = 0x7f05027e
mc.meson.kasumi:attr/carousel_alignment = 0x7f0300a7
mc.meson.kasumi:color/material_personalized__highlighted_text_inverse = 0x7f05027d
mc.meson.kasumi:id/tvModuleCategory = 0x7f08024a
mc.meson.kasumi:drawable/abc_text_select_handle_right_mtrl = 0x7f070071
mc.meson.kasumi:attr/logo = 0x7f0302e5
mc.meson.kasumi:color/material_personalized__highlighted_text = 0x7f05027c
mc.meson.kasumi:attr/navigationMode = 0x7f030380
mc.meson.kasumi:color/material_on_surface_emphasis_medium = 0x7f05027a
mc.meson.kasumi:attr/rotationCenterId = 0x7f0303db
mc.meson.kasumi:string/abc_activity_chooser_view_see_all = 0x7f110004
mc.meson.kasumi:color/material_on_primary_emphasis_high_type = 0x7f050276
mc.meson.kasumi:color/material_on_primary_disabled = 0x7f050275
mc.meson.kasumi:dimen/abc_action_bar_content_inset_with_nav = 0x7f060001
mc.meson.kasumi:color/material_grey_800 = 0x7f05026b
mc.meson.kasumi:macro/m3_comp_primary_navigation_tab_container_color = 0x7f0c00cb
mc.meson.kasumi:dimen/mtrl_badge_long_text_horizontal_padding = 0x7f06024c
mc.meson.kasumi:color/material_grey_50 = 0x7f050269
mc.meson.kasumi:style/Base.Widget.Material3.CompoundButton.Switch = 0x7f120112
mc.meson.kasumi:color/material_grey_300 = 0x7f050268
mc.meson.kasumi:style/Widget.AppCompat.RatingBar = 0x7f12032f
mc.meson.kasumi:color/material_dynamic_tertiary10 = 0x7f05025b
mc.meson.kasumi:styleable/TabItem = 0x7f130099
mc.meson.kasumi:color/material_dynamic_secondary90 = 0x7f050257
mc.meson.kasumi:dimen/compat_button_padding_vertical_material = 0x7f060059
mc.meson.kasumi:attr/telltales_tailScale = 0x7f030470
mc.meson.kasumi:string/mtrl_picker_day_of_week_column_header = 0x7f110095
mc.meson.kasumi:color/material_dynamic_secondary10 = 0x7f05024e
mc.meson.kasumi:attr/flow_firstVerticalStyle = 0x7f0301f9
mc.meson.kasumi:dimen/m3_comp_snackbar_container_elevation = 0x7f060189
mc.meson.kasumi:color/material_dynamic_primary99 = 0x7f05024c
mc.meson.kasumi:dimen/m3_searchview_divider_size = 0x7f0601e2
mc.meson.kasumi:color/md_theme_dark_onErrorContainer = 0x7f0502c3
mc.meson.kasumi:color/material_dynamic_tertiary95 = 0x7f050265
mc.meson.kasumi:macro/m3_comp_slider_active_track_color = 0x7f0c010b
mc.meson.kasumi:dimen/mtrl_btn_padding_bottom = 0x7f060265
mc.meson.kasumi:color/material_dynamic_primary40 = 0x7f050245
mc.meson.kasumi:color/material_dynamic_primary30 = 0x7f050244
mc.meson.kasumi:macro/m3_comp_date_picker_modal_container_color = 0x7f0c000d
mc.meson.kasumi:attr/dividerHorizontal = 0x7f030188
mc.meson.kasumi:color/material_personalized_color_surface_dim = 0x7f0502a2
mc.meson.kasumi:dimen/design_navigation_elevation = 0x7f060075
mc.meson.kasumi:color/material_dynamic_primary100 = 0x7f050242
mc.meson.kasumi:style/Widget.AppCompat.Light.ActionBar.TabBar = 0x7f120312
mc.meson.kasumi:style/Widget.Material3.Chip.Filter = 0x7f120376
mc.meson.kasumi:id/header_title = 0x7f0800fe
mc.meson.kasumi:color/material_dynamic_primary10 = 0x7f050241
mc.meson.kasumi:dimen/mtrl_progress_circular_size_extra_small = 0x7f0602da
mc.meson.kasumi:color/material_dynamic_neutral_variant90 = 0x7f05023d
mc.meson.kasumi:color/material_dynamic_neutral_variant80 = 0x7f05023c
mc.meson.kasumi:color/m3_switch_thumb_tint = 0x7f05015c
mc.meson.kasumi:style/TextAppearance.MaterialComponents.Badge = 0x7f120204
mc.meson.kasumi:color/md_theme_dark_primaryContainer = 0x7f0502cf
mc.meson.kasumi:color/material_dynamic_neutral_variant70 = 0x7f05023b
mc.meson.kasumi:layout/mtrl_alert_select_dialog_singlechoice = 0x7f0b005c
mc.meson.kasumi:dimen/design_fab_size_mini = 0x7f060071
mc.meson.kasumi:color/material_dynamic_secondary95 = 0x7f050258
mc.meson.kasumi:attr/paddingStart = 0x7f030398
mc.meson.kasumi:macro/m3_comp_filled_icon_button_toggle_unselected_icon_color = 0x7f0c004a
mc.meson.kasumi:color/m3_sys_color_dark_on_surface_variant = 0x7f05016c
mc.meson.kasumi:color/material_dynamic_neutral_variant60 = 0x7f05023a
mc.meson.kasumi:color/material_dynamic_neutral_variant10 = 0x7f050234
mc.meson.kasumi:color/material_dynamic_neutral_variant0 = 0x7f050233
mc.meson.kasumi:color/material_dynamic_neutral99 = 0x7f050232
mc.meson.kasumi:styleable/ShapeableImageView = 0x7f13008a
mc.meson.kasumi:color/material_dynamic_neutral95 = 0x7f050231
mc.meson.kasumi:dimen/abc_dialog_fixed_height_major = 0x7f06001c
mc.meson.kasumi:color/md_theme_light_onTertiary = 0x7f0502e8
mc.meson.kasumi:drawable/abc_text_select_handle_middle_mtrl = 0x7f070070
mc.meson.kasumi:attr/tintMode = 0x7f0304ce
mc.meson.kasumi:color/material_dynamic_neutral70 = 0x7f05022e
mc.meson.kasumi:dimen/mtrl_btn_icon_btn_padding_left = 0x7f060260
mc.meson.kasumi:color/material_dynamic_color_light_error_container = 0x7f050223
mc.meson.kasumi:style/Widget.Material3.FloatingActionButton.Large.Tertiary = 0x7f12039a
mc.meson.kasumi:attr/textOutlineThickness = 0x7f0304ab
mc.meson.kasumi:color/m3_sys_color_dark_error_container = 0x7f050160
mc.meson.kasumi:string/mtrl_picker_toggle_to_text_input_mode = 0x7f1100ae
mc.meson.kasumi:id/TOP_END = 0x7f08000c
mc.meson.kasumi:attr/layout_constraintHorizontal_bias = 0x7f0302a6
mc.meson.kasumi:color/material_dynamic_color_dark_on_error_container = 0x7f050221
mc.meson.kasumi:macro/m3_comp_navigation_bar_active_focus_state_layer_color = 0x7f0c0061
mc.meson.kasumi:color/material_deep_teal_500 = 0x7f05021c
mc.meson.kasumi:id/open_search_view_header_container = 0x7f080192
mc.meson.kasumi:color/material_blue_grey_900 = 0x7f050218
mc.meson.kasumi:color/m3_tonal_button_ripple_color_selector = 0x7f050216
mc.meson.kasumi:color/m3_timepicker_secondary_text_button_text_color = 0x7f050214
mc.meson.kasumi:color/material_dynamic_tertiary90 = 0x7f050264
mc.meson.kasumi:anim/m3_side_sheet_enter_from_left = 0x7f01002a
mc.meson.kasumi:color/m3_timepicker_display_text_color = 0x7f050212
mc.meson.kasumi:attr/yearSelectedStyle = 0x7f030524
mc.meson.kasumi:color/m3_timepicker_button_ripple_color = 0x7f05020d
mc.meson.kasumi:style/Base.Theme.MaterialComponents.Light.Bridge = 0x7f12007c
mc.meson.kasumi:color/m3_textfield_stroke_color = 0x7f05020b
mc.meson.kasumi:attr/lottie_imageAssetsFolder = 0x7f0302f5
mc.meson.kasumi:attr/tabIndicatorHeight = 0x7f03045b
mc.meson.kasumi:style/ThemeOverlay.Material3.ExtendedFloatingActionButton.Tertiary = 0x7f1202af
mc.meson.kasumi:color/m3_textfield_input_text_color = 0x7f050209
mc.meson.kasumi:color/m3_ref_palette_error95 = 0x7f050104
mc.meson.kasumi:color/m3_text_button_foreground_color_selector = 0x7f050205
mc.meson.kasumi:color/m3_tabs_text_color = 0x7f050202
mc.meson.kasumi:color/m3_tabs_ripple_color_secondary = 0x7f050201
mc.meson.kasumi:style/Base.TextAppearance.AppCompat.Large.Inverse = 0x7f12002f
mc.meson.kasumi:id/east = 0x7f0800d1
mc.meson.kasumi:drawable/$mtrl_switch_thumb_unchecked_pressed__0 = 0x7f070028
mc.meson.kasumi:color/m3_tabs_ripple_color = 0x7f050200
mc.meson.kasumi:color/m3_tabs_icon_color_secondary = 0x7f0501ff
mc.meson.kasumi:color/m3_sys_color_tertiary_fixed_dim = 0x7f0501fd
mc.meson.kasumi:id/btnApplySettings = 0x7f08026e
mc.meson.kasumi:color/material_slider_active_track_color = 0x7f0502b1
mc.meson.kasumi:color/m3_sys_color_tertiary_fixed = 0x7f0501fc
mc.meson.kasumi:color/m3_ref_palette_dynamic_neutral_variant80 = 0x7f0500c9
mc.meson.kasumi:attr/telltales_tailColor = 0x7f03046f
mc.meson.kasumi:macro/m3_comp_suggestion_chip_container_shape = 0x7f0c0117
mc.meson.kasumi:attr/trackColor = 0x7f0304ed
mc.meson.kasumi:color/material_dynamic_primary60 = 0x7f050247
mc.meson.kasumi:attr/layout_constraintBaseline_creator = 0x7f030291
mc.meson.kasumi:id/tag_on_receive_content_listener = 0x7f080213
mc.meson.kasumi:color/m3_sys_color_secondary_fixed_dim = 0x7f0501fb
mc.meson.kasumi:color/m3_sys_color_on_tertiary_fixed = 0x7f0501f6
mc.meson.kasumi:color/m3_sys_color_on_primary_fixed = 0x7f0501f2
mc.meson.kasumi:macro/m3_comp_filled_text_field_input_text_type = 0x7f0c0050
mc.meson.kasumi:layout/dialog_create_config = 0x7f0b0030
mc.meson.kasumi:color/m3_sys_color_light_tertiary = 0x7f0501f0
mc.meson.kasumi:color/m3_sys_color_light_surface_bright = 0x7f0501e8
mc.meson.kasumi:style/Widget.Material3.SideSheet.Modal = 0x7f1203da
mc.meson.kasumi:color/m3_sys_color_light_primary_container = 0x7f0501e4
mc.meson.kasumi:drawable/test_level_drawable = 0x7f070107
mc.meson.kasumi:color/m3_sys_color_light_outline_variant = 0x7f0501e2
mc.meson.kasumi:color/m3_sys_color_light_on_secondary = 0x7f0501db
mc.meson.kasumi:drawable/$mtrl_checkbox_button_unchecked_checked__1 = 0x7f07001f
mc.meson.kasumi:color/material_dynamic_color_dark_error_container = 0x7f05021f
mc.meson.kasumi:color/m3_sys_color_light_inverse_primary = 0x7f0501d4
mc.meson.kasumi:color/m3_sys_color_light_on_primary_container = 0x7f0501da
mc.meson.kasumi:attr/useDrawerArrowDrawable = 0x7f030507
mc.meson.kasumi:color/m3_sys_color_light_inverse_surface = 0x7f0501d5
mc.meson.kasumi:attr/materialCalendarStyle = 0x7f030318
mc.meson.kasumi:color/m3_sys_color_light_error = 0x7f0501d1
mc.meson.kasumi:style/Base.TextAppearance.AppCompat.Widget.ActionBar.Title.Inverse = 0x7f120043
mc.meson.kasumi:color/m3_sys_color_light_background = 0x7f0501d0
mc.meson.kasumi:attr/thumbStrokeColor = 0x7f0304bd
mc.meson.kasumi:color/m3_sys_color_dynamic_tertiary_fixed = 0x7f0501ce
mc.meson.kasumi:color/m3_sys_color_dynamic_primary_fixed_dim = 0x7f0501cb
mc.meson.kasumi:macro/m3_comp_navigation_drawer_active_focus_icon_color = 0x7f0c0078
mc.meson.kasumi:attr/searchIcon = 0x7f0303e5
mc.meson.kasumi:color/m3_sys_color_dynamic_on_tertiary_fixed_variant = 0x7f0501c9
mc.meson.kasumi:id/image = 0x7f08010b
mc.meson.kasumi:dimen/m3_searchbar_height = 0x7f0601db
mc.meson.kasumi:attr/secondaryActivityName = 0x7f0303e9
mc.meson.kasumi:color/m3_sys_color_dynamic_on_tertiary_fixed = 0x7f0501c8
mc.meson.kasumi:dimen/m3_comp_text_button_focus_state_layer_opacity = 0x7f06019c
mc.meson.kasumi:color/m3_sys_color_dynamic_on_primary_fixed = 0x7f0501c4
mc.meson.kasumi:drawable/ic_clock_black_24dp = 0x7f07009d
mc.meson.kasumi:attr/contentInsetStart = 0x7f030144
mc.meson.kasumi:dimen/design_bottom_navigation_active_item_min_width = 0x7f060060
mc.meson.kasumi:color/m3_sys_color_dynamic_light_tertiary = 0x7f0501c2
mc.meson.kasumi:style/TextAppearance.AppCompat.Large = 0x7f1201ab
mc.meson.kasumi:color/m3_sys_color_dynamic_light_surface = 0x7f0501b9
mc.meson.kasumi:style/Base.Widget.AppCompat.ImageButton = 0x7f1200e9
mc.meson.kasumi:color/mtrl_card_view_foreground = 0x7f05030d
mc.meson.kasumi:dimen/material_clock_face_margin_top = 0x7f060226
mc.meson.kasumi:attr/removeEmbeddedFabElevation = 0x7f0303d7
mc.meson.kasumi:style/TextAppearance.AppCompat.Light.SearchResult.Title = 0x7f1201ae
mc.meson.kasumi:color/m3_sys_color_dynamic_light_on_tertiary = 0x7f0501b1
mc.meson.kasumi:dimen/m3_searchbar_margin_vertical = 0x7f0601dd
mc.meson.kasumi:color/m3_sys_color_dynamic_light_on_primary = 0x7f0501ab
mc.meson.kasumi:drawable/abc_textfield_search_default_mtrl_alpha = 0x7f070075
mc.meson.kasumi:string/hide_bottom_view_on_scroll_behavior = 0x7f11004f
mc.meson.kasumi:dimen/m3_alert_dialog_action_top_padding = 0x7f06009e
mc.meson.kasumi:id/text = 0x7f08021b
mc.meson.kasumi:color/material_dynamic_tertiary30 = 0x7f05025e
mc.meson.kasumi:id/row_index_key = 0x7f0801bd
mc.meson.kasumi:color/m3_sys_color_dynamic_light_error_container = 0x7f0501a4
mc.meson.kasumi:style/Widget.AppCompat.CompoundButton.Switch = 0x7f12030a
mc.meson.kasumi:style/Widget.MaterialComponents.TextInputLayout.FilledBox.ExposedDropdownMenu = 0x7f12045f
mc.meson.kasumi:color/m3_sys_color_dynamic_dark_surface_variant = 0x7f05019f
mc.meson.kasumi:attr/shapeAppearanceMediumComponent = 0x7f0303f7
mc.meson.kasumi:dimen/mtrl_calendar_header_height = 0x7f060281
mc.meson.kasumi:color/m3_sys_color_dynamic_dark_surface_container_high = 0x7f05019a
mc.meson.kasumi:dimen/m3_bottomappbar_fab_cradle_rounded_corner_radius = 0x7f0600c6
mc.meson.kasumi:color/m3_sys_color_dynamic_dark_surface_bright = 0x7f050198
mc.meson.kasumi:color/abc_primary_text_disable_only_material_light = 0x7f05000a
mc.meson.kasumi:color/m3_sys_color_dynamic_dark_surface = 0x7f050197
mc.meson.kasumi:attr/maxAcceleration = 0x7f030332
mc.meson.kasumi:color/m3_sys_color_dynamic_dark_secondary_container = 0x7f050196
mc.meson.kasumi:style/TextAppearance.Material3.HeadlineMedium = 0x7f1201f8
mc.meson.kasumi:drawable/$avd_show_password__2 = 0x7f070005
mc.meson.kasumi:color/m3_sys_color_dynamic_dark_secondary = 0x7f050195
mc.meson.kasumi:color/m3_sys_color_dynamic_dark_on_tertiary_container = 0x7f050190
mc.meson.kasumi:attr/indeterminateAnimationType = 0x7f03024f
mc.meson.kasumi:macro/m3_comp_radio_button_unselected_focus_state_layer_color = 0x7f0c00e0
mc.meson.kasumi:dimen/m3_toolbar_text_size_title = 0x7f060220
mc.meson.kasumi:style/Widget.Material3.MaterialCalendar.Day.Invalid = 0x7f1203a9
mc.meson.kasumi:dimen/mtrl_calendar_day_vertical_padding = 0x7f06027a
mc.meson.kasumi:dimen/abc_edit_text_inset_bottom_material = 0x7f06002c
mc.meson.kasumi:dimen/m3_back_progress_main_container_min_edge_gap = 0x7f0600af
mc.meson.kasumi:drawable/abc_list_selector_disabled_holo_dark = 0x7f070055
mc.meson.kasumi:color/m3_sys_color_dynamic_dark_on_surface_variant = 0x7f05018e
mc.meson.kasumi:attr/grid_rows = 0x7f030222
mc.meson.kasumi:color/m3_sys_color_dynamic_dark_on_surface = 0x7f05018d
mc.meson.kasumi:color/m3_sys_color_dynamic_dark_on_error = 0x7f050187
mc.meson.kasumi:color/material_dynamic_neutral60 = 0x7f05022d
mc.meson.kasumi:color/m3_sys_color_dynamic_dark_on_background = 0x7f050186
mc.meson.kasumi:style/Base.Widget.AppCompat.AutoCompleteTextView = 0x7f1200d9
mc.meson.kasumi:interpolator/btn_checkbox_unchecked_mtrl_animation_interpolator_1 = 0x7f0a0003
mc.meson.kasumi:color/m3_sys_color_dynamic_dark_inverse_primary = 0x7f050184
mc.meson.kasumi:color/m3_sys_color_dynamic_dark_error = 0x7f050181
mc.meson.kasumi:dimen/design_bottom_sheet_modal_elevation = 0x7f06006c
mc.meson.kasumi:color/m3_sys_color_dark_tertiary_container = 0x7f05017f
mc.meson.kasumi:color/m3_sys_color_dark_tertiary = 0x7f05017e
mc.meson.kasumi:color/m3_sys_color_dark_surface_variant = 0x7f05017d
mc.meson.kasumi:style/Base.Widget.MaterialComponents.TextView = 0x7f12012b
mc.meson.kasumi:dimen/material_textinput_default_width = 0x7f060241
mc.meson.kasumi:attr/statusBarForeground = 0x7f030439
mc.meson.kasumi:attr/cornerFamilyTopRight = 0x7f030156
mc.meson.kasumi:color/m3_sys_color_dark_surface_container = 0x7f050177
mc.meson.kasumi:style/Base.Widget.AppCompat.ActionBar.TabView = 0x7f1200d3
mc.meson.kasumi:color/m3_sys_color_dark_surface_bright = 0x7f050176
mc.meson.kasumi:drawable/$m3_avd_hide_password__2 = 0x7f070009
mc.meson.kasumi:color/m3_sys_color_dark_surface = 0x7f050175
mc.meson.kasumi:attr/titleTextEllipsize = 0x7f0304dd
mc.meson.kasumi:color/m3_sys_color_dark_on_tertiary_container = 0x7f05016e
mc.meson.kasumi:style/Widget.AppCompat.ActionBar.Solid = 0x7f1202f6
mc.meson.kasumi:dimen/abc_action_bar_overflow_padding_start_material = 0x7f060008
mc.meson.kasumi:layout/material_timepicker_dialog = 0x7f0b0054
mc.meson.kasumi:color/m3_sys_color_dark_error = 0x7f05015f
mc.meson.kasumi:drawable/ic_call_answer_video = 0x7f070097
mc.meson.kasumi:color/m3_sys_color_dark_background = 0x7f05015e
mc.meson.kasumi:styleable/ConstraintSet = 0x7f13002e
mc.meson.kasumi:style/Base.Widget.MaterialComponents.MaterialCalendar.NavigationButton = 0x7f120122
mc.meson.kasumi:dimen/mtrl_extended_fab_top_padding = 0x7f0602b2
mc.meson.kasumi:color/m3_ref_palette_dynamic_tertiary40 = 0x7f0500f1
mc.meson.kasumi:style/Widget.MaterialComponents.MaterialCalendar.Fullscreen = 0x7f120433
mc.meson.kasumi:color/m3_slider_halo_color_legacy = 0x7f050157
mc.meson.kasumi:dimen/m3_back_progress_side_container_max_scale_y_distance = 0x7f0600b2
mc.meson.kasumi:color/m3_ref_palette_dynamic_neutral_variant22 = 0x7f0500c0
mc.meson.kasumi:dimen/design_tab_scrollable_min_width = 0x7f06008a
mc.meson.kasumi:color/m3_slider_active_track_color_legacy = 0x7f050156
mc.meson.kasumi:color/m3_slider_active_track_color = 0x7f050155
mc.meson.kasumi:macro/m3_comp_top_app_bar_small_container_color = 0x7f0c016f
mc.meson.kasumi:color/m3_simple_item_ripple_color = 0x7f050154
mc.meson.kasumi:string/call_notification_decline_action = 0x7f11002c
mc.meson.kasumi:dimen/mtrl_calendar_month_horizontal_padding = 0x7f060289
mc.meson.kasumi:color/m3_selection_control_ripple_color_selector = 0x7f050153
mc.meson.kasumi:dimen/mtrl_slider_widget_height = 0x7f0602ef
mc.meson.kasumi:macro/m3_comp_filled_autocomplete_menu_container_color = 0x7f0c0041
mc.meson.kasumi:attr/motionEffect_strict = 0x7f03036e
mc.meson.kasumi:color/m3_ref_palette_white = 0x7f050152
mc.meson.kasumi:macro/m3_comp_search_bar_pressed_state_layer_color = 0x7f0c00ec
mc.meson.kasumi:string/mtrl_checkbox_state_description_indeterminate = 0x7f110086
mc.meson.kasumi:anim/abc_shrink_fade_out_from_bottom = 0x7f010005
mc.meson.kasumi:dimen/material_emphasis_medium = 0x7f060236
mc.meson.kasumi:dimen/m3_comp_search_bar_hover_state_layer_opacity = 0x7f060171
mc.meson.kasumi:style/Widget.Material3.Snackbar.FullWidth = 0x7f1203e1
mc.meson.kasumi:color/m3_ref_palette_tertiary99 = 0x7f050151
mc.meson.kasumi:style/Base.Widget.AppCompat.ActionBar = 0x7f1200cf
mc.meson.kasumi:style/App.TabLayout.Rounded = 0x7f120011
mc.meson.kasumi:macro/m3_comp_switch_unselected_pressed_track_outline_color = 0x7f0c013f
mc.meson.kasumi:color/m3_ref_palette_tertiary80 = 0x7f05014e
mc.meson.kasumi:color/material_dynamic_secondary80 = 0x7f050256
mc.meson.kasumi:color/m3_ref_palette_tertiary60 = 0x7f05014c
mc.meson.kasumi:color/md_theme_dark_onSurface = 0x7f0502c8
mc.meson.kasumi:color/m3_ref_palette_tertiary50 = 0x7f05014b
mc.meson.kasumi:style/ThemeOverlay.MaterialComponents.TextInputEditText.FilledBox.Dense = 0x7f1202ec
mc.meson.kasumi:color/m3_ref_palette_tertiary30 = 0x7f050149
mc.meson.kasumi:id/transition_layout_save = 0x7f08023b
mc.meson.kasumi:attr/drawerLayoutCornerSize = 0x7f03019c
mc.meson.kasumi:color/m3_ref_palette_tertiary20 = 0x7f050148
mc.meson.kasumi:style/Widget.Material3.TabLayout.OnSurface = 0x7f1203e4
mc.meson.kasumi:string/mtrl_checkbox_state_description_unchecked = 0x7f110087
mc.meson.kasumi:color/m3_ref_palette_tertiary0 = 0x7f050145
mc.meson.kasumi:attr/windowFixedHeightMajor = 0x7f03051d
mc.meson.kasumi:color/m3_ref_palette_secondary90 = 0x7f050142
mc.meson.kasumi:color/m3_ref_palette_secondary70 = 0x7f050140
mc.meson.kasumi:color/m3_ref_palette_secondary60 = 0x7f05013f
mc.meson.kasumi:color/abc_tint_seek_thumb = 0x7f050016
mc.meson.kasumi:color/m3_ref_palette_secondary20 = 0x7f05013b
mc.meson.kasumi:dimen/notification_action_text_size = 0x7f06030d
mc.meson.kasumi:color/m3_ref_palette_secondary10 = 0x7f050139
mc.meson.kasumi:dimen/design_navigation_item_vertical_padding = 0x7f06007a
mc.meson.kasumi:style/Widget.AppCompat.DropDownItem.Spinner = 0x7f12030c
mc.meson.kasumi:style/ThemeOverlay.Material3.ExtendedFloatingActionButton.Surface = 0x7f1202ae
mc.meson.kasumi:color/m3_ref_palette_primary99 = 0x7f050137
mc.meson.kasumi:styleable/KeyPosition = 0x7f13004a
mc.meson.kasumi:color/m3_ref_palette_primary95 = 0x7f050136
mc.meson.kasumi:integer/m3_sys_motion_duration_short1 = 0x7f09001c
mc.meson.kasumi:attr/expanded = 0x7f0301c4
mc.meson.kasumi:color/minecraft_yellow = 0x7f050301
mc.meson.kasumi:color/m3_radiobutton_button_tint = 0x7f05009f
mc.meson.kasumi:color/m3_ref_palette_primary90 = 0x7f050135
mc.meson.kasumi:color/m3_ref_palette_primary30 = 0x7f05012f
mc.meson.kasumi:style/Widget.Material3.FloatingActionButton.Large.Surface = 0x7f120399
mc.meson.kasumi:color/m3_ref_palette_primary100 = 0x7f05012d
mc.meson.kasumi:drawable/abc_btn_switch_to_on_mtrl_00001 = 0x7f070036
mc.meson.kasumi:navigation/nav_graph = 0x7f0f0000
mc.meson.kasumi:macro/m3_comp_navigation_drawer_active_icon_color = 0x7f0c007e
mc.meson.kasumi:color/m3_ref_palette_primary10 = 0x7f05012c
mc.meson.kasumi:attr/textAppearanceHeadlineSmall = 0x7f030485
mc.meson.kasumi:color/m3_ref_palette_neutral_variant60 = 0x7f050125
mc.meson.kasumi:color/mtrl_btn_transparent_bg_color = 0x7f05030a
mc.meson.kasumi:drawable/abc_list_selector_holo_dark = 0x7f070057
mc.meson.kasumi:color/m3_ref_palette_neutral_variant40 = 0x7f050123
mc.meson.kasumi:dimen/m3_comp_filled_button_container_elevation = 0x7f060122
mc.meson.kasumi:style/ThemeOverlay.Material3.AutoCompleteTextView.OutlinedBox.Dense = 0x7f120293
mc.meson.kasumi:attr/navigationRailStyle = 0x7f030381
mc.meson.kasumi:color/m3_ref_palette_neutral96 = 0x7f05011b
mc.meson.kasumi:attr/actionMenuTextAppearance = 0x7f030011
mc.meson.kasumi:color/switch_thumb_normal_material_light = 0x7f050352
mc.meson.kasumi:attr/layout_constraintBaseline_toBaselineOf = 0x7f030292
mc.meson.kasumi:color/m3_ref_palette_neutral94 = 0x7f050119
mc.meson.kasumi:style/Theme.MaterialComponents.DayNight.Dialog.Alert.Bridge = 0x7f12025f
mc.meson.kasumi:color/m3_ref_palette_neutral92 = 0x7f050118
mc.meson.kasumi:drawable/$mtrl_checkbox_button_icon_indeterminate_unchecked__1 = 0x7f070016
mc.meson.kasumi:style/Theme.MaterialComponents.Light.Dialog.MinWidth = 0x7f12027c
mc.meson.kasumi:color/m3_ref_palette_neutral80 = 0x7f050115
mc.meson.kasumi:style/Widget.MaterialComponents.Button.TextButton.Dialog = 0x7f120410
mc.meson.kasumi:dimen/design_fab_translation_z_hovered_focused = 0x7f060073
mc.meson.kasumi:attr/textAppearanceLargePopupMenu = 0x7f030489
mc.meson.kasumi:color/m3_ref_palette_neutral70 = 0x7f050114
mc.meson.kasumi:color/md_theme_dark_tertiaryContainer = 0x7f0502d8
mc.meson.kasumi:color/m3_ref_palette_neutral60 = 0x7f050113
mc.meson.kasumi:color/m3_ref_palette_neutral6 = 0x7f050112
mc.meson.kasumi:color/m3_ref_palette_neutral50 = 0x7f050111
mc.meson.kasumi:dimen/m3_comp_outlined_icon_button_unselected_outline_width = 0x7f060154
mc.meson.kasumi:styleable/PopupWindowBackgroundState = 0x7f13007f
mc.meson.kasumi:dimen/m3_comp_switch_disabled_unselected_handle_opacity = 0x7f060192
mc.meson.kasumi:dimen/m3_fab_corner_size = 0x7f0601b6
mc.meson.kasumi:color/m3_ref_palette_neutral20 = 0x7f05010b
mc.meson.kasumi:style/ShapeAppearance.MaterialComponents.LargeComponent = 0x7f120189
mc.meson.kasumi:id/action_menu_divider = 0x7f080046
mc.meson.kasumi:dimen/m3_comp_outlined_text_field_disabled_input_text_opacity = 0x7f060155
mc.meson.kasumi:color/material_personalized_color_secondary = 0x7f050297
mc.meson.kasumi:id/material_clock_display = 0x7f080135
mc.meson.kasumi:animator/mtrl_fab_transformation_sheet_collapse_spec = 0x7f020020
mc.meson.kasumi:color/m3_ref_palette_neutral17 = 0x7f05010a
mc.meson.kasumi:color/m3_ref_palette_neutral10 = 0x7f050107
mc.meson.kasumi:layout/mtrl_alert_select_dialog_multichoice = 0x7f0b005b
mc.meson.kasumi:attr/windowActionBarOverlay = 0x7f03051b
mc.meson.kasumi:color/m3_sys_color_dark_inverse_surface = 0x7f050163
mc.meson.kasumi:string/mtrl_checkbox_button_icon_path_indeterminate = 0x7f11007f
mc.meson.kasumi:color/m3_ref_palette_error99 = 0x7f050105
mc.meson.kasumi:styleable/ActivityNavigator = 0x7f130007
mc.meson.kasumi:color/mtrl_outlined_icon_tint = 0x7f050327
mc.meson.kasumi:style/RtlOverlay.Widget.AppCompat.ActionBar.TitleItem = 0x7f120152
mc.meson.kasumi:string/searchbar_scrolling_view_behavior = 0x7f1100cb
mc.meson.kasumi:color/m3_ref_palette_error60 = 0x7f050100
mc.meson.kasumi:dimen/mtrl_navigation_rail_compact_width = 0x7f0602cc
mc.meson.kasumi:color/m3_ref_palette_error50 = 0x7f0500ff
mc.meson.kasumi:style/Base.Theme.Material3.Light.SideSheetDialog = 0x7f120071
mc.meson.kasumi:drawable/material_ic_keyboard_arrow_previous_black_24dp = 0x7f0700c7
mc.meson.kasumi:color/m3_ref_palette_error40 = 0x7f0500fe
mc.meson.kasumi:color/m3_sys_color_dynamic_light_surface_container_highest = 0x7f0501bd
mc.meson.kasumi:color/primary_text_disabled_material_dark = 0x7f050342
mc.meson.kasumi:color/material_timepicker_clockface = 0x7f0502b9
mc.meson.kasumi:color/m3_ref_palette_error20 = 0x7f0500fc
mc.meson.kasumi:color/m3_ref_palette_error10 = 0x7f0500fa
mc.meson.kasumi:id/supportScrollUp = 0x7f080205
mc.meson.kasumi:dimen/m3_navigation_rail_item_padding_bottom = 0x7f0601d0
mc.meson.kasumi:color/m3_ref_palette_error0 = 0x7f0500f9
mc.meson.kasumi:color/m3_ref_palette_dynamic_tertiary95 = 0x7f0500f7
mc.meson.kasumi:attr/bottomInsetScrimEnabled = 0x7f03007e
mc.meson.kasumi:color/m3_ref_palette_dynamic_tertiary90 = 0x7f0500f6
mc.meson.kasumi:drawable/mtrl_switch_thumb_pressed_unchecked = 0x7f0700ea
mc.meson.kasumi:drawable/$mtrl_switch_thumb_unchecked_checked__1 = 0x7f070027
mc.meson.kasumi:attr/icon = 0x7f03023f
mc.meson.kasumi:color/m3_ref_palette_dynamic_tertiary80 = 0x7f0500f5
mc.meson.kasumi:color/m3_ref_palette_dynamic_tertiary50 = 0x7f0500f2
mc.meson.kasumi:id/pathRelative = 0x7f0801a4
mc.meson.kasumi:color/m3_ref_palette_dynamic_tertiary20 = 0x7f0500ef
mc.meson.kasumi:color/m3_ref_palette_dynamic_tertiary10 = 0x7f0500ed
mc.meson.kasumi:color/m3_ref_palette_dynamic_secondary99 = 0x7f0500eb
mc.meson.kasumi:dimen/m3_bottom_sheet_modal_elevation = 0x7f0600c4
mc.meson.kasumi:color/cardview_dark_background = 0x7f050030
mc.meson.kasumi:color/design_dark_default_color_primary_variant = 0x7f05003f
mc.meson.kasumi:drawable/abc_btn_radio_material_anim = 0x7f070033
mc.meson.kasumi:color/m3_ref_palette_secondary0 = 0x7f050138
mc.meson.kasumi:color/m3_ref_palette_dynamic_secondary95 = 0x7f0500ea
mc.meson.kasumi:dimen/m3_comp_filled_card_hover_state_layer_opacity = 0x7f060127
mc.meson.kasumi:color/m3_ref_palette_dynamic_secondary80 = 0x7f0500e8
mc.meson.kasumi:color/m3_ref_palette_dynamic_secondary60 = 0x7f0500e6
mc.meson.kasumi:color/m3_ref_palette_dynamic_secondary50 = 0x7f0500e5
mc.meson.kasumi:color/m3_ref_palette_dynamic_secondary40 = 0x7f0500e4
mc.meson.kasumi:color/m3_ref_palette_dynamic_secondary30 = 0x7f0500e3
mc.meson.kasumi:style/Theme.MaterialComponents.Light.Bridge = 0x7f120273
mc.meson.kasumi:style/Base.TextAppearance.AppCompat.Display3 = 0x7f12002a
mc.meson.kasumi:color/m3_ref_palette_dynamic_secondary20 = 0x7f0500e2
mc.meson.kasumi:style/Widget.MaterialComponents.AppBarLayout.PrimarySurface = 0x7f1203fc
mc.meson.kasumi:dimen/m3_side_sheet_width = 0x7f0601e8
mc.meson.kasumi:style/Widget.Material3.BottomSheet.Modal = 0x7f12035c
mc.meson.kasumi:color/m3_ref_palette_dynamic_secondary100 = 0x7f0500e1
mc.meson.kasumi:color/m3_ref_palette_dynamic_secondary10 = 0x7f0500e0
mc.meson.kasumi:drawable/abc_ic_menu_overflow_material = 0x7f070045
mc.meson.kasumi:color/m3_ref_palette_dynamic_secondary0 = 0x7f0500df
mc.meson.kasumi:attr/textAppearanceBodyMedium = 0x7f030476
mc.meson.kasumi:color/m3_ref_palette_dynamic_primary80 = 0x7f0500db
mc.meson.kasumi:color/black = 0x7f050021
mc.meson.kasumi:color/m3_ref_palette_dynamic_primary70 = 0x7f0500da
mc.meson.kasumi:dimen/mtrl_btn_dialog_btn_min_width = 0x7f06025a
mc.meson.kasumi:dimen/m3_bottom_nav_item_padding_top = 0x7f0600c0
mc.meson.kasumi:color/m3_ref_palette_dynamic_primary60 = 0x7f0500d9
mc.meson.kasumi:color/m3_ref_palette_neutral_variant10 = 0x7f05011f
mc.meson.kasumi:style/Base.TextAppearance.AppCompat.Widget.Button.Inverse = 0x7f120049
mc.meson.kasumi:id/etConfigName = 0x7f0800de
mc.meson.kasumi:drawable/abc_tab_indicator_mtrl_alpha = 0x7f07006d
mc.meson.kasumi:macro/m3_comp_navigation_drawer_label_text_type = 0x7f0c0091
mc.meson.kasumi:attr/constraints = 0x7f03013d
mc.meson.kasumi:color/minecraft_red = 0x7f0502ff
mc.meson.kasumi:color/m3_slider_thumb_color = 0x7f05015a
mc.meson.kasumi:string/exposed_dropdown_menu_content_description = 0x7f110041
mc.meson.kasumi:color/m3_ref_palette_dynamic_primary20 = 0x7f0500d5
mc.meson.kasumi:styleable/Transition = 0x7f1300a3
mc.meson.kasumi:dimen/mtrl_calendar_day_width = 0x7f06027b
mc.meson.kasumi:dimen/design_snackbar_extra_spacing_horizontal = 0x7f060082
mc.meson.kasumi:style/Base.TextAppearance.AppCompat.Body1 = 0x7f120024
mc.meson.kasumi:color/m3_ref_palette_dynamic_primary0 = 0x7f0500d2
mc.meson.kasumi:color/m3_ref_palette_dynamic_neutral_variant95 = 0x7f0500ce
mc.meson.kasumi:style/Widget.AppCompat.Light.ActionBar.TabView.Inverse = 0x7f120317
mc.meson.kasumi:dimen/mtrl_badge_text_size = 0x7f06024f
mc.meson.kasumi:macro/m3_comp_time_picker_period_selector_selected_label_text_color = 0x7f0c0158
mc.meson.kasumi:color/m3_ref_palette_dynamic_neutral_variant90 = 0x7f0500cb
mc.meson.kasumi:attr/homeLayout = 0x7f03023b
mc.meson.kasumi:dimen/design_appbar_elevation = 0x7f06005e
mc.meson.kasumi:integer/design_tab_indicator_anim_duration_ms = 0x7f090008
mc.meson.kasumi:color/m3_sys_color_on_secondary_fixed_variant = 0x7f0501f5
mc.meson.kasumi:color/m3_ref_palette_dynamic_neutral_variant6 = 0x7f0500c6
mc.meson.kasumi:layout/design_layout_snackbar = 0x7f0b0023
mc.meson.kasumi:color/m3_ref_palette_error100 = 0x7f0500fb
mc.meson.kasumi:color/m3_ref_palette_dynamic_primary90 = 0x7f0500dc
mc.meson.kasumi:attr/customNavigationLayout = 0x7f03016e
mc.meson.kasumi:color/m3_ref_palette_dynamic_neutral_variant30 = 0x7f0500c2
mc.meson.kasumi:color/m3_ref_palette_dynamic_neutral_variant24 = 0x7f0500c1
mc.meson.kasumi:style/Widget.Material3.MaterialTimePicker.Display.TextInputEditText = 0x7f1203c6
mc.meson.kasumi:id/titleDividerNoCustom = 0x7f08022e
mc.meson.kasumi:dimen/m3_comp_outlined_card_container_elevation = 0x7f060150
mc.meson.kasumi:attr/colorSurfaceContainerLow = 0x7f03012b
mc.meson.kasumi:color/m3_ref_palette_dynamic_neutral_variant17 = 0x7f0500be
mc.meson.kasumi:color/material_dynamic_tertiary60 = 0x7f050261
mc.meson.kasumi:style/ShapeAppearanceOverlay.MaterialComponents.TextInputLayout.FilledBox = 0x7f12019f
mc.meson.kasumi:id/view_transition = 0x7f08025c
mc.meson.kasumi:color/m3_ref_palette_dynamic_neutral_variant12 = 0x7f0500bd
mc.meson.kasumi:dimen/mtrl_extended_fab_start_padding = 0x7f0602b0
mc.meson.kasumi:color/m3_ref_palette_dynamic_neutral_variant100 = 0x7f0500bc
mc.meson.kasumi:styleable/Navigator = 0x7f13007b
mc.meson.kasumi:dimen/m3_sys_motion_easing_legacy_accelerate_control_y2 = 0x7f060201
mc.meson.kasumi:color/m3_ref_palette_dynamic_neutral_variant10 = 0x7f0500bb
mc.meson.kasumi:style/Base.V21.ThemeOverlay.MaterialComponents.BottomSheetDialog = 0x7f1200b9
mc.meson.kasumi:attr/listPreferredItemPaddingLeft = 0x7f0302e2
mc.meson.kasumi:color/m3_ref_palette_dynamic_neutral_variant0 = 0x7f0500ba
mc.meson.kasumi:styleable/MotionLabel = 0x7f13006c
mc.meson.kasumi:color/m3_ref_palette_dynamic_neutral99 = 0x7f0500b9
mc.meson.kasumi:style/Theme.MaterialComponents.DayNight.Dialog.FixedSize = 0x7f120261
mc.meson.kasumi:color/material_deep_teal_200 = 0x7f05021b
mc.meson.kasumi:dimen/m3_ripple_pressed_alpha = 0x7f0601d8
mc.meson.kasumi:color/m3_ref_palette_dynamic_neutral98 = 0x7f0500b8
mc.meson.kasumi:styleable/MaterialCheckBox = 0x7f13005b
mc.meson.kasumi:attr/cardElevation = 0x7f0300a1
mc.meson.kasumi:color/background_floating_material_light = 0x7f05001e
mc.meson.kasumi:dimen/mtrl_shape_corner_size_small_component = 0x7f0602e4
mc.meson.kasumi:id/CTRL = 0x7f080003
mc.meson.kasumi:color/m3_ref_palette_dynamic_neutral95 = 0x7f0500b6
mc.meson.kasumi:styleable/SearchBar = 0x7f130087
mc.meson.kasumi:dimen/m3_bottomappbar_horizontal_padding = 0x7f0600ca
mc.meson.kasumi:attr/backgroundInsetEnd = 0x7f03004f
mc.meson.kasumi:color/m3_card_foreground_color = 0x7f05006f
mc.meson.kasumi:attr/floatingActionButtonSurfaceStyle = 0x7f0301f4
mc.meson.kasumi:attr/materialIconButtonFilledTonalStyle = 0x7f030325
mc.meson.kasumi:color/m3_ref_palette_dynamic_neutral90 = 0x7f0500b3
mc.meson.kasumi:string/mtrl_picker_today_description = 0x7f1100ab
mc.meson.kasumi:id/material_textinput_timepicker = 0x7f080142
mc.meson.kasumi:attr/horizontalOffsetWithText = 0x7f03023d
mc.meson.kasumi:drawable/$mtrl_checkbox_button_icon_indeterminate_checked__0 = 0x7f070014
mc.meson.kasumi:color/m3_ref_palette_dynamic_neutral87 = 0x7f0500b2
mc.meson.kasumi:color/material_dynamic_color_dark_error = 0x7f05021e
mc.meson.kasumi:id/progress_circular = 0x7f0801ac
mc.meson.kasumi:color/m3_ref_palette_dynamic_neutral70 = 0x7f0500b0
mc.meson.kasumi:style/Base.V14.Theme.Material3.Light.Dialog = 0x7f12009c
mc.meson.kasumi:dimen/abc_action_bar_stacked_max_height = 0x7f060009
mc.meson.kasumi:color/m3_ref_palette_dynamic_neutral60 = 0x7f0500af
mc.meson.kasumi:style/TextAppearance.AppCompat.Light.Widget.PopupMenu.Large = 0x7f1201af
mc.meson.kasumi:attr/colorOnPrimaryFixedVariant = 0x7f03010a
mc.meson.kasumi:color/m3_ref_palette_dynamic_neutral40 = 0x7f0500ac
mc.meson.kasumi:dimen/mtrl_btn_focused_z = 0x7f06025e
mc.meson.kasumi:color/m3_ref_palette_dynamic_neutral4 = 0x7f0500ab
mc.meson.kasumi:dimen/mtrl_snackbar_padding_horizontal = 0x7f0602f5
mc.meson.kasumi:color/m3_ref_palette_dynamic_neutral30 = 0x7f0500aa
mc.meson.kasumi:attr/suggestionRowLayout = 0x7f03044b
mc.meson.kasumi:string/abc_search_hint = 0x7f110012
mc.meson.kasumi:color/m3_ref_palette_dynamic_neutral24 = 0x7f0500a9
mc.meson.kasumi:dimen/m3_side_sheet_standard_elevation = 0x7f0601e7
mc.meson.kasumi:attr/listPreferredItemPaddingStart = 0x7f0302e4
mc.meson.kasumi:attr/textureEffect = 0x7f0304b0
mc.meson.kasumi:macro/m3_comp_outlined_text_field_focus_input_text_color = 0x7f0c00b9
mc.meson.kasumi:color/m3_ref_palette_dynamic_neutral22 = 0x7f0500a8
mc.meson.kasumi:string/floating_window_permission_needed = 0x7f110049
mc.meson.kasumi:id/mtrl_picker_header_title_and_selection = 0x7f080165
mc.meson.kasumi:anim/linear_indeterminate_line2_head_interpolator = 0x7f010024
mc.meson.kasumi:style/Base.Widget.AppCompat.ActionButton = 0x7f1200d4
mc.meson.kasumi:color/m3_ref_palette_dynamic_neutral100 = 0x7f0500a4
mc.meson.kasumi:styleable/Carousel = 0x7f13001e
mc.meson.kasumi:string/material_timepicker_text_input_mode_description = 0x7f11007a
mc.meson.kasumi:dimen/mtrl_high_ripple_default_alpha = 0x7f0602ba
mc.meson.kasumi:string/mtrl_switch_track_path = 0x7f1100b7
mc.meson.kasumi:color/m3_navigation_rail_item_with_indicator_label_tint = 0x7f05009b
mc.meson.kasumi:color/m3_ref_palette_dynamic_neutral_variant60 = 0x7f0500c7
mc.meson.kasumi:dimen/m3_simple_item_color_hovered_alpha = 0x7f0601e9
mc.meson.kasumi:color/m3_navigation_rail_item_with_indicator_icon_tint = 0x7f05009a
mc.meson.kasumi:macro/m3_comp_filled_button_container_color = 0x7f0c0043
mc.meson.kasumi:color/m3_ref_palette_dynamic_tertiary99 = 0x7f0500f8
mc.meson.kasumi:style/ShapeAppearance.Material3.Corner.Large = 0x7f12017e
mc.meson.kasumi:color/m3_navigation_bar_ripple_color_selector = 0x7f050095
mc.meson.kasumi:style/Widget.AppCompat.ActionBar.TabView = 0x7f1202f9
mc.meson.kasumi:color/m3_navigation_bar_item_with_indicator_label_tint = 0x7f050094
mc.meson.kasumi:drawable/$avd_show_password__0 = 0x7f070003
mc.meson.kasumi:dimen/m3_sys_motion_easing_standard_decelerate_control_y2 = 0x7f060219
mc.meson.kasumi:attr/indeterminateProgressStyle = 0x7f030250
mc.meson.kasumi:dimen/abc_text_size_caption_material = 0x7f060042
mc.meson.kasumi:macro/m3_comp_time_picker_container_color = 0x7f0c014e
mc.meson.kasumi:color/material_dynamic_primary90 = 0x7f05024a
mc.meson.kasumi:color/m3_hint_foreground = 0x7f050091
mc.meson.kasumi:attr/subMenuArrow = 0x7f03043d
mc.meson.kasumi:string/settings_coming_soon = 0x7f1100d1
mc.meson.kasumi:color/m3_filled_icon_button_container_color_selector = 0x7f05008f
mc.meson.kasumi:interpolator/m3_sys_motion_easing_standard_decelerate = 0x7f0a000d
mc.meson.kasumi:color/m3_fab_efab_foreground_color_selector = 0x7f05008d
mc.meson.kasumi:integer/show_password_duration = 0x7f090043
mc.meson.kasumi:drawable/$m3_avd_show_password__1 = 0x7f07000b
mc.meson.kasumi:color/m3_ref_palette_dynamic_neutral_variant94 = 0x7f0500cd
mc.meson.kasumi:macro/m3_comp_navigation_drawer_inactive_label_text_color = 0x7f0c008d
mc.meson.kasumi:anim/btn_checkbox_to_checked_box_inner_merged_animation = 0x7f01000d
mc.meson.kasumi:color/m3_fab_efab_background_color_selector = 0x7f05008c
mc.meson.kasumi:color/m3_slider_inactive_track_color_legacy = 0x7f050159
mc.meson.kasumi:anim/linear_indeterminate_line1_tail_interpolator = 0x7f010023
mc.meson.kasumi:attr/circularflow_viewCenter = 0x7f0300dd
mc.meson.kasumi:color/m3_efab_ripple_color_selector = 0x7f05008a
mc.meson.kasumi:dimen/mtrl_shape_corner_size_large_component = 0x7f0602e2
mc.meson.kasumi:dimen/m3_comp_search_view_docked_header_container_height = 0x7f060174
mc.meson.kasumi:macro/m3_comp_navigation_rail_inactive_hover_state_layer_color = 0x7f0c009b
mc.meson.kasumi:color/m3_sys_color_dark_inverse_primary = 0x7f050162
mc.meson.kasumi:style/ThemeOverlay.Material3.BottomAppBar = 0x7f120294
mc.meson.kasumi:style/TextAppearance.Compat.Notification.Title = 0x7f1201d4
mc.meson.kasumi:color/m3_dynamic_dark_primary_text_disable_only = 0x7f050084
mc.meson.kasumi:color/material_personalized_color_primary_text = 0x7f050295
mc.meson.kasumi:dimen/abc_select_dialog_padding_start_material = 0x7f06003a
mc.meson.kasumi:color/m3_dynamic_dark_highlighted_text = 0x7f050082
mc.meson.kasumi:attr/textAppearanceBodySmall = 0x7f030477
mc.meson.kasumi:dimen/m3_sys_motion_easing_legacy_control_x2 = 0x7f060203
mc.meson.kasumi:color/m3_default_color_primary_text = 0x7f05007e
mc.meson.kasumi:id/ivModuleIcon = 0x7f08011b
mc.meson.kasumi:animator/mtrl_extended_fab_hide_motion_spec = 0x7f02001b
mc.meson.kasumi:color/m3_dark_default_color_primary_text = 0x7f050079
mc.meson.kasumi:attr/selectionRequired = 0x7f0303ed
mc.meson.kasumi:dimen/mtrl_chip_text_size = 0x7f0602a2
mc.meson.kasumi:style/TextAppearance.AppCompat.Headline = 0x7f1201a9
mc.meson.kasumi:dimen/mtrl_btn_letter_spacing = 0x7f060263
mc.meson.kasumi:style/ThemeOverlay.Material3.MaterialCalendar.Fullscreen = 0x7f1202bb
mc.meson.kasumi:drawable/design_ic_visibility = 0x7f070088
mc.meson.kasumi:attr/carousel_nextState = 0x7f0300ad
mc.meson.kasumi:attr/layout_constraintGuide_begin = 0x7f03029e
mc.meson.kasumi:color/m3_chip_text_color = 0x7f050078
mc.meson.kasumi:style/ThemeOverlay.MaterialComponents = 0x7f1202cd
mc.meson.kasumi:attr/materialTimePickerTheme = 0x7f030330
mc.meson.kasumi:color/m3_chip_background_color = 0x7f050075
mc.meson.kasumi:id/tag_on_apply_window_listener = 0x7f080212
mc.meson.kasumi:attr/placeholderActivityName = 0x7f0303ac
mc.meson.kasumi:dimen/mtrl_calendar_header_divider_thickness = 0x7f060280
mc.meson.kasumi:color/m3_chip_assist_text_color = 0x7f050074
mc.meson.kasumi:macro/m3_comp_switch_unselected_track_outline_color = 0x7f0c0141
mc.meson.kasumi:id/stretch = 0x7f080202
mc.meson.kasumi:color/m3_calendar_item_stroke_color = 0x7f05006e
mc.meson.kasumi:color/m3_calendar_item_disabled_text = 0x7f05006d
mc.meson.kasumi:attr/buttonBarNegativeButtonStyle = 0x7f030090
mc.meson.kasumi:attr/drawPath = 0x7f030191
mc.meson.kasumi:drawable/ic_m3_chip_check = 0x7f0700aa
mc.meson.kasumi:attr/itemTextAppearanceInactive = 0x7f030278
mc.meson.kasumi:id/indicatorVisual = 0x7f080112
mc.meson.kasumi:color/m3_button_outline_color_selector = 0x7f05006a
mc.meson.kasumi:attr/flow_lastHorizontalStyle = 0x7f0301ff
mc.meson.kasumi:id/transitionToStart = 0x7f080237
mc.meson.kasumi:dimen/m3_comp_switch_disabled_track_opacity = 0x7f060191
mc.meson.kasumi:color/m3_bottom_sheet_drag_handle_color = 0x7f050067
mc.meson.kasumi:style/Widget.MaterialComponents.PopupMenu.ContextMenu = 0x7f12044c
mc.meson.kasumi:color/m3_assist_chip_stroke_color = 0x7f050066
mc.meson.kasumi:dimen/design_bottom_navigation_text_size = 0x7f06006a
mc.meson.kasumi:color/m3_assist_chip_icon_tint_color = 0x7f050065
mc.meson.kasumi:style/ThemeOverlay.Material3.Toolbar.Surface = 0x7f1202cb
mc.meson.kasumi:color/highlighted_text_material_dark = 0x7f050062
mc.meson.kasumi:anim/btn_radio_to_on_mtrl_dot_group_animation = 0x7f010016
mc.meson.kasumi:attr/materialIconButtonFilledStyle = 0x7f030324
mc.meson.kasumi:color/foreground_material_light = 0x7f050061
mc.meson.kasumi:styleable/NavHostFragment = 0x7f130075
mc.meson.kasumi:color/error_color_material_light = 0x7f05005f
mc.meson.kasumi:color/error_color_material_dark = 0x7f05005e
mc.meson.kasumi:color/dim_foreground_material_light = 0x7f05005d
mc.meson.kasumi:style/Base.V21.Theme.AppCompat.Dialog = 0x7f1200af
mc.meson.kasumi:attr/materialCalendarHeaderConfirmButton = 0x7f030310
mc.meson.kasumi:color/m3_ref_palette_dynamic_primary99 = 0x7f0500de
mc.meson.kasumi:attr/extendedFloatingActionButtonTertiaryStyle = 0x7f0301d4
mc.meson.kasumi:attr/materialSearchViewToolbarStyle = 0x7f03032c
mc.meson.kasumi:string/character_counter_overflowed_content_description = 0x7f110032
mc.meson.kasumi:dimen/m3_comp_filter_chip_container_height = 0x7f06012b
mc.meson.kasumi:attr/thumbIconTintMode = 0x7f0304bb
mc.meson.kasumi:style/ShapeAppearance.M3.Comp.Badge.Shape = 0x7f120163
mc.meson.kasumi:color/m3_sys_color_light_on_primary = 0x7f0501d9
mc.meson.kasumi:color/design_snackbar_background_color = 0x7f050059
mc.meson.kasumi:macro/m3_comp_navigation_rail_inactive_focus_state_layer_color = 0x7f0c009a
mc.meson.kasumi:color/m3_ref_palette_secondary40 = 0x7f05013d
mc.meson.kasumi:color/m3_sys_color_dynamic_dark_inverse_on_surface = 0x7f050183
mc.meson.kasumi:style/Base.Theme.AppCompat.Light.DialogWhenLarge = 0x7f120065
mc.meson.kasumi:dimen/m3_comp_input_chip_container_elevation = 0x7f060130
mc.meson.kasumi:attr/shapeAppearanceCornerLarge = 0x7f0303f3
mc.meson.kasumi:color/design_fab_stroke_top_inner_color = 0x7f050056
mc.meson.kasumi:style/Widget.MaterialComponents.TabLayout.PrimarySurface = 0x7f120457
mc.meson.kasumi:color/design_fab_shadow_start_color = 0x7f050053
mc.meson.kasumi:style/Platform.AppCompat.Light = 0x7f120144
mc.meson.kasumi:color/design_fab_shadow_mid_color = 0x7f050052
mc.meson.kasumi:attr/cardForegroundColor = 0x7f0300a2
mc.meson.kasumi:attr/viewTransitionOnPositiveCross = 0x7f030510
mc.meson.kasumi:attr/triggerReceiver = 0x7f030501
mc.meson.kasumi:macro/m3_comp_dialog_supporting_text_color = 0x7f0c0026
mc.meson.kasumi:color/design_default_color_surface = 0x7f05004f
mc.meson.kasumi:color/m3_fab_ripple_color_selector = 0x7f05008e
mc.meson.kasumi:bool/abc_config_actionMenuItemAllCaps = 0x7f040001
mc.meson.kasumi:id/easeOut = 0x7f0800d0
mc.meson.kasumi:color/m3_ref_palette_dynamic_neutral17 = 0x7f0500a6
mc.meson.kasumi:id/open_search_view_toolbar_container = 0x7f080198
mc.meson.kasumi:color/design_default_color_secondary = 0x7f05004d
mc.meson.kasumi:color/m3_ref_palette_dynamic_neutral_variant98 = 0x7f0500d0
mc.meson.kasumi:attr/colorOnContainerUnchecked = 0x7f030104
mc.meson.kasumi:color/design_default_color_primary_dark = 0x7f05004b
mc.meson.kasumi:id/action_container = 0x7f08003c
mc.meson.kasumi:color/material_personalized_color_on_primary = 0x7f050287
mc.meson.kasumi:drawable/mode_option_indicator = 0x7f0700cb
mc.meson.kasumi:attr/grid_skips = 0x7f030223
mc.meson.kasumi:color/design_default_color_primary = 0x7f05004a
mc.meson.kasumi:attr/floatingActionButtonSmallStyle = 0x7f0301f0
mc.meson.kasumi:attr/motionEasingEmphasizedAccelerateInterpolator = 0x7f030361
mc.meson.kasumi:color/design_default_color_on_surface = 0x7f050049
mc.meson.kasumi:color/design_default_color_on_secondary = 0x7f050048
mc.meson.kasumi:attr/buttonTint = 0x7f03009d
mc.meson.kasumi:color/m3_navigation_item_background_color = 0x7f050096
mc.meson.kasumi:drawable/abc_scrubber_primary_mtrl_alpha = 0x7f070061
mc.meson.kasumi:color/design_default_color_on_primary = 0x7f050047
mc.meson.kasumi:style/Base.Theme.Material3.Dark = 0x7f120066
mc.meson.kasumi:layout/mtrl_calendar_days_of_week = 0x7f0b0060
mc.meson.kasumi:color/design_default_color_on_background = 0x7f050045
mc.meson.kasumi:attr/lineHeight = 0x7f0302d3
mc.meson.kasumi:dimen/abc_list_item_height_large_material = 0x7f060030
mc.meson.kasumi:id/exitUntilCollapsed = 0x7f0800df
mc.meson.kasumi:attr/windowMinWidthMajor = 0x7f030521
mc.meson.kasumi:color/m3_ref_palette_secondary50 = 0x7f05013e
mc.meson.kasumi:color/design_default_color_background = 0x7f050043
mc.meson.kasumi:color/design_dark_default_color_surface = 0x7f050042
mc.meson.kasumi:color/design_dark_default_color_secondary_variant = 0x7f050041
mc.meson.kasumi:style/Theme.Material3.Dark.BottomSheetDialog = 0x7f120237
mc.meson.kasumi:id/custom = 0x7f0800ad
mc.meson.kasumi:color/material_harmonized_color_error_container = 0x7f05026f
mc.meson.kasumi:dimen/abc_text_size_title_material_toolbar = 0x7f060050
mc.meson.kasumi:color/m3_ref_palette_dynamic_neutral_variant92 = 0x7f0500cc
mc.meson.kasumi:style/ShapeAppearanceOverlay.Material3.Corner.Top = 0x7f120192
mc.meson.kasumi:attr/titleEnabled = 0x7f0304d3
mc.meson.kasumi:layout/material_clockface_view = 0x7f0b004e
mc.meson.kasumi:dimen/abc_switch_padding = 0x7f06003e
mc.meson.kasumi:dimen/mtrl_min_touch_target_size = 0x7f0602c2
mc.meson.kasumi:styleable/ActionMode = 0x7f130004
mc.meson.kasumi:anim/btn_radio_to_off_mtrl_dot_group_animation = 0x7f010013
mc.meson.kasumi:color/m3_sys_color_primary_fixed = 0x7f0501f8
mc.meson.kasumi:attr/layout_collapseMode = 0x7f03028d
mc.meson.kasumi:color/design_dark_default_color_primary = 0x7f05003d
mc.meson.kasumi:style/Base.Widget.AppCompat.SearchView = 0x7f1200ff
mc.meson.kasumi:color/design_dark_default_color_on_error = 0x7f050039
mc.meson.kasumi:style/ThemeOverlay.AppCompat.Dark = 0x7f120285
mc.meson.kasumi:macro/m3_comp_search_view_header_supporting_text_type = 0x7f0c00f8
mc.meson.kasumi:color/design_box_stroke_color = 0x7f050035
mc.meson.kasumi:attr/navigationIcon = 0x7f03037e
mc.meson.kasumi:attr/motionEffect_start = 0x7f03036d
mc.meson.kasumi:attr/materialDividerHeavyStyle = 0x7f030322
mc.meson.kasumi:color/cardview_shadow_start_color = 0x7f050033
mc.meson.kasumi:attr/closeIconTint = 0x7f0300ea
mc.meson.kasumi:attr/buttonStyle = 0x7f03009b
mc.meson.kasumi:color/card_background_tertiary = 0x7f05002f
mc.meson.kasumi:dimen/material_helper_text_font_1_3_padding_horizontal = 0x7f06023e
mc.meson.kasumi:color/call_notification_answer_color = 0x7f05002b
mc.meson.kasumi:color/bright_foreground_inverse_material_dark = 0x7f050025
mc.meson.kasumi:attr/expandedTitleMarginTop = 0x7f0301cb
mc.meson.kasumi:color/bright_foreground_disabled_material_dark = 0x7f050023
mc.meson.kasumi:color/m3_ref_palette_tertiary10 = 0x7f050146
mc.meson.kasumi:style/Base.TextAppearance.Widget.AppCompat.Toolbar.Subtitle = 0x7f120056
mc.meson.kasumi:attr/autoCompleteMode = 0x7f030042
mc.meson.kasumi:color/background_material_dark = 0x7f05001f
mc.meson.kasumi:id/androidx_window_activity_scope = 0x7f080057
mc.meson.kasumi:attr/layout_constraintWidth_percent = 0x7f0302bc
mc.meson.kasumi:color/accent_material_light = 0x7f05001a
mc.meson.kasumi:color/abc_tint_spinner = 0x7f050017
mc.meson.kasumi:id/switchSetting = 0x7f08020b
mc.meson.kasumi:color/abc_tint_default = 0x7f050014
mc.meson.kasumi:style/Widget.Material3.CompoundButton.Switch = 0x7f12038d
mc.meson.kasumi:dimen/m3_chip_corner_size = 0x7f0600f4
mc.meson.kasumi:attr/firstBaselineToTopHeight = 0x7f0301e6
mc.meson.kasumi:color/m3_sys_color_dynamic_light_outline = 0x7f0501b3
mc.meson.kasumi:anim/fragment_fast_out_extra_slow_in = 0x7f010021
mc.meson.kasumi:color/abc_tint_btn_checkable = 0x7f050013
mc.meson.kasumi:color/abc_secondary_text_material_dark = 0x7f050011
mc.meson.kasumi:attr/isMaterial3Theme = 0x7f03025b
mc.meson.kasumi:color/abc_search_url_text_pressed = 0x7f05000f
mc.meson.kasumi:color/m3_sys_color_dynamic_dark_inverse_surface = 0x7f050185
mc.meson.kasumi:color/m3_sys_color_dark_primary_container = 0x7f050172
mc.meson.kasumi:style/Theme.Material3.Light.Dialog = 0x7f12024e
mc.meson.kasumi:style/Base.ThemeOverlay.AppCompat.Dark = 0x7f120087
mc.meson.kasumi:color/m3_default_color_secondary_text = 0x7f05007f
mc.meson.kasumi:color/abc_btn_colored_text_material = 0x7f050003
mc.meson.kasumi:attr/materialCalendarTheme = 0x7f030319
mc.meson.kasumi:dimen/m3_sys_elevation_level5 = 0x7f0601f5
mc.meson.kasumi:style/Widget.AppCompat.CompoundButton.CheckBox = 0x7f120308
mc.meson.kasumi:color/abc_primary_text_disable_only_material_dark = 0x7f050009
mc.meson.kasumi:integer/abc_config_activityShortDur = 0x7f090001
mc.meson.kasumi:attr/clockFaceBackgroundColor = 0x7f0300e1
mc.meson.kasumi:style/TextAppearance.M3.Sys.Typescale.DisplaySmall = 0x7f1201e5
mc.meson.kasumi:color/abc_background_cache_hint_selector_material_dark = 0x7f050000
mc.meson.kasumi:color/m3_chip_stroke_color = 0x7f050077
mc.meson.kasumi:style/TextAppearance.AppCompat = 0x7f1201a0
mc.meson.kasumi:color/m3_sys_color_dynamic_light_on_surface_variant = 0x7f0501b0
mc.meson.kasumi:attr/actionModeCloseButtonStyle = 0x7f030014
mc.meson.kasumi:attr/multiChoiceItemLayout = 0x7f03037b
mc.meson.kasumi:attr/panelMenuListTheme = 0x7f03039d
mc.meson.kasumi:color/abc_btn_colored_borderless_text_material = 0x7f050002
mc.meson.kasumi:id/content = 0x7f0800a4
mc.meson.kasumi:drawable/abc_switch_thumb_material = 0x7f07006a
mc.meson.kasumi:attr/windowFixedWidthMinor = 0x7f030520
mc.meson.kasumi:attr/windowFixedWidthMajor = 0x7f03051f
mc.meson.kasumi:macro/m3_comp_outlined_text_field_disabled_input_text_color = 0x7f0c00b2
mc.meson.kasumi:dimen/mtrl_calendar_header_toggle_margin_bottom = 0x7f060285
mc.meson.kasumi:attr/indicatorSize = 0x7f030255
mc.meson.kasumi:id/rightToLeft = 0x7f0801b9
mc.meson.kasumi:color/m3_sys_color_dynamic_light_on_tertiary_container = 0x7f0501b2
mc.meson.kasumi:attr/windowActionModeOverlay = 0x7f03051c
mc.meson.kasumi:string/m3_sys_motion_easing_legacy = 0x7f11005e
mc.meson.kasumi:plurals/mtrl_badge_content_description = 0x7f100000
mc.meson.kasumi:drawable/notification_bg_normal_pressed = 0x7f0700f8
mc.meson.kasumi:dimen/m3_sys_motion_easing_legacy_decelerate_control_x2 = 0x7f060207
mc.meson.kasumi:attr/lastBaselineToBottomHeight = 0x7f030283
mc.meson.kasumi:dimen/mtrl_textinput_box_stroke_width_focused = 0x7f060300
mc.meson.kasumi:attr/waveVariesBy = 0x7f030519
mc.meson.kasumi:attr/foregroundInsidePadding = 0x7f030217
mc.meson.kasumi:attr/onPositiveCross = 0x7f03038d
mc.meson.kasumi:attr/titleTextColor = 0x7f0304dc
mc.meson.kasumi:color/background_floating_material_dark = 0x7f05001d
mc.meson.kasumi:attr/queryBackground = 0x7f0303c6
mc.meson.kasumi:attr/wavePhase = 0x7f030517
mc.meson.kasumi:style/Widget.AppCompat.SeekBar = 0x7f120334
mc.meson.kasumi:dimen/m3_small_fab_size = 0x7f0601ed
mc.meson.kasumi:attr/waveOffset = 0x7f030515
mc.meson.kasumi:attr/blendSrc = 0x7f030078
mc.meson.kasumi:attr/warmth = 0x7f030513
mc.meson.kasumi:style/TextAppearance.AppCompat.Body1 = 0x7f1201a1
mc.meson.kasumi:dimen/m3_comp_slider_active_handle_height = 0x7f060181
mc.meson.kasumi:styleable/NavigationBarView = 0x7f130078
mc.meson.kasumi:string/path_password_strike_through = 0x7f1100c7
mc.meson.kasumi:color/m3_sys_color_dynamic_light_on_error_container = 0x7f0501aa
mc.meson.kasumi:attr/visibilityMode = 0x7f030511
mc.meson.kasumi:string/mtrl_picker_toggle_to_year_selection = 0x7f1100af
mc.meson.kasumi:attr/colorTertiaryFixed = 0x7f030133
mc.meson.kasumi:drawable/material_ic_menu_arrow_up_black_24dp = 0x7f0700ca
mc.meson.kasumi:dimen/material_emphasis_high_type = 0x7f060235
mc.meson.kasumi:attr/motionDurationExtraLong4 = 0x7f030351
mc.meson.kasumi:style/Widget.Material3.NavigationRailView.ActiveIndicator = 0x7f1203ca
mc.meson.kasumi:color/m3_button_ripple_color_selector = 0x7f05006c
mc.meson.kasumi:style/Widget.MaterialComponents.TextInputEditText.OutlinedBox.Dense = 0x7f12045b
mc.meson.kasumi:attr/selectorSize = 0x7f0303ee
mc.meson.kasumi:attr/layout_collapseParallaxMultiplier = 0x7f03028e
mc.meson.kasumi:attr/viewTransitionOnNegativeCross = 0x7f03050f
mc.meson.kasumi:style/TextAppearance.AppCompat.Widget.ActionMode.Title.Inverse = 0x7f1201c5
mc.meson.kasumi:attr/viewInflaterClass = 0x7f03050c
mc.meson.kasumi:color/md_theme_dark_background = 0x7f0502bb
mc.meson.kasumi:attr/verticalOffsetWithText = 0x7f03050b
mc.meson.kasumi:attr/windowMinWidthMinor = 0x7f030522
mc.meson.kasumi:attr/verticalOffset = 0x7f03050a
mc.meson.kasumi:string/material_timepicker_am = 0x7f110074
mc.meson.kasumi:attr/useMaterialThemeColors = 0x7f030508
mc.meson.kasumi:attr/triggerSlack = 0x7f030502
mc.meson.kasumi:mipmap/ic_launcher = 0x7f0e0000
mc.meson.kasumi:attr/transitionShapeAppearance = 0x7f0304ff
mc.meson.kasumi:color/m3_ref_palette_dynamic_secondary90 = 0x7f0500e9
mc.meson.kasumi:color/design_dark_default_color_background = 0x7f050036
mc.meson.kasumi:attr/buttonCompat = 0x7f030094
mc.meson.kasumi:dimen/abc_dialog_fixed_width_minor = 0x7f06001f
mc.meson.kasumi:attr/badgeWithTextShapeAppearance = 0x7f030065
mc.meson.kasumi:anim/abc_popup_exit = 0x7f010004
mc.meson.kasumi:animator/m3_card_state_list_anim = 0x7f02000d
mc.meson.kasumi:attr/keyboardIcon = 0x7f03027c
mc.meson.kasumi:attr/transitionPathRotate = 0x7f0304fe
mc.meson.kasumi:id/tag_window_insets_animation_callback = 0x7f08021a
mc.meson.kasumi:dimen/m3_comp_assist_chip_flat_container_elevation = 0x7f0600fc
mc.meson.kasumi:attr/transitionFlags = 0x7f0304fd
mc.meson.kasumi:attr/transitionEasing = 0x7f0304fc
mc.meson.kasumi:style/Widget.Material3.MaterialCalendar.HeaderSelection = 0x7f1203b3
mc.meson.kasumi:drawable/$mtrl_checkbox_button_icon_unchecked_checked__0 = 0x7f070018
mc.meson.kasumi:attr/colorPrimaryContainer = 0x7f03011a
mc.meson.kasumi:attr/fastScrollHorizontalTrackDrawable = 0x7f0301e1
mc.meson.kasumi:attr/lottie_url = 0x7f0302fd
mc.meson.kasumi:color/m3_ref_palette_neutral12 = 0x7f050109
mc.meson.kasumi:id/noState = 0x7f080181
mc.meson.kasumi:attr/trackDecoration = 0x7f0304f1
mc.meson.kasumi:attr/touchAnchorId = 0x7f0304e9
mc.meson.kasumi:attr/backgroundTint = 0x7f030055
mc.meson.kasumi:attr/track = 0x7f0304ec
mc.meson.kasumi:color/material_dynamic_color_light_on_error = 0x7f050224
mc.meson.kasumi:attr/touchAnchorSide = 0x7f0304ea
mc.meson.kasumi:attr/colorTertiary = 0x7f030131
mc.meson.kasumi:attr/tooltipText = 0x7f0304e7
mc.meson.kasumi:style/Widget.Material3.ActionMode = 0x7f12034c
mc.meson.kasumi:color/m3_sys_color_dynamic_secondary_fixed_dim = 0x7f0501cd
mc.meson.kasumi:attr/tooltipStyle = 0x7f0304e6
mc.meson.kasumi:attr/tooltipForegroundColor = 0x7f0304e4
mc.meson.kasumi:macro/m3_comp_outlined_card_hover_outline_color = 0x7f0c00ad
mc.meson.kasumi:dimen/m3_comp_input_chip_with_avatar_avatar_size = 0x7f060133
mc.meson.kasumi:attr/toolbarId = 0x7f0304e0
mc.meson.kasumi:dimen/design_navigation_item_icon_padding = 0x7f060079
mc.meson.kasumi:attr/toggleCheckedStateOnClick = 0x7f0304df
mc.meson.kasumi:color/m3_sys_color_dark_surface_dim = 0x7f05017c
mc.meson.kasumi:drawable/notification_bg_normal = 0x7f0700f7
mc.meson.kasumi:attr/titleTextStyle = 0x7f0304de
mc.meson.kasumi:color/material_personalized_color_tertiary_container = 0x7f0502a6
mc.meson.kasumi:attr/compatShadowEnabled = 0x7f030136
mc.meson.kasumi:attr/titleTextAppearance = 0x7f0304db
mc.meson.kasumi:attr/titleMargins = 0x7f0304d9
mc.meson.kasumi:dimen/m3_btn_max_width = 0x7f0600d8
mc.meson.kasumi:attr/titleMarginEnd = 0x7f0304d6
mc.meson.kasumi:attr/animateRelativeTo = 0x7f030038
mc.meson.kasumi:attr/textFillColor = 0x7f0304a0
mc.meson.kasumi:color/m3_ref_palette_dynamic_neutral12 = 0x7f0500a5
mc.meson.kasumi:attr/region_widthMoreThan = 0x7f0303d6
mc.meson.kasumi:attr/splitMinSmallestWidth = 0x7f03041a
mc.meson.kasumi:attr/titleMarginBottom = 0x7f0304d5
mc.meson.kasumi:attr/alwaysExpand = 0x7f030034
mc.meson.kasumi:attr/round = 0x7f0303dc
mc.meson.kasumi:attr/titleMargin = 0x7f0304d4
mc.meson.kasumi:drawable/abc_btn_radio_to_on_mtrl_000 = 0x7f070034
mc.meson.kasumi:attr/trackDecorationTintMode = 0x7f0304f3
mc.meson.kasumi:attr/tint = 0x7f0304cd
mc.meson.kasumi:drawable/mtrl_checkbox_button = 0x7f0700cd
mc.meson.kasumi:attr/tickRadiusActive = 0x7f0304ca
mc.meson.kasumi:attr/marginRightSystemWindowInsets = 0x7f030301
mc.meson.kasumi:dimen/material_time_picker_minimum_screen_width = 0x7f060245
mc.meson.kasumi:dimen/m3_comp_elevated_button_disabled_container_elevation = 0x7f060109
mc.meson.kasumi:attr/errorIconDrawable = 0x7f0301bc
mc.meson.kasumi:attr/colorErrorContainer = 0x7f030101
mc.meson.kasumi:attr/tickMarkTint = 0x7f0304c8
mc.meson.kasumi:color/design_bottom_navigation_shadow_color = 0x7f050034
mc.meson.kasumi:attr/tickMark = 0x7f0304c7
mc.meson.kasumi:attr/tickColorInactive = 0x7f0304c6
mc.meson.kasumi:id/material_minute_tv = 0x7f080141
mc.meson.kasumi:color/m3_sys_color_light_error_container = 0x7f0501d2
mc.meson.kasumi:color/m3_sys_color_dynamic_dark_outline_variant = 0x7f050192
mc.meson.kasumi:attr/wavePeriod = 0x7f030516
mc.meson.kasumi:attr/waveDecay = 0x7f030514
mc.meson.kasumi:dimen/design_snackbar_max_width = 0x7f060083
mc.meson.kasumi:color/m3_sys_color_dark_on_surface = 0x7f05016b
mc.meson.kasumi:attr/thumbTrackGapSize = 0x7f0304c2
mc.meson.kasumi:id/horizontal_only = 0x7f080105
mc.meson.kasumi:color/m3_sys_color_dynamic_dark_primary = 0x7f050193
mc.meson.kasumi:attr/thumbTextPadding = 0x7f0304bf
mc.meson.kasumi:style/Base.TextAppearance.AppCompat.Widget.ActionBar.Subtitle.Inverse = 0x7f120041
mc.meson.kasumi:macro/m3_comp_search_view_header_leading_icon_color = 0x7f0c00f6
mc.meson.kasumi:attr/layout_goneMarginEnd = 0x7f0302c2
mc.meson.kasumi:attr/motionEffect_move = 0x7f03036c
mc.meson.kasumi:color/design_fab_stroke_top_outer_color = 0x7f050057
mc.meson.kasumi:style/Widget.MaterialComponents.BottomNavigationView.Colored = 0x7f120407
mc.meson.kasumi:attr/materialCircleRadius = 0x7f03031f
mc.meson.kasumi:attr/layout_constraintVertical_chainStyle = 0x7f0302b6
mc.meson.kasumi:style/MaterialAlertDialog.MaterialComponents.Picker.Date.Calendar = 0x7f12013b
mc.meson.kasumi:string/bottomsheet_action_collapse = 0x7f110025
mc.meson.kasumi:attr/thumbRadius = 0x7f0304bc
mc.meson.kasumi:animator/m3_extended_fab_change_size_expand_motion_spec = 0x7f020011
mc.meson.kasumi:attr/actionBarSplitStyle = 0x7f030007
mc.meson.kasumi:macro/m3_comp_time_picker_period_selector_unselected_hover_state_layer_color = 0x7f0c015b
mc.meson.kasumi:attr/thumbIconTint = 0x7f0304ba
mc.meson.kasumi:attr/motionEasingEmphasizedDecelerateInterpolator = 0x7f030362
mc.meson.kasumi:dimen/notification_top_pad_large_text = 0x7f06031a
mc.meson.kasumi:dimen/m3_comp_slider_disabled_active_track_opacity = 0x7f060184
mc.meson.kasumi:anim/fade_out = 0x7f01001e
mc.meson.kasumi:macro/m3_comp_navigation_drawer_headline_color = 0x7f0c0084
mc.meson.kasumi:attr/thumbIconSize = 0x7f0304b9
mc.meson.kasumi:color/m3_sys_color_dynamic_dark_surface_container = 0x7f050199
mc.meson.kasumi:color/m3_ref_palette_error30 = 0x7f0500fd
mc.meson.kasumi:attr/thumbIcon = 0x7f0304b8
mc.meson.kasumi:drawable/ic_kasumi_logo = 0x7f0700a6
mc.meson.kasumi:attr/itemRippleColor = 0x7f03026a
mc.meson.kasumi:attr/thumbElevation = 0x7f0304b6
mc.meson.kasumi:attr/thumbTintMode = 0x7f0304c1
mc.meson.kasumi:attr/popUpToSaveState = 0x7f0303b6
mc.meson.kasumi:style/ThemeOverlay.MaterialComponents.Dialog.Alert.Framework = 0x7f1202de
mc.meson.kasumi:style/Base.Widget.AppCompat.CompoundButton.RadioButton = 0x7f1200e3
mc.meson.kasumi:id/spread_inside = 0x7f0801f2
mc.meson.kasumi:color/bottom_nav_item_color = 0x7f050022
mc.meson.kasumi:style/Base.V28.Theme.AppCompat = 0x7f1200c5
mc.meson.kasumi:dimen/m3_comp_filter_chip_flat_container_elevation = 0x7f06012d
mc.meson.kasumi:dimen/m3_chip_elevated_elevation = 0x7f0600f7
mc.meson.kasumi:style/RtlOverlay.Widget.AppCompat.Search.DropDown = 0x7f12015a
mc.meson.kasumi:mipmap/ic_launcher_round = 0x7f0e0001
mc.meson.kasumi:dimen/m3_comp_navigation_bar_active_indicator_width = 0x7f060137
mc.meson.kasumi:attr/startIconTint = 0x7f03042c
mc.meson.kasumi:attr/barLength = 0x7f030068
mc.meson.kasumi:color/abc_background_cache_hint_selector_material_light = 0x7f050001
mc.meson.kasumi:macro/m3_comp_suggestion_chip_label_text_type = 0x7f0c0118
mc.meson.kasumi:drawable/ic_m3_chip_close = 0x7f0700ac
mc.meson.kasumi:color/primary_text_default_material_light = 0x7f050341
mc.meson.kasumi:attr/attributeName = 0x7f030040
mc.meson.kasumi:attr/searchHintIcon = 0x7f0303e4
mc.meson.kasumi:style/Base.Widget.Material3.FloatingActionButton.Large = 0x7f120116
mc.meson.kasumi:attr/checkedButton = 0x7f0300b8
mc.meson.kasumi:dimen/mtrl_slider_tick_radius = 0x7f0602ec
mc.meson.kasumi:attr/theme = 0x7f0304b3
mc.meson.kasumi:color/accent_material_dark = 0x7f050019
mc.meson.kasumi:attr/textureWidth = 0x7f0304b2
mc.meson.kasumi:drawable/$mtrl_switch_thumb_checked_pressed__0 = 0x7f070021
mc.meson.kasumi:styleable/AppCompatImageView = 0x7f130011
mc.meson.kasumi:attr/crossfade = 0x7f030163
mc.meson.kasumi:macro/m3_comp_search_bar_supporting_text_type = 0x7f0c00ef
mc.meson.kasumi:color/m3_sys_color_dynamic_on_primary_fixed_variant = 0x7f0501c5
mc.meson.kasumi:id/accessibility_custom_action_6 = 0x7f08002d
mc.meson.kasumi:attr/textInputOutlinedExposedDropdownMenuStyle = 0x7f0304a6
mc.meson.kasumi:attr/textInputOutlinedDenseStyle = 0x7f0304a5
mc.meson.kasumi:style/Theme.MaterialComponents.NoActionBar = 0x7f120281
mc.meson.kasumi:style/TextAppearance.Widget.AppCompat.Toolbar.Subtitle = 0x7f120216
mc.meson.kasumi:macro/m3_comp_time_picker_period_selector_selected_pressed_state_layer_color = 0x7f0c0159
mc.meson.kasumi:color/mtrl_choice_chip_background_color = 0x7f050313
mc.meson.kasumi:attr/motionEasingStandardInterpolator = 0x7f030369
mc.meson.kasumi:attr/materialSearchBarStyle = 0x7f030328
mc.meson.kasumi:attr/state_dragged = 0x7f030432
mc.meson.kasumi:attr/textInputFilledStyle = 0x7f0304a3
mc.meson.kasumi:attr/textEndPadding = 0x7f03049f
mc.meson.kasumi:style/TextAppearance.Widget.AppCompat.ExpandedMenu.Item = 0x7f120215
mc.meson.kasumi:attr/lottie_loop = 0x7f0302f6
mc.meson.kasumi:attr/textAppearanceHeadline6 = 0x7f030482
mc.meson.kasumi:anim/abc_slide_out_top = 0x7f010009
mc.meson.kasumi:id/indicatorWorld = 0x7f080113
mc.meson.kasumi:drawable/mtrl_ic_checkbox_checked = 0x7f0700dd
mc.meson.kasumi:attr/colorContainer = 0x7f0300fc
mc.meson.kasumi:color/m3_dynamic_highlighted_text = 0x7f050087
mc.meson.kasumi:attr/textColorSearchUrl = 0x7f03049e
mc.meson.kasumi:attr/menu = 0x7f03033d
mc.meson.kasumi:style/ShapeAppearance.MaterialComponents.Badge = 0x7f120188
mc.meson.kasumi:attr/marginLeftSystemWindowInsets = 0x7f030300
mc.meson.kasumi:style/ThemeOverlay.MaterialComponents.Dark = 0x7f1202d9
mc.meson.kasumi:id/wrap_content = 0x7f08026a
mc.meson.kasumi:attr/textColorAlertDialogListItem = 0x7f03049d
mc.meson.kasumi:macro/m3_comp_extended_fab_tertiary_container_color = 0x7f0c0034
mc.meson.kasumi:attr/textBackgroundZoom = 0x7f03049c
mc.meson.kasumi:attr/actionBarStyle = 0x7f030008
mc.meson.kasumi:dimen/design_snackbar_elevation = 0x7f060081
mc.meson.kasumi:color/m3_sys_color_dynamic_light_on_error = 0x7f0501a9
mc.meson.kasumi:style/Base.Widget.AppCompat.Button.Small = 0x7f1200df
mc.meson.kasumi:attr/textBackgroundRotate = 0x7f03049b
mc.meson.kasumi:color/material_dynamic_secondary100 = 0x7f05024f
mc.meson.kasumi:id/accessibility_custom_action_28 = 0x7f080026
mc.meson.kasumi:color/m3_ref_palette_dynamic_neutral_variant20 = 0x7f0500bf
mc.meson.kasumi:attr/textInputLayoutFocusedRectEnabled = 0x7f0304a4
mc.meson.kasumi:attr/startIconCheckable = 0x7f030427
mc.meson.kasumi:attr/contentPaddingStart = 0x7f03014b
mc.meson.kasumi:style/Animation.AppCompat.Tooltip = 0x7f120004
mc.meson.kasumi:attr/textBackgroundPanY = 0x7f03049a
mc.meson.kasumi:attr/region_heightLessThan = 0x7f0303d3
mc.meson.kasumi:color/bright_foreground_inverse_material_light = 0x7f050026
mc.meson.kasumi:attr/textBackgroundPanX = 0x7f030499
mc.meson.kasumi:macro/m3_comp_slider_inactive_track_color = 0x7f0c0110
mc.meson.kasumi:drawable/mtrl_checkbox_button_checked_unchecked = 0x7f0700ce
mc.meson.kasumi:attr/textBackground = 0x7f030498
mc.meson.kasumi:attr/badgeTextAppearance = 0x7f03005e
mc.meson.kasumi:color/material_personalized_color_primary_inverse = 0x7f050294
mc.meson.kasumi:attr/textAppearanceTitleLarge = 0x7f030495
mc.meson.kasumi:style/TextAppearance.AppCompat.Medium.Inverse = 0x7f1201b2
mc.meson.kasumi:attr/materialSwitchStyle = 0x7f03032d
mc.meson.kasumi:attr/textAppearanceSubtitle2 = 0x7f030494
mc.meson.kasumi:color/call_notification_decline_color = 0x7f05002c
mc.meson.kasumi:attr/textAppearanceSmallPopupMenu = 0x7f030492
mc.meson.kasumi:style/Theme.MaterialComponents.Light.DarkActionBar = 0x7f120274
mc.meson.kasumi:drawable/btn_radio_off_mtrl = 0x7f07007e
mc.meson.kasumi:attr/actionModeCutDrawable = 0x7f030018
mc.meson.kasumi:attr/checkedIcon = 0x7f0300ba
mc.meson.kasumi:attr/textAppearanceLabelMedium = 0x7f030487
mc.meson.kasumi:attr/chipGroupStyle = 0x7f0300c6
mc.meson.kasumi:string/abc_prepend_shortcut_label = 0x7f110011
mc.meson.kasumi:attr/textAppearanceSearchResultTitle = 0x7f030491
mc.meson.kasumi:attr/minSeparation = 0x7f030344
mc.meson.kasumi:drawable/abc_popup_background_mtrl_mult = 0x7f07005a
mc.meson.kasumi:color/m3_sys_color_dark_secondary = 0x7f050173
mc.meson.kasumi:attr/motionEasingEmphasized = 0x7f030360
mc.meson.kasumi:color/m3_sys_color_dark_outline = 0x7f05016f
mc.meson.kasumi:string/abc_menu_delete_shortcut_label = 0x7f11000a
mc.meson.kasumi:attr/switchMinWidth = 0x7f03044c
mc.meson.kasumi:style/Base.ThemeOverlay.AppCompat.Dialog = 0x7f120089
mc.meson.kasumi:dimen/material_filled_edittext_font_1_3_padding_top = 0x7f060238
mc.meson.kasumi:attr/textInputFilledExposedDropdownMenuStyle = 0x7f0304a2
mc.meson.kasumi:attr/paddingEnd = 0x7f030395
mc.meson.kasumi:color/m3_ref_palette_error80 = 0x7f050102
mc.meson.kasumi:attr/textAppearanceListItemSmall = 0x7f03048d
mc.meson.kasumi:dimen/abc_seekbar_track_progress_height_material = 0x7f060039
mc.meson.kasumi:attr/yearStyle = 0x7f030525
mc.meson.kasumi:attr/applyMotionScene = 0x7f03003b
mc.meson.kasumi:color/m3_ref_palette_tertiary40 = 0x7f05014a
mc.meson.kasumi:style/Widget.Material3.SearchBar.Outlined = 0x7f1203d4
mc.meson.kasumi:dimen/m3_side_sheet_margin_detached = 0x7f0601e5
mc.meson.kasumi:style/Widget.Material3.Button.UnelevatedButton = 0x7f12036f
mc.meson.kasumi:dimen/m3_comp_primary_navigation_tab_active_indicator_height = 0x7f06015c
mc.meson.kasumi:dimen/notification_small_icon_size_as_large = 0x7f060317
mc.meson.kasumi:attr/textAppearanceHeadline5 = 0x7f030481
mc.meson.kasumi:attr/layout_dodgeInsetEdges = 0x7f0302bd
mc.meson.kasumi:color/cardview_shadow_end_color = 0x7f050032
mc.meson.kasumi:id/group_divider = 0x7f0800fa
mc.meson.kasumi:attr/sideSheetDialogTheme = 0x7f030407
mc.meson.kasumi:attr/textAppearanceHeadline2 = 0x7f03047e
mc.meson.kasumi:layout/m3_auto_complete_simple_item = 0x7f0b0046
mc.meson.kasumi:attr/textAppearanceDisplayLarge = 0x7f03047a
mc.meson.kasumi:attr/textAppearanceDisplayMedium = 0x7f03047b
mc.meson.kasumi:drawable/material_ic_calendar_black_24dp = 0x7f0700c2
mc.meson.kasumi:color/m3_sys_color_dynamic_dark_surface_container_highest = 0x7f05019b
mc.meson.kasumi:attr/textAppearanceBodyLarge = 0x7f030475
mc.meson.kasumi:dimen/m3_comp_outlined_text_field_outline_width = 0x7f060159
mc.meson.kasumi:attr/telltales_velocityMode = 0x7f030471
mc.meson.kasumi:attr/endIconDrawable = 0x7f0301ae
mc.meson.kasumi:attr/layout_constraintDimensionRatio = 0x7f03029b
mc.meson.kasumi:string/material_timepicker_clock_mode_description = 0x7f110075
mc.meson.kasumi:id/btnImportConfig = 0x7f080081
mc.meson.kasumi:attr/tabTextColor = 0x7f03046b
mc.meson.kasumi:style/TextAppearance.M3.Sys.Typescale.BodyLarge = 0x7f1201e0
mc.meson.kasumi:macro/m3_comp_radio_button_selected_hover_icon_color = 0x7f0c00da
mc.meson.kasumi:attr/mimeType = 0x7f030341
mc.meson.kasumi:attr/textureBlurFactor = 0x7f0304af
mc.meson.kasumi:style/ThemeOverlay.Material3.DayNight.SideSheetDialog = 0x7f1202a5
mc.meson.kasumi:attr/colorOutline = 0x7f030117
mc.meson.kasumi:attr/tabTextAppearance = 0x7f03046a
mc.meson.kasumi:drawable/notification_icon_background = 0x7f0700f9
mc.meson.kasumi:attr/popupMenuBackground = 0x7f0303b7
mc.meson.kasumi:color/design_default_color_secondary_variant = 0x7f05004e
mc.meson.kasumi:macro/m3_comp_filled_card_container_shape = 0x7f0c0047
mc.meson.kasumi:attr/motionDurationMedium4 = 0x7f030359
mc.meson.kasumi:attr/borderRound = 0x7f030079
mc.meson.kasumi:attr/tabSelectedTextColor = 0x7f030468
mc.meson.kasumi:dimen/m3_badge_size = 0x7f0600b5
mc.meson.kasumi:attr/circularflow_radiusInDP = 0x7f0300dc
mc.meson.kasumi:attr/tabSecondaryStyle = 0x7f030466
mc.meson.kasumi:id/tvModuleStatus = 0x7f080272
mc.meson.kasumi:attr/titleMarginStart = 0x7f0304d7
mc.meson.kasumi:attr/textAppearancePopupMenuHeader = 0x7f03048f
mc.meson.kasumi:string/m3_sys_motion_easing_emphasized_path_data = 0x7f11005d
mc.meson.kasumi:attr/buttonStyleSmall = 0x7f03009c
mc.meson.kasumi:attr/boxCornerRadiusBottomStart = 0x7f030087
mc.meson.kasumi:macro/m3_comp_switch_unselected_pressed_handle_color = 0x7f0c013b
mc.meson.kasumi:attr/tabRippleColor = 0x7f030465
mc.meson.kasumi:dimen/m3_chip_icon_size = 0x7f0600f9
mc.meson.kasumi:styleable/SplitPairRule = 0x7f130091
mc.meson.kasumi:macro/m3_comp_search_bar_pressed_supporting_text_color = 0x7f0c00ed
mc.meson.kasumi:attr/labelStyle = 0x7f030280
mc.meson.kasumi:color/m3_sys_color_light_on_tertiary_container = 0x7f0501e0
mc.meson.kasumi:id/noScroll = 0x7f080180
mc.meson.kasumi:attr/secondaryActivityAction = 0x7f0303e8
mc.meson.kasumi:macro/m3_comp_switch_disabled_unselected_track_color = 0x7f0c011e
mc.meson.kasumi:attr/tabPaddingBottom = 0x7f030461
mc.meson.kasumi:style/Widget.AppCompat.PopupMenu.Overflow = 0x7f12032b
mc.meson.kasumi:style/Theme.Material3.Dark.Dialog = 0x7f120238
mc.meson.kasumi:color/m3_sys_color_dynamic_on_secondary_fixed = 0x7f0501c6
mc.meson.kasumi:color/m3_ref_palette_dynamic_neutral6 = 0x7f0500ae
mc.meson.kasumi:id/special_effects_controller_view_tag = 0x7f0801ee
mc.meson.kasumi:attr/tabMode = 0x7f03045f
mc.meson.kasumi:style/Widget.AppCompat.ActionButton.Overflow = 0x7f1202fc
mc.meson.kasumi:color/m3_icon_button_icon_color_selector = 0x7f050092
mc.meson.kasumi:id/open_search_view_dummy_toolbar = 0x7f080190
mc.meson.kasumi:attr/buttonIconTint = 0x7f030098
mc.meson.kasumi:attr/tabMinWidth = 0x7f03045e
mc.meson.kasumi:dimen/m3_bottom_sheet_drag_handle_bottom_padding = 0x7f0600c2
mc.meson.kasumi:attr/tabMaxWidth = 0x7f03045d
mc.meson.kasumi:attr/tabInlineLabel = 0x7f03045c
mc.meson.kasumi:dimen/m3_comp_filled_autocomplete_menu_container_elevation = 0x7f060121
mc.meson.kasumi:dimen/m3_small_fab_max_image_size = 0x7f0601ec
mc.meson.kasumi:color/foreground_material_dark = 0x7f050060
mc.meson.kasumi:attr/tabIndicatorFullWidth = 0x7f030459
mc.meson.kasumi:attr/maxButtonHeight = 0x7f030334
mc.meson.kasumi:attr/tabGravity = 0x7f030452
mc.meson.kasumi:attr/suffixTextColor = 0x7f03044a
mc.meson.kasumi:style/Widget.MaterialComponents.TextInputLayout.OutlinedBox = 0x7f120460
mc.meson.kasumi:attr/textAppearanceBody2 = 0x7f030474
mc.meson.kasumi:attr/boxCornerRadiusTopStart = 0x7f030089
mc.meson.kasumi:attr/suffixText = 0x7f030448
mc.meson.kasumi:attr/subtitleTextColor = 0x7f030446
mc.meson.kasumi:dimen/mtrl_switch_thumb_size = 0x7f0602f9
mc.meson.kasumi:attr/endIconTint = 0x7f0301b2
mc.meson.kasumi:integer/mtrl_switch_track_viewport_width = 0x7f09003e
mc.meson.kasumi:attr/itemPadding = 0x7f030267
mc.meson.kasumi:attr/colorPrimaryVariant = 0x7f030120
mc.meson.kasumi:attr/subtitle = 0x7f030443
mc.meson.kasumi:attr/subheaderInsetStart = 0x7f030440
mc.meson.kasumi:string/mtrl_picker_text_input_date_hint = 0x7f1100a5
mc.meson.kasumi:attr/divider = 0x7f030186
mc.meson.kasumi:attr/windowFixedHeightMinor = 0x7f03051e
mc.meson.kasumi:attr/motionDurationMedium3 = 0x7f030358
mc.meson.kasumi:color/material_personalized_color_secondary_text = 0x7f050299
mc.meson.kasumi:style/Base.Theme.Material3.Light.BottomSheetDialog = 0x7f12006d
mc.meson.kasumi:attr/thumbStrokeWidth = 0x7f0304be
mc.meson.kasumi:dimen/mtrl_tooltip_padding = 0x7f06030a
mc.meson.kasumi:attr/subheaderColor = 0x7f03043e
mc.meson.kasumi:color/m3_sys_color_light_surface_container = 0x7f0501e9
mc.meson.kasumi:attr/subtitleTextAppearance = 0x7f030445
mc.meson.kasumi:attr/statusBarBackground = 0x7f030438
mc.meson.kasumi:style/ShapeAppearance.Material3.Corner.Full = 0x7f12017d
mc.meson.kasumi:color/m3_ref_palette_dynamic_neutral20 = 0x7f0500a7
mc.meson.kasumi:string/mtrl_timepicker_cancel = 0x7f1100b8
mc.meson.kasumi:attr/state_lifted = 0x7f030436
mc.meson.kasumi:attr/state_collapsed = 0x7f030430
mc.meson.kasumi:string/mtrl_badge_numberless_content_description = 0x7f11007c
mc.meson.kasumi:attr/toolbarSurfaceStyle = 0x7f0304e3
mc.meson.kasumi:style/Widget.MaterialComponents.PopupMenu = 0x7f12044b
mc.meson.kasumi:anim/nav_default_exit_anim = 0x7f010032
mc.meson.kasumi:attr/stateLabels = 0x7f03042e
mc.meson.kasumi:style/Theme.Material3.Dark = 0x7f120236
mc.meson.kasumi:id/llModeOptions = 0x7f080129
mc.meson.kasumi:color/m3_text_button_ripple_color_selector = 0x7f050206
mc.meson.kasumi:style/ShapeAppearanceOverlay.Material3.Corner.Bottom = 0x7f12018f
mc.meson.kasumi:attr/paddingBottomSystemWindowInsets = 0x7f030394
mc.meson.kasumi:attr/startIconTintMode = 0x7f03042d
mc.meson.kasumi:attr/startIconScaleType = 0x7f03042b
mc.meson.kasumi:id/switchNotifications = 0x7f08020a
mc.meson.kasumi:dimen/mtrl_extended_fab_end_padding_icon = 0x7f0602ab
mc.meson.kasumi:dimen/m3_comp_sheet_bottom_docked_modal_container_elevation = 0x7f06017c
mc.meson.kasumi:attr/fastScrollHorizontalThumbDrawable = 0x7f0301e0
mc.meson.kasumi:color/md_theme_light_surface = 0x7f0502f2
mc.meson.kasumi:style/Widget.AppCompat.Spinner.DropDown.ActionBar = 0x7f120338
mc.meson.kasumi:attr/srcCompat = 0x7f030423
mc.meson.kasumi:attr/springMass = 0x7f030420
mc.meson.kasumi:integer/m3_sys_motion_duration_long3 = 0x7f090016
mc.meson.kasumi:attr/lottie_speed = 0x7f0302fc
mc.meson.kasumi:dimen/abc_cascading_menus_min_smallest_width = 0x7f060016
mc.meson.kasumi:attr/fontWeight = 0x7f030214
mc.meson.kasumi:style/TextAppearance.Material3.BodyMedium = 0x7f1201f2
mc.meson.kasumi:attr/springDamping = 0x7f03041f
mc.meson.kasumi:string/mtrl_picker_date_header_unselected = 0x7f110094
mc.meson.kasumi:color/bright_foreground_material_dark = 0x7f050027
mc.meson.kasumi:attr/iconGravity = 0x7f030241
mc.meson.kasumi:attr/splitRatio = 0x7f03041c
mc.meson.kasumi:style/Base.V14.Widget.MaterialComponents.AutoCompleteTextView = 0x7f1200ad
mc.meson.kasumi:color/material_on_surface_emphasis_high_type = 0x7f050279
mc.meson.kasumi:attr/altSrc = 0x7f030033
mc.meson.kasumi:macro/m3_comp_sheet_bottom_docked_drag_handle_color = 0x7f0c0106
mc.meson.kasumi:attr/splitMinWidth = 0x7f03041b
mc.meson.kasumi:style/Widget.Material3.Button.TextButton.Dialog.Flush = 0x7f120369
mc.meson.kasumi:attr/layout_constraintGuide_percent = 0x7f0302a0
mc.meson.kasumi:id/masked = 0x7f080132
mc.meson.kasumi:attr/spinnerStyle = 0x7f030418
mc.meson.kasumi:color/dim_foreground_disabled_material_dark = 0x7f05005a
mc.meson.kasumi:attr/spinnerDropDownItemStyle = 0x7f030417
mc.meson.kasumi:attr/dropDownListViewStyle = 0x7f03019f
mc.meson.kasumi:attr/spinBars = 0x7f030416
mc.meson.kasumi:style/Base.TextAppearance.MaterialComponents.Badge = 0x7f120051
mc.meson.kasumi:attr/submitBackground = 0x7f030442
mc.meson.kasumi:style/ThemeOverlay.MaterialComponents.Light.Dialog.Alert.Framework = 0x7f1202e0
mc.meson.kasumi:attr/sliderStyle = 0x7f030411
mc.meson.kasumi:color/material_timepicker_button_stroke = 0x7f0502b7
mc.meson.kasumi:styleable/State = 0x7f130093
mc.meson.kasumi:attr/sizePercent = 0x7f030410
mc.meson.kasumi:attr/fabCradleMargin = 0x7f0301da
mc.meson.kasumi:layout/mtrl_alert_select_dialog_item = 0x7f0b005a
mc.meson.kasumi:attr/singleSelection = 0x7f03040f
mc.meson.kasumi:attr/marginTopSystemWindowInsets = 0x7f030302
mc.meson.kasumi:attr/singleLine = 0x7f03040e
mc.meson.kasumi:attr/singleChoiceItemLayout = 0x7f03040d
mc.meson.kasumi:attr/navigationIconTint = 0x7f03037f
mc.meson.kasumi:animator/m3_elevated_chip_state_list_anim = 0x7f02000f
mc.meson.kasumi:string/call_notification_hang_up_action = 0x7f11002d
mc.meson.kasumi:color/material_personalized_color_secondary_text_inverse = 0x7f05029a
mc.meson.kasumi:style/TextAppearance.AppCompat.Inverse = 0x7f1201aa
mc.meson.kasumi:attr/simpleItems = 0x7f03040c
mc.meson.kasumi:styleable/ActionMenuItemView = 0x7f130002
mc.meson.kasumi:attr/colorSurface = 0x7f030126
mc.meson.kasumi:dimen/design_snackbar_min_width = 0x7f060084
mc.meson.kasumi:color/mtrl_card_view_ripple = 0x7f05030e
mc.meson.kasumi:style/ShapeAppearanceOverlay.Material3.NavigationView.Item = 0x7f120194
mc.meson.kasumi:attr/sideSheetModalStyle = 0x7f030408
mc.meson.kasumi:anim/btn_checkbox_to_unchecked_box_inner_merged_animation = 0x7f010010
mc.meson.kasumi:styleable/GradientColorItem = 0x7f130041
mc.meson.kasumi:attr/shrinkMotionSpec = 0x7f030406
mc.meson.kasumi:id/north = 0x7f080184
mc.meson.kasumi:attr/showText = 0x7f030404
mc.meson.kasumi:attr/showPaths = 0x7f030403
mc.meson.kasumi:string/mtrl_picker_confirm = 0x7f110091
mc.meson.kasumi:attr/showMarker = 0x7f030401
mc.meson.kasumi:attr/showDividers = 0x7f030400
mc.meson.kasumi:attr/tickRadiusInactive = 0x7f0304cb
mc.meson.kasumi:attr/showAnimationBehavior = 0x7f0303fd
mc.meson.kasumi:id/collapsingToolbar = 0x7f080270
mc.meson.kasumi:attr/shapeCornerFamily = 0x7f0303fa
mc.meson.kasumi:attr/layout_constraintTop_toBottomOf = 0x7f0302b3
mc.meson.kasumi:attr/shapeAppearanceOverlay = 0x7f0303f8
mc.meson.kasumi:attr/font = 0x7f030209
mc.meson.kasumi:attr/homeAsUpIndicator = 0x7f03023a
mc.meson.kasumi:color/dim_foreground_disabled_material_light = 0x7f05005b
mc.meson.kasumi:color/design_fab_stroke_end_outer_color = 0x7f050055
mc.meson.kasumi:style/Widget.MaterialComponents.NavigationRailView = 0x7f120445
mc.meson.kasumi:attr/layout_editor_absoluteX = 0x7f0302be
mc.meson.kasumi:style/TextAppearance.Design.Hint = 0x7f1201da
mc.meson.kasumi:color/design_dark_default_color_on_background = 0x7f050038
mc.meson.kasumi:attr/simpleItemSelectedColor = 0x7f03040a
mc.meson.kasumi:dimen/design_bottom_navigation_height = 0x7f060063
mc.meson.kasumi:attr/customIntegerValue = 0x7f03016d
mc.meson.kasumi:dimen/abc_action_bar_subtitle_bottom_margin_material = 0x7f06000b
mc.meson.kasumi:attr/suffixTextAppearance = 0x7f030449
mc.meson.kasumi:color/abc_search_url_text = 0x7f05000d
mc.meson.kasumi:attr/shapeAppearanceCornerSmall = 0x7f0303f5
mc.meson.kasumi:drawable/abc_btn_colored_material = 0x7f070030
mc.meson.kasumi:style/Theme.MaterialComponents.Dialog.Alert.Bridge = 0x7f12026a
mc.meson.kasumi:attr/path_percent = 0x7f0303a5
mc.meson.kasumi:string/search_menu_title = 0x7f1100ca
mc.meson.kasumi:attr/shapeAppearanceCornerExtraLarge = 0x7f0303f1
mc.meson.kasumi:attr/tabBackground = 0x7f030450
mc.meson.kasumi:attr/shapeAppearance = 0x7f0303f0
mc.meson.kasumi:attr/liftOnScrollColor = 0x7f0302d0
mc.meson.kasumi:attr/hintAnimationEnabled = 0x7f030236
mc.meson.kasumi:attr/seekBarStyle = 0x7f0303ea
mc.meson.kasumi:color/card_background_primary = 0x7f05002d
mc.meson.kasumi:style/Widget.Material3.CardView.Elevated = 0x7f120370
mc.meson.kasumi:attr/shapeAppearanceLargeComponent = 0x7f0303f6
mc.meson.kasumi:dimen/mtrl_card_dragged_z = 0x7f06029e
mc.meson.kasumi:attr/tabSelectedTextAppearance = 0x7f030467
mc.meson.kasumi:attr/enterAnim = 0x7f0301b7
mc.meson.kasumi:style/Widget.Material3.Button.OutlinedButton.Icon = 0x7f120366
mc.meson.kasumi:attr/scrimAnimationDuration = 0x7f0303e1
mc.meson.kasumi:attr/shapeAppearanceCornerMedium = 0x7f0303f4
mc.meson.kasumi:attr/scaleFromTextSize = 0x7f0303e0
mc.meson.kasumi:layout/compact_category_page = 0x7f0b001f
mc.meson.kasumi:id/icon = 0x7f080106
mc.meson.kasumi:attr/methodName = 0x7f030340
mc.meson.kasumi:color/m3_button_foreground_color_selector = 0x7f050069
mc.meson.kasumi:style/Widget.MaterialComponents.ExtendedFloatingActionButton = 0x7f120426
mc.meson.kasumi:attr/measureWithLargestChild = 0x7f03033c
mc.meson.kasumi:style/Widget.MaterialComponents.TabLayout = 0x7f120455
mc.meson.kasumi:macro/m3_sys_color_dark_surface_tint = 0x7f0c0175
mc.meson.kasumi:attr/rippleColor = 0x7f0303da
mc.meson.kasumi:macro/m3_comp_time_input_time_input_field_label_text_color = 0x7f0c0149
mc.meson.kasumi:attr/region_heightMoreThan = 0x7f0303d4
mc.meson.kasumi:style/Widget.MaterialComponents.ActionMode = 0x7f1203fa
mc.meson.kasumi:attr/recyclerViewStyle = 0x7f0303d2
mc.meson.kasumi:attr/reactiveGuide_valueId = 0x7f0303d1
mc.meson.kasumi:dimen/m3_large_fab_size = 0x7f0601ba
mc.meson.kasumi:dimen/design_fab_image_size = 0x7f060070
mc.meson.kasumi:color/m3_sys_color_dynamic_light_on_background = 0x7f0501a8
mc.meson.kasumi:attr/ratingBarStyleSmall = 0x7f0303cd
mc.meson.kasumi:attr/autoCompleteTextViewStyle = 0x7f030043
mc.meson.kasumi:color/m3_navigation_item_icon_tint = 0x7f050097
mc.meson.kasumi:macro/m3_comp_extended_fab_secondary_container_color = 0x7f0c0030
mc.meson.kasumi:attr/textAppearanceLineHeightEnabled = 0x7f03048a
mc.meson.kasumi:dimen/mtrl_bottomappbar_fab_cradle_vertical_offset = 0x7f060257
mc.meson.kasumi:styleable/include = 0x7f1300aa
mc.meson.kasumi:attr/ratingBarStyleIndicator = 0x7f0303cc
mc.meson.kasumi:style/ThemeOverlay.AppCompat.DayNight = 0x7f120287
mc.meson.kasumi:dimen/m3_comp_extended_fab_primary_container_height = 0x7f06010d
mc.meson.kasumi:color/m3_sys_color_dynamic_dark_tertiary = 0x7f0501a0
mc.meson.kasumi:color/m3_ref_palette_neutral90 = 0x7f050117
mc.meson.kasumi:dimen/m3_sys_motion_easing_linear_control_x1 = 0x7f06020a
mc.meson.kasumi:anim/mtrl_card_lowers_interpolator = 0x7f010030
mc.meson.kasumi:attr/layout_wrapBehaviorInParent = 0x7f0302ce
mc.meson.kasumi:id/tag_state_description = 0x7f080216
mc.meson.kasumi:color/m3_ref_palette_tertiary100 = 0x7f050147
mc.meson.kasumi:attr/rangeFillColor = 0x7f0303ca
mc.meson.kasumi:layout/abc_action_menu_layout = 0x7f0b0003
mc.meson.kasumi:color/mtrl_textinput_default_box_stroke_color = 0x7f050335
mc.meson.kasumi:drawable/ic_kasumi_floating = 0x7f0700a5
mc.meson.kasumi:attr/queryPatterns = 0x7f0303c8
mc.meson.kasumi:color/material_on_background_emphasis_medium = 0x7f050274
mc.meson.kasumi:attr/chipSpacingHorizontal = 0x7f0300cf
mc.meson.kasumi:attr/actionModeCopyDrawable = 0x7f030017
mc.meson.kasumi:attr/quantizeMotionSteps = 0x7f0303c5
mc.meson.kasumi:attr/quantizeMotionInterpolator = 0x7f0303c3
mc.meson.kasumi:string/connected = 0x7f110038
mc.meson.kasumi:color/m3_ref_palette_dynamic_neutral_variant96 = 0x7f0500cf
mc.meson.kasumi:attr/progressBarPadding = 0x7f0303c1
mc.meson.kasumi:color/m3_chip_ripple_color = 0x7f050076
mc.meson.kasumi:attr/primaryActivityName = 0x7f0303c0
mc.meson.kasumi:attr/preserveIconSpacing = 0x7f0303be
mc.meson.kasumi:attr/actionProviderClass = 0x7f030024
mc.meson.kasumi:attr/popupTheme = 0x7f0303b9
mc.meson.kasumi:attr/prefixTextAppearance = 0x7f0303bc
mc.meson.kasumi:macro/m3_comp_navigation_drawer_inactive_pressed_state_layer_color = 0x7f0c0090
mc.meson.kasumi:integer/mtrl_chip_anim_duration = 0x7f090036
mc.meson.kasumi:attr/expandedTitleMarginBottom = 0x7f0301c8
mc.meson.kasumi:attr/popUpToInclusive = 0x7f0303b5
mc.meson.kasumi:style/Base.V23.Theme.AppCompat = 0x7f1200bc
mc.meson.kasumi:color/primary_material_dark = 0x7f05033e
mc.meson.kasumi:attr/popupWindowStyle = 0x7f0303ba
mc.meson.kasumi:attr/popUpTo = 0x7f0303b4
mc.meson.kasumi:style/Theme.MaterialComponents.Light.Dialog.MinWidth.Bridge = 0x7f12027d
mc.meson.kasumi:style/MaterialAlertDialog.Material3.Title.Text.CenterStacked = 0x7f120138
mc.meson.kasumi:attr/topInsetScrimEnabled = 0x7f0304e8
mc.meson.kasumi:id/tvConfigTime = 0x7f080245
mc.meson.kasumi:attr/thumbHeight = 0x7f0304b7
mc.meson.kasumi:attr/materialCardViewOutlinedStyle = 0x7f03031d
mc.meson.kasumi:attr/addElevationShadow = 0x7f03002b
mc.meson.kasumi:attr/popEnterAnim = 0x7f0303b2
mc.meson.kasumi:drawable/card_background = 0x7f070083
mc.meson.kasumi:attr/polarRelativeTo = 0x7f0303b1
mc.meson.kasumi:style/Widget.MaterialComponents.NavigationView = 0x7f12044a
mc.meson.kasumi:style/Base.V21.Theme.AppCompat = 0x7f1200ae
mc.meson.kasumi:color/m3_checkbox_button_tint = 0x7f050073
mc.meson.kasumi:attr/cornerSizeBottomRight = 0x7f03015a
mc.meson.kasumi:id/text_input_error_icon = 0x7f080223
mc.meson.kasumi:attr/pivotAnchor = 0x7f0303ab
mc.meson.kasumi:style/Base.Widget.AppCompat.Light.ActionBar.TabText.Inverse = 0x7f1200ee
mc.meson.kasumi:attr/passwordToggleDrawable = 0x7f0303a0
mc.meson.kasumi:style/App.Switch.Material = 0x7f120010
mc.meson.kasumi:attr/textAppearanceHeadline1 = 0x7f03047d
mc.meson.kasumi:attr/perpendicularPath_percent = 0x7f0303aa
mc.meson.kasumi:attr/tabIndicatorColor = 0x7f030458
mc.meson.kasumi:style/Base.ThemeOverlay.MaterialComponents.Dialog = 0x7f120091
mc.meson.kasumi:id/textinput_counter = 0x7f080225
mc.meson.kasumi:drawable/design_password_eye = 0x7f07008a
mc.meson.kasumi:attr/percentX = 0x7f0303a8
mc.meson.kasumi:attr/percentWidth = 0x7f0303a7
mc.meson.kasumi:dimen/m3_comp_sheet_side_docked_standard_container_elevation = 0x7f060180
mc.meson.kasumi:attr/layout_constraintRight_creator = 0x7f0302ac
mc.meson.kasumi:dimen/m3_navigation_rail_icon_size = 0x7f0601cb
mc.meson.kasumi:attr/lottie_rawRes = 0x7f0302f8
mc.meson.kasumi:attr/endIconMode = 0x7f0301b0
mc.meson.kasumi:attr/largeFontVerticalOffsetAdjustment = 0x7f030282
mc.meson.kasumi:attr/tabPaddingTop = 0x7f030464
mc.meson.kasumi:macro/m3_comp_fab_primary_container_color = 0x7f0c0036
mc.meson.kasumi:dimen/tooltip_margin = 0x7f06031e
mc.meson.kasumi:attr/layout_constraintLeft_creator = 0x7f0302a9
mc.meson.kasumi:attr/passwordToggleEnabled = 0x7f0303a1
mc.meson.kasumi:attr/maxNumber = 0x7f030339
mc.meson.kasumi:attr/searchPrefixText = 0x7f0303e6
mc.meson.kasumi:styleable/ButtonBarLayout = 0x7f13001b
mc.meson.kasumi:attr/passwordToggleContentDescription = 0x7f03039f
mc.meson.kasumi:styleable/AppCompatSeekBar = 0x7f130012
mc.meson.kasumi:style/Theme.Material3.Light.Dialog.Alert = 0x7f12024f
mc.meson.kasumi:drawable/abc_btn_radio_material = 0x7f070032
mc.meson.kasumi:attr/startIconDrawable = 0x7f030429
mc.meson.kasumi:attr/motionEasingDecelerated = 0x7f03035f
mc.meson.kasumi:drawable/ic_search_black_24 = 0x7f0700b2
mc.meson.kasumi:attr/panelMenuListWidth = 0x7f03039e
mc.meson.kasumi:dimen/abc_action_bar_default_padding_end_material = 0x7f060003
mc.meson.kasumi:attr/uri = 0x7f030505
mc.meson.kasumi:attr/customDimension = 0x7f03016b
mc.meson.kasumi:attr/textLocale = 0x7f0304a9
mc.meson.kasumi:attr/state_above_anchor = 0x7f03042f
mc.meson.kasumi:color/m3_dynamic_default_color_primary_text = 0x7f050085
mc.meson.kasumi:attr/tabPaddingEnd = 0x7f030462
mc.meson.kasumi:string/mtrl_switch_track_decoration_path = 0x7f1100b6
mc.meson.kasumi:attr/layout_scrollFlags = 0x7f0302cc
mc.meson.kasumi:dimen/m3_comp_search_bar_container_height = 0x7f060170
mc.meson.kasumi:dimen/m3_comp_switch_selected_pressed_state_layer_opacity = 0x7f060196
mc.meson.kasumi:dimen/mtrl_btn_snackbar_margin_horizontal = 0x7f06026a
mc.meson.kasumi:color/m3_ref_palette_error90 = 0x7f050103
mc.meson.kasumi:style/Widget.MaterialComponents.PopupMenu.Overflow = 0x7f12044e
mc.meson.kasumi:style/TextAppearance.M3.Sys.Typescale.TitleSmall = 0x7f1201ee
mc.meson.kasumi:style/Base.Widget.Material3.Light.ActionBar.Solid = 0x7f120118
mc.meson.kasumi:attr/actionBarDivider = 0x7f030003
mc.meson.kasumi:attr/badgeRadius = 0x7f030059
mc.meson.kasumi:dimen/mtrl_btn_hovered_z = 0x7f06025f
mc.meson.kasumi:color/material_dynamic_neutral0 = 0x7f050226
mc.meson.kasumi:attr/overlay = 0x7f030392
mc.meson.kasumi:attr/nullable = 0x7f030386
mc.meson.kasumi:style/Widget.AppCompat.Spinner = 0x7f120336
mc.meson.kasumi:dimen/mtrl_exposed_dropdown_menu_popup_vertical_padding = 0x7f0602a5
mc.meson.kasumi:dimen/hint_alpha_material_dark = 0x7f060096
mc.meson.kasumi:color/md_theme_dark_onTertiaryContainer = 0x7f0502cb
mc.meson.kasumi:attr/nestedScrollable = 0x7f030385
mc.meson.kasumi:color/material_personalized_color_surface_container = 0x7f05029d
mc.meson.kasumi:color/m3_sys_color_on_primary_fixed_variant = 0x7f0501f3
mc.meson.kasumi:attr/textAppearanceListItem = 0x7f03048b
mc.meson.kasumi:attr/nestedScrollViewStyle = 0x7f030384
mc.meson.kasumi:id/appName = 0x7f08005e
mc.meson.kasumi:dimen/mtrl_navigation_item_icon_size = 0x7f0602c8
mc.meson.kasumi:attr/tabIndicatorGravity = 0x7f03045a
mc.meson.kasumi:attr/motionTarget = 0x7f030377
mc.meson.kasumi:id/tvCurrentConfigStats = 0x7f080248
mc.meson.kasumi:dimen/m3_comp_time_picker_time_selector_pressed_state_layer_opacity = 0x7f0601a7
mc.meson.kasumi:attr/dayTodayStyle = 0x7f030177
mc.meson.kasumi:attr/motionStagger = 0x7f030376
mc.meson.kasumi:attr/behavior_halfExpandedRatio = 0x7f030071
mc.meson.kasumi:attr/framePosition = 0x7f030218
mc.meson.kasumi:styleable/ConstraintLayout_Layout = 0x7f13002a
mc.meson.kasumi:attr/listItemLayout = 0x7f0302da
mc.meson.kasumi:attr/titleCollapseMode = 0x7f0304d2
mc.meson.kasumi:dimen/m3_badge_with_text_horizontal_offset = 0x7f0600b7
mc.meson.kasumi:attr/autoShowKeyboard = 0x7f030044
mc.meson.kasumi:style/Base.TextAppearance.AppCompat.Menu = 0x7f120034
mc.meson.kasumi:id/clip_horizontal = 0x7f080099
mc.meson.kasumi:attr/motionProgress = 0x7f030375
mc.meson.kasumi:id/antiClockwise = 0x7f08005a
mc.meson.kasumi:attr/materialTimePickerStyle = 0x7f03032f
mc.meson.kasumi:attr/carousel_firstView = 0x7f0300aa
mc.meson.kasumi:attr/motionInterpolator = 0x7f030372
mc.meson.kasumi:dimen/m3_bottom_sheet_elevation = 0x7f0600c3
mc.meson.kasumi:attr/motionEffect_viewTransition = 0x7f030371
mc.meson.kasumi:dimen/mtrl_low_ripple_default_alpha = 0x7f0602be
mc.meson.kasumi:attr/lottie_fallbackRes = 0x7f0302f2
mc.meson.kasumi:style/Base.Theme.MaterialComponents.Light.Dialog.Bridge = 0x7f120081
mc.meson.kasumi:attr/percentY = 0x7f0303a9
mc.meson.kasumi:attr/motionEffect_translationY = 0x7f030370
mc.meson.kasumi:drawable/abc_cab_background_top_material = 0x7f070039
mc.meson.kasumi:style/Base.Theme.AppCompat.Light.Dialog.MinWidth = 0x7f120064
mc.meson.kasumi:dimen/m3_comp_navigation_rail_container_elevation = 0x7f060147
mc.meson.kasumi:attr/expandedTitleTextColor = 0x7f0301cd
mc.meson.kasumi:style/Widget.Material3.TextInputLayout.OutlinedBox = 0x7f1203ee
mc.meson.kasumi:attr/drawableRightCompat = 0x7f030195
mc.meson.kasumi:attr/motionEffect_translationX = 0x7f03036f
mc.meson.kasumi:styleable/TextInputEditText = 0x7f13009d
mc.meson.kasumi:attr/dividerColor = 0x7f030187
mc.meson.kasumi:attr/tabStyle = 0x7f030469
mc.meson.kasumi:macro/m3_comp_radio_button_unselected_focus_icon_color = 0x7f0c00df
mc.meson.kasumi:attr/motion_triggerOnCollision = 0x7f030379
mc.meson.kasumi:attr/motionEffect_alpha = 0x7f03036a
mc.meson.kasumi:attr/itemShapeInsetEnd = 0x7f03026f
mc.meson.kasumi:attr/motionEasingStandardDecelerateInterpolator = 0x7f030368
mc.meson.kasumi:attr/checkedIconMargin = 0x7f0300bd
mc.meson.kasumi:attr/materialSearchViewToolbarHeight = 0x7f03032b
mc.meson.kasumi:attr/motionEasingStandardAccelerateInterpolator = 0x7f030367
mc.meson.kasumi:style/Base.Theme.MaterialComponents.Dialog.Bridge = 0x7f120077
mc.meson.kasumi:string/mtrl_picker_invalid_format_example = 0x7f110098
mc.meson.kasumi:color/material_harmonized_color_error = 0x7f05026e
mc.meson.kasumi:attr/motionEasingLinearInterpolator = 0x7f030365
mc.meson.kasumi:color/m3_ref_palette_neutral_variant30 = 0x7f050122
mc.meson.kasumi:macro/m3_comp_switch_selected_pressed_icon_color = 0x7f0c012b
mc.meson.kasumi:color/m3_ref_palette_dynamic_primary10 = 0x7f0500d3
mc.meson.kasumi:attr/motionDurationShort4 = 0x7f03035d
mc.meson.kasumi:drawable/abc_scrubber_control_off_mtrl_alpha = 0x7f07005e
mc.meson.kasumi:attr/actionModeWebSearchDrawable = 0x7f030021
mc.meson.kasumi:color/bright_foreground_disabled_material_light = 0x7f050024
mc.meson.kasumi:style/CardView.Dark = 0x7f12012d
mc.meson.kasumi:style/Base.Widget.AppCompat.RatingBar.Indicator = 0x7f1200fd
mc.meson.kasumi:style/Base.Theme.MaterialComponents = 0x7f120072
mc.meson.kasumi:layout/abc_action_bar_up_container = 0x7f0b0001
mc.meson.kasumi:attr/motionPath = 0x7f030373
mc.meson.kasumi:color/abc_search_url_text_selected = 0x7f050010
mc.meson.kasumi:drawable/abc_ab_share_pack_mtrl_alpha = 0x7f070029
mc.meson.kasumi:color/m3_slider_inactive_track_color = 0x7f050158
mc.meson.kasumi:macro/m3_comp_icon_button_selected_icon_color = 0x7f0c0059
mc.meson.kasumi:attr/motionDurationMedium1 = 0x7f030356
mc.meson.kasumi:id/elastic = 0x7f0800d5
mc.meson.kasumi:dimen/mtrl_calendar_navigation_height = 0x7f06028c
mc.meson.kasumi:attr/colorOnPrimaryContainer = 0x7f030108
mc.meson.kasumi:attr/motionDurationLong3 = 0x7f030354
mc.meson.kasumi:id/reverseSawtooth = 0x7f0801b7
mc.meson.kasumi:color/design_dark_default_color_on_primary = 0x7f05003a
mc.meson.kasumi:animator/mtrl_fab_transformation_sheet_expand_spec = 0x7f020021
mc.meson.kasumi:macro/m3_comp_secondary_navigation_tab_active_indicator_color = 0x7f0c00fa
mc.meson.kasumi:attr/actionModePopupWindowStyle = 0x7f03001b
mc.meson.kasumi:attr/motionDurationExtraLong2 = 0x7f03034f
mc.meson.kasumi:style/TextAppearance.AppCompat.Widget.PopupMenu.Header = 0x7f1201cb
mc.meson.kasumi:attr/carousel_emptyViewsBehavior = 0x7f0300a9
mc.meson.kasumi:attr/closeIconStartPadding = 0x7f0300e9
mc.meson.kasumi:attr/layout = 0x7f030286
mc.meson.kasumi:string/nav_app_bar_open_drawer_description = 0x7f1100bb
mc.meson.kasumi:macro/m3_comp_switch_disabled_unselected_handle_color = 0x7f0c011c
mc.meson.kasumi:attr/motionDurationExtraLong1 = 0x7f03034e
mc.meson.kasumi:attr/motionDebug = 0x7f03034d
mc.meson.kasumi:dimen/m3_comp_secondary_navigation_tab_pressed_state_layer_opacity = 0x7f060179
mc.meson.kasumi:attr/indicatorColor = 0x7f030251
mc.meson.kasumi:attr/mock_labelBackgroundColor = 0x7f030349
mc.meson.kasumi:color/m3_ref_palette_dynamic_neutral_variant40 = 0x7f0500c4
mc.meson.kasumi:attr/mock_label = 0x7f030348
mc.meson.kasumi:attr/mock_diagonalsColor = 0x7f030347
mc.meson.kasumi:attr/behavior_skipCollapsed = 0x7f030077
mc.meson.kasumi:attr/minWidth = 0x7f030346
mc.meson.kasumi:styleable/RecycleListView = 0x7f130083
mc.meson.kasumi:color/material_grey_100 = 0x7f050267
mc.meson.kasumi:attr/colorOnSecondaryFixedVariant = 0x7f03010f
mc.meson.kasumi:attr/state_with_icon = 0x7f030437
mc.meson.kasumi:style/Theme.AppCompat.DayNight.NoActionBar = 0x7f120220
mc.meson.kasumi:color/material_personalized_color_on_secondary = 0x7f050289
mc.meson.kasumi:attr/minHeight = 0x7f030342
mc.meson.kasumi:color/md_theme_light_onError = 0x7f0502e0
mc.meson.kasumi:dimen/abc_control_inset_material = 0x7f060019
mc.meson.kasumi:style/Theme.MaterialComponents.Light.Dialog.Alert.Bridge = 0x7f120278
mc.meson.kasumi:attr/tickColor = 0x7f0304c4
mc.meson.kasumi:attr/motionEasingLinear = 0x7f030364
mc.meson.kasumi:attr/viewTransitionMode = 0x7f03050d
mc.meson.kasumi:attr/maxWidth = 0x7f03033b
mc.meson.kasumi:dimen/m3_comp_fab_primary_hover_container_elevation = 0x7f060118
mc.meson.kasumi:attr/maxVelocity = 0x7f03033a
mc.meson.kasumi:attr/maxHeight = 0x7f030336
mc.meson.kasumi:attr/materialCalendarFullscreenTheme = 0x7f03030e
mc.meson.kasumi:dimen/m3_comp_text_button_pressed_state_layer_opacity = 0x7f06019e
mc.meson.kasumi:attr/maxActionInlineWidth = 0x7f030333
mc.meson.kasumi:string/abc_searchview_description_search = 0x7f110015
mc.meson.kasumi:animator/mtrl_card_state_list_anim = 0x7f020017
mc.meson.kasumi:attr/layout_goneMarginStart = 0x7f0302c5
mc.meson.kasumi:attr/layout_anchor = 0x7f03028a
mc.meson.kasumi:dimen/mtrl_extended_fab_translation_z_hovered_focused = 0x7f0602b4
mc.meson.kasumi:attr/materialTimePickerTitleStyle = 0x7f030331
mc.meson.kasumi:color/material_dynamic_primary70 = 0x7f050248
mc.meson.kasumi:dimen/design_snackbar_background_corner_radius = 0x7f060080
mc.meson.kasumi:attr/materialThemeOverlay = 0x7f03032e
mc.meson.kasumi:style/Widget.MaterialComponents.TextInputLayout.FilledBox = 0x7f12045c
mc.meson.kasumi:color/material_personalized_color_tertiary = 0x7f0502a5
mc.meson.kasumi:dimen/m3_comp_fab_primary_container_elevation = 0x7f060115
mc.meson.kasumi:color/abc_color_highlight_material = 0x7f050004
mc.meson.kasumi:style/Widget.Material3.FloatingActionButton.Secondary = 0x7f12039c
mc.meson.kasumi:color/m3_ref_palette_primary40 = 0x7f050130
mc.meson.kasumi:attr/materialSearchViewStyle = 0x7f03032a
mc.meson.kasumi:drawable/abc_scrubber_track_mtrl_alpha = 0x7f070062
mc.meson.kasumi:style/Base.TextAppearance.MaterialComponents.Button = 0x7f120052
mc.meson.kasumi:dimen/m3_comp_progress_indicator_track_thickness = 0x7f060164
mc.meson.kasumi:color/m3_ref_palette_error70 = 0x7f050101
mc.meson.kasumi:color/design_dark_default_color_on_secondary = 0x7f05003b
mc.meson.kasumi:style/TextAppearance.Design.Tab = 0x7f1201df
mc.meson.kasumi:attr/materialSearchViewPrefixStyle = 0x7f030329
mc.meson.kasumi:attr/colorPrimaryFixed = 0x7f03011c
mc.meson.kasumi:string/m3_sys_motion_easing_emphasized_accelerate = 0x7f11005b
mc.meson.kasumi:attr/materialIconButtonOutlinedStyle = 0x7f030326
mc.meson.kasumi:dimen/mtrl_navigation_rail_elevation = 0x7f0602ce
mc.meson.kasumi:macro/m3_comp_filled_icon_button_toggle_selected_icon_color = 0x7f0c0049
mc.meson.kasumi:layout/abc_action_mode_close_item_material = 0x7f0b0005
mc.meson.kasumi:attr/clearsTag = 0x7f0300df
mc.meson.kasumi:style/Theme.Material3.Dark.NoActionBar = 0x7f12023c
mc.meson.kasumi:attr/customPixelDimension = 0x7f03016f
mc.meson.kasumi:color/material_personalized_color_outline = 0x7f050290
mc.meson.kasumi:color/material_dynamic_secondary40 = 0x7f050252
mc.meson.kasumi:anim/abc_tooltip_enter = 0x7f01000a
mc.meson.kasumi:style/Base.Animation.AppCompat.Dialog = 0x7f12001a
mc.meson.kasumi:attr/materialCardViewStyle = 0x7f03031e
mc.meson.kasumi:color/md_theme_dark_surfaceTint = 0x7f0502d5
mc.meson.kasumi:attr/voiceIcon = 0x7f030512
mc.meson.kasumi:attr/materialCardViewElevatedStyle = 0x7f03031b
mc.meson.kasumi:color/m3_ref_palette_dynamic_tertiary30 = 0x7f0500f0
mc.meson.kasumi:macro/m3_comp_date_picker_modal_date_today_container_outline_color = 0x7f0c0012
mc.meson.kasumi:attr/yearTodayStyle = 0x7f030526
mc.meson.kasumi:attr/materialCalendarYearNavigationButton = 0x7f03031a
mc.meson.kasumi:attr/textAppearanceHeadline3 = 0x7f03047f
mc.meson.kasumi:attr/contentInsetStartWithNavigation = 0x7f030145
mc.meson.kasumi:attr/materialCalendarHeaderToggleButton = 0x7f030315
mc.meson.kasumi:attr/materialCalendarHeaderSelection = 0x7f030313
mc.meson.kasumi:color/m3_ref_palette_primary20 = 0x7f05012e
mc.meson.kasumi:attr/subtitleTextStyle = 0x7f030447
mc.meson.kasumi:attr/motion_postLayoutCollision = 0x7f030378
mc.meson.kasumi:attr/materialCalendarHeaderLayout = 0x7f030312
mc.meson.kasumi:dimen/mtrl_navigation_rail_margin = 0x7f0602d1
mc.meson.kasumi:style/Base.V14.Theme.MaterialComponents.Light.Bridge = 0x7f1200a3
mc.meson.kasumi:attr/arcMode = 0x7f03003c
mc.meson.kasumi:macro/m3_comp_time_input_time_input_field_focus_outline_color = 0x7f0c0148
mc.meson.kasumi:attr/chipStandaloneStyle = 0x7f0300d1
mc.meson.kasumi:style/Theme.MaterialComponents.DayNight.BottomSheetDialog = 0x7f120259
mc.meson.kasumi:id/mini = 0x7f08014c
mc.meson.kasumi:attr/dividerInsetEnd = 0x7f030189
mc.meson.kasumi:attr/motionPathRotate = 0x7f030374
mc.meson.kasumi:string/side_sheet_accessibility_pane_title = 0x7f1100d2
mc.meson.kasumi:attr/springBoundary = 0x7f03041e
mc.meson.kasumi:color/m3_ref_palette_dynamic_tertiary0 = 0x7f0500ec
mc.meson.kasumi:attr/offsetAlignmentMode = 0x7f030389
mc.meson.kasumi:drawable/design_snackbar_background = 0x7f07008b
mc.meson.kasumi:attr/activityName = 0x7f03002a
mc.meson.kasumi:attr/materialCalendarHeaderCancelButton = 0x7f03030f
mc.meson.kasumi:style/Base.V7.Widget.AppCompat.AutoCompleteTextView = 0x7f1200cc
mc.meson.kasumi:drawable/abc_text_cursor_material = 0x7f07006e
mc.meson.kasumi:dimen/m3_timepicker_display_stroke_width = 0x7f06021e
mc.meson.kasumi:style/Theme.AppCompat.Light.NoActionBar = 0x7f12022c
mc.meson.kasumi:id/accessibility_custom_action_3 = 0x7f080028
mc.meson.kasumi:attr/materialCalendarDayOfWeekLabel = 0x7f03030d
mc.meson.kasumi:attr/materialCalendarDay = 0x7f03030c
mc.meson.kasumi:attr/materialButtonToggleGroupStyle = 0x7f03030b
mc.meson.kasumi:style/Base.Widget.AppCompat.SearchView.ActionBar = 0x7f120100
mc.meson.kasumi:layout/item_setting_toggle = 0x7f0b0042
mc.meson.kasumi:dimen/m3_slider_thumb_elevation = 0x7f0601eb
mc.meson.kasumi:attr/materialButtonOutlinedStyle = 0x7f030309
mc.meson.kasumi:attr/buttonBarStyle = 0x7f030093
mc.meson.kasumi:attr/showDelay = 0x7f0303ff
mc.meson.kasumi:attr/fontProviderAuthority = 0x7f03020b
mc.meson.kasumi:attr/materialAlertDialogTitleTextStyle = 0x7f030308
mc.meson.kasumi:attr/materialAlertDialogTheme = 0x7f030305
mc.meson.kasumi:color/m3_ref_palette_dynamic_neutral0 = 0x7f0500a2
mc.meson.kasumi:dimen/m3_sys_motion_easing_legacy_decelerate_control_y2 = 0x7f060209
mc.meson.kasumi:style/Theme.AppCompat.Dialog = 0x7f120221
mc.meson.kasumi:layout/mtrl_calendar_vertical = 0x7f0b0066
mc.meson.kasumi:attr/lottie_repeatMode = 0x7f0302fb
mc.meson.kasumi:dimen/mtrl_progress_indicator_full_rounded_corner_radius = 0x7f0602e0
mc.meson.kasumi:dimen/m3_sys_motion_easing_standard_accelerate_control_y1 = 0x7f060210
mc.meson.kasumi:style/Platform.V25.AppCompat.Light = 0x7f12014f
mc.meson.kasumi:attr/title = 0x7f0304d0
mc.meson.kasumi:dimen/abc_dialog_min_width_major = 0x7f060022
mc.meson.kasumi:color/mtrl_filled_background_color = 0x7f05031a
mc.meson.kasumi:attr/lottie_renderMode = 0x7f0302f9
mc.meson.kasumi:style/ThemeOverlay.MaterialComponents.TimePicker.Display.TextInputEditText = 0x7f1202f1
mc.meson.kasumi:macro/m3_comp_radio_button_disabled_selected_icon_color = 0x7f0c00d6
mc.meson.kasumi:color/m3_ref_palette_dynamic_tertiary100 = 0x7f0500ee
mc.meson.kasumi:dimen/m3_comp_switch_unselected_pressed_state_layer_opacity = 0x7f06019b
mc.meson.kasumi:attr/chipStrokeWidth = 0x7f0300d4
mc.meson.kasumi:color/material_on_background_disabled = 0x7f050272
mc.meson.kasumi:attr/contentPaddingEnd = 0x7f030148
mc.meson.kasumi:animator/design_appbar_state_list_animator = 0x7f020000
mc.meson.kasumi:dimen/compat_button_padding_horizontal_material = 0x7f060058
mc.meson.kasumi:anim/abc_slide_in_top = 0x7f010007
mc.meson.kasumi:attr/checkedState = 0x7f0300c1
mc.meson.kasumi:attr/lottie_fileName = 0x7f0302f3
mc.meson.kasumi:attr/cornerFamily = 0x7f030152
mc.meson.kasumi:color/design_default_color_primary_variant = 0x7f05004c
mc.meson.kasumi:layout/mtrl_layout_snackbar_include = 0x7f0b0069
mc.meson.kasumi:attr/lottie_defaultFontFileExtension = 0x7f0302f0
mc.meson.kasumi:id/navigation_bar_item_small_label_view = 0x7f08017b
mc.meson.kasumi:attr/drawerArrowStyle = 0x7f03019b
mc.meson.kasumi:color/m3_dark_default_color_secondary_text = 0x7f05007a
mc.meson.kasumi:attr/lottie_clipTextToBoundingBox = 0x7f0302ed
mc.meson.kasumi:attr/closeIconEndPadding = 0x7f0300e7
mc.meson.kasumi:attr/shortcutMatchRequired = 0x7f0303fb
mc.meson.kasumi:color/md_theme_light_secondary = 0x7f0502ef
mc.meson.kasumi:attr/lottie_asyncUpdates = 0x7f0302ea
mc.meson.kasumi:dimen/m3_bottom_nav_item_active_indicator_height = 0x7f0600bc
mc.meson.kasumi:dimen/abc_action_bar_default_height_material = 0x7f060002
mc.meson.kasumi:attr/colorSurfaceContainerHighest = 0x7f03012a
mc.meson.kasumi:attr/lottieAnimationViewStyle = 0x7f0302e9
mc.meson.kasumi:id/accessibility_custom_action_31 = 0x7f08002a
mc.meson.kasumi:attr/lottie_autoPlay = 0x7f0302eb
mc.meson.kasumi:dimen/m3_alert_dialog_icon_size = 0x7f0600a2
mc.meson.kasumi:color/md_theme_light_onErrorContainer = 0x7f0502e1
mc.meson.kasumi:drawable/ic_mtrl_chip_checked_black = 0x7f0700ae
mc.meson.kasumi:attr/listPreferredItemPaddingEnd = 0x7f0302e1
mc.meson.kasumi:style/Widget.Material3.SearchView = 0x7f1203d5
mc.meson.kasumi:style/Base.V24.Theme.Material3.Dark = 0x7f1200be
mc.meson.kasumi:style/Base.V23.Theme.AppCompat.Light = 0x7f1200bd
mc.meson.kasumi:attr/listLayout = 0x7f0302db
mc.meson.kasumi:style/ThemeOverlay.MaterialComponents.ActionBar.Primary = 0x7f1202cf
mc.meson.kasumi:attr/hintTextColor = 0x7f030239
mc.meson.kasumi:attr/reactiveGuide_applyToAllConstraintSets = 0x7f0303cf
mc.meson.kasumi:color/background_material_light = 0x7f050020
mc.meson.kasumi:attr/statusBarScrim = 0x7f03043a
mc.meson.kasumi:attr/listChoiceIndicatorSingleAnimated = 0x7f0302d8
mc.meson.kasumi:style/Widget.AppCompat.ListView.DropDown = 0x7f120328
mc.meson.kasumi:attr/tabContentStart = 0x7f030451
mc.meson.kasumi:attr/carousel_backwardTransition = 0x7f0300a8
mc.meson.kasumi:attr/limitBoundsTo = 0x7f0302d2
mc.meson.kasumi:style/Widget.Material3.MaterialCalendar.Fullscreen = 0x7f1203ae
mc.meson.kasumi:attr/textAppearanceOverline = 0x7f03048e
mc.meson.kasumi:string/connecting = 0x7f110039
mc.meson.kasumi:attr/liftOnScroll = 0x7f0302cf
mc.meson.kasumi:dimen/abc_action_button_min_height_material = 0x7f06000d
mc.meson.kasumi:macro/m3_comp_outlined_card_focus_outline_color = 0x7f0c00ac
mc.meson.kasumi:attr/transformPivotTarget = 0x7f0304fa
mc.meson.kasumi:style/Base.Widget.AppCompat.ActionBar.TabText = 0x7f1200d2
mc.meson.kasumi:attr/layout_scrollEffect = 0x7f0302cb
mc.meson.kasumi:attr/overlapAnchor = 0x7f030391
mc.meson.kasumi:color/m3_ref_palette_dynamic_neutral_variant87 = 0x7f0500ca
mc.meson.kasumi:dimen/cardview_default_radius = 0x7f060054
mc.meson.kasumi:style/TextAppearance.M3.Sys.Typescale.HeadlineLarge = 0x7f1201e6
mc.meson.kasumi:dimen/mtrl_navigation_bar_item_default_icon_size = 0x7f0602c3
mc.meson.kasumi:attr/layout_optimizationLevel = 0x7f0302ca
mc.meson.kasumi:color/material_dynamic_tertiary80 = 0x7f050263
mc.meson.kasumi:attr/trackTint = 0x7f0304f8
mc.meson.kasumi:attr/tabUnboundedRipple = 0x7f03046c
mc.meson.kasumi:style/Widget.MaterialComponents.AppBarLayout.Primary = 0x7f1203fb
mc.meson.kasumi:style/ShapeAppearance.M3.Comp.SearchBar.Avatar.Shape = 0x7f12016c
mc.meson.kasumi:string/call_notification_answer_action = 0x7f11002a
mc.meson.kasumi:dimen/m3_sys_motion_easing_standard_control_y1 = 0x7f060214
mc.meson.kasumi:drawable/abc_textfield_default_mtrl_alpha = 0x7f070073
mc.meson.kasumi:attr/layout_marginBaseline = 0x7f0302c9
mc.meson.kasumi:style/ThemeOverlay.MaterialComponents.AutoCompleteTextView.FilledBox.Dense = 0x7f1202d3
mc.meson.kasumi:attr/layout_constraintBaseline_toTopOf = 0x7f030294
mc.meson.kasumi:attr/layout_insetEdge = 0x7f0302c7
mc.meson.kasumi:dimen/m3_badge_vertical_offset = 0x7f0600b6
mc.meson.kasumi:color/material_on_surface_disabled = 0x7f050278
mc.meson.kasumi:attr/badgeWidth = 0x7f030062
mc.meson.kasumi:attr/layout_goneMarginLeft = 0x7f0302c3
mc.meson.kasumi:attr/textAppearanceSubtitle1 = 0x7f030493
mc.meson.kasumi:dimen/mtrl_progress_circular_size_small = 0x7f0602dc
mc.meson.kasumi:dimen/mtrl_navigation_item_horizontal_padding = 0x7f0602c6
mc.meson.kasumi:color/md_theme_light_onPrimaryContainer = 0x7f0502e3
mc.meson.kasumi:style/Theme.MaterialComponents.DayNight.Dialog.MinWidth.Bridge = 0x7f120264
mc.meson.kasumi:attr/linearProgressIndicatorStyle = 0x7f0302d5
mc.meson.kasumi:attr/layout_goneMarginBaseline = 0x7f0302c0
mc.meson.kasumi:dimen/mtrl_btn_text_btn_padding_right = 0x7f06026e
mc.meson.kasumi:attr/trackCornerRadius = 0x7f0304f0
mc.meson.kasumi:animator/m3_extended_fab_show_motion_spec = 0x7f020013
mc.meson.kasumi:attr/buttonBarButtonStyle = 0x7f03008f
mc.meson.kasumi:style/Theme.MaterialComponents.DayNight.DarkActionBar.Bridge = 0x7f12025c
mc.meson.kasumi:attr/backgroundStacked = 0x7f030054
mc.meson.kasumi:style/Base.Widget.AppCompat.ActionButton.Overflow = 0x7f1200d6
mc.meson.kasumi:attr/deltaPolarRadius = 0x7f03017f
mc.meson.kasumi:color/material_dynamic_secondary30 = 0x7f050251
mc.meson.kasumi:color/m3_ref_palette_dynamic_neutral94 = 0x7f0500b5
mc.meson.kasumi:attr/textInputFilledDenseStyle = 0x7f0304a1
mc.meson.kasumi:attr/onHide = 0x7f03038b
mc.meson.kasumi:attr/cornerFamilyBottomRight = 0x7f030154
mc.meson.kasumi:style/Widget.AppCompat.PopupMenu = 0x7f12032a
mc.meson.kasumi:macro/m3_comp_switch_unselected_pressed_track_color = 0x7f0c013e
mc.meson.kasumi:dimen/m3_comp_navigation_drawer_icon_size = 0x7f060141
mc.meson.kasumi:id/callMeasure = 0x7f080086
mc.meson.kasumi:attr/colorOnTertiary = 0x7f030113
mc.meson.kasumi:attr/layout_constraintWidth_min = 0x7f0302bb
mc.meson.kasumi:color/mtrl_btn_text_color_selector = 0x7f050309
mc.meson.kasumi:color/m3_timepicker_time_input_stroke_color = 0x7f050215
mc.meson.kasumi:attr/chipIconSize = 0x7f0300c9
mc.meson.kasumi:attr/layout_constraintWidth_default = 0x7f0302b9
mc.meson.kasumi:drawable/abc_list_selector_background_transition_holo_light = 0x7f070054
mc.meson.kasumi:dimen/m3_comp_search_bar_avatar_size = 0x7f06016e
mc.meson.kasumi:attr/layout_constraintWidth = 0x7f0302b8
mc.meson.kasumi:dimen/mtrl_calendar_days_of_week_height = 0x7f06027c
mc.meson.kasumi:attr/currentState = 0x7f030164
mc.meson.kasumi:string/fly = 0x7f11004c
mc.meson.kasumi:color/design_dark_default_color_error = 0x7f050037
mc.meson.kasumi:dimen/tooltip_y_offset_touch = 0x7f060323
mc.meson.kasumi:attr/lottie_clipToCompositionBounds = 0x7f0302ee
mc.meson.kasumi:style/ShapeAppearanceOverlay.MaterialAlertDialog.Material3 = 0x7f120197
mc.meson.kasumi:attr/layout_constraintVertical_weight = 0x7f0302b7
mc.meson.kasumi:id/titleBar = 0x7f08022d
mc.meson.kasumi:id/right_icon = 0x7f0801ba
mc.meson.kasumi:id/immediateStop = 0x7f08010c
mc.meson.kasumi:dimen/m3_extended_fab_min_height = 0x7f0601b2
mc.meson.kasumi:color/m3_timepicker_display_background_color = 0x7f050210
mc.meson.kasumi:drawable/notification_action_background = 0x7f0700f2
mc.meson.kasumi:color/m3_ref_palette_secondary100 = 0x7f05013a
mc.meson.kasumi:attr/materialClockStyle = 0x7f030320
mc.meson.kasumi:attr/layout_constraintTop_creator = 0x7f0302b2
mc.meson.kasumi:string/abc_capital_on = 0x7f110007
mc.meson.kasumi:drawable/abc_textfield_activated_mtrl_alpha = 0x7f070072
mc.meson.kasumi:attr/actionTextColorAlpha = 0x7f030025
mc.meson.kasumi:attr/layout_constraintTag = 0x7f0302b1
mc.meson.kasumi:attr/buttonPanelSideLayout = 0x7f03009a
mc.meson.kasumi:color/m3_dynamic_hint_foreground = 0x7f050088
mc.meson.kasumi:attr/layout_constraintLeft_toRightOf = 0x7f0302ab
mc.meson.kasumi:anim/design_snackbar_out = 0x7f01001c
mc.meson.kasumi:color/m3_timepicker_button_background_color = 0x7f05020c
mc.meson.kasumi:animator/m3_extended_fab_hide_motion_spec = 0x7f020012
mc.meson.kasumi:dimen/m3_comp_search_view_full_screen_header_container_height = 0x7f060175
mc.meson.kasumi:style/Widget.AppCompat.ActionBar = 0x7f1202f5
mc.meson.kasumi:attr/imageRotate = 0x7f03024d
mc.meson.kasumi:attr/upDuration = 0x7f030504
mc.meson.kasumi:attr/layout_constraintHeight_min = 0x7f0302a4
mc.meson.kasumi:attr/itemBackground = 0x7f03025e
mc.meson.kasumi:color/material_dynamic_color_dark_on_error = 0x7f050220
mc.meson.kasumi:id/textTop = 0x7f080221
mc.meson.kasumi:attr/layout_constraintHeight_max = 0x7f0302a3
mc.meson.kasumi:dimen/m3_navigation_item_shape_inset_top = 0x7f0601c5
mc.meson.kasumi:attr/paddingLeftSystemWindowInsets = 0x7f030396
mc.meson.kasumi:attr/searchViewStyle = 0x7f0303e7
mc.meson.kasumi:macro/m3_comp_sheet_side_detached_container_shape = 0x7f0c0107
mc.meson.kasumi:macro/m3_comp_date_picker_modal_year_selection_year_selected_label_text_color = 0x7f0c0020
mc.meson.kasumi:attr/itemShapeAppearanceOverlay = 0x7f03026c
mc.meson.kasumi:drawable/notify_panel_notification_icon_bg = 0x7f0700fe
mc.meson.kasumi:attr/layout_constraintHeight_default = 0x7f0302a2
mc.meson.kasumi:attr/enforceMaterialTheme = 0x7f0301b4
mc.meson.kasumi:styleable/AnimatedStateListDrawableItem = 0x7f13000b
mc.meson.kasumi:style/Widget.Material3.Button.TonalButton = 0x7f12036d
mc.meson.kasumi:style/TextAppearance.AppCompat.Widget.DropDownItem = 0x7f1201ca
mc.meson.kasumi:attr/lottie_progress = 0x7f0302f7
mc.meson.kasumi:id/decor_content_parent = 0x7f0800b3
mc.meson.kasumi:color/material_dynamic_secondary60 = 0x7f050254
mc.meson.kasumi:attr/colorOnPrimaryFixed = 0x7f030109
mc.meson.kasumi:style/ThemeOverlay.Material3.PersonalizedColors = 0x7f1202c1
mc.meson.kasumi:attr/floatingActionButtonLargePrimaryStyle = 0x7f0301e7
mc.meson.kasumi:attr/haloRadius = 0x7f03022a
mc.meson.kasumi:style/Base.ThemeOverlay.AppCompat.Dialog.Alert = 0x7f12008a
mc.meson.kasumi:attr/restoreState = 0x7f0303d8
mc.meson.kasumi:attr/borderWidth = 0x7f03007b
mc.meson.kasumi:attr/layout_constraintCircleAngle = 0x7f030299
mc.meson.kasumi:color/abc_decor_view_status_guard_light = 0x7f050006
mc.meson.kasumi:attr/itemIconSize = 0x7f030263
mc.meson.kasumi:attr/closeIconSize = 0x7f0300e8
mc.meson.kasumi:drawable/abc_ic_voice_search_api_material = 0x7f07004a
mc.meson.kasumi:attr/layout_constraintCircle = 0x7f030298
mc.meson.kasumi:attr/simpleItemSelectedRippleColor = 0x7f03040b
mc.meson.kasumi:attr/deriveConstraintsFrom = 0x7f030180
mc.meson.kasumi:color/m3_ref_palette_neutral_variant80 = 0x7f050127
mc.meson.kasumi:attr/layout_constraintBottom_toTopOf = 0x7f030297
mc.meson.kasumi:drawable/btn_checkbox_unchecked_mtrl = 0x7f07007c
mc.meson.kasumi:attr/errorIconTint = 0x7f0301bd
mc.meson.kasumi:styleable/Tooltip = 0x7f1300a1
mc.meson.kasumi:color/m3_dynamic_dark_hint_foreground = 0x7f050083
mc.meson.kasumi:attr/clearTop = 0x7f0300de
mc.meson.kasumi:dimen/mtrl_extended_fab_end_padding = 0x7f0602aa
mc.meson.kasumi:attr/dataPattern = 0x7f030173
mc.meson.kasumi:anim/abc_grow_fade_in_from_bottom = 0x7f010002
mc.meson.kasumi:color/mtrl_choice_chip_text_color = 0x7f050315
mc.meson.kasumi:attr/layout_constraintBottom_toBottomOf = 0x7f030296
mc.meson.kasumi:macro/m3_comp_filter_chip_container_shape = 0x7f0c0057
mc.meson.kasumi:attr/layout_constraintBaseline_toBottomOf = 0x7f030293
mc.meson.kasumi:attr/tabIndicatorAnimationMode = 0x7f030457
mc.meson.kasumi:attr/endIconCheckable = 0x7f0301ac
mc.meson.kasumi:attr/layout_constrainedWidth = 0x7f030290
mc.meson.kasumi:attr/layoutDescription = 0x7f030287
mc.meson.kasumi:attr/curveFit = 0x7f030167
mc.meson.kasumi:attr/drawableStartCompat = 0x7f030197
mc.meson.kasumi:style/Base.TextAppearance.AppCompat.Widget.Button = 0x7f120046
mc.meson.kasumi:attr/constraintRotate = 0x7f030137
mc.meson.kasumi:id/horizontal = 0x7f080104
mc.meson.kasumi:attr/chipStyle = 0x7f0300d5
mc.meson.kasumi:attr/labelBehavior = 0x7f03027f
mc.meson.kasumi:style/ThemeOverlay.AppCompat.Light = 0x7f12028b
mc.meson.kasumi:color/m3_ref_palette_dynamic_neutral_variant99 = 0x7f0500d1
mc.meson.kasumi:dimen/design_navigation_max_width = 0x7f06007b
mc.meson.kasumi:macro/m3_comp_date_picker_modal_weekdays_label_text_type = 0x7f0c001e
mc.meson.kasumi:attr/extendedFloatingActionButtonPrimaryStyle = 0x7f0301d0
mc.meson.kasumi:attr/circularProgressIndicatorStyle = 0x7f0300d8
mc.meson.kasumi:dimen/material_clock_face_margin_bottom = 0x7f060225
mc.meson.kasumi:id/month_navigation_fragment_toggle = 0x7f080150
mc.meson.kasumi:attr/keylines = 0x7f03027d
mc.meson.kasumi:color/m3_sys_color_dark_surface_container_highest = 0x7f050179
mc.meson.kasumi:attr/tabPadding = 0x7f030460
mc.meson.kasumi:dimen/m3_comp_primary_navigation_tab_active_focus_state_layer_opacity = 0x7f06015a
mc.meson.kasumi:attr/itemTextAppearance = 0x7f030275
mc.meson.kasumi:id/animateToStart = 0x7f080059
mc.meson.kasumi:color/m3_ref_palette_dynamic_neutral92 = 0x7f0500b4
mc.meson.kasumi:style/ShapeAppearance.M3.Sys.Shape.Corner.ExtraSmall = 0x7f120175
mc.meson.kasumi:attr/itemSpacing = 0x7f030272
mc.meson.kasumi:style/Theme.MaterialComponents.Light.Dialog.Bridge = 0x7f120279
mc.meson.kasumi:attr/textOutlineColor = 0x7f0304aa
mc.meson.kasumi:color/m3_slider_thumb_color_legacy = 0x7f05015b
mc.meson.kasumi:attr/elevationOverlayColor = 0x7f0301a8
mc.meson.kasumi:string/mtrl_switch_thumb_path_name = 0x7f1100b3
mc.meson.kasumi:id/btnBackInTitle = 0x7f080077
mc.meson.kasumi:attr/cornerSize = 0x7f030158
mc.meson.kasumi:attr/itemPaddingBottom = 0x7f030268
mc.meson.kasumi:style/Widget.Material3.Button.OutlinedButton = 0x7f120365
mc.meson.kasumi:style/ThemeOverlay.MaterialComponents.ActionBar = 0x7f1202ce
mc.meson.kasumi:macro/m3_comp_top_app_bar_medium_headline_type = 0x7f0c016e
mc.meson.kasumi:color/design_default_color_on_error = 0x7f050046
mc.meson.kasumi:attr/prefixTextColor = 0x7f0303bd
mc.meson.kasumi:attr/alertDialogTheme = 0x7f03002f
mc.meson.kasumi:id/graph_wrap = 0x7f0800f9
mc.meson.kasumi:attr/itemIconTint = 0x7f030264
mc.meson.kasumi:macro/m3_comp_slider_disabled_active_track_color = 0x7f0c010c
mc.meson.kasumi:dimen/m3_large_fab_max_image_size = 0x7f0601b9
mc.meson.kasumi:attr/trackTintMode = 0x7f0304f9
mc.meson.kasumi:attr/fabAlignmentMode = 0x7f0301d6
mc.meson.kasumi:anim/design_bottom_sheet_slide_out = 0x7f01001a
mc.meson.kasumi:attr/itemHorizontalPadding = 0x7f030260
mc.meson.kasumi:styleable/NavDeepLink = 0x7f130072
mc.meson.kasumi:attr/shouldRemoveExpandedCorners = 0x7f0303fc
mc.meson.kasumi:attr/waveShape = 0x7f030518
mc.meson.kasumi:attr/textAppearanceBody1 = 0x7f030473
mc.meson.kasumi:attr/lottie_repeatCount = 0x7f0302fa
mc.meson.kasumi:macro/m3_comp_time_picker_time_selector_unselected_focus_state_layer_color = 0x7f0c0168
mc.meson.kasumi:id/mtrl_calendar_frame = 0x7f080158
mc.meson.kasumi:attr/editTextStyle = 0x7f0301a5
mc.meson.kasumi:dimen/m3_extended_fab_start_padding = 0x7f0601b3
mc.meson.kasumi:attr/layoutDuringTransition = 0x7f030288
mc.meson.kasumi:attr/textAppearanceLabelLarge = 0x7f030486
mc.meson.kasumi:style/Base.Widget.AppCompat.ListView = 0x7f1200f4
mc.meson.kasumi:attr/materialAlertDialogTitleIconStyle = 0x7f030306
mc.meson.kasumi:style/ShapeAppearanceOverlay.MaterialComponents.Chip = 0x7f120199
mc.meson.kasumi:attr/isLightTheme = 0x7f030259
mc.meson.kasumi:drawable/floating_item_background = 0x7f070091
mc.meson.kasumi:attr/indicatorInset = 0x7f030254
mc.meson.kasumi:attr/indicatorDirectionLinear = 0x7f030253
mc.meson.kasumi:color/m3_sys_color_dynamic_light_inverse_primary = 0x7f0501a6
mc.meson.kasumi:drawable/mtrl_switch_thumb = 0x7f0700e4
mc.meson.kasumi:anim/nav_default_enter_anim = 0x7f010031
mc.meson.kasumi:color/m3_sys_color_dark_outline_variant = 0x7f050170
mc.meson.kasumi:attr/imageZoom = 0x7f03024e
mc.meson.kasumi:attr/backgroundColor = 0x7f03004d
mc.meson.kasumi:attr/imagePanY = 0x7f03024c
mc.meson.kasumi:color/m3_sys_color_dark_surface_container_low = 0x7f05017a
mc.meson.kasumi:styleable/FloatingActionButton_Behavior_Layout = 0x7f130038
mc.meson.kasumi:style/ThemeOverlay.Material3.AutoCompleteTextView.FilledBox.Dense = 0x7f120291
mc.meson.kasumi:attr/imagePanX = 0x7f03024b
mc.meson.kasumi:style/Widget.MaterialComponents.MaterialCalendar.Year.Today = 0x7f120442
mc.meson.kasumi:attr/itemStrokeColor = 0x7f030273
mc.meson.kasumi:attr/insetForeground = 0x7f030258
mc.meson.kasumi:attr/useCompatPadding = 0x7f030506
mc.meson.kasumi:color/mtrl_navigation_bar_colored_ripple_color = 0x7f05031f
mc.meson.kasumi:attr/region_widthLessThan = 0x7f0303d5
mc.meson.kasumi:attr/startIconContentDescription = 0x7f030428
mc.meson.kasumi:attr/fabCradleRoundedCornerRadius = 0x7f0301db
mc.meson.kasumi:attr/iconTint = 0x7f030245
mc.meson.kasumi:style/TextAppearance.AppCompat.Widget.ActionBar.Subtitle = 0x7f1201be
mc.meson.kasumi:animator/m3_chip_state_list_anim = 0x7f02000e
mc.meson.kasumi:id/allStates = 0x7f080055
mc.meson.kasumi:anim/abc_slide_out_bottom = 0x7f010008
mc.meson.kasumi:attr/iconSize = 0x7f030243
mc.meson.kasumi:style/Widget.Material3.MaterialCalendar.Day = 0x7f1203a8
mc.meson.kasumi:attr/actionModeFindDrawable = 0x7f030019
mc.meson.kasumi:styleable/ForegroundLinearLayout = 0x7f13003c
mc.meson.kasumi:style/Base.V14.Theme.MaterialComponents.Dialog = 0x7f1200a0
mc.meson.kasumi:attr/trackColorActive = 0x7f0304ee
mc.meson.kasumi:drawable/$mtrl_checkbox_button_unchecked_checked__0 = 0x7f07001e
mc.meson.kasumi:style/Base.Theme.AppCompat.Light.Dialog.FixedSize = 0x7f120063
mc.meson.kasumi:id/rvExpandedModules = 0x7f0801c0
mc.meson.kasumi:anim/abc_fade_out = 0x7f010001
mc.meson.kasumi:attr/helperText = 0x7f03022d
mc.meson.kasumi:attr/number = 0x7f030387
mc.meson.kasumi:attr/hideAnimationBehavior = 0x7f030231
mc.meson.kasumi:style/TextAppearance.M3.Sys.Typescale.BodySmall = 0x7f1201e2
mc.meson.kasumi:attr/colorSurfaceContainerLowest = 0x7f03012c
mc.meson.kasumi:dimen/m3_menu_elevation = 0x7f0601bc
mc.meson.kasumi:dimen/m3_comp_time_picker_period_selector_pressed_state_layer_opacity = 0x7f0601a4
mc.meson.kasumi:attr/iconEndPadding = 0x7f030240
mc.meson.kasumi:dimen/mtrl_shape_corner_size_medium_component = 0x7f0602e3
mc.meson.kasumi:attr/autoSizeStepGranularity = 0x7f030048
mc.meson.kasumi:attr/state_liftable = 0x7f030435
mc.meson.kasumi:attr/carousel_forwardTransition = 0x7f0300ab
mc.meson.kasumi:style/Base.V14.ThemeOverlay.MaterialComponents.MaterialAlertDialog = 0x7f1200ac
mc.meson.kasumi:attr/maxCharacterCount = 0x7f030335
mc.meson.kasumi:macro/m3_comp_switch_selected_pressed_handle_color = 0x7f0c012a
mc.meson.kasumi:attr/layout_constraintLeft_toLeftOf = 0x7f0302aa
mc.meson.kasumi:style/MaterialAlertDialog.MaterialComponents.Title.Icon.CenterStacked = 0x7f12013e
mc.meson.kasumi:dimen/m3_comp_fab_primary_hover_state_layer_opacity = 0x7f060119
mc.meson.kasumi:attr/fontProviderQuery = 0x7f030210
mc.meson.kasumi:string/general_settings = 0x7f11004e
mc.meson.kasumi:id/moduleContainer = 0x7f08014d
mc.meson.kasumi:attr/horizontalOffset = 0x7f03023c
mc.meson.kasumi:attr/hintTextAppearance = 0x7f030238
mc.meson.kasumi:dimen/material_bottom_sheet_max_width = 0x7f060221
mc.meson.kasumi:anim/mtrl_bottom_sheet_slide_in = 0x7f01002e
mc.meson.kasumi:style/AlertDialog.AppCompat = 0x7f120000
mc.meson.kasumi:attr/lStar = 0x7f03027e
mc.meson.kasumi:macro/m3_comp_outlined_text_field_supporting_text_type = 0x7f0c00c5
mc.meson.kasumi:attr/endIconTintMode = 0x7f0301b3
mc.meson.kasumi:dimen/m3_comp_switch_disabled_selected_icon_opacity = 0x7f060190
mc.meson.kasumi:attr/helperTextTextColor = 0x7f030230
mc.meson.kasumi:id/open_search_view_status_bar_spacer = 0x7f080196
mc.meson.kasumi:color/m3_checkbox_button_icon_tint = 0x7f050072
mc.meson.kasumi:color/m3_ref_palette_primary70 = 0x7f050133
mc.meson.kasumi:attr/helperTextTextAppearance = 0x7f03022f
mc.meson.kasumi:style/Widget.MaterialComponents.TimePicker.Clock = 0x7f120467
mc.meson.kasumi:id/accessibility_custom_action_27 = 0x7f080025
mc.meson.kasumi:dimen/m3_btn_stroke_size = 0x7f0600dd
mc.meson.kasumi:style/Platform.MaterialComponents.Light.Dialog = 0x7f120148
mc.meson.kasumi:id/mtrl_calendar_days_of_week = 0x7f080157
mc.meson.kasumi:dimen/m3_comp_slider_disabled_handle_opacity = 0x7f060185
mc.meson.kasumi:color/material_dynamic_neutral_variant40 = 0x7f050238
mc.meson.kasumi:attr/elevationOverlayAccentColor = 0x7f0301a7
mc.meson.kasumi:attr/grid_useRtl = 0x7f030225
mc.meson.kasumi:anim/abc_slide_in_bottom = 0x7f010006
mc.meson.kasumi:attr/checkedTextViewStyle = 0x7f0300c2
mc.meson.kasumi:attr/grid_horizontalGaps = 0x7f03021f
mc.meson.kasumi:style/Widget.AppCompat.Button.Borderless = 0x7f120301
mc.meson.kasumi:dimen/m3_comp_slider_disabled_inactive_track_opacity = 0x7f060186
mc.meson.kasumi:style/Base.Theme.MaterialComponents.DialogWhenLarge = 0x7f12007a
mc.meson.kasumi:macro/m3_comp_extended_fab_primary_icon_color = 0x7f0c002e
mc.meson.kasumi:attr/colorSurfaceDim = 0x7f03012d
mc.meson.kasumi:attr/grid_rowWeights = 0x7f030221
mc.meson.kasumi:attr/graph = 0x7f03021c
mc.meson.kasumi:style/TextAppearance.AppCompat.Subhead = 0x7f1201b8
mc.meson.kasumi:attr/values = 0x7f030509
mc.meson.kasumi:color/m3_dynamic_dark_default_color_secondary_text = 0x7f050081
mc.meson.kasumi:attr/state_error = 0x7f030433
mc.meson.kasumi:attr/gapBetweenBars = 0x7f030219
mc.meson.kasumi:color/mtrl_tabs_icon_color_selector = 0x7f050330
mc.meson.kasumi:attr/itemShapeInsetTop = 0x7f030271
mc.meson.kasumi:anim/m3_side_sheet_exit_to_right = 0x7f01002d
mc.meson.kasumi:drawable/abc_ic_arrow_drop_right_black_24dp = 0x7f07003f
mc.meson.kasumi:style/Widget.Material3.TabLayout.Secondary = 0x7f1203e5
mc.meson.kasumi:macro/m3_comp_navigation_drawer_inactive_pressed_label_text_color = 0x7f0c008f
mc.meson.kasumi:attr/onNegativeCross = 0x7f03038c
mc.meson.kasumi:id/accessibility_custom_action_25 = 0x7f080023
mc.meson.kasumi:drawable/abc_seekbar_tick_mark_material = 0x7f070064
mc.meson.kasumi:dimen/m3_comp_outlined_card_icon_size = 0x7f060152
mc.meson.kasumi:anim/m3_bottom_sheet_slide_out = 0x7f010027
mc.meson.kasumi:attr/queryHint = 0x7f0303c7
mc.meson.kasumi:color/m3_textfield_filled_background_color = 0x7f050207
mc.meson.kasumi:attr/forceApplySystemWindowInsetTop = 0x7f030215
mc.meson.kasumi:interpolator/m3_sys_motion_easing_emphasized = 0x7f0a0007
mc.meson.kasumi:dimen/m3_sys_motion_easing_linear_control_y2 = 0x7f06020d
mc.meson.kasumi:color/m3_ref_palette_primary50 = 0x7f050131
mc.meson.kasumi:animator/fragment_fade_exit = 0x7f020006
mc.meson.kasumi:style/Base.Widget.Design.TabLayout = 0x7f120109
mc.meson.kasumi:attr/motionDurationMedium2 = 0x7f030357
mc.meson.kasumi:attr/grid_orientation = 0x7f030220
mc.meson.kasumi:attr/trackDecorationTint = 0x7f0304f2
mc.meson.kasumi:attr/layout_constraintEnd_toEndOf = 0x7f03029c
mc.meson.kasumi:id/accessibility_custom_action_8 = 0x7f08002f
mc.meson.kasumi:attr/fontProviderSystemFontFamily = 0x7f030211
mc.meson.kasumi:style/Widget.MaterialComponents.Toolbar.PrimarySurface = 0x7f120471
mc.meson.kasumi:color/md_theme_light_surfaceVariant = 0x7f0502f4
mc.meson.kasumi:color/m3_sys_color_dynamic_dark_on_primary = 0x7f050189
mc.meson.kasumi:integer/mtrl_calendar_header_orientation = 0x7f090031
mc.meson.kasumi:attr/animationMode = 0x7f030039
mc.meson.kasumi:attr/badgeHeight = 0x7f030058
mc.meson.kasumi:styleable/NavigationView = 0x7f13007a
mc.meson.kasumi:attr/fontProviderPackage = 0x7f03020f
mc.meson.kasumi:style/ThemeOverlay.Material3.DynamicColors.DayNight = 0x7f1202aa
mc.meson.kasumi:color/notification_action_color_filter = 0x7f05033a
mc.meson.kasumi:attr/fontProviderFetchTimeout = 0x7f03020e
mc.meson.kasumi:dimen/mtrl_alert_dialog_background_inset_bottom = 0x7f060246
mc.meson.kasumi:dimen/m3_sys_motion_easing_emphasized_decelerate_control_x2 = 0x7f0601fb
mc.meson.kasumi:color/material_dynamic_color_light_on_error_container = 0x7f050225
mc.meson.kasumi:string/mtrl_picker_toggle_to_day_selection = 0x7f1100ad
mc.meson.kasumi:attr/fontProviderFetchStrategy = 0x7f03020d
mc.meson.kasumi:attr/fontProviderCerts = 0x7f03020c
mc.meson.kasumi:dimen/m3_comp_fab_primary_pressed_state_layer_opacity = 0x7f06011e
mc.meson.kasumi:attr/passwordToggleTintMode = 0x7f0303a3
mc.meson.kasumi:attr/lottie_ignoreDisabledSystemAnimations = 0x7f0302f4
mc.meson.kasumi:drawable/abc_btn_radio_to_on_mtrl_015 = 0x7f070035
mc.meson.kasumi:style/Widget.Material3.AutoCompleteTextView.OutlinedBox = 0x7f120350
mc.meson.kasumi:attr/headerLayout = 0x7f03022b
mc.meson.kasumi:style/ThemeOverlay.MaterialComponents.BottomSheetDialog = 0x7f1202d8
mc.meson.kasumi:attr/defaultState = 0x7f03017d
mc.meson.kasumi:attr/flow_verticalGap = 0x7f030206
mc.meson.kasumi:id/postLayout = 0x7f0801aa
mc.meson.kasumi:attr/toolbarStyle = 0x7f0304e2
mc.meson.kasumi:style/Widget.AppCompat.Light.ListView.DropDown = 0x7f120320
mc.meson.kasumi:string/floating_window_title = 0x7f11004b
mc.meson.kasumi:color/m3_elevated_chip_background_color = 0x7f05008b
mc.meson.kasumi:attr/textAppearanceHeadlineMedium = 0x7f030484
mc.meson.kasumi:style/Base.Widget.MaterialComponents.CheckedTextView = 0x7f12011f
mc.meson.kasumi:attr/flow_padding = 0x7f030203
mc.meson.kasumi:attr/ifTagSet = 0x7f030249
mc.meson.kasumi:macro/m3_comp_switch_selected_pressed_state_layer_color = 0x7f0c012c
mc.meson.kasumi:attr/hintEnabled = 0x7f030237
mc.meson.kasumi:style/Widget.Material3.CircularProgressIndicator = 0x7f12037f
mc.meson.kasumi:color/m3_popupmenu_overlay_color = 0x7f05009d
mc.meson.kasumi:attr/expandedHintEnabled = 0x7f0301c5
mc.meson.kasumi:attr/fastScrollVerticalThumbDrawable = 0x7f0301e2
mc.meson.kasumi:attr/floatingActionButtonLargeSecondaryStyle = 0x7f0301e8
mc.meson.kasumi:attr/duration = 0x7f0301a1
mc.meson.kasumi:dimen/tooltip_precise_anchor_extra_offset = 0x7f06031f
mc.meson.kasumi:dimen/m3_comp_progress_indicator_active_indicator_track_space = 0x7f060162
mc.meson.kasumi:attr/expandedTitleMarginStart = 0x7f0301ca
mc.meson.kasumi:dimen/m3_sys_state_hover_state_layer_opacity = 0x7f06021c
mc.meson.kasumi:attr/cornerSizeTopLeft = 0x7f03015b
mc.meson.kasumi:dimen/m3_comp_text_button_hover_state_layer_opacity = 0x7f06019d
mc.meson.kasumi:attr/flow_maxElementsWrap = 0x7f030202
mc.meson.kasumi:color/m3_ref_palette_secondary80 = 0x7f050141
mc.meson.kasumi:attr/flow_lastVerticalStyle = 0x7f030201
mc.meson.kasumi:style/TextAppearance.Material3.TitleSmall = 0x7f120203
mc.meson.kasumi:attr/isMaterial3DynamicColorApplied = 0x7f03025a
mc.meson.kasumi:style/Theme.AppCompat.NoActionBar = 0x7f12022d
mc.meson.kasumi:color/m3_sys_color_dark_on_error_container = 0x7f050166
mc.meson.kasumi:attr/backgroundSplit = 0x7f030053
mc.meson.kasumi:drawable/settings_panel_background = 0x7f070100
mc.meson.kasumi:color/m3_ref_palette_dynamic_neutral50 = 0x7f0500ad
mc.meson.kasumi:style/Widget.AppCompat.Light.ActionBar = 0x7f12030f
mc.meson.kasumi:attr/drawerLayoutStyle = 0x7f03019d
mc.meson.kasumi:attr/drawableTint = 0x7f030198
mc.meson.kasumi:attr/floatingActionButtonSmallSecondaryStyle = 0x7f0301ef
mc.meson.kasumi:attr/haloColor = 0x7f030229
mc.meson.kasumi:string/m3_ref_typeface_plain_medium = 0x7f110058
mc.meson.kasumi:attr/elevationOverlayEnabled = 0x7f0301a9
mc.meson.kasumi:attr/alertDialogCenterButtons = 0x7f03002d
mc.meson.kasumi:dimen/mtrl_calendar_text_input_padding_top = 0x7f060293
mc.meson.kasumi:dimen/m3_back_progress_side_container_max_scale_x_distance_grow = 0x7f0600b0
mc.meson.kasumi:anim/btn_radio_to_on_mtrl_ring_outer_path_animation = 0x7f010018
mc.meson.kasumi:integer/m3_sys_shape_corner_extra_small_corner_family = 0x7f090022
mc.meson.kasumi:drawable/abc_btn_borderless_material = 0x7f07002b
mc.meson.kasumi:attr/floatingActionButtonLargeStyle = 0x7f0301e9
mc.meson.kasumi:styleable/AppCompatEmojiHelper = 0x7f130010
mc.meson.kasumi:id/percent = 0x7f0801a6
mc.meson.kasumi:attr/simpleItemLayout = 0x7f030409
mc.meson.kasumi:attr/behavior_draggable = 0x7f03006e
mc.meson.kasumi:attr/buttonBarNeutralButtonStyle = 0x7f030091
mc.meson.kasumi:attr/colorBackgroundFloating = 0x7f0300fa
mc.meson.kasumi:attr/materialCalendarMonthNavigationButton = 0x7f030317
mc.meson.kasumi:attr/finishPrimaryWithSecondary = 0x7f0301e4
mc.meson.kasumi:color/material_personalized_color_control_activated = 0x7f05027f
mc.meson.kasumi:layout/mtrl_layout_snackbar = 0x7f0b0068
mc.meson.kasumi:id/view_tree_on_back_pressed_dispatcher_owner = 0x7f08025e
mc.meson.kasumi:color/m3_sys_color_dark_inverse_on_surface = 0x7f050161
mc.meson.kasumi:style/Widget.Material3.Slider.Legacy = 0x7f1203de
mc.meson.kasumi:attr/colorSecondaryFixed = 0x7f030123
mc.meson.kasumi:attr/fabSize = 0x7f0301de
mc.meson.kasumi:anim/m3_side_sheet_enter_from_right = 0x7f01002b
mc.meson.kasumi:style/Base.Widget.AppCompat.DrawerArrowToggle.Common = 0x7f1200e6
mc.meson.kasumi:attr/fabCradleVerticalOffset = 0x7f0301dc
mc.meson.kasumi:attr/navigationViewStyle = 0x7f030382
mc.meson.kasumi:attr/fabAlignmentModeEndMargin = 0x7f0301d7
mc.meson.kasumi:color/md_theme_light_shadow = 0x7f0502f1
mc.meson.kasumi:string/setting_disabled = 0x7f1100ce
mc.meson.kasumi:macro/m3_comp_switch_selected_icon_color = 0x7f0c0129
mc.meson.kasumi:dimen/m3_btn_elevation = 0x7f0600d0
mc.meson.kasumi:attr/counterMaxLength = 0x7f03015e
mc.meson.kasumi:style/ShapeAppearance.MaterialComponents.MediumComponent = 0x7f12018a
mc.meson.kasumi:id/filled = 0x7f0800e8
mc.meson.kasumi:id/action_settings_to_home = 0x7f08004d
mc.meson.kasumi:color/m3_sys_color_on_secondary_fixed = 0x7f0501f4
mc.meson.kasumi:anim/fade_in = 0x7f01001d
mc.meson.kasumi:attr/listPreferredItemPaddingRight = 0x7f0302e3
mc.meson.kasumi:attr/iconTintMode = 0x7f030246
mc.meson.kasumi:attr/floatingActionButtonPrimaryStyle = 0x7f0301ec
mc.meson.kasumi:color/m3_ref_palette_secondary30 = 0x7f05013c
mc.meson.kasumi:attr/extendedFloatingActionButtonSurfaceStyle = 0x7f0301d3
mc.meson.kasumi:macro/m3_comp_date_picker_modal_year_selection_year_selected_container_color = 0x7f0c001f
mc.meson.kasumi:id/position = 0x7f0801a9
mc.meson.kasumi:dimen/mtrl_btn_disabled_elevation = 0x7f06025b
mc.meson.kasumi:string/abc_menu_space_shortcut_label = 0x7f11000f
mc.meson.kasumi:dimen/design_bottom_navigation_item_min_width = 0x7f060066
mc.meson.kasumi:style/Base.TextAppearance.AppCompat.Title.Inverse = 0x7f12003d
mc.meson.kasumi:integer/m3_sys_motion_duration_short2 = 0x7f09001d
mc.meson.kasumi:color/minecraft_red_dark = 0x7f050300
mc.meson.kasumi:attr/extendedFloatingActionButtonStyle = 0x7f0301d2
mc.meson.kasumi:style/RtlOverlay.Widget.AppCompat.PopupMenuItem.SubmenuArrow = 0x7f120157
mc.meson.kasumi:macro/m3_comp_time_picker_time_selector_unselected_label_text_color = 0x7f0c016a
mc.meson.kasumi:attr/actionBarTabTextStyle = 0x7f03000b
mc.meson.kasumi:attr/extendedFloatingActionButtonSecondaryStyle = 0x7f0301d1
mc.meson.kasumi:color/material_grey_600 = 0x7f05026a
mc.meson.kasumi:color/abc_hint_foreground_material_light = 0x7f050008
mc.meson.kasumi:dimen/m3_alert_dialog_title_bottom_margin = 0x7f0600a3
mc.meson.kasumi:attr/expandedTitleMarginEnd = 0x7f0301c9
mc.meson.kasumi:macro/m3_comp_filled_text_field_container_color = 0x7f0c004b
mc.meson.kasumi:attr/expandedTitleMargin = 0x7f0301c7
mc.meson.kasumi:dimen/mtrl_card_checked_icon_margin = 0x7f06029b
mc.meson.kasumi:dimen/mtrl_tooltip_minHeight = 0x7f060308
mc.meson.kasumi:drawable/$avd_hide_password__0 = 0x7f070000
mc.meson.kasumi:attr/expandedTitleGravity = 0x7f0301c6
mc.meson.kasumi:interpolator/mtrl_fast_out_slow_in = 0x7f0a000f
mc.meson.kasumi:attr/cardPreventCornerOverlap = 0x7f0300a4
mc.meson.kasumi:id/btnExportAll = 0x7f08007e
mc.meson.kasumi:attr/expandActivityOverflowButtonDrawable = 0x7f0301c3
mc.meson.kasumi:color/m3_sys_color_dynamic_light_on_secondary_container = 0x7f0501ae
mc.meson.kasumi:attr/checkMarkTintMode = 0x7f0300b6
mc.meson.kasumi:attr/data = 0x7f030172
mc.meson.kasumi:dimen/m3_comp_badge_size = 0x7f060100
mc.meson.kasumi:drawable/ic_call_decline = 0x7f070099
mc.meson.kasumi:attr/motionEasingStandard = 0x7f030366
mc.meson.kasumi:attr/fabAnchorMode = 0x7f0301d8
mc.meson.kasumi:dimen/m3_comp_fab_primary_large_icon_size = 0x7f06011c
mc.meson.kasumi:attr/carousel_previousState = 0x7f0300ae
mc.meson.kasumi:layout/mtrl_picker_header_fullscreen = 0x7f0b006f
mc.meson.kasumi:drawable/ic_arrow_drop_down_24 = 0x7f070094
mc.meson.kasumi:attr/errorContentDescription = 0x7f0301ba
mc.meson.kasumi:styleable/KeyFramesAcceleration = 0x7f130048
mc.meson.kasumi:id/above = 0x7f08000e
mc.meson.kasumi:attr/grid_columnWeights = 0x7f03021d
mc.meson.kasumi:anim/design_bottom_sheet_slide_in = 0x7f010019
mc.meson.kasumi:style/ThemeOverlay.MaterialComponents.Dialog = 0x7f1202dc
mc.meson.kasumi:attr/errorAccessibilityLiveRegion = 0x7f0301b9
mc.meson.kasumi:drawable/notification_bg_low_normal = 0x7f0700f5
mc.meson.kasumi:dimen/m3_badge_offset = 0x7f0600b4
mc.meson.kasumi:color/m3_card_ripple_color = 0x7f050070
mc.meson.kasumi:styleable/Toolbar = 0x7f1300a0
mc.meson.kasumi:attr/behavior_peekHeight = 0x7f030074
mc.meson.kasumi:style/Widget.MaterialComponents.MaterialCalendar.Day.Invalid = 0x7f12042e
mc.meson.kasumi:attr/ensureMinTouchTargetSize = 0x7f0301b6
mc.meson.kasumi:attr/constraint_referenced_ids = 0x7f03013b
mc.meson.kasumi:style/Base.ThemeOverlay.AppCompat.ActionBar = 0x7f120086
mc.meson.kasumi:attr/fastScrollEnabled = 0x7f0301df
mc.meson.kasumi:attr/constraintSetEnd = 0x7f030139
mc.meson.kasumi:style/Widget.MaterialComponents.MaterialCalendar = 0x7f12042c
mc.meson.kasumi:color/mtrl_btn_text_btn_ripple_color = 0x7f050307
mc.meson.kasumi:attr/flow_horizontalGap = 0x7f0301fc
mc.meson.kasumi:attr/textAppearanceHeadlineLarge = 0x7f030483
mc.meson.kasumi:macro/m3_comp_outlined_text_field_focus_supporting_text_color = 0x7f0c00bc
mc.meson.kasumi:attr/carousel_touchUpMode = 0x7f0300af
mc.meson.kasumi:color/m3_ref_palette_neutral99 = 0x7f05011d
mc.meson.kasumi:attr/counterTextAppearance = 0x7f030161
mc.meson.kasumi:attr/flow_firstHorizontalStyle = 0x7f0301f7
mc.meson.kasumi:attr/floatingActionButtonLargeSurfaceStyle = 0x7f0301ea
mc.meson.kasumi:attr/drawableTintMode = 0x7f030199
mc.meson.kasumi:attr/enableEdgeToEdge = 0x7f0301ab
mc.meson.kasumi:color/m3_ref_palette_tertiary70 = 0x7f05014d
mc.meson.kasumi:attr/contentPaddingLeft = 0x7f030149
mc.meson.kasumi:color/m3_sys_color_dynamic_dark_error_container = 0x7f050182
mc.meson.kasumi:attr/bottomAppBarStyle = 0x7f03007d
mc.meson.kasumi:attr/layout_constrainedHeight = 0x7f03028f
mc.meson.kasumi:attr/indicatorDirectionCircular = 0x7f030252
mc.meson.kasumi:id/path = 0x7f0801a3
mc.meson.kasumi:attr/SharedValueId = 0x7f030001
mc.meson.kasumi:attr/fabAnimationMode = 0x7f0301d9
mc.meson.kasumi:color/md_theme_dark_inversePrimary = 0x7f0502bf
mc.meson.kasumi:style/Theme.MaterialComponents.DayNight.Dialog.Bridge = 0x7f120260
mc.meson.kasumi:macro/m3_comp_time_picker_time_selector_selected_label_text_color = 0x7f0c0163
mc.meson.kasumi:dimen/m3_btn_elevated_btn_elevation = 0x7f0600cf
mc.meson.kasumi:drawable/material_ic_clear_black_24dp = 0x7f0700c3
mc.meson.kasumi:attr/colorOnSurface = 0x7f030110
mc.meson.kasumi:dimen/material_helper_text_font_1_3_padding_top = 0x7f06023f
mc.meson.kasumi:attr/itemMinHeight = 0x7f030266
mc.meson.kasumi:dimen/m3_badge_with_text_offset = 0x7f0600b8
mc.meson.kasumi:attr/layout_constraintTop_toTopOf = 0x7f0302b4
mc.meson.kasumi:id/submenuarrow = 0x7f080203
mc.meson.kasumi:attr/drawableTopCompat = 0x7f03019a
mc.meson.kasumi:id/action_features_to_settings = 0x7f080042
mc.meson.kasumi:drawable/$mtrl_checkbox_button_icon_checked_unchecked__2 = 0x7f070013
mc.meson.kasumi:attr/chipMinTouchTargetSize = 0x7f0300cd
mc.meson.kasumi:attr/textAllCaps = 0x7f030472
mc.meson.kasumi:attr/logoDescription = 0x7f0302e7
mc.meson.kasumi:attr/showMotionSpec = 0x7f030402
mc.meson.kasumi:style/Widget.AppCompat.ListView = 0x7f120327
mc.meson.kasumi:dimen/mtrl_navigation_rail_icon_margin = 0x7f0602cf
mc.meson.kasumi:id/nav_graph = 0x7f080170
mc.meson.kasumi:attr/colorOnErrorContainer = 0x7f030106
mc.meson.kasumi:attr/arrowShaftLength = 0x7f03003f
mc.meson.kasumi:attr/drawableLeftCompat = 0x7f030194
mc.meson.kasumi:attr/minTouchTargetSize = 0x7f030345
mc.meson.kasumi:attr/drawableEndCompat = 0x7f030193
mc.meson.kasumi:macro/m3_comp_date_picker_modal_header_supporting_text_color = 0x7f0c0017
mc.meson.kasumi:attr/dropdownListPreferredItemHeight = 0x7f0301a0
mc.meson.kasumi:style/Base.Theme.Material3.Light.DialogWhenLarge = 0x7f120070
mc.meson.kasumi:color/m3_ref_palette_neutral100 = 0x7f050108
mc.meson.kasumi:attr/pressedTranslationZ = 0x7f0303bf
mc.meson.kasumi:id/btnBack = 0x7f080076
mc.meson.kasumi:attr/floatingActionButtonSmallSurfaceStyle = 0x7f0301f1
mc.meson.kasumi:attr/dragThreshold = 0x7f030190
mc.meson.kasumi:style/Widget.Material3.Toolbar.Surface = 0x7f1203f4
mc.meson.kasumi:style/Base.ThemeOverlay.Material3.Dialog = 0x7f12008e
mc.meson.kasumi:id/easeInOut = 0x7f0800cf
mc.meson.kasumi:dimen/design_snackbar_text_size = 0x7f060088
mc.meson.kasumi:color/material_personalized_color_text_secondary_and_tertiary_inverse = 0x7f0502aa
mc.meson.kasumi:attr/dynamicColorThemeOverlay = 0x7f0301a2
mc.meson.kasumi:drawable/mtrl_switch_track = 0x7f0700ee
mc.meson.kasumi:drawable/abc_btn_switch_to_on_mtrl_00012 = 0x7f070037
mc.meson.kasumi:styleable/KeyTrigger = 0x7f13004c
mc.meson.kasumi:color/md_theme_light_outline = 0x7f0502ea
mc.meson.kasumi:style/Widget.AppCompat.Spinner.Underlined = 0x7f120339
mc.meson.kasumi:attr/layout_behavior = 0x7f03028c
mc.meson.kasumi:attr/dividerThickness = 0x7f03018c
mc.meson.kasumi:style/Base.V14.Theme.MaterialComponents.Dialog.Bridge = 0x7f1200a1
mc.meson.kasumi:drawable/$ic_launcher_background__0 = 0x7f070006
mc.meson.kasumi:attr/dividerPadding = 0x7f03018b
mc.meson.kasumi:attr/collapsedTitleTextAppearance = 0x7f0300f1
mc.meson.kasumi:color/material_dynamic_neutral_variant95 = 0x7f05023e
mc.meson.kasumi:attr/dividerInsetStart = 0x7f03018a
mc.meson.kasumi:dimen/m3_navigation_item_horizontal_padding = 0x7f0601c0
mc.meson.kasumi:id/tabMode = 0x7f08020d
mc.meson.kasumi:dimen/m3_bottomappbar_fab_cradle_vertical_offset = 0x7f0600c7
mc.meson.kasumi:attr/logoAdjustViewBounds = 0x7f0302e6
mc.meson.kasumi:style/Base.TextAppearance.MaterialComponents.Headline6 = 0x7f120053
mc.meson.kasumi:color/mtrl_textinput_hovered_box_stroke_color = 0x7f050339
mc.meson.kasumi:attr/layout_constraintStart_toEndOf = 0x7f0302af
mc.meson.kasumi:dimen/m3_btn_padding_top = 0x7f0600dc
mc.meson.kasumi:attr/indicatorTrackGapSize = 0x7f030256
mc.meson.kasumi:interpolator/mtrl_linear_out_slow_in = 0x7f0a0011
mc.meson.kasumi:integer/m3_sys_motion_duration_extra_long1 = 0x7f090010
mc.meson.kasumi:attr/dialogPreferredPadding = 0x7f030183
mc.meson.kasumi:id/nav_features = 0x7f08016f
mc.meson.kasumi:dimen/design_fab_elevation = 0x7f06006f
mc.meson.kasumi:attr/dialogCornerRadius = 0x7f030182
mc.meson.kasumi:color/m3_switch_track_tint = 0x7f05015d
mc.meson.kasumi:attr/editTextBackground = 0x7f0301a3
mc.meson.kasumi:dimen/m3_navigation_item_vertical_padding = 0x7f0601c6
mc.meson.kasumi:attr/titleMarginTop = 0x7f0304d8
mc.meson.kasumi:attr/destination = 0x7f030181
mc.meson.kasumi:attr/elevation = 0x7f0301a6
mc.meson.kasumi:drawable/abc_list_pressed_holo_light = 0x7f070052
mc.meson.kasumi:attr/argType = 0x7f03003d
mc.meson.kasumi:color/material_dynamic_tertiary0 = 0x7f05025a
mc.meson.kasumi:attr/itemMaxLines = 0x7f030265
mc.meson.kasumi:attr/textAppearanceCaption = 0x7f030479
mc.meson.kasumi:color/m3_highlighted_text = 0x7f050090
mc.meson.kasumi:integer/m3_sys_motion_duration_short4 = 0x7f09001f
mc.meson.kasumi:drawable/$avd_show_password__1 = 0x7f070004
mc.meson.kasumi:attr/flow_horizontalBias = 0x7f0301fb
mc.meson.kasumi:string/mtrl_switch_thumb_path_unchecked = 0x7f1100b5
mc.meson.kasumi:drawable/tab_indicator_rounded = 0x7f070104
mc.meson.kasumi:attr/lottie_enableMergePathsForKitKatAndAbove = 0x7f0302f1
mc.meson.kasumi:attr/defaultNavHost = 0x7f03017a
mc.meson.kasumi:layout/mtrl_search_bar = 0x7f0b0075
mc.meson.kasumi:id/title = 0x7f08022c
mc.meson.kasumi:color/mtrl_btn_text_btn_bg_color_selector = 0x7f050306
mc.meson.kasumi:attr/trackColorInactive = 0x7f0304ef
mc.meson.kasumi:attr/floatingActionButtonTertiaryStyle = 0x7f0301f5
mc.meson.kasumi:style/Base.Widget.AppCompat.Spinner.Underlined = 0x7f120104
mc.meson.kasumi:dimen/notification_action_icon_size = 0x7f06030c
mc.meson.kasumi:attr/defaultDuration = 0x7f030178
mc.meson.kasumi:dimen/m3_btn_text_btn_icon_padding_left = 0x7f0600de
mc.meson.kasumi:style/ShapeAppearanceOverlay.MaterialComponents.MaterialCalendar.Window.Fullscreen = 0x7f12019d
mc.meson.kasumi:id/onInterceptTouchReturnSwipe = 0x7f08018a
mc.meson.kasumi:attr/materialCardViewFilledStyle = 0x7f03031c
mc.meson.kasumi:id/showHome = 0x7f0801df
mc.meson.kasumi:attr/trackHeight = 0x7f0304f4
mc.meson.kasumi:dimen/m3_comp_elevated_card_container_elevation = 0x7f06010a
mc.meson.kasumi:style/RtlOverlay.Widget.AppCompat.PopupMenuItem.Title = 0x7f120159
mc.meson.kasumi:attr/circularflow_defaultRadius = 0x7f0300db
mc.meson.kasumi:attr/itemHorizontalTranslationEnabled = 0x7f030261
mc.meson.kasumi:styleable/NavInclude = 0x7f130076
mc.meson.kasumi:attr/contentInsetLeft = 0x7f030142
mc.meson.kasumi:attr/itemFillColor = 0x7f03025f
mc.meson.kasumi:style/TextAppearance.M3.Sys.Typescale.DisplayLarge = 0x7f1201e3
mc.meson.kasumi:attr/lottie_colorFilter = 0x7f0302ef
mc.meson.kasumi:color/m3_sys_color_dynamic_dark_outline = 0x7f050191
mc.meson.kasumi:attr/closeItemLayout = 0x7f0300ec
mc.meson.kasumi:style/Widget.MaterialComponents.ActionBar.PrimarySurface = 0x7f1203f7
mc.meson.kasumi:attr/actionModeSelectAllDrawable = 0x7f03001c
mc.meson.kasumi:attr/customStringValue = 0x7f030171
mc.meson.kasumi:id/startVertical = 0x7f0801fc
mc.meson.kasumi:attr/customFloatValue = 0x7f03016c
mc.meson.kasumi:attr/boxCornerRadiusBottomEnd = 0x7f030086
mc.meson.kasumi:attr/maxImageSize = 0x7f030337
mc.meson.kasumi:attr/customColorValue = 0x7f03016a
mc.meson.kasumi:attr/drawableSize = 0x7f030196
mc.meson.kasumi:style/Widget.Material3.SearchView.Prefix = 0x7f1203d6
mc.meson.kasumi:style/Widget.Material3.MaterialCalendar.MonthNavigationButton = 0x7f1203b8
mc.meson.kasumi:dimen/m3_comp_filter_chip_elevated_container_elevation = 0x7f06012c
mc.meson.kasumi:attr/strokeWidth = 0x7f03043c
mc.meson.kasumi:styleable/Chip = 0x7f130020
mc.meson.kasumi:attr/cursorErrorColor = 0x7f030166
mc.meson.kasumi:attr/layout_constraintEnd_toStartOf = 0x7f03029d
mc.meson.kasumi:attr/motionDurationShort1 = 0x7f03035a
mc.meson.kasumi:dimen/m3_comp_primary_navigation_tab_inactive_pressed_state_layer_opacity = 0x7f060160
mc.meson.kasumi:style/Widget.Material3.Button.TonalButton.Icon = 0x7f12036e
mc.meson.kasumi:attr/counterOverflowTextColor = 0x7f030160
mc.meson.kasumi:attr/hideNavigationIcon = 0x7f030233
mc.meson.kasumi:attr/counterOverflowTextAppearance = 0x7f03015f
mc.meson.kasumi:style/Base.Theme.AppCompat.Dialog.MinWidth = 0x7f12005d
mc.meson.kasumi:attr/itemActiveIndicatorStyle = 0x7f03025d
mc.meson.kasumi:attr/counterEnabled = 0x7f03015d
mc.meson.kasumi:layout/select_dialog_singlechoice_material = 0x7f0b007f
mc.meson.kasumi:anim/slide_in_right = 0x7f010035
mc.meson.kasumi:style/Widget.MaterialComponents.MaterialCalendar.HeaderLayout.Fullscreen = 0x7f120438
mc.meson.kasumi:attr/motionDurationShort2 = 0x7f03035b
mc.meson.kasumi:attr/activityChooserViewStyle = 0x7f030029
mc.meson.kasumi:dimen/m3_chip_checked_hovered_translation_z = 0x7f0600f3
mc.meson.kasumi:attr/textureHeight = 0x7f0304b1
mc.meson.kasumi:color/material_harmonized_color_on_error_container = 0x7f050271
mc.meson.kasumi:attr/customBoolean = 0x7f030168
mc.meson.kasumi:string/mtrl_picker_range_header_only_start_selected = 0x7f11009f
mc.meson.kasumi:dimen/m3_sys_state_focus_state_layer_opacity = 0x7f06021b
mc.meson.kasumi:drawable/abc_seekbar_track_material = 0x7f070065
mc.meson.kasumi:attr/cornerSizeTopRight = 0x7f03015c
mc.meson.kasumi:attr/maxLines = 0x7f030338
mc.meson.kasumi:drawable/$mtrl_checkbox_button_icon_unchecked_checked__2 = 0x7f07001a
mc.meson.kasumi:attr/exitAnim = 0x7f0301c2
mc.meson.kasumi:attr/goIcon = 0x7f03021b
mc.meson.kasumi:style/Base.Theme.Material3.Light.Dialog = 0x7f12006e
mc.meson.kasumi:attr/trackInsideCornerSize = 0x7f0304f5
mc.meson.kasumi:attr/layout_constraintRight_toRightOf = 0x7f0302ae
mc.meson.kasumi:color/m3_sys_color_dynamic_light_surface_container = 0x7f0501bb
mc.meson.kasumi:color/material_dynamic_neutral30 = 0x7f05022a
mc.meson.kasumi:attr/cornerFamilyTopLeft = 0x7f030155
mc.meson.kasumi:dimen/mtrl_alert_dialog_background_inset_end = 0x7f060247
mc.meson.kasumi:anim/btn_checkbox_to_checked_icon_null_animation = 0x7f01000f
mc.meson.kasumi:macro/m3_comp_navigation_drawer_active_pressed_label_text_color = 0x7f0c0082
mc.meson.kasumi:layout/material_time_chip = 0x7f0b0051
mc.meson.kasumi:dimen/m3_comp_extended_fab_primary_container_elevation = 0x7f06010c
mc.meson.kasumi:attr/cornerFamilyBottomLeft = 0x7f030153
mc.meson.kasumi:style/ThemeOverlay.Material3.HarmonizedColors = 0x7f1202b4
mc.meson.kasumi:attr/titlePositionInterpolator = 0x7f0304da
mc.meson.kasumi:attr/buttonBarPositiveButtonStyle = 0x7f030092
mc.meson.kasumi:attr/stackFromEnd = 0x7f030424
mc.meson.kasumi:attr/background = 0x7f03004c
mc.meson.kasumi:color/card_background_secondary = 0x7f05002e
mc.meson.kasumi:attr/paddingTopSystemWindowInsets = 0x7f03039b
mc.meson.kasumi:attr/textInputStyle = 0x7f0304a8
mc.meson.kasumi:dimen/abc_text_size_large_material = 0x7f060048
mc.meson.kasumi:attr/behavior_hideable = 0x7f030072
mc.meson.kasumi:dimen/abc_dialog_padding_material = 0x7f060024
mc.meson.kasumi:layout/design_layout_tab_icon = 0x7f0b0025
mc.meson.kasumi:attr/coplanarSiblingViewId = 0x7f030151
mc.meson.kasumi:color/m3_sys_color_dark_surface_container_high = 0x7f050178
mc.meson.kasumi:attr/hoveredFocusedTranslationZ = 0x7f03023e
mc.meson.kasumi:macro/m3_comp_badge_large_label_text_color = 0x7f0c0003
mc.meson.kasumi:id/transition_transform = 0x7f08023f
mc.meson.kasumi:attr/mock_labelColor = 0x7f03034a
mc.meson.kasumi:attr/badgeWidePadding = 0x7f030061
mc.meson.kasumi:attr/materialAlertDialogButtonSpacerVisibility = 0x7f030304
mc.meson.kasumi:attr/controlBackground = 0x7f03014f
mc.meson.kasumi:style/ShapeAppearance.M3.Sys.Shape.Corner.Large = 0x7f120177
mc.meson.kasumi:attr/navigationContentDescription = 0x7f03037d
mc.meson.kasumi:attr/onShow = 0x7f03038e
mc.meson.kasumi:macro/m3_comp_date_picker_modal_range_selection_active_indicator_container_color = 0x7f0c0019
mc.meson.kasumi:attr/autoSizeTextType = 0x7f030049
mc.meson.kasumi:layout/mtrl_alert_dialog_actions = 0x7f0b0058
mc.meson.kasumi:dimen/mtrl_tooltip_minWidth = 0x7f060309
mc.meson.kasumi:style/Widget.Material3.SearchBar = 0x7f1203d3
mc.meson.kasumi:style/ThemeOverlay.MaterialComponents.AutoCompleteTextView.FilledBox = 0x7f1202d2
mc.meson.kasumi:attr/actionBarTabStyle = 0x7f03000a
mc.meson.kasumi:color/abc_tint_edittext = 0x7f050015
mc.meson.kasumi:color/m3_ref_palette_neutral_variant70 = 0x7f050126
mc.meson.kasumi:style/ThemeOverlay.MaterialComponents.AutoCompleteTextView.OutlinedBox.Dense = 0x7f1202d5
mc.meson.kasumi:macro/m3_comp_outlined_card_container_shape = 0x7f0c00a9
mc.meson.kasumi:attr/layout_constraintBottom_creator = 0x7f030295
mc.meson.kasumi:attr/textAppearanceTitleMedium = 0x7f030496
mc.meson.kasumi:style/Base.Widget.AppCompat.PopupMenu = 0x7f1200f7
mc.meson.kasumi:dimen/m3_comp_search_bar_pressed_state_layer_opacity = 0x7f060172
mc.meson.kasumi:dimen/m3_card_elevation = 0x7f0600ea
mc.meson.kasumi:color/m3_dark_hint_foreground = 0x7f05007c
mc.meson.kasumi:attr/layout_constraintWidth_max = 0x7f0302ba
mc.meson.kasumi:string/auto_connect = 0x7f110022
mc.meson.kasumi:attr/dialogTheme = 0x7f030184
mc.meson.kasumi:color/m3_sys_color_dynamic_dark_on_secondary_container = 0x7f05018c
mc.meson.kasumi:color/m3_navigation_bar_item_with_indicator_icon_tint = 0x7f050093
mc.meson.kasumi:attr/logoScaleType = 0x7f0302e8
mc.meson.kasumi:attr/contentInsetEnd = 0x7f030140
mc.meson.kasumi:style/Widget.MaterialComponents.CompoundButton.RadioButton = 0x7f120424
mc.meson.kasumi:color/m3_sys_color_light_primary = 0x7f0501e3
mc.meson.kasumi:attr/collapsingToolbarLayoutStyle = 0x7f0300f7
mc.meson.kasumi:dimen/m3_bottom_nav_min_height = 0x7f0600c1
mc.meson.kasumi:attr/collapsingToolbarLayoutMediumSize = 0x7f0300f5
mc.meson.kasumi:integer/mtrl_card_anim_delay_ms = 0x7f090034
mc.meson.kasumi:attr/content = 0x7f03013e
mc.meson.kasumi:style/Widget.MaterialComponents.Slider = 0x7f120451
mc.meson.kasumi:color/m3_timepicker_clock_text_color = 0x7f05020f
mc.meson.kasumi:integer/mtrl_switch_thumb_viewport_size = 0x7f09003c
mc.meson.kasumi:attr/itemShapeInsetBottom = 0x7f03026e
mc.meson.kasumi:color/material_dynamic_neutral90 = 0x7f050230
mc.meson.kasumi:attr/layout_constraintCircleRadius = 0x7f03029a
mc.meson.kasumi:style/Base.Widget.MaterialComponents.Slider = 0x7f120127
mc.meson.kasumi:attr/flow_verticalBias = 0x7f030205
mc.meson.kasumi:attr/paddingStartSystemWindowInsets = 0x7f030399
mc.meson.kasumi:id/list_item = 0x7f080128
mc.meson.kasumi:attr/commitIcon = 0x7f030135
mc.meson.kasumi:styleable/CardView = 0x7f13001d
mc.meson.kasumi:attr/colorTertiaryFixedDim = 0x7f030134
mc.meson.kasumi:drawable/ic_circle = 0x7f07010a
mc.meson.kasumi:color/material_dynamic_color_light_error = 0x7f050222
mc.meson.kasumi:color/m3_ref_palette_neutral_variant100 = 0x7f050120
mc.meson.kasumi:attr/colorTertiaryContainer = 0x7f030132
mc.meson.kasumi:attr/touchRegionId = 0x7f0304eb
mc.meson.kasumi:dimen/material_clock_period_toggle_horizontal_gap = 0x7f06022c
mc.meson.kasumi:attr/dayStyle = 0x7f030176
mc.meson.kasumi:color/highlighted_text_material_light = 0x7f050063
mc.meson.kasumi:attr/quantizeMotionPhase = 0x7f0303c4
mc.meson.kasumi:dimen/mtrl_snackbar_background_corner_radius = 0x7f0602f1
mc.meson.kasumi:dimen/m3_card_stroke_width = 0x7f0600ec
mc.meson.kasumi:attr/scrimVisibleHeightTrigger = 0x7f0303e3
mc.meson.kasumi:dimen/m3_comp_filled_card_container_elevation = 0x7f060124
mc.meson.kasumi:color/m3_sys_color_light_on_surface_variant = 0x7f0501de
mc.meson.kasumi:color/design_error = 0x7f050050
mc.meson.kasumi:style/Widget.AppCompat.Button.ButtonBar.AlertDialog = 0x7f120303
mc.meson.kasumi:color/m3_dynamic_primary_text_disable_only = 0x7f050089
mc.meson.kasumi:color/m3_card_stroke_color = 0x7f050071
mc.meson.kasumi:layout/abc_action_menu_item_layout = 0x7f0b0002
mc.meson.kasumi:attr/colorSurfaceInverse = 0x7f03012e
mc.meson.kasumi:styleable/MaterialCalendarItem = 0x7f130059
mc.meson.kasumi:macro/m3_comp_slider_disabled_inactive_track_color = 0x7f0c010e
mc.meson.kasumi:id/locale = 0x7f08012b
mc.meson.kasumi:color/m3_sys_color_light_on_surface = 0x7f0501dd
mc.meson.kasumi:attr/badgeStyle = 0x7f03005c
mc.meson.kasumi:attr/deltaPolarAngle = 0x7f03017e
mc.meson.kasumi:dimen/sliding_pane_detail_pane_width = 0x7f06031b
mc.meson.kasumi:style/Theme.Material3.Light = 0x7f12024c
mc.meson.kasumi:layout/activity_splash = 0x7f0b001e
mc.meson.kasumi:attr/thickness = 0x7f0304b4
mc.meson.kasumi:attr/emojiCompatEnabled = 0x7f0301aa
mc.meson.kasumi:dimen/mtrl_navigation_rail_icon_size = 0x7f0602d0
mc.meson.kasumi:color/abc_secondary_text_material_light = 0x7f050012
mc.meson.kasumi:attr/navGraph = 0x7f03037c
mc.meson.kasumi:macro/m3_comp_time_picker_time_selector_selected_pressed_state_layer_color = 0x7f0c0164
mc.meson.kasumi:anim/abc_popup_enter = 0x7f010003
mc.meson.kasumi:string/notifications = 0x7f1100c1
mc.meson.kasumi:attr/placeholderText = 0x7f0303ad
mc.meson.kasumi:layout/item_module_grid = 0x7f0b003e
mc.meson.kasumi:attr/flow_wrapMode = 0x7f030208
mc.meson.kasumi:attr/marginHorizontal = 0x7f0302ff
mc.meson.kasumi:attr/selectableItemBackgroundBorderless = 0x7f0303ec
mc.meson.kasumi:dimen/m3_sys_state_pressed_state_layer_opacity = 0x7f06021d
mc.meson.kasumi:style/Base.TextAppearance.AppCompat.Subhead = 0x7f12003a
mc.meson.kasumi:id/baseline = 0x7f08006a
mc.meson.kasumi:attr/mock_showLabel = 0x7f03034c
mc.meson.kasumi:color/m3_ref_palette_neutral98 = 0x7f05011c
mc.meson.kasumi:attr/colorPrimary = 0x7f030119
mc.meson.kasumi:attr/reactiveGuide_animateChange = 0x7f0303ce
mc.meson.kasumi:dimen/abc_star_big = 0x7f06003b
mc.meson.kasumi:attr/backgroundInsetTop = 0x7f030051
mc.meson.kasumi:string/abc_menu_alt_shortcut_label = 0x7f110008
mc.meson.kasumi:attr/layout_goneMarginRight = 0x7f0302c4
mc.meson.kasumi:dimen/m3_snackbar_margin = 0x7f0601ef
mc.meson.kasumi:styleable/FlowLayout = 0x7f130039
mc.meson.kasumi:attr/colorPrimaryFixedDim = 0x7f03011d
mc.meson.kasumi:color/m3_sys_color_dynamic_light_primary_container = 0x7f0501b6
mc.meson.kasumi:attr/collapseIcon = 0x7f0300ee
mc.meson.kasumi:id/outline = 0x7f080199
mc.meson.kasumi:color/mtrl_switch_track_tint = 0x7f05032e
mc.meson.kasumi:attr/layout_keyline = 0x7f0302c8
mc.meson.kasumi:style/Widget.Material3.NavigationView = 0x7f1203cc
mc.meson.kasumi:color/md_theme_light_errorContainer = 0x7f0502db
mc.meson.kasumi:macro/m3_comp_fab_surface_icon_color = 0x7f0c003e
mc.meson.kasumi:attr/colorOnTertiaryFixed = 0x7f030115
mc.meson.kasumi:style/Theme.AppCompat.Light.Dialog = 0x7f120228
mc.meson.kasumi:attr/contentPaddingBottom = 0x7f030147
mc.meson.kasumi:attr/collapsingToolbarLayoutLargeStyle = 0x7f0300f4
mc.meson.kasumi:attr/pathMotionArc = 0x7f0303a4
mc.meson.kasumi:attr/colorOnSurfaceVariant = 0x7f030112
mc.meson.kasumi:style/Widget.AppCompat.Light.ActionButton.Overflow = 0x7f12031a
mc.meson.kasumi:attr/menuGravity = 0x7f03033f
mc.meson.kasumi:color/material_personalized_color_secondary_container = 0x7f050298
mc.meson.kasumi:attr/clockHandColor = 0x7f0300e2
mc.meson.kasumi:animator/fragment_close_exit = 0x7f020004
mc.meson.kasumi:dimen/m3_comp_slider_stop_indicator_size = 0x7f060188
mc.meson.kasumi:style/Theme.AppCompat.DayNight.Dialog = 0x7f12021c
mc.meson.kasumi:attr/carousel_infinite = 0x7f0300ac
mc.meson.kasumi:attr/colorOnPrimarySurface = 0x7f03010b
mc.meson.kasumi:attr/actionButtonStyle = 0x7f03000e
mc.meson.kasumi:attr/actionBarPopupTheme = 0x7f030005
mc.meson.kasumi:attr/hideOnScroll = 0x7f030235
mc.meson.kasumi:style/TextAppearance.AppCompat.Widget.ActionBar.Title = 0x7f1201c0
mc.meson.kasumi:dimen/mtrl_alert_dialog_picker_background_inset = 0x7f06024a
mc.meson.kasumi:dimen/m3_btn_text_btn_icon_padding_right = 0x7f0600df
mc.meson.kasumi:style/Base.Theme.MaterialComponents.Light.Dialog.MinWidth = 0x7f120083
mc.meson.kasumi:color/m3_ref_palette_neutral_variant20 = 0x7f050121
mc.meson.kasumi:attr/backHandlingEnabled = 0x7f03004b
mc.meson.kasumi:style/Base.Widget.Material3.ActionMode = 0x7f12010b
mc.meson.kasumi:drawable/abc_list_pressed_holo_dark = 0x7f070051
mc.meson.kasumi:style/Widget.Material3.Search.ActionButton.Overflow = 0x7f1203d1
mc.meson.kasumi:attr/switchStyle = 0x7f03044e
mc.meson.kasumi:attr/colorOnTertiaryContainer = 0x7f030114
mc.meson.kasumi:layout/design_navigation_item_subheader = 0x7f0b002b
mc.meson.kasumi:attr/contentDescription = 0x7f03013f
mc.meson.kasumi:style/Widget.Material3.Button.IconButton.Outlined = 0x7f120364
mc.meson.kasumi:id/search_badge = 0x7f0801cc
mc.meson.kasumi:attr/showTitle = 0x7f030405
mc.meson.kasumi:anim/fragment_fade_enter = 0x7f01001f
mc.meson.kasumi:color/material_dynamic_tertiary50 = 0x7f050260
mc.meson.kasumi:style/Base.Theme.Material3.Dark.Dialog.FixedSize = 0x7f120069
mc.meson.kasumi:attr/colorOnSecondaryContainer = 0x7f03010d
mc.meson.kasumi:anim/m3_motion_fade_exit = 0x7f010029
mc.meson.kasumi:attr/colorSurfaceContainer = 0x7f030128
mc.meson.kasumi:attr/colorOnBackground = 0x7f030102
mc.meson.kasumi:attr/actionModePasteDrawable = 0x7f03001a
mc.meson.kasumi:dimen/m3_comp_date_picker_modal_header_container_height = 0x7f060105
mc.meson.kasumi:drawable/floating_button_style = 0x7f07008f
mc.meson.kasumi:attr/colorButtonNormal = 0x7f0300fb
mc.meson.kasumi:dimen/m3_comp_navigation_bar_hover_state_layer_opacity = 0x7f06013b
mc.meson.kasumi:id/ivStatusIcon = 0x7f080271
mc.meson.kasumi:attr/actionModeShareDrawable = 0x7f03001d
mc.meson.kasumi:id/recyclerViewConfigs = 0x7f0801b1
mc.meson.kasumi:attr/floatingActionButtonStyle = 0x7f0301f3
mc.meson.kasumi:id/design_navigation_view = 0x7f0800bb
mc.meson.kasumi:dimen/mtrl_slider_thumb_radius = 0x7f0602ea
mc.meson.kasumi:style/Widget.MaterialComponents.Tooltip = 0x7f120473
mc.meson.kasumi:attr/autoTransition = 0x7f03004a
mc.meson.kasumi:dimen/mtrl_snackbar_margin = 0x7f0602f3
mc.meson.kasumi:dimen/fastscroll_minimum_range = 0x7f060092
mc.meson.kasumi:style/RtlOverlay.Widget.AppCompat.SearchView.MagIcon = 0x7f12015f
mc.meson.kasumi:drawable/abc_ic_search_api_material = 0x7f070049
mc.meson.kasumi:dimen/abc_edit_text_inset_top_material = 0x7f06002e
mc.meson.kasumi:color/m3_navigation_item_text_color = 0x7f050099
mc.meson.kasumi:dimen/m3_comp_input_chip_container_height = 0x7f060131
mc.meson.kasumi:attr/colorAccent = 0x7f0300f9
mc.meson.kasumi:dimen/m3_comp_filled_text_field_disabled_active_indicator_opacity = 0x7f06012a
mc.meson.kasumi:animator/m3_card_elevated_state_list_anim = 0x7f02000c
mc.meson.kasumi:color/material_dynamic_secondary0 = 0x7f05024d
mc.meson.kasumi:color/m3_ref_palette_secondary95 = 0x7f050143
mc.meson.kasumi:attr/actionModeCloseContentDescription = 0x7f030015
mc.meson.kasumi:drawable/m3_popupmenu_background_overlay = 0x7f0700ba
mc.meson.kasumi:attr/chipStrokeColor = 0x7f0300d3
mc.meson.kasumi:layout/abc_action_mode_bar = 0x7f0b0004
mc.meson.kasumi:dimen/material_time_picker_minimum_screen_height = 0x7f060244
mc.meson.kasumi:style/Widget.Material3.LinearProgressIndicator = 0x7f1203a4
mc.meson.kasumi:animator/mtrl_extended_fab_change_size_expand_motion_spec = 0x7f02001a
mc.meson.kasumi:styleable/NavHost = 0x7f130074
mc.meson.kasumi:attr/cursorColor = 0x7f030165
mc.meson.kasumi:attr/autoSizeMinTextSize = 0x7f030046
mc.meson.kasumi:color/material_timepicker_clock_text_color = 0x7f0502b8
mc.meson.kasumi:dimen/m3_sys_motion_easing_standard_control_x2 = 0x7f060213
mc.meson.kasumi:dimen/m3_searchview_elevation = 0x7f0601e3
mc.meson.kasumi:style/ShapeAppearanceOverlay.MaterialComponents.ExtendedFloatingActionButton = 0x7f12019a
mc.meson.kasumi:color/abc_search_url_text_normal = 0x7f05000e
mc.meson.kasumi:color/m3_ref_palette_dynamic_primary100 = 0x7f0500d4
mc.meson.kasumi:attr/endIconMinSize = 0x7f0301af
mc.meson.kasumi:style/Theme.Material3.DynamicColors.Dark = 0x7f120246
mc.meson.kasumi:attr/chipSpacing = 0x7f0300ce
mc.meson.kasumi:style/Base.V24.Theme.Material3.Light = 0x7f1200c0
mc.meson.kasumi:attr/colorSurfaceVariant = 0x7f03012f
mc.meson.kasumi:attr/checkedChip = 0x7f0300b9
mc.meson.kasumi:animator/fragment_close_enter = 0x7f020003
mc.meson.kasumi:dimen/mtrl_slider_label_radius = 0x7f0602e7
mc.meson.kasumi:attr/iconPadding = 0x7f030242
mc.meson.kasumi:style/Theme.Material3.DynamicColors.DayNight.NoActionBar = 0x7f120249
mc.meson.kasumi:layout/abc_screen_simple_overlay_action_mode = 0x7f0b0016
mc.meson.kasumi:drawable/$mtrl_checkbox_button_icon_indeterminate_unchecked__2 = 0x7f070017
mc.meson.kasumi:style/TextAppearance.MaterialComponents.Headline3 = 0x7f12020c
mc.meson.kasumi:attr/textInputOutlinedStyle = 0x7f0304a7
mc.meson.kasumi:attr/radioButtonStyle = 0x7f0303c9
mc.meson.kasumi:animator/mtrl_fab_show_motion_spec = 0x7f02001f
mc.meson.kasumi:style/ThemeOverlay.Material3.MaterialCalendar = 0x7f1202ba
mc.meson.kasumi:attr/allowStacking = 0x7f030030
mc.meson.kasumi:attr/borderRoundPercent = 0x7f03007a
mc.meson.kasumi:id/textinput_placeholder = 0x7f080228
mc.meson.kasumi:id/center = 0x7f08008c
mc.meson.kasumi:attr/colorSurfaceBright = 0x7f030127
mc.meson.kasumi:dimen/m3_comp_navigation_drawer_focus_state_layer_opacity = 0x7f06013f
mc.meson.kasumi:anim/nav_default_pop_exit_anim = 0x7f010034
mc.meson.kasumi:attr/closeIconVisible = 0x7f0300eb
mc.meson.kasumi:color/m3_ref_palette_dynamic_primary95 = 0x7f0500dd
mc.meson.kasumi:style/Base.TextAppearance.AppCompat.Widget.ActionBar.Title = 0x7f120042
mc.meson.kasumi:attr/errorTextAppearance = 0x7f0301c0
mc.meson.kasumi:string/m3_ref_typeface_brand_regular = 0x7f110057
mc.meson.kasumi:attr/closeIcon = 0x7f0300e5
mc.meson.kasumi:attr/subheaderTextAppearance = 0x7f030441
mc.meson.kasumi:attr/grid_spans = 0x7f030224
mc.meson.kasumi:style/Base.AlertDialog.AppCompat = 0x7f120018
mc.meson.kasumi:id/open_search_view_edit_text = 0x7f080191
mc.meson.kasumi:attr/clockIcon = 0x7f0300e3
mc.meson.kasumi:dimen/m3_comp_navigation_bar_icon_size = 0x7f06013c
mc.meson.kasumi:attr/listPreferredItemHeight = 0x7f0302de
mc.meson.kasumi:styleable/MaterialTimePicker = 0x7f130063
mc.meson.kasumi:color/m3_ref_palette_dynamic_neutral80 = 0x7f0500b1
mc.meson.kasumi:style/Base.V14.ThemeOverlay.MaterialComponents.Dialog = 0x7f1200aa
mc.meson.kasumi:id/save_non_transition_alpha = 0x7f0801c2
mc.meson.kasumi:dimen/mtrl_navigation_item_shape_vertical_margin = 0x7f0602ca
mc.meson.kasumi:macro/m3_comp_outlined_text_field_hover_supporting_text_color = 0x7f0c00bf
mc.meson.kasumi:color/m3_ref_palette_dynamic_neutral10 = 0x7f0500a3
mc.meson.kasumi:id/action_home_to_features = 0x7f080043
mc.meson.kasumi:id/accessibility_custom_action_19 = 0x7f08001c
mc.meson.kasumi:attr/paddingBottomNoButtons = 0x7f030393
mc.meson.kasumi:dimen/mtrl_extended_fab_icon_size = 0x7f0602ac
mc.meson.kasumi:attr/behavior_autoShrink = 0x7f03006d
mc.meson.kasumi:macro/m3_comp_outlined_text_field_error_trailing_icon_color = 0x7f0c00b8
mc.meson.kasumi:dimen/m3_comp_filter_chip_with_icon_icon_size = 0x7f06012f
mc.meson.kasumi:color/purple_700 = 0x7f050346
mc.meson.kasumi:drawable/mtrl_bottomsheet_drag_handle = 0x7f0700cc
mc.meson.kasumi:attr/circularflow_angles = 0x7f0300d9
mc.meson.kasumi:attr/isMaterialTheme = 0x7f03025c
mc.meson.kasumi:style/ThemeOverlay.Material3.Button.TonalButton = 0x7f12029f
mc.meson.kasumi:attr/trackThickness = 0x7f0304f7
mc.meson.kasumi:attr/motionDurationLong4 = 0x7f030355
mc.meson.kasumi:attr/colorOnSecondary = 0x7f03010c
mc.meson.kasumi:color/mtrl_on_primary_text_btn_text_color_selector = 0x7f050325
mc.meson.kasumi:id/scrollIndicatorUp = 0x7f0801c9
mc.meson.kasumi:attr/grid_validateInputs = 0x7f030226
mc.meson.kasumi:attr/circleRadius = 0x7f0300d7
mc.meson.kasumi:attr/moveWhenScrollAtTop = 0x7f03037a
mc.meson.kasumi:attr/collapsedTitleTextColor = 0x7f0300f2
mc.meson.kasumi:macro/m3_comp_outlined_text_field_error_supporting_text_color = 0x7f0c00b7
mc.meson.kasumi:id/navigation_header_container = 0x7f08017c
mc.meson.kasumi:dimen/mtrl_extended_fab_disabled_elevation = 0x7f0602a7
mc.meson.kasumi:attr/boxBackgroundMode = 0x7f030084
mc.meson.kasumi:string/nav_home = 0x7f1100bd
mc.meson.kasumi:anim/linear_indeterminate_line1_head_interpolator = 0x7f010022
mc.meson.kasumi:styleable/ConstraintLayout_placeholder = 0x7f13002c
mc.meson.kasumi:id/nav_home = 0x7f080171
mc.meson.kasumi:attr/ifTagNotSet = 0x7f030248
mc.meson.kasumi:string/material_slider_range_end = 0x7f110071
mc.meson.kasumi:color/material_dynamic_primary95 = 0x7f05024b
mc.meson.kasumi:attr/chipStartPadding = 0x7f0300d2
mc.meson.kasumi:attr/chipMinHeight = 0x7f0300cc
mc.meson.kasumi:style/ShapeAppearance.M3.Sys.Shape.Corner.Full = 0x7f120176
mc.meson.kasumi:attr/saturation = 0x7f0303df
mc.meson.kasumi:attr/placeholderTextColor = 0x7f0303af
mc.meson.kasumi:drawable/avd_show_password = 0x7f070079
mc.meson.kasumi:macro/m3_comp_top_app_bar_medium_headline_color = 0x7f0c016d
mc.meson.kasumi:attr/flow_lastHorizontalBias = 0x7f0301fe
mc.meson.kasumi:attr/chipEndPadding = 0x7f0300c5
mc.meson.kasumi:attr/chipCornerRadius = 0x7f0300c4
mc.meson.kasumi:dimen/m3_comp_filled_button_with_icon_icon_size = 0x7f060123
mc.meson.kasumi:attr/checkedIconVisible = 0x7f0300c0
mc.meson.kasumi:dimen/m3_sys_motion_easing_emphasized_accelerate_control_x1 = 0x7f0601f6
mc.meson.kasumi:dimen/m3_comp_outlined_text_field_focus_outline_width = 0x7f060158
mc.meson.kasumi:attr/autoSizePresetSizes = 0x7f030047
mc.meson.kasumi:id/switchAutoConnect = 0x7f080206
mc.meson.kasumi:attr/nestedScrollFlags = 0x7f030383
mc.meson.kasumi:color/material_dynamic_primary50 = 0x7f050246
mc.meson.kasumi:style/Widget.AppCompat.ButtonBar.AlertDialog = 0x7f120307
mc.meson.kasumi:string/abc_searchview_description_voice = 0x7f110017
mc.meson.kasumi:attr/hideOnContentScroll = 0x7f030234
mc.meson.kasumi:attr/endIconScaleType = 0x7f0301b1
mc.meson.kasumi:color/m3_button_background_color_selector = 0x7f050068
mc.meson.kasumi:color/material_blue_grey_800 = 0x7f050217
mc.meson.kasumi:attr/checkedIconSize = 0x7f0300be
mc.meson.kasumi:styleable/CollapsingToolbarLayout_Layout = 0x7f130026
mc.meson.kasumi:attr/itemVerticalPadding = 0x7f03027a
mc.meson.kasumi:attr/checkedIconGravity = 0x7f0300bc
mc.meson.kasumi:dimen/mtrl_fab_translation_z_pressed = 0x7f0602b9
mc.meson.kasumi:dimen/m3_fab_translation_z_pressed = 0x7f0601b8
mc.meson.kasumi:layout/material_clock_display_divider = 0x7f0b004a
mc.meson.kasumi:attr/endIconContentDescription = 0x7f0301ad
mc.meson.kasumi:color/material_cursor_color = 0x7f05021a
mc.meson.kasumi:style/Theme.Design = 0x7f12022e
mc.meson.kasumi:anim/btn_checkbox_to_unchecked_icon_null_animation = 0x7f010012
mc.meson.kasumi:style/TextAppearance.AppCompat.Light.Widget.PopupMenu.Small = 0x7f1201b0
mc.meson.kasumi:attr/launchSingleTop = 0x7f030285
mc.meson.kasumi:anim/bounce = 0x7f01000c
mc.meson.kasumi:attr/windowActionBar = 0x7f03051a
mc.meson.kasumi:attr/actionDropDownStyle = 0x7f03000f
mc.meson.kasumi:attr/checkboxStyle = 0x7f0300b7
mc.meson.kasumi:color/md_theme_dark_onPrimaryContainer = 0x7f0502c5
mc.meson.kasumi:dimen/mtrl_calendar_content_padding = 0x7f060275
mc.meson.kasumi:attr/behavior_significantVelocityThreshold = 0x7f030076
mc.meson.kasumi:color/m3_ref_palette_primary60 = 0x7f050132
mc.meson.kasumi:attr/chainUseRtl = 0x7f0300b3
mc.meson.kasumi:color/m3_sys_color_dynamic_light_on_surface = 0x7f0501af
mc.meson.kasumi:attr/colorControlHighlight = 0x7f0300fe
mc.meson.kasumi:style/Widget.Material3.MaterialCalendar.DayTextView = 0x7f1203ad
mc.meson.kasumi:dimen/abc_text_size_menu_material = 0x7f06004b
mc.meson.kasumi:attr/layout_goneMarginBottom = 0x7f0302c1
mc.meson.kasumi:color/m3_sys_color_dark_on_secondary_container = 0x7f05016a
mc.meson.kasumi:id/fill = 0x7f0800e5
mc.meson.kasumi:attr/splitLayoutDirection = 0x7f030419
mc.meson.kasumi:attr/itemTextAppearanceActive = 0x7f030276
mc.meson.kasumi:style/Animation.Material3.SideSheetDialog.Left = 0x7f120008
mc.meson.kasumi:dimen/mtrl_high_ripple_focused_alpha = 0x7f0602bb
mc.meson.kasumi:attr/errorEnabled = 0x7f0301bb
mc.meson.kasumi:attr/layout_constraintStart_toStartOf = 0x7f0302b0
mc.meson.kasumi:animator/mtrl_btn_unelevated_state_list_anim = 0x7f020016
mc.meson.kasumi:attr/lottie_cacheComposition = 0x7f0302ec
mc.meson.kasumi:attr/chipIconTint = 0x7f0300ca
mc.meson.kasumi:style/Theme.MaterialComponents.Light.BottomSheetDialog = 0x7f120272
mc.meson.kasumi:anim/m3_side_sheet_exit_to_left = 0x7f01002c
mc.meson.kasumi:attr/colorPrimarySurface = 0x7f03011f
mc.meson.kasumi:dimen/m3_back_progress_bottom_container_max_scale_y_distance = 0x7f0600ad
mc.meson.kasumi:style/Widget.Material3.TextInputEditText.FilledBox.Dense = 0x7f1203e7
mc.meson.kasumi:string/mtrl_picker_date_header_title = 0x7f110093
mc.meson.kasumi:attr/layout_constraintGuide_end = 0x7f03029f
mc.meson.kasumi:color/design_dark_default_color_primary_dark = 0x7f05003e
mc.meson.kasumi:dimen/abc_action_bar_overflow_padding_end_material = 0x7f060007
mc.meson.kasumi:attr/cardUseCompatPadding = 0x7f0300a5
mc.meson.kasumi:attr/cardMaxElevation = 0x7f0300a3
mc.meson.kasumi:attr/labelVisibilityMode = 0x7f030281
mc.meson.kasumi:id/viewCategoryIndicator = 0x7f08025a
mc.meson.kasumi:attr/flow_lastVerticalBias = 0x7f030200
mc.meson.kasumi:dimen/tooltip_precise_anchor_threshold = 0x7f060320
mc.meson.kasumi:attr/layout_constraintRight_toLeftOf = 0x7f0302ad
mc.meson.kasumi:attr/cornerSizeBottomLeft = 0x7f030159
mc.meson.kasumi:style/Widget.Material3.Button.TextButton.Dialog.Icon = 0x7f12036a
mc.meson.kasumi:id/nav_config_management = 0x7f08016d
mc.meson.kasumi:attr/animateCircleAngleTo = 0x7f030035
mc.meson.kasumi:attr/snackbarTextViewStyle = 0x7f030414
mc.meson.kasumi:attr/errorShown = 0x7f0301bf
mc.meson.kasumi:attr/collapsingToolbarLayoutLargeSize = 0x7f0300f3
mc.meson.kasumi:dimen/design_tab_text_size = 0x7f06008b
mc.meson.kasumi:id/direct = 0x7f0800be
mc.meson.kasumi:animator/m3_extended_fab_state_list_animator = 0x7f020014
mc.meson.kasumi:id/accessibility_custom_action_30 = 0x7f080029
mc.meson.kasumi:attr/checkMarkCompat = 0x7f0300b4
mc.meson.kasumi:attr/itemShapeAppearance = 0x7f03026b
mc.meson.kasumi:id/material_clock_hand = 0x7f080138
mc.meson.kasumi:attr/constraint_referenced_tags = 0x7f03013c
mc.meson.kasumi:style/Base.Widget.MaterialComponents.TextInputLayout = 0x7f12012a
mc.meson.kasumi:attr/brightness = 0x7f03008e
mc.meson.kasumi:style/ThemeOverlay.MaterialComponents.Light = 0x7f1202df
mc.meson.kasumi:attr/colorSurfaceContainerHigh = 0x7f030129
mc.meson.kasumi:style/ThemeOverlay.Material3.BottomSheetDialog = 0x7f120297
mc.meson.kasumi:style/TextAppearance.M3.Sys.Typescale.TitleLarge = 0x7f1201ec
mc.meson.kasumi:style/Base.Widget.AppCompat.ActionBar.Solid = 0x7f1200d0
mc.meson.kasumi:id/cos = 0x7f0800a9
mc.meson.kasumi:attr/buttonIcon = 0x7f030096
mc.meson.kasumi:attr/buttonGravity = 0x7f030095
mc.meson.kasumi:color/material_on_background_emphasis_high_type = 0x7f050273
mc.meson.kasumi:style/Widget.Material3.MaterialCalendar.Item = 0x7f1203b7
mc.meson.kasumi:attr/layout_constraintHeight = 0x7f0302a1
mc.meson.kasumi:anim/btn_checkbox_to_checked_box_outer_merged_animation = 0x7f01000e
mc.meson.kasumi:id/material_hour_text_input = 0x7f08013d
mc.meson.kasumi:drawable/$mtrl_checkbox_button_icon_unchecked_indeterminate__1 = 0x7f07001c
mc.meson.kasumi:attr/reverseLayout = 0x7f0303d9
mc.meson.kasumi:style/TextAppearance.AppCompat.Medium = 0x7f1201b1
mc.meson.kasumi:attr/paddingRightSystemWindowInsets = 0x7f030397
mc.meson.kasumi:id/packed = 0x7f08019c
mc.meson.kasumi:attr/popExitAnim = 0x7f0303b3
mc.meson.kasumi:attr/barrierDirection = 0x7f03006a
mc.meson.kasumi:attr/boxStrokeWidthFocused = 0x7f03008d
mc.meson.kasumi:attr/cardViewStyle = 0x7f0300a6
mc.meson.kasumi:style/Theme.MaterialComponents.Dialog.FixedSize = 0x7f12026c
mc.meson.kasumi:attr/chipBackgroundColor = 0x7f0300c3
mc.meson.kasumi:dimen/m3_comp_navigation_rail_active_indicator_height = 0x7f060145
mc.meson.kasumi:dimen/disabled_alpha_material_dark = 0x7f06008e
mc.meson.kasumi:attr/boxStrokeErrorColor = 0x7f03008b
mc.meson.kasumi:id/parallax = 0x7f08019d
mc.meson.kasumi:attr/chipIconVisible = 0x7f0300cb
mc.meson.kasumi:dimen/m3_btn_padding_left = 0x7f0600da
mc.meson.kasumi:attr/boxCornerRadiusTopEnd = 0x7f030088
mc.meson.kasumi:style/Theme.AppCompat.DialogWhenLarge = 0x7f120224
mc.meson.kasumi:attr/bottomSheetStyle = 0x7f030082
mc.meson.kasumi:string/abc_searchview_description_clear = 0x7f110013
mc.meson.kasumi:attr/titleCentered = 0x7f0304d1
mc.meson.kasumi:attr/colorOutlineVariant = 0x7f030118
mc.meson.kasumi:attr/fontStyle = 0x7f030212
mc.meson.kasumi:dimen/mtrl_calendar_title_baseline_to_top_fullscreen = 0x7f060295
mc.meson.kasumi:color/material_divider_color = 0x7f05021d
mc.meson.kasumi:color/material_dynamic_neutral_variant99 = 0x7f05023f
mc.meson.kasumi:interpolator/m3_sys_motion_easing_emphasized_decelerate = 0x7f0a0009
mc.meson.kasumi:color/md_theme_light_background = 0x7f0502d9
mc.meson.kasumi:attr/constraintSet = 0x7f030138
mc.meson.kasumi:dimen/mtrl_btn_padding_left = 0x7f060266
mc.meson.kasumi:color/m3_sys_color_primary_fixed_dim = 0x7f0501f9
mc.meson.kasumi:attr/colorSecondaryVariant = 0x7f030125
mc.meson.kasumi:interpolator/m3_sys_motion_easing_emphasized_accelerate = 0x7f0a0008
mc.meson.kasumi:attr/setsTag = 0x7f0303ef
mc.meson.kasumi:attr/editTextColor = 0x7f0301a4
mc.meson.kasumi:color/m3_sys_color_dynamic_light_surface_variant = 0x7f0501c1
mc.meson.kasumi:attr/badgeTextColor = 0x7f03005f
mc.meson.kasumi:color/mtrl_indicator_text_color = 0x7f05031d
mc.meson.kasumi:attr/chipIcon = 0x7f0300c7
mc.meson.kasumi:drawable/abc_btn_check_to_on_mtrl_000 = 0x7f07002e
mc.meson.kasumi:id/dragRight = 0x7f0800ca
mc.meson.kasumi:attr/tooltipFrameBackground = 0x7f0304e5
mc.meson.kasumi:attr/collapsingToolbarLayoutMediumStyle = 0x7f0300f6
mc.meson.kasumi:style/Base.Widget.AppCompat.Light.ActionBar.Solid = 0x7f1200eb
mc.meson.kasumi:attr/coordinatorLayoutStyle = 0x7f030150
mc.meson.kasumi:macro/m3_comp_filled_text_field_error_active_indicator_color = 0x7f0c004d
mc.meson.kasumi:layout/select_dialog_multichoice_material = 0x7f0b007e
mc.meson.kasumi:attr/showAsAction = 0x7f0303fe
mc.meson.kasumi:drawable/notification_oversize_large_icon_bg = 0x7f0700fa
mc.meson.kasumi:color/m3_ref_palette_neutral87 = 0x7f050116
mc.meson.kasumi:attr/circularflow_defaultAngle = 0x7f0300da
mc.meson.kasumi:attr/behavior_fitToContents = 0x7f030070
mc.meson.kasumi:id/notification_main_column = 0x7f080186
mc.meson.kasumi:color/teal_700 = 0x7f050354
mc.meson.kasumi:attr/carousel_touchUp_velocityThreshold = 0x7f0300b1
mc.meson.kasumi:attr/animateMenuItems = 0x7f030036
mc.meson.kasumi:attr/behavior_autoHide = 0x7f03006c
mc.meson.kasumi:dimen/abc_search_view_preferred_height = 0x7f060036
mc.meson.kasumi:style/Widget.Material3.AppBarLayout = 0x7f12034d
mc.meson.kasumi:style/Base.ThemeOverlay.Material3.AutoCompleteTextView = 0x7f12008c
mc.meson.kasumi:attr/flow_firstHorizontalBias = 0x7f0301f6
mc.meson.kasumi:color/m3_dynamic_dark_default_color_primary_text = 0x7f050080
mc.meson.kasumi:color/m3_ref_palette_dynamic_neutral_variant4 = 0x7f0500c3
mc.meson.kasumi:attr/triggerId = 0x7f030500
mc.meson.kasumi:id/topPanel = 0x7f080234
mc.meson.kasumi:attr/contentPaddingTop = 0x7f03014c
mc.meson.kasumi:style/Widget.Material3.AutoCompleteTextView.FilledBox = 0x7f12034e
mc.meson.kasumi:style/TextAppearance.MaterialComponents.Chip = 0x7f120209
mc.meson.kasumi:id/hardware = 0x7f0800fd
mc.meson.kasumi:attr/chipIconEnabled = 0x7f0300c8
mc.meson.kasumi:color/mtrl_navigation_item_text_color = 0x7f050324
mc.meson.kasumi:drawable/category_indicator = 0x7f070084
mc.meson.kasumi:attr/badgeWithTextRadius = 0x7f030064
mc.meson.kasumi:attr/listPopupWindowStyle = 0x7f0302dd
mc.meson.kasumi:color/m3_ref_palette_neutral_variant90 = 0x7f050128
mc.meson.kasumi:attr/layoutManager = 0x7f030289
mc.meson.kasumi:color/m3_ref_palette_primary0 = 0x7f05012b
mc.meson.kasumi:color/material_personalized_color_text_primary_inverse_disable_only = 0x7f0502a9
mc.meson.kasumi:attr/floatingActionButtonLargeTertiaryStyle = 0x7f0301eb
mc.meson.kasumi:style/Base.TextAppearance.AppCompat.SearchResult.Title = 0x7f120037
mc.meson.kasumi:attr/colorSecondary = 0x7f030121
mc.meson.kasumi:attr/defaultMarginsEnabled = 0x7f030179
mc.meson.kasumi:styleable/CustomAttribute = 0x7f130031
mc.meson.kasumi:style/RtlOverlay.Widget.AppCompat.Search.DropDown.Query = 0x7f12015d
mc.meson.kasumi:color/m3_sys_color_light_on_background = 0x7f0501d6
mc.meson.kasumi:attr/badgeShapeAppearance = 0x7f03005a
mc.meson.kasumi:attr/onCross = 0x7f03038a
mc.meson.kasumi:dimen/m3_comp_extended_fab_primary_focus_state_layer_opacity = 0x7f06010f
mc.meson.kasumi:attr/contentInsetEndWithActions = 0x7f030141
mc.meson.kasumi:macro/m3_comp_date_picker_modal_header_headline_color = 0x7f0c0015
mc.meson.kasumi:layout/abc_tooltip = 0x7f0b001b
mc.meson.kasumi:attr/badgeWithTextShapeAppearanceOverlay = 0x7f030066
mc.meson.kasumi:attr/mock_showDiagonals = 0x7f03034b
mc.meson.kasumi:attr/actionOverflowButtonStyle = 0x7f030022
mc.meson.kasumi:id/end = 0x7f0800d8
mc.meson.kasumi:attr/prefixText = 0x7f0303bb
mc.meson.kasumi:style/Base.V14.Theme.MaterialComponents = 0x7f12009e
mc.meson.kasumi:attr/backgroundInsetStart = 0x7f030050
mc.meson.kasumi:attr/backgroundInsetBottom = 0x7f03004e
mc.meson.kasumi:style/ThemeOverlay.AppCompat.Dialog = 0x7f120289
mc.meson.kasumi:string/abc_shareactionprovider_share_with_application = 0x7f110019
mc.meson.kasumi:drawable/$mtrl_checkbox_button_icon_unchecked_indeterminate__0 = 0x7f07001b
mc.meson.kasumi:style/Widget.Material3.Button.IconButton.Filled = 0x7f120362
mc.meson.kasumi:attr/chipSpacingVertical = 0x7f0300d0
mc.meson.kasumi:style/Widget.Material3.MaterialCalendar.HeaderTitle = 0x7f1203b5
mc.meson.kasumi:dimen/m3_btn_icon_btn_padding_right = 0x7f0600d2
mc.meson.kasumi:attr/appBarLayoutStyle = 0x7f03003a
mc.meson.kasumi:macro/m3_comp_navigation_drawer_inactive_icon_color = 0x7f0c008c
mc.meson.kasumi:attr/lottie_useCompositionFrameRate = 0x7f0302fe
mc.meson.kasumi:layout/fragment_home = 0x7f0b0035
mc.meson.kasumi:attr/bottomNavigationStyle = 0x7f03007f
mc.meson.kasumi:drawable/design_fab_background = 0x7f070087
mc.meson.kasumi:attr/autoSizeMaxTextSize = 0x7f030045
mc.meson.kasumi:dimen/material_cursor_inset = 0x7f060230
mc.meson.kasumi:dimen/m3_navigation_menu_divider_horizontal_padding = 0x7f0601c7
mc.meson.kasumi:dimen/design_navigation_icon_padding = 0x7f060076
mc.meson.kasumi:attr/splitTrack = 0x7f03041d
mc.meson.kasumi:attr/itemShapeFillColor = 0x7f03026d
mc.meson.kasumi:macro/m3_comp_outlined_button_hover_outline_color = 0x7f0c00a5
mc.meson.kasumi:attr/actionBarItemBackground = 0x7f030004
mc.meson.kasumi:dimen/material_font_2_0_box_collapsed_padding_top = 0x7f06023c
mc.meson.kasumi:attr/actionBarSize = 0x7f030006
mc.meson.kasumi:id/open_search_view_toolbar = 0x7f080197
mc.meson.kasumi:animator/nav_default_enter_anim = 0x7f020022
mc.meson.kasumi:dimen/m3_badge_with_text_size = 0x7f0600b9
mc.meson.kasumi:attr/errorIconTintMode = 0x7f0301be
mc.meson.kasumi:color/m3_sys_color_dark_on_tertiary = 0x7f05016d
mc.meson.kasumi:macro/m3_comp_search_bar_container_color = 0x7f0c00e6
mc.meson.kasumi:attr/textAppearanceTitleSmall = 0x7f030497
mc.meson.kasumi:color/m3_ref_palette_secondary99 = 0x7f050144
mc.meson.kasumi:dimen/abc_text_size_subtitle_material_toolbar = 0x7f06004e
mc.meson.kasumi:attr/animateNavigationIcon = 0x7f030037
mc.meson.kasumi:id/bounce = 0x7f080072
mc.meson.kasumi:attr/alertDialogStyle = 0x7f03002e
mc.meson.kasumi:dimen/mtrl_navigation_rail_text_bottom_margin = 0x7f0602d2
mc.meson.kasumi:drawable/mtrl_ic_arrow_drop_down = 0x7f0700d9
mc.meson.kasumi:attr/layout_constraintHorizontal_chainStyle = 0x7f0302a7
mc.meson.kasumi:id/selection_type = 0x7f0801d9
mc.meson.kasumi:attr/alertDialogButtonGroupStyle = 0x7f03002c
mc.meson.kasumi:id/material_timepicker_view = 0x7f080147
mc.meson.kasumi:animator/mtrl_extended_fab_state_list_animator = 0x7f02001d
mc.meson.kasumi:drawable/notification_tile_bg = 0x7f0700fd
mc.meson.kasumi:attr/colorControlActivated = 0x7f0300fd
mc.meson.kasumi:drawable/abc_scrubber_control_to_pressed_mtrl_005 = 0x7f070060
mc.meson.kasumi:attr/itemStrokeWidth = 0x7f030274
mc.meson.kasumi:style/ThemeOverlay.Material3.ExtendedFloatingActionButton.Secondary = 0x7f1202ad
mc.meson.kasumi:attr/actionViewClass = 0x7f030026
mc.meson.kasumi:color/design_fab_shadow_end_color = 0x7f050051
mc.meson.kasumi:attr/colorError = 0x7f030100
mc.meson.kasumi:styleable/BottomAppBar = 0x7f130018
mc.meson.kasumi:color/material_dynamic_tertiary70 = 0x7f050262
mc.meson.kasumi:style/Widget.AppCompat.Button.Borderless.Colored = 0x7f120302
mc.meson.kasumi:attr/ttcIndex = 0x7f030503
mc.meson.kasumi:attr/motionDurationExtraLong3 = 0x7f030350
mc.meson.kasumi:macro/m3_comp_navigation_bar_active_hover_label_text_color = 0x7f0c0063
mc.meson.kasumi:attr/badgeWithTextWidth = 0x7f030067
mc.meson.kasumi:dimen/m3_fab_translation_z_hovered_focused = 0x7f0601b7
mc.meson.kasumi:attr/startDestination = 0x7f030426
mc.meson.kasumi:style/Widget.AppCompat.Spinner.DropDown = 0x7f120337
mc.meson.kasumi:color/material_personalized_color_surface_container_high = 0x7f05029e
mc.meson.kasumi:style/Widget.Material3.AutoCompleteTextView.FilledBox.Dense = 0x7f12034f
mc.meson.kasumi:string/abc_searchview_description_submit = 0x7f110016
mc.meson.kasumi:id/none = 0x7f080182
mc.meson.kasumi:attr/onStateTransition = 0x7f03038f
mc.meson.kasumi:dimen/m3_comp_fab_primary_pressed_container_elevation = 0x7f06011d
mc.meson.kasumi:style/ThemeOverlay.Material3.MaterialTimePicker = 0x7f1202bd
mc.meson.kasumi:color/m3_ref_palette_dynamic_secondary70 = 0x7f0500e7
mc.meson.kasumi:macro/m3_comp_outlined_button_focus_outline_color = 0x7f0c00a4
mc.meson.kasumi:id/action_mode_bar = 0x7f080048
mc.meson.kasumi:color/cardview_light_background = 0x7f050031
mc.meson.kasumi:style/ShapeAppearance.M3.Comp.NavigationBar.ActiveIndicator.Shape = 0x7f120167
mc.meson.kasumi:attr/boxCollapsedPaddingTop = 0x7f030085
mc.meson.kasumi:attr/textAppearanceLabelSmall = 0x7f030488
mc.meson.kasumi:attr/actionModeStyle = 0x7f03001f
mc.meson.kasumi:color/minecraft_orange_dark = 0x7f0502fc
mc.meson.kasumi:attr/layout_constraintHorizontal_weight = 0x7f0302a8
mc.meson.kasumi:id/btnEditConfig = 0x7f08007c
mc.meson.kasumi:color/design_default_color_error = 0x7f050044
mc.meson.kasumi:attr/itemPaddingTop = 0x7f030269
mc.meson.kasumi:dimen/m3_chip_dragged_translation_z = 0x7f0600f6
mc.meson.kasumi:style/ThemeOverlay.MaterialComponents.TextInputEditText.OutlinedBox.Dense = 0x7f1202ee
mc.meson.kasumi:animator/m3_appbar_state_list_animator = 0x7f020009
mc.meson.kasumi:style/ShapeAppearanceOverlay.MaterialComponents.MaterialCalendar.Day = 0x7f12019c
mc.meson.kasumi:dimen/m3_navigation_item_shape_inset_bottom = 0x7f0601c2
mc.meson.kasumi:style/Widget.AppCompat.Light.ActionBar.TabBar.Inverse = 0x7f120313
mc.meson.kasumi:attr/dividerVertical = 0x7f03018d
mc.meson.kasumi:styleable/MotionScene = 0x7f13006e
mc.meson.kasumi:macro/m3_comp_outlined_card_disabled_outline_color = 0x7f0c00aa
mc.meson.kasumi:animator/fragment_fade_enter = 0x7f020005
mc.meson.kasumi:style/Widget.MaterialComponents.BottomAppBar.PrimarySurface = 0x7f120405
mc.meson.kasumi:attr/targetPackage = 0x7f03046e
mc.meson.kasumi:macro/m3_comp_time_picker_time_selector_label_text_type = 0x7f0c015f
mc.meson.kasumi:integer/app_bar_elevation_anim_duration = 0x7f090002
mc.meson.kasumi:id/carryVelocity = 0x7f08008b
mc.meson.kasumi:attr/tabPaddingStart = 0x7f030463
mc.meson.kasumi:attr/colorOnSecondaryFixed = 0x7f03010e
mc.meson.kasumi:attr/tabIndicatorAnimationDuration = 0x7f030456
mc.meson.kasumi:styleable/ActivityFilter = 0x7f130006
mc.meson.kasumi:color/m3_dark_primary_text_disable_only = 0x7f05007d
mc.meson.kasumi:style/Widget.Material3.CircularProgressIndicator.Small = 0x7f120386
mc.meson.kasumi:attr/SharedValue = 0x7f030000
mc.meson.kasumi:style/ThemeOverlay.Material3.TabLayout = 0x7f1202c5
mc.meson.kasumi:color/m3_ref_palette_neutral_variant99 = 0x7f05012a
mc.meson.kasumi:attr/spanCount = 0x7f030415
mc.meson.kasumi:style/ThemeOverlay.Material3.BottomNavigationView = 0x7f120296
mc.meson.kasumi:animator/m3_extended_fab_change_size_collapse_motion_spec = 0x7f020010
mc.meson.kasumi:color/m3_sys_color_dark_surface_container_lowest = 0x7f05017b
mc.meson.kasumi:animator/nav_default_pop_exit_anim = 0x7f020025
mc.meson.kasumi:dimen/mtrl_calendar_selection_text_baseline_to_bottom_fullscreen = 0x7f060291
mc.meson.kasumi:style/Widget.MaterialComponents.TextInputLayout.FilledBox.Dense.ExposedDropdownMenu = 0x7f12045e
mc.meson.kasumi:attr/actionModeCloseDrawable = 0x7f030016
mc.meson.kasumi:dimen/abc_action_button_min_width_overflow_material = 0x7f06000f
mc.meson.kasumi:color/material_dynamic_primary80 = 0x7f050249
mc.meson.kasumi:attr/textAppearanceButton = 0x7f030478
mc.meson.kasumi:dimen/m3_comp_assist_chip_flat_outline_width = 0x7f0600fd
mc.meson.kasumi:animator/mtrl_chip_state_list_anim = 0x7f020018
mc.meson.kasumi:dimen/material_font_1_3_box_collapsed_padding_top = 0x7f06023b
mc.meson.kasumi:attr/actionModeSplitBackground = 0x7f03001e
mc.meson.kasumi:attr/lineSpacing = 0x7f0302d4
mc.meson.kasumi:attr/boxStrokeColor = 0x7f03008a
mc.meson.kasumi:dimen/mtrl_calendar_action_padding = 0x7f060273
mc.meson.kasumi:attr/activeIndicatorLabelPadding = 0x7f030027
mc.meson.kasumi:id/tag_unhandled_key_listeners = 0x7f080219
mc.meson.kasumi:attr/flow_horizontalAlign = 0x7f0301fa
mc.meson.kasumi:macro/m3_comp_switch_disabled_unselected_icon_color = 0x7f0c011d
mc.meson.kasumi:attr/actionBarWidgetTheme = 0x7f03000d
mc.meson.kasumi:layout/fragment_module_settings = 0x7f0b0036
mc.meson.kasumi:dimen/m3_comp_secondary_navigation_tab_hover_state_layer_opacity = 0x7f060178
mc.meson.kasumi:attr/contentScrim = 0x7f03014d
mc.meson.kasumi:attr/materialButtonStyle = 0x7f03030a
mc.meson.kasumi:dimen/m3_comp_secondary_navigation_tab_active_indicator_height = 0x7f060176
mc.meson.kasumi:layout/abc_popup_menu_header_item_layout = 0x7f0b0012
mc.meson.kasumi:dimen/m3_card_elevated_disabled_z = 0x7f0600e6
mc.meson.kasumi:attr/alphabeticModifiers = 0x7f030032
mc.meson.kasumi:attr/actionBarTheme = 0x7f03000c
mc.meson.kasumi:dimen/m3_comp_sheet_bottom_docked_drag_handle_height = 0x7f06017a
mc.meson.kasumi:attr/drawableBottomCompat = 0x7f030192
mc.meson.kasumi:macro/m3_comp_navigation_bar_inactive_hover_label_text_color = 0x7f0c0070
mc.meson.kasumi:attr/tickColorActive = 0x7f0304c5
mc.meson.kasumi:anim/linear_indeterminate_line2_tail_interpolator = 0x7f010025
mc.meson.kasumi:attr/colorOnPrimary = 0x7f030107
mc.meson.kasumi:dimen/mtrl_calendar_selection_baseline_to_top_fullscreen = 0x7f06028f
mc.meson.kasumi:style/ShapeAppearance.MaterialComponents.SmallComponent = 0x7f12018b
mc.meson.kasumi:attr/placeholderTextAppearance = 0x7f0303ae
mc.meson.kasumi:attr/actionLayout = 0x7f030010
mc.meson.kasumi:attr/actionBarTabBarStyle = 0x7f030009
mc.meson.kasumi:dimen/mtrl_calendar_year_width = 0x7f06029a
mc.meson.kasumi:id/open_search_view_divider = 0x7f08018f
mc.meson.kasumi:dimen/m3_comp_elevated_button_container_elevation = 0x7f060108
mc.meson.kasumi:attr/backgroundOverlayColorAlpha = 0x7f030052
mc.meson.kasumi:layout/mtrl_picker_fullscreen = 0x7f0b006d
mc.meson.kasumi:drawable/m3_selection_control_ripple = 0x7f0700bc
mc.meson.kasumi:attr/defaultQueryHint = 0x7f03017b
mc.meson.kasumi:id/clockwise = 0x7f08009b
mc.meson.kasumi:attr/contentInsetRight = 0x7f030143
mc.meson.kasumi:dimen/abc_button_inset_vertical_material = 0x7f060013
mc.meson.kasumi:attr/badgeVerticalPadding = 0x7f030060
mc.meson.kasumi:style/Widget.MaterialComponents.TimePicker.ImageButton.ShapeAppearance = 0x7f12046e
mc.meson.kasumi:attr/materialIconButtonStyle = 0x7f030327
mc.meson.kasumi:style/Base.Widget.MaterialComponents.Chip = 0x7f120120
mc.meson.kasumi:id/left = 0x7f080121
mc.meson.kasumi:attr/buttonTintMode = 0x7f03009e
mc.meson.kasumi:styleable/Slider = 0x7f13008c
mc.meson.kasumi:attr/checkedIconTint = 0x7f0300bf
mc.meson.kasumi:attr/initialActivityCount = 0x7f030257
mc.meson.kasumi:attr/flow_verticalStyle = 0x7f030207
mc.meson.kasumi:style/TextAppearance.Material3.HeadlineLarge = 0x7f1201f7
mc.meson.kasumi:color/m3_button_ripple_color = 0x7f05006b
mc.meson.kasumi:attr/actionModeBackground = 0x7f030013
mc.meson.kasumi:animator/nav_default_exit_anim = 0x7f020023
mc.meson.kasumi:dimen/m3_sys_motion_easing_emphasized_decelerate_control_x1 = 0x7f0601fa
mc.meson.kasumi:color/m3_sys_color_dark_primary = 0x7f050171
mc.meson.kasumi:style/Widget.Material3.ExtendedFloatingActionButton.Surface = 0x7f120395
mc.meson.kasumi:attr/height = 0x7f03022c
mc.meson.kasumi:style/Widget.MaterialComponents.TextInputLayout.OutlinedBox.Dense.ExposedDropdownMenu = 0x7f120462
mc.meson.kasumi:string/material_clock_display_divider = 0x7f110065
mc.meson.kasumi:color/m3_ref_palette_primary80 = 0x7f050134
mc.meson.kasumi:color/abc_tint_switch_track = 0x7f050018
mc.meson.kasumi:attr/expandedTitleTextAppearance = 0x7f0301cc
mc.meson.kasumi:style/ShapeAppearance.M3.Comp.DatePicker.Modal.Date.Container.Shape = 0x7f120165
mc.meson.kasumi:macro/m3_comp_fab_surface_container_color = 0x7f0c003d
mc.meson.kasumi:layout/design_navigation_menu_item = 0x7f0b002d
mc.meson.kasumi:attr/badgeShapeAppearanceOverlay = 0x7f03005b
mc.meson.kasumi:style/ThemeOverlay.MaterialComponents.MaterialAlertDialog.Picker.Date.Calendar = 0x7f1202e4
mc.meson.kasumi:id/cancel_button = 0x7f080087
mc.meson.kasumi:animator/mtrl_fab_hide_motion_spec = 0x7f02001e
mc.meson.kasumi:drawable/abc_ic_menu_selectall_mtrl_alpha = 0x7f070047
mc.meson.kasumi:style/Widget.Material3.FloatingActionButton.Primary = 0x7f12039b
mc.meson.kasumi:attr/grid_columns = 0x7f03021e
mc.meson.kasumi:string/mtrl_checkbox_button_path_checked = 0x7f110081
mc.meson.kasumi:attr/itemIconPadding = 0x7f030262
mc.meson.kasumi:attr/actionModeTheme = 0x7f030020
mc.meson.kasumi:color/design_icon_tint = 0x7f050058
mc.meson.kasumi:style/Widget.Design.CollapsingToolbar = 0x7f120343
mc.meson.kasumi:color/m3_ref_palette_black = 0x7f0500a1
mc.meson.kasumi:dimen/abc_list_item_height_small_material = 0x7f060032
mc.meson.kasumi:dimen/design_bottom_sheet_peek_height_min = 0x7f06006d
mc.meson.kasumi:attr/dragDirection = 0x7f03018e
mc.meson.kasumi:dimen/mtrl_calendar_day_horizontal_padding = 0x7f060278
mc.meson.kasumi:color/material_blue_grey_950 = 0x7f050219
mc.meson.kasumi:id/action_bar_container = 0x7f080036
mc.meson.kasumi:attr/toolbarNavigationButtonStyle = 0x7f0304e1
mc.meson.kasumi:drawable/resize_handle_background = 0x7f0700ff
mc.meson.kasumi:animator/mtrl_extended_fab_show_motion_spec = 0x7f02001c
mc.meson.kasumi:string/abc_menu_shift_shortcut_label = 0x7f11000e
mc.meson.kasumi:attr/gestureInsetBottomIgnored = 0x7f03021a
mc.meson.kasumi:attr/counterTextColor = 0x7f030162
mc.meson.kasumi:color/mtrl_btn_bg_color_selector = 0x7f050303
mc.meson.kasumi:attr/actionMenuTextColor = 0x7f030012
mc.meson.kasumi:attr/borderlessButtonStyle = 0x7f03007c
mc.meson.kasumi:string/m3_sys_motion_easing_standard = 0x7f110062
mc.meson.kasumi:attr/contentPaddingRight = 0x7f03014a
mc.meson.kasumi:anim/slide_out_left = 0x7f010036
mc.meson.kasumi:color/mtrl_navigation_bar_ripple_color = 0x7f050321
mc.meson.kasumi:attr/materialAlertDialogBodyTextStyle = 0x7f030303
mc.meson.kasumi:color/md_theme_light_onBackground = 0x7f0502df
mc.meson.kasumi:drawable/mtrl_switch_thumb_unchecked_checked = 0x7f0700ec
mc.meson.kasumi:attr/action = 0x7f030002
mc.meson.kasumi:attr/extendStrategy = 0x7f0301cf
mc.meson.kasumi:attr/collapsedTitleGravity = 0x7f0300f0
mc.meson.kasumi:drawable/custom_scrollbar_thumb = 0x7f070085
mc.meson.kasumi:attr/bottomSheetDialogTheme = 0x7f030080
mc.meson.kasumi:drawable/abc_action_bar_item_background_material = 0x7f07002a
mc.meson.kasumi:integer/mtrl_calendar_year_selector_span = 0x7f090033
mc.meson.kasumi:dimen/m3_comp_navigation_bar_container_elevation = 0x7f060138
mc.meson.kasumi:color/md_theme_light_tertiaryContainer = 0x7f0502f6
mc.meson.kasumi:attr/customReference = 0x7f030170
mc.meson.kasumi:color/androidx_core_secondary_text_default_material_light = 0x7f05001c
mc.meson.kasumi:attr/barrierAllowsGoneWidgets = 0x7f030069
mc.meson.kasumi:color/material_harmonized_color_on_error = 0x7f050270
mc.meson.kasumi:attr/lastItemDecorated = 0x7f030284
mc.meson.kasumi:color/md_theme_dark_error = 0x7f0502bc
mc.meson.kasumi:attr/liftOnScrollTargetViewId = 0x7f0302d1
mc.meson.kasumi:drawable/$mtrl_checkbox_button_unchecked_checked__2 = 0x7f070020
mc.meson.kasumi:attr/colorOnError = 0x7f030105
mc.meson.kasumi:attr/cardCornerRadius = 0x7f0300a0
mc.meson.kasumi:color/m3_ref_palette_dynamic_tertiary70 = 0x7f0500f4
mc.meson.kasumi:style/Widget.Material3.CollapsingToolbar = 0x7f120387
mc.meson.kasumi:attr/colorPrimaryInverse = 0x7f03011e
mc.meson.kasumi:attr/autoAdjustToWithinGrandparentBounds = 0x7f030041
mc.meson.kasumi:style/Widget.Material3.ExtendedFloatingActionButton.Tertiary = 0x7f120396
mc.meson.kasumi:color/m3_ref_palette_neutral95 = 0x7f05011a
mc.meson.kasumi:attr/badgeWithTextHeight = 0x7f030063
mc.meson.kasumi:color/material_personalized_color_on_primary_container = 0x7f050288
mc.meson.kasumi:drawable/m3_tabs_line_indicator = 0x7f0700be
mc.meson.kasumi:dimen/m3_sys_motion_easing_legacy_decelerate_control_y1 = 0x7f060208
mc.meson.kasumi:color/androidx_core_ripple_material_light = 0x7f05001b
mc.meson.kasumi:attr/helperTextEnabled = 0x7f03022e
mc.meson.kasumi:animator/fragment_open_exit = 0x7f020008
mc.meson.kasumi:style/Widget.MaterialComponents.NavigationRailView.Colored.Compact = 0x7f120447
mc.meson.kasumi:animator/fragment_open_enter = 0x7f020007
mc.meson.kasumi:style/MaterialAlertDialog.MaterialComponents.Title.Text.CenterStacked = 0x7f120142
mc.meson.kasumi:color/switch_thumb_disabled_material_light = 0x7f05034e
mc.meson.kasumi:macro/m3_comp_outlined_text_field_error_outline_color = 0x7f0c00b6
mc.meson.kasumi:color/mtrl_btn_stroke_color_selector = 0x7f050305
mc.meson.kasumi:anim/abc_fade_in = 0x7f010000
mc.meson.kasumi:animator/m3_btn_elevated_btn_state_list_anim = 0x7f02000a
mc.meson.kasumi:macro/m3_comp_switch_unselected_handle_color = 0x7f0c0134
mc.meson.kasumi:integer/bottom_sheet_slide_duration = 0x7f090003
mc.meson.kasumi:animator/design_fab_hide_motion_spec = 0x7f020001
mc.meson.kasumi:id/material_minute_text_input = 0x7f080140
mc.meson.kasumi:attr/colorOnSurfaceInverse = 0x7f030111
mc.meson.kasumi:attr/listMenuViewStyle = 0x7f0302dc
mc.meson.kasumi:color/material_personalized_color_primary_text_inverse = 0x7f050296
mc.meson.kasumi:attr/selectableItemBackground = 0x7f0303eb
mc.meson.kasumi:layout/abc_activity_chooser_view = 0x7f0b0006
mc.meson.kasumi:color/m3_ref_palette_dynamic_primary50 = 0x7f0500d8
mc.meson.kasumi:attr/subtitleCentered = 0x7f030444
mc.meson.kasumi:style/Base.Widget.AppCompat.Button = 0x7f1200da
mc.meson.kasumi:style/Base.ThemeOverlay.MaterialComponents.MaterialAlertDialog = 0x7f120095
mc.meson.kasumi:layout/abc_select_dialog_material = 0x7f0b001a
mc.meson.kasumi:id/mtrl_picker_header_selection_text = 0x7f080164
mc.meson.kasumi:attr/color = 0x7f0300f8
mc.meson.kasumi:attr/motionDurationLong1 = 0x7f030352
mc.meson.kasumi:anim/m3_bottom_sheet_slide_in = 0x7f010026
mc.meson.kasumi:animator/mtrl_extended_fab_change_size_collapse_motion_spec = 0x7f020019
mc.meson.kasumi:anim/nav_default_pop_enter_anim = 0x7f010033
mc.meson.kasumi:attr/menuAlignmentMode = 0x7f03033e
mc.meson.kasumi:color/m3_textfield_indicator_text_color = 0x7f050208
mc.meson.kasumi:color/abc_decor_view_status_guard = 0x7f050005
mc.meson.kasumi:color/m3_ref_palette_dynamic_tertiary60 = 0x7f0500f3
mc.meson.kasumi:anim/m3_motion_fade_enter = 0x7f010028
mc.meson.kasumi:style/ShapeAppearance.M3.Comp.Sheet.Side.Docked.Container.Shape = 0x7f12016f
mc.meson.kasumi:id/material_timepicker_container = 0x7f080144
mc.meson.kasumi:attr/dayInvalidStyle = 0x7f030174
mc.meson.kasumi:style/Widget.Material3.Chip.Suggestion = 0x7f12037c
mc.meson.kasumi:attr/switchTextAppearance = 0x7f03044f
mc.meson.kasumi:dimen/m3_btn_translation_z_hovered = 0x7f0600e3
mc.meson.kasumi:id/snapMargins = 0x7f0801ea
mc.meson.kasumi:attr/collapseContentDescription = 0x7f0300ed
mc.meson.kasumi:color/design_dark_default_color_on_surface = 0x7f05003c
mc.meson.kasumi:dimen/m3_comp_navigation_rail_hover_state_layer_opacity = 0x7f06014a
mc.meson.kasumi:attr/checkMarkTint = 0x7f0300b5
mc.meson.kasumi:animator/nav_default_pop_enter_anim = 0x7f020024
mc.meson.kasumi:color/button_material_dark = 0x7f050029
mc.meson.kasumi:style/Widget.Material3.CompoundButton.RadioButton = 0x7f12038c
mc.meson.kasumi:dimen/item_touch_helper_swipe_escape_max_velocity = 0x7f06009b
mc.meson.kasumi:interpolator/btn_radio_to_on_mtrl_animation_interpolator_0 = 0x7f0a0005
mc.meson.kasumi:color/mtrl_switch_track_decoration_tint = 0x7f05032d
mc.meson.kasumi:attr/defaultScrollFlagsEnabled = 0x7f03017c
mc.meson.kasumi:attr/tintNavigationIcon = 0x7f0304cf
mc.meson.kasumi:attr/badgeText = 0x7f03005d
mc.meson.kasumi:color/m3_radiobutton_ripple_tint = 0x7f0500a0
mc.meson.kasumi:styleable/CheckedTextView = 0x7f13001f
mc.meson.kasumi:style/Widget.Design.ScrimInsetsFrameLayout = 0x7f120346
mc.meson.kasumi:macro/m3_comp_secondary_navigation_tab_with_icon_inactive_icon_color = 0x7f0c0103
mc.meson.kasumi:color/m3_navigation_rail_ripple_color_selector = 0x7f05009c
mc.meson.kasumi:dimen/notification_media_narrow_margin = 0x7f060313
mc.meson.kasumi:style/Theme.AppCompat.DayNight.Dialog.Alert = 0x7f12021d
mc.meson.kasumi:dimen/mtrl_btn_disabled_z = 0x7f06025c
mc.meson.kasumi:anim/btn_radio_to_off_mtrl_ring_outer_path_animation = 0x7f010015
mc.meson.kasumi:style/Base.ThemeOverlay.MaterialComponents.Light.Dialog.Alert.Framework = 0x7f120094
mc.meson.kasumi:attr/listDividerAlertDialog = 0x7f0302d9
mc.meson.kasumi:attr/boxBackgroundColor = 0x7f030083
mc.meson.kasumi:attr/materialDisplayDividerStyle = 0x7f030321
mc.meson.kasumi:attr/materialAlertDialogTitlePanelStyle = 0x7f030307
mc.meson.kasumi:macro/m3_comp_filled_tonal_button_label_text_color = 0x7f0c0053
mc.meson.kasumi:dimen/def_drawer_elevation = 0x7f06005d
mc.meson.kasumi:attr/flow_firstVerticalBias = 0x7f0301f8
mc.meson.kasumi:attr/placeholder_emptyVisibility = 0x7f0303b0
mc.meson.kasumi:style/Base.TextAppearance.AppCompat.Widget.ActionMode.Title = 0x7f120045
mc.meson.kasumi:anim/design_snackbar_in = 0x7f01001b
mc.meson.kasumi:attr/keyPositionType = 0x7f03027b
mc.meson.kasumi:attr/materialDividerStyle = 0x7f030323
mc.meson.kasumi:dimen/mtrl_calendar_maximum_default_fullscreen_minor_axis = 0x7f060288
mc.meson.kasumi:style/Widget.Material3.TextInputLayout.FilledBox.Dense.ExposedDropdownMenu = 0x7f1203ec
mc.meson.kasumi:color/abc_hint_foreground_material_dark = 0x7f050007
mc.meson.kasumi:string/mtrl_switch_thumb_path_morphing = 0x7f1100b2
mc.meson.kasumi:string/about = 0x7f11001b
mc.meson.kasumi:color/m3_ref_palette_tertiary95 = 0x7f050150
mc.meson.kasumi:color/primary_text_default_material_dark = 0x7f050340
mc.meson.kasumi:style/Base.TextAppearance.MaterialComponents.Subtitle2 = 0x7f120054
mc.meson.kasumi:string/mtrl_timepicker_confirm = 0x7f1100b9
mc.meson.kasumi:attr/itemTextAppearanceActiveBoldEnabled = 0x7f030277
mc.meson.kasumi:attr/textAppearanceDisplaySmall = 0x7f03047c
mc.meson.kasumi:attr/collapsedSize = 0x7f0300ef
