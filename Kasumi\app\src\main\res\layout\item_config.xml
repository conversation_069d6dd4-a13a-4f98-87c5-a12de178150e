<?xml version="1.0" encoding="utf-8"?>
<com.google.android.material.card.MaterialCardView xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:id="@+id/cardConfig"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:layout_marginVertical="4dp"
    app:cardElevation="2dp"
    app:cardCornerRadius="12dp"
    app:cardBackgroundColor="?attr/colorSurface"
    android:foreground="?attr/selectableItemBackground">

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="horizontal"
        android:padding="16dp"
        android:gravity="center_vertical">

        <!-- 配置信息 -->
        <LinearLayout
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:orientation="vertical">

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="horizontal"
                android:gravity="center_vertical">

                <TextView
                    android:id="@+id/tvConfigName"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_weight="1"
                    android:text="配置名称"
                    android:textAppearance="?attr/textAppearanceTitleMedium"
                    android:textColor="?attr/colorOnSurface"
                    android:textStyle="bold" />

                <ImageView
                    android:id="@+id/ivCurrentIndicator"
                    android:layout_width="24dp"
                    android:layout_height="24dp"
                    android:src="@drawable/ic_check_circle_24"
                    android:visibility="gone"
                    app:tint="?attr/colorPrimary" />

            </LinearLayout>

            <TextView
                android:id="@+id/tvConfigDescription"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:text="配置描述"
                android:textAppearance="?attr/textAppearanceBodyMedium"
                android:textColor="?attr/colorOnSurfaceVariant"
                android:layout_marginTop="4dp"
                android:maxLines="2"
                android:ellipsize="end" />

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="horizontal"
                android:layout_marginTop="8dp">

                <TextView
                    android:id="@+id/tvConfigStats"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_weight="1"
                    android:text="0/20 功能已启用"
                    android:textAppearance="?attr/textAppearanceBodySmall"
                    android:textColor="?attr/colorPrimary"
                    android:textStyle="bold" />

                <TextView
                    android:id="@+id/tvConfigTime"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="2024-01-01 12:00"
                    android:textAppearance="?attr/textAppearanceBodySmall"
                    android:textColor="?attr/colorOnSurfaceVariant" />

            </LinearLayout>

        </LinearLayout>

        <!-- 操作按钮 -->
        <LinearLayout
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:orientation="horizontal"
            android:layout_marginStart="12dp">

            <ImageButton
                android:id="@+id/btnEditConfig"
                android:layout_width="40dp"
                android:layout_height="40dp"
                android:src="@drawable/ic_edit_24"
                android:background="?attr/selectableItemBackgroundBorderless"
                android:contentDescription="编辑配置"
                app:tint="?attr/colorOnSurfaceVariant" />

            <ImageButton
                android:id="@+id/btnExportConfig"
                android:layout_width="40dp"
                android:layout_height="40dp"
                android:src="@drawable/ic_arrow_drop_down_24"
                android:background="?attr/selectableItemBackgroundBorderless"
                android:contentDescription="导出配置"
                app:tint="?attr/colorOnSurfaceVariant"
                android:layout_marginStart="4dp"
                android:rotation="180" />

            <ImageButton
                android:id="@+id/btnDeleteConfig"
                android:layout_width="40dp"
                android:layout_height="40dp"
                android:src="@drawable/ic_delete_24"
                android:background="?attr/selectableItemBackgroundBorderless"
                android:contentDescription="删除配置"
                app:tint="?attr/colorError"
                android:layout_marginStart="4dp" />

        </LinearLayout>

    </LinearLayout>

</com.google.android.material.card.MaterialCardView>
