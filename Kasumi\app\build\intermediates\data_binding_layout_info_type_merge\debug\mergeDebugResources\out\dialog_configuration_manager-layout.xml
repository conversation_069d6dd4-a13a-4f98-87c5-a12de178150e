<?xml version="1.0" encoding="utf-8" standalone="yes"?><Layout layout="dialog_configuration_manager" modulePackage="mc.meson.kasumi" filePath="app\src\main\res\layout\dialog_configuration_manager.xml" directory="layout" isMerge="false" isBindingData="false" rootNodeType="android.widget.LinearLayout"><Targets><Target tag="layout/dialog_configuration_manager_0" view="LinearLayout"><Expressions/><location startLine="1" startOffset="0" endLine="58" endOffset="14"/></Target><Target id="@+id/rvConfigurations" view="androidx.recyclerview.widget.RecyclerView"><Expressions/><location startLine="18" startOffset="4" endLine="24" endOffset="44"/></Target><Target id="@+id/btnImportConfig" view="com.google.android.material.button.MaterialButton"><Expressions/><location startLine="33" startOffset="8" endLine="39" endOffset="63"/></Target><Target id="@+id/btnExportConfig" view="com.google.android.material.button.MaterialButton"><Expressions/><location startLine="41" startOffset="8" endLine="47" endOffset="63"/></Target><Target id="@+id/btnCloseDialog" view="com.google.android.material.button.MaterialButton"><Expressions/><location startLine="49" startOffset="8" endLine="54" endOffset="63"/></Target></Targets></Layout>