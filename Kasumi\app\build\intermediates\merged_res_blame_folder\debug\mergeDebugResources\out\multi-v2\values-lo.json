{"logs": [{"outputFile": "mc.meson.kasumi.app-mergeDebugResources-47:/values-lo/values-lo.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-4\\c6de447ab1a7b632fe56ac0b139a6541\\transformed\\navigation-ui-2.7.7\\res\\values-lo\\values-lo.xml", "from": {"startLines": "2,3", "startColumns": "4,4", "startOffsets": "55,164", "endColumns": "108,109", "endOffsets": "159,269"}, "to": {"startLines": "111,112", "startColumns": "4,4", "startOffsets": "9283,9392", "endColumns": "108,109", "endOffsets": "9387,9497"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-4\\c1f1d66677a2054cc4b3d0f2744c5c9f\\transformed\\material-1.12.0\\res\\values-lo\\values-lo.xml", "from": {"startLines": "2,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "100,269,343,414,495,581,664,779,898,981,1048,1114,1203,1272,1331,1426,1492,1557,1615,1680,1741,1801,1907,1968,2028,2086,2157,2276,2362,2439,2529,2614,2696,2839,2914,2990,3121,3211,3289,3344,3399,3465,3534,3608,3679,3758,3831,3908,3977,4047,4144,4229,4304,4397,4490,4564,4633,4727,4779,4862,4929,5013,5097,5159,5223,5286,5356,5455,5553,5648,5742,5801,5860,5939,6024,6101", "endLines": "5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80", "endColumns": "12,73,70,80,85,82,114,118,82,66,65,88,68,58,94,65,64,57,64,60,59,105,60,59,57,70,118,85,76,89,84,81,142,74,75,130,89,77,54,54,65,68,73,70,78,72,76,68,69,96,84,74,92,92,73,68,93,51,82,66,83,83,61,63,62,69,98,97,94,93,58,58,78,84,76,75", "endOffsets": "264,338,409,490,576,659,774,893,976,1043,1109,1198,1267,1326,1421,1487,1552,1610,1675,1736,1796,1902,1963,2023,2081,2152,2271,2357,2434,2524,2609,2691,2834,2909,2985,3116,3206,3284,3339,3394,3460,3529,3603,3674,3753,3826,3903,3972,4042,4139,4224,4299,4392,4485,4559,4628,4722,4774,4857,4924,5008,5092,5154,5218,5281,5351,5450,5548,5643,5737,5796,5855,5934,6019,6096,6172"}, "to": {"startLines": "2,33,34,35,36,37,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,113,115,116,117", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "150,2986,3060,3131,3212,3298,4087,4202,4321,4404,4471,4537,4626,4695,4754,4849,4915,4980,5038,5103,5164,5224,5330,5391,5451,5509,5580,5699,5785,5862,5952,6037,6119,6262,6337,6413,6544,6634,6712,6767,6822,6888,6957,7031,7102,7181,7254,7331,7400,7470,7567,7652,7727,7820,7913,7987,8056,8150,8202,8285,8352,8436,8520,8582,8646,8709,8779,8878,8976,9071,9165,9224,9502,9663,9748,9825", "endLines": "5,33,34,35,36,37,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,113,115,116,117", "endColumns": "12,73,70,80,85,82,114,118,82,66,65,88,68,58,94,65,64,57,64,60,59,105,60,59,57,70,118,85,76,89,84,81,142,74,75,130,89,77,54,54,65,68,73,70,78,72,76,68,69,96,84,74,92,92,73,68,93,51,82,66,83,83,61,63,62,69,98,97,94,93,58,58,78,84,76,75", "endOffsets": "314,3055,3126,3207,3293,3376,4197,4316,4399,4466,4532,4621,4690,4749,4844,4910,4975,5033,5098,5159,5219,5325,5386,5446,5504,5575,5694,5780,5857,5947,6032,6114,6257,6332,6408,6539,6629,6707,6762,6817,6883,6952,7026,7097,7176,7249,7326,7395,7465,7562,7647,7722,7815,7908,7982,8051,8145,8197,8280,8347,8431,8515,8577,8641,8704,8774,8873,8971,9066,9160,9219,9278,9576,9743,9820,9896"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-4\\588763d874820f4a3b3fefea157e8181\\transformed\\core-1.13.1\\res\\values-lo\\values-lo.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,151,254,353,451,552,650,761", "endColumns": "95,102,98,97,100,97,110,100", "endOffsets": "146,249,348,446,547,645,756,857"}, "to": {"startLines": "38,39,40,41,42,43,44,118", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "3381,3477,3580,3679,3777,3878,3976,9901", "endColumns": "95,102,98,97,100,97,110,100", "endOffsets": "3472,3575,3674,3772,3873,3971,4082,9997"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-4\\f3bee05a55d0be5267577c93938c9880\\transformed\\appcompat-1.7.1\\res\\values-lo\\values-lo.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,208,311,424,509,613,724,802,879,970,1063,1155,1249,1349,1442,1537,1633,1724,1815,1896,2003,2107,2205,2308,2412,2516,2673,2772", "endColumns": "102,102,112,84,103,110,77,76,90,92,91,93,99,92,94,95,90,90,80,106,103,97,102,103,103,156,98,81", "endOffsets": "203,306,419,504,608,719,797,874,965,1058,1150,1244,1344,1437,1532,1628,1719,1810,1891,1998,2102,2200,2303,2407,2511,2668,2767,2849"}, "to": {"startLines": "6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,114", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "319,422,525,638,723,827,938,1016,1093,1184,1277,1369,1463,1563,1656,1751,1847,1938,2029,2110,2217,2321,2419,2522,2626,2730,2887,9581", "endColumns": "102,102,112,84,103,110,77,76,90,92,91,93,99,92,94,95,90,90,80,106,103,97,102,103,103,156,98,81", "endOffsets": "417,520,633,718,822,933,1011,1088,1179,1272,1364,1458,1558,1651,1746,1842,1933,2024,2105,2212,2316,2414,2517,2621,2725,2882,2981,9658"}}]}]}