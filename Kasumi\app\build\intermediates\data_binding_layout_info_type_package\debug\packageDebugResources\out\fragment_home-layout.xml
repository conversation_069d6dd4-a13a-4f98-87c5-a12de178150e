<?xml version="1.0" encoding="utf-8" standalone="yes"?><Layout layout="fragment_home" modulePackage="mc.meson.kasumi" filePath="app\src\main\res\layout\fragment_home.xml" directory="layout" isMerge="false" isBindingData="false" rootNodeType="androidx.core.widget.NestedScrollView"><Targets><Target tag="layout/fragment_home_0" view="androidx.core.widget.NestedScrollView"><Expressions/><location startLine="1" startOffset="0" endLine="171" endOffset="39"/></Target><Target id="@+id/welcomeText" view="TextView"><Expressions/><location startLine="39" startOffset="16" endLine="45" endOffset="69"/></Target><Target id="@+id/statusIndicator" view="View"><Expressions/><location startLine="82" startOffset="20" endLine="87" endOffset="57"/></Target><Target id="@+id/statusText" view="TextView"><Expressions/><location startLine="89" startOffset="20" endLine="96" endOffset="73"/></Target><Target id="@+id/btnFloatingWindow" view="com.google.android.material.button.MaterialButton"><Expressions/><location startLine="143" startOffset="20" endLine="153" endOffset="66"/></Target><Target id="@+id/btnLandscape" view="com.google.android.material.button.MaterialButton"><Expressions/><location startLine="155" startOffset="20" endLine="161" endOffset="61"/></Target></Targets></Layout>