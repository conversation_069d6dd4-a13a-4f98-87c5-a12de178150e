// Generated by view binder compiler. Do not edit!
package mc.meson.kasumi.databinding;

import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.ImageButton;
import android.widget.LinearLayout;
import android.widget.TextView;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.recyclerview.widget.RecyclerView;
import androidx.viewbinding.ViewBinding;
import androidx.viewbinding.ViewBindings;
import com.google.android.material.button.MaterialButton;
import com.google.android.material.materialswitch.MaterialSwitch;
import java.lang.NullPointerException;
import java.lang.Override;
import java.lang.String;
import mc.meson.kasumi.R;

public final class FragmentModuleSettingsBinding implements ViewBinding {
  @NonNull
  private final LinearLayout rootView;

  @NonNull
  public final ImageButton btnBack;

  @NonNull
  public final MaterialButton btnResetSettings;

  @NonNull
  public final LinearLayout layoutNoSettings;

  @NonNull
  public final RecyclerView recyclerViewSettings;

  @NonNull
  public final MaterialSwitch switchModuleEnabled;

  @NonNull
  public final TextView tvModuleDescription;

  @NonNull
  public final TextView tvModuleName;

  private FragmentModuleSettingsBinding(@NonNull LinearLayout rootView,
      @NonNull ImageButton btnBack, @NonNull MaterialButton btnResetSettings,
      @NonNull LinearLayout layoutNoSettings, @NonNull RecyclerView recyclerViewSettings,
      @NonNull MaterialSwitch switchModuleEnabled, @NonNull TextView tvModuleDescription,
      @NonNull TextView tvModuleName) {
    this.rootView = rootView;
    this.btnBack = btnBack;
    this.btnResetSettings = btnResetSettings;
    this.layoutNoSettings = layoutNoSettings;
    this.recyclerViewSettings = recyclerViewSettings;
    this.switchModuleEnabled = switchModuleEnabled;
    this.tvModuleDescription = tvModuleDescription;
    this.tvModuleName = tvModuleName;
  }

  @Override
  @NonNull
  public LinearLayout getRoot() {
    return rootView;
  }

  @NonNull
  public static FragmentModuleSettingsBinding inflate(@NonNull LayoutInflater inflater) {
    return inflate(inflater, null, false);
  }

  @NonNull
  public static FragmentModuleSettingsBinding inflate(@NonNull LayoutInflater inflater,
      @Nullable ViewGroup parent, boolean attachToParent) {
    View root = inflater.inflate(R.layout.fragment_module_settings, parent, false);
    if (attachToParent) {
      parent.addView(root);
    }
    return bind(root);
  }

  @NonNull
  public static FragmentModuleSettingsBinding bind(@NonNull View rootView) {
    // The body of this method is generated in a way you would not otherwise write.
    // This is done to optimize the compiled bytecode for size and performance.
    int id;
    missingId: {
      id = R.id.btnBack;
      ImageButton btnBack = ViewBindings.findChildViewById(rootView, id);
      if (btnBack == null) {
        break missingId;
      }

      id = R.id.btnResetSettings;
      MaterialButton btnResetSettings = ViewBindings.findChildViewById(rootView, id);
      if (btnResetSettings == null) {
        break missingId;
      }

      id = R.id.layoutNoSettings;
      LinearLayout layoutNoSettings = ViewBindings.findChildViewById(rootView, id);
      if (layoutNoSettings == null) {
        break missingId;
      }

      id = R.id.recyclerViewSettings;
      RecyclerView recyclerViewSettings = ViewBindings.findChildViewById(rootView, id);
      if (recyclerViewSettings == null) {
        break missingId;
      }

      id = R.id.switchModuleEnabled;
      MaterialSwitch switchModuleEnabled = ViewBindings.findChildViewById(rootView, id);
      if (switchModuleEnabled == null) {
        break missingId;
      }

      id = R.id.tvModuleDescription;
      TextView tvModuleDescription = ViewBindings.findChildViewById(rootView, id);
      if (tvModuleDescription == null) {
        break missingId;
      }

      id = R.id.tvModuleName;
      TextView tvModuleName = ViewBindings.findChildViewById(rootView, id);
      if (tvModuleName == null) {
        break missingId;
      }

      return new FragmentModuleSettingsBinding((LinearLayout) rootView, btnBack, btnResetSettings,
          layoutNoSettings, recyclerViewSettings, switchModuleEnabled, tvModuleDescription,
          tvModuleName);
    }
    String missingId = rootView.getResources().getResourceName(id);
    throw new NullPointerException("Missing required view with ID: ".concat(missingId));
  }
}
