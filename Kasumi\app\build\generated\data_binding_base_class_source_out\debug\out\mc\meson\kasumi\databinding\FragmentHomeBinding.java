// Generated by view binder compiler. Do not edit!
package mc.meson.kasumi.databinding;

import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.TextView;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.core.widget.NestedScrollView;
import androidx.viewbinding.ViewBinding;
import androidx.viewbinding.ViewBindings;
import com.google.android.material.button.MaterialButton;
import java.lang.NullPointerException;
import java.lang.Override;
import java.lang.String;
import mc.meson.kasumi.R;

public final class FragmentHomeBinding implements ViewBinding {
  @NonNull
  private final NestedScrollView rootView;

  @NonNull
  public final MaterialButton btnFloatingWindow;

  @NonNull
  public final MaterialButton btnLandscape;

  @NonNull
  public final View statusIndicator;

  @NonNull
  public final TextView statusText;

  @NonNull
  public final TextView welcomeText;

  private FragmentHomeBinding(@NonNull NestedScrollView rootView,
      @NonNull MaterialButton btnFloatingWindow, @NonNull MaterialButton btnLandscape,
      @NonNull View statusIndicator, @NonNull TextView statusText, @NonNull TextView welcomeText) {
    this.rootView = rootView;
    this.btnFloatingWindow = btnFloatingWindow;
    this.btnLandscape = btnLandscape;
    this.statusIndicator = statusIndicator;
    this.statusText = statusText;
    this.welcomeText = welcomeText;
  }

  @Override
  @NonNull
  public NestedScrollView getRoot() {
    return rootView;
  }

  @NonNull
  public static FragmentHomeBinding inflate(@NonNull LayoutInflater inflater) {
    return inflate(inflater, null, false);
  }

  @NonNull
  public static FragmentHomeBinding inflate(@NonNull LayoutInflater inflater,
      @Nullable ViewGroup parent, boolean attachToParent) {
    View root = inflater.inflate(R.layout.fragment_home, parent, false);
    if (attachToParent) {
      parent.addView(root);
    }
    return bind(root);
  }

  @NonNull
  public static FragmentHomeBinding bind(@NonNull View rootView) {
    // The body of this method is generated in a way you would not otherwise write.
    // This is done to optimize the compiled bytecode for size and performance.
    int id;
    missingId: {
      id = R.id.btnFloatingWindow;
      MaterialButton btnFloatingWindow = ViewBindings.findChildViewById(rootView, id);
      if (btnFloatingWindow == null) {
        break missingId;
      }

      id = R.id.btnLandscape;
      MaterialButton btnLandscape = ViewBindings.findChildViewById(rootView, id);
      if (btnLandscape == null) {
        break missingId;
      }

      id = R.id.statusIndicator;
      View statusIndicator = ViewBindings.findChildViewById(rootView, id);
      if (statusIndicator == null) {
        break missingId;
      }

      id = R.id.statusText;
      TextView statusText = ViewBindings.findChildViewById(rootView, id);
      if (statusText == null) {
        break missingId;
      }

      id = R.id.welcomeText;
      TextView welcomeText = ViewBindings.findChildViewById(rootView, id);
      if (welcomeText == null) {
        break missingId;
      }

      return new FragmentHomeBinding((NestedScrollView) rootView, btnFloatingWindow, btnLandscape,
          statusIndicator, statusText, welcomeText);
    }
    String missingId = rootView.getResources().getResourceName(id);
    throw new NullPointerException("Missing required view with ID: ".concat(missingId));
  }
}
