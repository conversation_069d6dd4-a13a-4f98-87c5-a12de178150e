[{"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.7\\mc.meson.kasumi.app-mergeDebugResources-48:\\layout\\fragment_hack_features.xml", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.7\\mc.meson.kasumi.app-main-51:\\layout\\fragment_hack_features.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.7\\mc.meson.kasumi.app-mergeDebugResources-48:\\layout\\item_compact_module.xml", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.7\\mc.meson.kasumi.app-main-51:\\layout\\item_compact_module.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.7\\mc.meson.kasumi.app-mergeDebugResources-48:\\layout\\item_setting_mode.xml", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.7\\mc.meson.kasumi.app-main-51:\\layout\\item_setting_mode.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.7\\mc.meson.kasumi.app-mergeDebugResources-48:\\layout\\fragment_home.xml", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.7\\mc.meson.kasumi.app-main-51:\\layout\\fragment_home.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.7\\mc.meson.kasumi.app-mergeDebugResources-48:\\layout\\item_module_grid.xml", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.7\\mc.meson.kasumi.app-main-51:\\layout\\item_module_grid.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.7\\mc.meson.kasumi.app-mergeDebugResources-48:\\layout\\floating_window_expanded_v2.xml", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.7\\mc.meson.kasumi.app-main-51:\\layout\\floating_window_expanded_v2.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.7\\mc.meson.kasumi.app-mergeDebugResources-48:\\layout\\module_settings_panel.xml", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.7\\mc.meson.kasumi.app-main-51:\\layout\\module_settings_panel.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.7\\mc.meson.kasumi.app-mergeDebugResources-48:\\layout\\activity_main_with_navigation.xml", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.7\\mc.meson.kasumi.app-main-51:\\layout\\activity_main_with_navigation.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.7\\mc.meson.kasumi.app-mergeDebugResources-48:\\layout\\floating_window_collapsed.xml", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.7\\mc.meson.kasumi.app-main-51:\\layout\\floating_window_collapsed.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.7\\mc.meson.kasumi.app-mergeDebugResources-48:\\layout\\item_setting_slider.xml", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.7\\mc.meson.kasumi.app-main-51:\\layout\\item_setting_slider.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.7\\mc.meson.kasumi.app-mergeDebugResources-48:\\layout\\activity_splash.xml", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.7\\mc.meson.kasumi.app-main-51:\\layout\\activity_splash.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.7\\mc.meson.kasumi.app-mergeDebugResources-48:\\layout\\item_setting_toggle.xml", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.7\\mc.meson.kasumi.app-main-51:\\layout\\item_setting_toggle.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.7\\mc.meson.kasumi.app-mergeDebugResources-48:\\layout\\item_mode_option.xml", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.7\\mc.meson.kasumi.app-main-51:\\layout\\item_mode_option.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.7\\mc.meson.kasumi.app-mergeDebugResources-48:\\layout\\floating_window_compact_v2.xml", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.7\\mc.meson.kasumi.app-main-51:\\layout\\floating_window_compact_v2.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.7\\mc.meson.kasumi.app-mergeDebugResources-48:\\layout\\item_expanded_module.xml", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.7\\mc.meson.kasumi.app-main-51:\\layout\\item_expanded_module.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.7\\mc.meson.kasumi.app-mergeDebugResources-48:\\layout\\fragment_settings.xml", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.7\\mc.meson.kasumi.app-main-51:\\layout\\fragment_settings.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.7\\mc.meson.kasumi.app-mergeDebugResources-48:\\layout\\floating_window_compact.xml", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.7\\mc.meson.kasumi.app-main-51:\\layout\\floating_window_compact.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.7\\mc.meson.kasumi.app-mergeDebugResources-48:\\layout\\floating_window_expanded_new.xml", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.7\\mc.meson.kasumi.app-main-51:\\layout\\floating_window_expanded_new.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.7\\mc.meson.kasumi.app-mergeDebugResources-48:\\layout\\item_module_list.xml", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.7\\mc.meson.kasumi.app-main-51:\\layout\\item_module_list.xml"}, {"merged": "mc.meson.kasumi.app-mergeDebugResources-48:/layout/fragment_settings.xml", "source": "mc.meson.kasumi.app-main-51:/layout/fragment_settings.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.7\\mc.meson.kasumi.app-mergeDebugResources-48:\\layout\\compact_category_page.xml", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.7\\mc.meson.kasumi.app-main-51:\\layout\\compact_category_page.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.7\\mc.meson.kasumi.app-mergeDebugResources-48:\\layout\\activity_main.xml", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.7\\mc.meson.kasumi.app-main-51:\\layout\\activity_main.xml"}]