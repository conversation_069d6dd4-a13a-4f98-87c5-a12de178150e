[{"merged": "mc.meson.kasumi.app-mergeDebugResources-48:/layout/compact_category_page.xml", "source": "mc.meson.kasumi.app-main-51:/layout/compact_category_page.xml"}, {"merged": "mc.meson.kasumi.app-mergeDebugResources-48:/layout/item_expanded_module.xml", "source": "mc.meson.kasumi.app-main-51:/layout/item_expanded_module.xml"}, {"merged": "mc.meson.kasumi.app-mergeDebugResources-48:/layout/fragment_module_settings.xml", "source": "mc.meson.kasumi.app-main-51:/layout/fragment_module_settings.xml"}, {"merged": "mc.meson.kasumi.app-mergeDebugResources-48:/layout/item_module_grid.xml", "source": "mc.meson.kasumi.app-main-51:/layout/item_module_grid.xml"}, {"merged": "mc.meson.kasumi.app-mergeDebugResources-48:/layout/item_compact_module.xml", "source": "mc.meson.kasumi.app-main-51:/layout/item_compact_module.xml"}, {"merged": "mc.meson.kasumi.app-mergeDebugResources-48:/layout/fragment_hack_features.xml", "source": "mc.meson.kasumi.app-main-51:/layout/fragment_hack_features.xml"}, {"merged": "mc.meson.kasumi.app-mergeDebugResources-48:/layout/fragment_config_management.xml", "source": "mc.meson.kasumi.app-main-51:/layout/fragment_config_management.xml"}, {"merged": "mc.meson.kasumi.app-mergeDebugResources-48:/layout/floating_window_compact_v2.xml", "source": "mc.meson.kasumi.app-main-51:/layout/floating_window_compact_v2.xml"}, {"merged": "mc.meson.kasumi.app-mergeDebugResources-48:/layout/module_settings_panel.xml", "source": "mc.meson.kasumi.app-main-51:/layout/module_settings_panel.xml"}, {"merged": "mc.meson.kasumi.app-mergeDebugResources-48:/layout/dialog_create_config.xml", "source": "mc.meson.kasumi.app-main-51:/layout/dialog_create_config.xml"}, {"merged": "mc.meson.kasumi.app-mergeDebugResources-48:/layout/item_module_list.xml", "source": "mc.meson.kasumi.app-main-51:/layout/item_module_list.xml"}, {"merged": "mc.meson.kasumi.app-mergeDebugResources-48:/layout/item_config.xml", "source": "mc.meson.kasumi.app-main-51:/layout/item_config.xml"}, {"merged": "mc.meson.kasumi.app-mergeDebugResources-48:/layout/item_setting_toggle.xml", "source": "mc.meson.kasumi.app-main-51:/layout/item_setting_toggle.xml"}, {"merged": "mc.meson.kasumi.app-mergeDebugResources-48:/layout/fragment_settings.xml", "source": "mc.meson.kasumi.app-main-51:/layout/fragment_settings.xml"}, {"merged": "mc.meson.kasumi.app-mergeDebugResources-48:/layout/floating_window_expanded_new.xml", "source": "mc.meson.kasumi.app-main-51:/layout/floating_window_expanded_new.xml"}, {"merged": "mc.meson.kasumi.app-mergeDebugResources-48:/layout/activity_main.xml", "source": "mc.meson.kasumi.app-main-51:/layout/activity_main.xml"}, {"merged": "mc.meson.kasumi.app-mergeDebugResources-48:/layout/item_setting_mode.xml", "source": "mc.meson.kasumi.app-main-51:/layout/item_setting_mode.xml"}, {"merged": "mc.meson.kasumi.app-mergeDebugResources-48:/layout/item_setting_slider.xml", "source": "mc.meson.kasumi.app-main-51:/layout/item_setting_slider.xml"}, {"merged": "mc.meson.kasumi.app-mergeDebugResources-48:/layout/activity_splash.xml", "source": "mc.meson.kasumi.app-main-51:/layout/activity_splash.xml"}, {"merged": "mc.meson.kasumi.app-mergeDebugResources-48:/layout/activity_main_with_navigation.xml", "source": "mc.meson.kasumi.app-main-51:/layout/activity_main_with_navigation.xml"}, {"merged": "mc.meson.kasumi.app-mergeDebugResources-48:/layout/item_mode_option.xml", "source": "mc.meson.kasumi.app-main-51:/layout/item_mode_option.xml"}, {"merged": "mc.meson.kasumi.app-mergeDebugResources-48:/layout/fragment_home.xml", "source": "mc.meson.kasumi.app-main-51:/layout/fragment_home.xml"}]