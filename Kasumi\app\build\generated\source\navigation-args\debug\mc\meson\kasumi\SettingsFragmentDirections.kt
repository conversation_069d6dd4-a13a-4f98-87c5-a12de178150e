package mc.meson.kasumi

import androidx.navigation.ActionOnlyNavDirections
import androidx.navigation.NavDirections

public class SettingsFragmentDirections private constructor() {
  public companion object {
    public fun actionSettingsToHome(): NavDirections =
        ActionOnlyNavDirections(R.id.action_settings_to_home)

    public fun actionSettingsToFeatures(): NavDirections =
        ActionOnlyNavDirections(R.id.action_settings_to_features)
  }
}
