<vector xmlns:android="http://schemas.android.com/apk/res/android"
    android:width="108dp"
    android:height="108dp"
    android:viewportWidth="108"
    android:viewportHeight="108">

    <!-- 基于你提供的SVG的月牙形状 - App图标版本 -->
    <group android:scaleX="2.5"
        android:scaleY="2.5"
        android:pivotX="54"
        android:pivotY="54">

        <!-- 主要月牙形状 -->
        <path android:pathData="M12,21C7.5,21 3.8,17.5 3,12.8C3.02,12.3 3.4,11.9 3.9,11.9C4.2,11.9 4.5,12.1 4.7,12.3C4.71,12.31 4.72,12.3 4.72,12.28C5.8,13.4 7.3,14.1 8.9,14.1C12.2,14.1 14.9,11.4 14.9,8.1C14.9,6.4 14.2,4.9 13.1,3.8C13.1,3.8 13.11,3.8 13.11,3.79C12.9,3.6 12.8,3.3 12.8,3C12.8,2.5 13.2,2.1 13.7,2.1C18.6,2.5 22.5,6.4 22.5,11.3C22.5,16.8 18,21 12,21Z"
            android:fillColor="?attr/colorPrimary" />

        <!-- 内部高光效果 -->
        <path android:pathData="M12,18.5C8.5,18.5 5.5,15.8 4.8,12.5C4.81,12.2 5.0,12.0 5.3,12.0C5.5,12.0 5.7,12.1 5.8,12.3C5.81,12.31 5.82,12.3 5.82,12.28C6.6,13.1 7.7,13.6 8.5,13.6C10.8,13.6 12.7,11.7 12.7,9.4C12.7,8.2 12.2,7.1 11.4,6.3C11.4,6.3 11.41,6.3 11.41,6.29C11.2,6.1 11.1,5.9 11.1,5.7C11.1,5.4 11.3,5.2 11.6,5.2C15.2,5.5 18.0,8.3 18.0,11.9C18.0,15.8 14.8,18.5 12,18.5Z"
            android:fillColor="?attr/colorPrimaryContainer"
            android:fillAlpha="0.4" />

    </group>

</vector>