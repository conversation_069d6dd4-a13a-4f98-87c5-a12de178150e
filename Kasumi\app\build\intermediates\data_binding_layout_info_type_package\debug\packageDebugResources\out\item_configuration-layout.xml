<?xml version="1.0" encoding="utf-8" standalone="yes"?><Layout layout="item_configuration" modulePackage="mc.meson.kasumi" filePath="app\src\main\res\layout\item_configuration.xml" directory="layout" isMerge="false" isBindingData="false" rootNodeType="com.google.android.material.card.MaterialCardView"><Targets><Target tag="layout/item_configuration_0" view="com.google.android.material.card.MaterialCardView"><Expressions/><location startLine="1" startOffset="0" endLine="110" endOffset="51"/></Target><Target id="@+id/tvConfigName" view="TextView"><Expressions/><location startLine="32" startOffset="16" endLine="41" endOffset="45"/></Target><Target id="@+id/tvCurrentIndicator" view="TextView"><Expressions/><location startLine="44" startOffset="16" endLine="54" endOffset="47"/></Target><Target id="@+id/tvConfigDescription" view="TextView"><Expressions/><location startLine="58" startOffset="12" endLine="67" endOffset="48"/></Target><Target id="@+id/tvConfigTime" view="TextView"><Expressions/><location startLine="69" startOffset="12" endLine="76" endOffset="48"/></Target><Target id="@+id/btnApplyConfig" view="com.google.android.material.button.MaterialButton"><Expressions/><location startLine="87" startOffset="12" endLine="93" endOffset="67"/></Target><Target id="@+id/btnConfigMenu" view="com.google.android.material.button.MaterialButton"><Expressions/><location startLine="95" startOffset="12" endLine="104" endOffset="67"/></Target></Targets></Layout>