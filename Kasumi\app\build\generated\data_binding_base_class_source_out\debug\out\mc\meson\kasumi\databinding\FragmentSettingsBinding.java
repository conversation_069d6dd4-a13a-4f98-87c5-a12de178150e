// Generated by view binder compiler. Do not edit!
package mc.meson.kasumi.databinding;

import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.TextView;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.core.widget.NestedScrollView;
import androidx.viewbinding.ViewBinding;
import androidx.viewbinding.ViewBindings;
import com.google.android.material.button.MaterialButton;
import com.google.android.material.materialswitch.MaterialSwitch;
import java.lang.NullPointerException;
import java.lang.Override;
import java.lang.String;
import mc.meson.kasumi.R;

public final class FragmentSettingsBinding implements ViewBinding {
  @NonNull
  private final NestedScrollView rootView;

  @NonNull
  public final MaterialButton btnCheckUpdate;

  @NonNull
  public final MaterialButton btnCreateConfig;

  @NonNull
  public final MaterialButton btnManageConfigs;

  @NonNull
  public final MaterialButton btnSwitchConfig;

  @NonNull
  public final MaterialSwitch switchAutoConnect;

  @NonNull
  public final MaterialSwitch switchDarkMode;

  @NonNull
  public final MaterialSwitch switchNotifications;

  @NonNull
  public final TextView tvCurrentConfig;

  private FragmentSettingsBinding(@NonNull NestedScrollView rootView,
      @NonNull MaterialButton btnCheckUpdate, @NonNull MaterialButton btnCreateConfig,
      @NonNull MaterialButton btnManageConfigs, @NonNull MaterialButton btnSwitchConfig,
      @NonNull MaterialSwitch switchAutoConnect, @NonNull MaterialSwitch switchDarkMode,
      @NonNull MaterialSwitch switchNotifications, @NonNull TextView tvCurrentConfig) {
    this.rootView = rootView;
    this.btnCheckUpdate = btnCheckUpdate;
    this.btnCreateConfig = btnCreateConfig;
    this.btnManageConfigs = btnManageConfigs;
    this.btnSwitchConfig = btnSwitchConfig;
    this.switchAutoConnect = switchAutoConnect;
    this.switchDarkMode = switchDarkMode;
    this.switchNotifications = switchNotifications;
    this.tvCurrentConfig = tvCurrentConfig;
  }

  @Override
  @NonNull
  public NestedScrollView getRoot() {
    return rootView;
  }

  @NonNull
  public static FragmentSettingsBinding inflate(@NonNull LayoutInflater inflater) {
    return inflate(inflater, null, false);
  }

  @NonNull
  public static FragmentSettingsBinding inflate(@NonNull LayoutInflater inflater,
      @Nullable ViewGroup parent, boolean attachToParent) {
    View root = inflater.inflate(R.layout.fragment_settings, parent, false);
    if (attachToParent) {
      parent.addView(root);
    }
    return bind(root);
  }

  @NonNull
  public static FragmentSettingsBinding bind(@NonNull View rootView) {
    // The body of this method is generated in a way you would not otherwise write.
    // This is done to optimize the compiled bytecode for size and performance.
    int id;
    missingId: {
      id = R.id.btnCheckUpdate;
      MaterialButton btnCheckUpdate = ViewBindings.findChildViewById(rootView, id);
      if (btnCheckUpdate == null) {
        break missingId;
      }

      id = R.id.btnCreateConfig;
      MaterialButton btnCreateConfig = ViewBindings.findChildViewById(rootView, id);
      if (btnCreateConfig == null) {
        break missingId;
      }

      id = R.id.btnManageConfigs;
      MaterialButton btnManageConfigs = ViewBindings.findChildViewById(rootView, id);
      if (btnManageConfigs == null) {
        break missingId;
      }

      id = R.id.btnSwitchConfig;
      MaterialButton btnSwitchConfig = ViewBindings.findChildViewById(rootView, id);
      if (btnSwitchConfig == null) {
        break missingId;
      }

      id = R.id.switchAutoConnect;
      MaterialSwitch switchAutoConnect = ViewBindings.findChildViewById(rootView, id);
      if (switchAutoConnect == null) {
        break missingId;
      }

      id = R.id.switchDarkMode;
      MaterialSwitch switchDarkMode = ViewBindings.findChildViewById(rootView, id);
      if (switchDarkMode == null) {
        break missingId;
      }

      id = R.id.switchNotifications;
      MaterialSwitch switchNotifications = ViewBindings.findChildViewById(rootView, id);
      if (switchNotifications == null) {
        break missingId;
      }

      id = R.id.tvCurrentConfig;
      TextView tvCurrentConfig = ViewBindings.findChildViewById(rootView, id);
      if (tvCurrentConfig == null) {
        break missingId;
      }

      return new FragmentSettingsBinding((NestedScrollView) rootView, btnCheckUpdate,
          btnCreateConfig, btnManageConfigs, btnSwitchConfig, switchAutoConnect, switchDarkMode,
          switchNotifications, tvCurrentConfig);
    }
    String missingId = rootView.getResources().getResourceName(id);
    throw new NullPointerException("Missing required view with ID: ".concat(missingId));
  }
}
