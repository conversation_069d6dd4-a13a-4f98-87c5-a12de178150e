package mc.meson.kasumi.config

import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.ImageButton
import android.widget.ImageView
import android.widget.TextView
import androidx.cardview.widget.CardView
import androidx.recyclerview.widget.RecyclerView
import mc.meson.kasumi.R

/**
 * 配置列表适配器
 */
class ConfigAdapter(
    private var configs: List<ConfigData>,
    private var currentConfigId: String?,
    private val onConfigClick: (ConfigData) -> Unit,
    private val onConfigEdit: (ConfigData) -> Unit,
    private val onConfigDelete: (ConfigData) -> Unit,
    private val onConfigExport: (ConfigData) -> Unit
) : RecyclerView.Adapter<ConfigAdapter.ViewHolder>() {
    
    class ViewHolder(view: View) : RecyclerView.ViewHolder(view) {
        val cardView: CardView = view.findViewById(R.id.cardConfig)
        val tvName: TextView = view.findViewById(R.id.tvConfigName)
        val tvDescription: TextView = view.findViewById(R.id.tvConfigDescription)
        val tvStats: TextView = view.findViewById(R.id.tvConfigStats)
        val tvTime: TextView = view.findViewById(R.id.tvConfigTime)
        val ivCurrentIndicator: ImageView = view.findViewById(R.id.ivCurrentIndicator)
        val btnEdit: ImageButton = view.findViewById(R.id.btnEditConfig)
        val btnDelete: ImageButton = view.findViewById(R.id.btnDeleteConfig)
        val btnExport: ImageButton = view.findViewById(R.id.btnExportConfig)
    }
    
    override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): ViewHolder {
        val view = LayoutInflater.from(parent.context)
            .inflate(R.layout.item_config, parent, false)
        return ViewHolder(view)
    }
    
    override fun onBindViewHolder(holder: ViewHolder, position: Int) {
        val config = configs[position]
        val context = holder.itemView.context
        val isCurrentConfig = config.id == currentConfigId
        
        // 设置基本信息
        holder.tvName.text = config.name
        holder.tvDescription.text = config.description.ifEmpty { "无描述" }
        holder.tvStats.text = "${config.getEnabledModuleCount()}/${config.getTotalModuleCount()}"
        // 只显示时间，不显示日期
        holder.tvTime.text = config.getFormattedTime().substringAfter(" ")
        
        // 移除当前配置指示器，改用颜色区分
        holder.ivCurrentIndicator.visibility = View.GONE
        
        // 设置卡片样式
        if (isCurrentConfig) {
            // 选中状态：深棕色背景，白色文字
            holder.cardView.setCardBackgroundColor(context.getColor(R.color.md_theme_light_primary))
            holder.tvName.setTextColor(context.getColor(R.color.white))
            holder.tvDescription.setTextColor(context.getColor(R.color.white))
            holder.tvStats.setTextColor(context.getColor(R.color.white))
            holder.tvTime.setTextColor(context.getColor(R.color.white))
            // 设置按钮图标为白色
            holder.btnEdit.setColorFilter(context.getColor(R.color.white))
            holder.btnDelete.setColorFilter(context.getColor(R.color.white))
            holder.btnExport.setColorFilter(context.getColor(R.color.white))
        } else {
            // 未选中状态：正常颜色
            holder.cardView.setCardBackgroundColor(context.getColor(R.color.card_background_primary))
            holder.tvName.setTextColor(context.getColor(R.color.md_theme_light_onSurface))
            holder.tvDescription.setTextColor(context.getColor(R.color.md_theme_light_onSurfaceVariant))
            holder.tvStats.setTextColor(context.getColor(R.color.md_theme_light_primary))
            holder.tvTime.setTextColor(context.getColor(R.color.md_theme_light_onSurfaceVariant))
            // 设置按钮图标为正常颜色
            holder.btnEdit.setColorFilter(context.getColor(R.color.md_theme_light_onSurface))
            holder.btnDelete.setColorFilter(context.getColor(R.color.md_theme_light_onSurface))
            holder.btnExport.setColorFilter(context.getColor(R.color.md_theme_light_onSurface))
        }
        
        // 设置点击监听器
        holder.cardView.setOnClickListener {
            if (!isCurrentConfig) {
                onConfigClick(config)
            }
        }
        
        holder.btnEdit.setOnClickListener {
            onConfigEdit(config)
        }
        
        holder.btnDelete.setOnClickListener {
            onConfigDelete(config)
        }
        
        holder.btnExport.setOnClickListener {
            onConfigExport(config)
        }
        
        // 禁用删除当前配置（如果只有一个配置）
        holder.btnDelete.isEnabled = !(isCurrentConfig && configs.size == 1)
        holder.btnDelete.alpha = if (holder.btnDelete.isEnabled) 1.0f else 0.5f
    }
    
    override fun getItemCount() = configs.size
    
    /**
     * 更新配置列表
     */
    fun updateConfigs(newConfigs: List<ConfigData>, newCurrentConfigId: String?) {
        configs = newConfigs
        currentConfigId = newCurrentConfigId
        notifyDataSetChanged()
    }
}
