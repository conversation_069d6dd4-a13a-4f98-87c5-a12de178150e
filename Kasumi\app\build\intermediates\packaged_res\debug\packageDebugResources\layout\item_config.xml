<?xml version="1.0" encoding="utf-8"?>
<com.google.android.material.card.MaterialCardView xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:id="@+id/cardConfig"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:layout_marginVertical="4dp"
    app:cardElevation="2dp"
    app:cardCornerRadius="12dp"
    app:cardBackgroundColor="?attr/colorSurface"
    android:foreground="?attr/selectableItemBackground">

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="horizontal"
        android:padding="12dp"
        android:gravity="center_vertical">

        <!-- 配置信息 -->
        <LinearLayout
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:orientation="vertical">

            <!-- 第一行：配置名称 + 统计信息 -->
            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="horizontal"
                android:gravity="center_vertical">

                <TextView
                    android:id="@+id/tvConfigName"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_weight="1"
                    android:text="配置名称"
                    android:textAppearance="?attr/textAppearanceTitleMedium"
                    android:textColor="?attr/colorOnSurface"
                    android:textStyle="bold"
                    android:maxLines="1"
                    android:ellipsize="end" />

                <TextView
                    android:id="@+id/tvConfigStats"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="0/20"
                    android:textAppearance="?attr/textAppearanceBodySmall"
                    android:textColor="?attr/colorPrimary"
                    android:textStyle="bold"
                    android:layout_marginStart="8dp" />

            </LinearLayout>

            <!-- 第二行：描述 + 时间 -->
            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="horizontal"
                android:layout_marginTop="2dp"
                android:gravity="center_vertical">

                <TextView
                    android:id="@+id/tvConfigDescription"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_weight="1"
                    android:text="配置描述"
                    android:textAppearance="?attr/textAppearanceBodySmall"
                    android:textColor="?attr/colorOnSurfaceVariant"
                    android:maxLines="1"
                    android:ellipsize="end" />

                <TextView
                    android:id="@+id/tvConfigTime"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="16:33"
                    android:textAppearance="?attr/textAppearanceBodySmall"
                    android:textColor="?attr/colorOnSurfaceVariant"
                    android:layout_marginStart="8dp" />

            </LinearLayout>

            <!-- 隐藏的当前配置指示器 -->
            <ImageView
                android:id="@+id/ivCurrentIndicator"
                android:layout_width="0dp"
                android:layout_height="0dp"
                android:visibility="gone" />

        </LinearLayout>

        <!-- 操作按钮 - 横向排列 -->
        <LinearLayout
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:orientation="horizontal"
            android:layout_marginStart="12dp">

            <ImageButton
                android:id="@+id/btnEditConfig"
                android:layout_width="32dp"
                android:layout_height="32dp"
                android:src="@drawable/ic_settings_24"
                android:background="?attr/selectableItemBackgroundBorderless"
                android:contentDescription="编辑配置"
                app:tint="?attr/colorOnSurfaceVariant" />

            <ImageButton
                android:id="@+id/btnExportConfig"
                android:layout_width="32dp"
                android:layout_height="32dp"
                android:src="@drawable/ic_export_24"
                android:background="?attr/selectableItemBackgroundBorderless"
                android:contentDescription="导出配置"
                app:tint="?attr/colorOnSurfaceVariant"
                android:layout_marginStart="4dp" />

            <ImageButton
                android:id="@+id/btnDeleteConfig"
                android:layout_width="32dp"
                android:layout_height="32dp"
                android:src="@drawable/ic_close_24"
                android:background="?attr/selectableItemBackgroundBorderless"
                android:contentDescription="删除配置"
                app:tint="?attr/colorError"
                android:layout_marginStart="4dp" />

        </LinearLayout>

    </LinearLayout>

</com.google.android.material.card.MaterialCardView>
