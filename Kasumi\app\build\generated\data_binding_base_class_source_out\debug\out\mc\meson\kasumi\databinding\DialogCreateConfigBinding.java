// Generated by view binder compiler. Do not edit!
package mc.meson.kasumi.databinding;

import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.LinearLayout;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.viewbinding.ViewBinding;
import androidx.viewbinding.ViewBindings;
import com.google.android.material.textfield.TextInputEditText;
import java.lang.NullPointerException;
import java.lang.Override;
import java.lang.String;
import mc.meson.kasumi.R;

public final class DialogCreateConfigBinding implements ViewBinding {
  @NonNull
  private final LinearLayout rootView;

  @NonNull
  public final TextInputEditText etConfigDescription;

  @NonNull
  public final TextInputEditText etConfigName;

  private DialogCreateConfigBinding(@NonNull LinearLayout rootView,
      @NonNull TextInputEditText etConfigDescription, @NonNull TextInputEditText etConfigName) {
    this.rootView = rootView;
    this.etConfigDescription = etConfigDescription;
    this.etConfigName = etConfigName;
  }

  @Override
  @NonNull
  public LinearLayout getRoot() {
    return rootView;
  }

  @NonNull
  public static DialogCreateConfigBinding inflate(@NonNull LayoutInflater inflater) {
    return inflate(inflater, null, false);
  }

  @NonNull
  public static DialogCreateConfigBinding inflate(@NonNull LayoutInflater inflater,
      @Nullable ViewGroup parent, boolean attachToParent) {
    View root = inflater.inflate(R.layout.dialog_create_config, parent, false);
    if (attachToParent) {
      parent.addView(root);
    }
    return bind(root);
  }

  @NonNull
  public static DialogCreateConfigBinding bind(@NonNull View rootView) {
    // The body of this method is generated in a way you would not otherwise write.
    // This is done to optimize the compiled bytecode for size and performance.
    int id;
    missingId: {
      id = R.id.etConfigDescription;
      TextInputEditText etConfigDescription = ViewBindings.findChildViewById(rootView, id);
      if (etConfigDescription == null) {
        break missingId;
      }

      id = R.id.etConfigName;
      TextInputEditText etConfigName = ViewBindings.findChildViewById(rootView, id);
      if (etConfigName == null) {
        break missingId;
      }

      return new DialogCreateConfigBinding((LinearLayout) rootView, etConfigDescription,
          etConfigName);
    }
    String missingId = rootView.getResources().getResourceName(id);
    throw new NullPointerException("Missing required view with ID: ".concat(missingId));
  }
}
