package mc.meson.kasumi.config

import android.app.Activity
import android.content.Intent
import android.os.Bundle
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import androidx.activity.result.contract.ActivityResultContracts
import androidx.fragment.app.Fragment
import androidx.navigation.fragment.findNavController
import androidx.recyclerview.widget.LinearLayoutManager
import com.google.android.material.dialog.MaterialAlertDialogBuilder
import com.google.android.material.snackbar.Snackbar
import com.google.android.material.textfield.TextInputEditText
import mc.meson.kasumi.R
import mc.meson.kasumi.databinding.FragmentConfigManagementBinding

/**
 * 配置管理页面
 * 支持创建、删除、切换、导入、导出配置
 */
class ConfigManagementFragment : Fragment(), ConfigChangeListener {
    
    private var _binding: FragmentConfigManagementBinding? = null
    private val binding get() = _binding!!
    
    private lateinit var configAdapter: ConfigAdapter
    
    // 文件选择器
    private val exportLauncher = registerForActivityResult(
        ActivityResultContracts.StartActivityForResult()
    ) { result ->
        if (result.resultCode == Activity.RESULT_OK) {
            result.data?.data?.let { uri ->
                handleExport(uri)
            }
        }
    }
    
    private val importLauncher = registerForActivityResult(
        ActivityResultContracts.StartActivityForResult()
    ) { result ->
        if (result.resultCode == Activity.RESULT_OK) {
            result.data?.data?.let { uri ->
                handleImport(uri)
            }
        }
    }
    
    private var pendingExportConfig: ConfigData? = null
    
    override fun onCreateView(
        inflater: LayoutInflater,
        container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View {
        _binding = FragmentConfigManagementBinding.inflate(inflater, container, false)
        return binding.root
    }
    
    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)

        ConfigManager.addListener(this)
        setupRecyclerView()
        setupListeners()
        updateUI()
    }
    
    private fun setupRecyclerView() {
        configAdapter = ConfigAdapter(
            configs = ConfigManager.getAllConfigs(),
            currentConfigId = ConfigManager.getCurrentConfigId(),
            onConfigClick = { config ->
                switchToConfig(config)
            },
            onConfigEdit = { config ->
                editConfig(config)
            },
            onConfigDelete = { config ->
                deleteConfig(config)
            },
            onConfigExport = { config ->
                exportConfig(config)
            }
        )
        
        binding.recyclerViewConfigs.apply {
            layoutManager = LinearLayoutManager(requireContext())
            adapter = configAdapter
        }
    }
    
    private fun setupListeners() {
        // 返回按钮
        binding.btnBack.setOnClickListener {
            findNavController().navigateUp()
        }
        
        // 创建新配置
        binding.btnCreateConfig.setOnClickListener {
            showCreateConfigDialog()
        }
        
        // 导入配置
        binding.btnImportConfig.setOnClickListener {
            importConfigs()
        }
        
        // 导出所有配置
        binding.btnExportAll.setOnClickListener {
            exportAllConfigs()
        }
    }
    
    private fun updateUI() {
        val configs = ConfigManager.getAllConfigs()
        val currentConfig = ConfigManager.getCurrentConfig()

        // 更新当前配置信息
        if (currentConfig != null) {
            binding.tvCurrentConfigName.text = currentConfig.name
            binding.tvCurrentConfigDescription.text = currentConfig.description
            binding.tvCurrentConfigStats.text =
                "${currentConfig.getEnabledModuleCount()}/${currentConfig.getTotalModuleCount()} 功能已启用"
        }

        // 更新配置列表
        configAdapter.updateConfigs(configs, ConfigManager.getCurrentConfigId())

        // 更新空状态
        if (configs.isEmpty()) {
            binding.recyclerViewConfigs.visibility = View.GONE
            binding.layoutEmptyState.visibility = View.VISIBLE
        } else {
            binding.recyclerViewConfigs.visibility = View.VISIBLE
            binding.layoutEmptyState.visibility = View.GONE
        }
    }
    
    private fun switchToConfig(config: ConfigData) {
        if (ConfigManager.setCurrentConfig(config.id)) {
            Snackbar.make(binding.root, "已切换到配置: ${config.name}", Snackbar.LENGTH_SHORT)
                .setBackgroundTint(requireContext().getColor(R.color.minecraft_green))
                .setTextColor(requireContext().getColor(R.color.white))
                .show()
        } else {
            Snackbar.make(binding.root, "切换配置失败", Snackbar.LENGTH_SHORT)
                .setBackgroundTint(requireContext().getColor(R.color.minecraft_red))
                .setTextColor(requireContext().getColor(R.color.white))
                .show()
        }
    }
    
    private fun editConfig(config: ConfigData) {
        showEditConfigDialog(config)
    }
    
    private fun deleteConfig(config: ConfigData) {
        MaterialAlertDialogBuilder(requireContext())
            .setTitle("删除配置")
            .setMessage("确定要删除配置 \"${config.name}\" 吗？此操作无法撤销。")
            .setPositiveButton("删除") { _, _ ->
                if (ConfigManager.deleteConfig(config.id)) {
                    Snackbar.make(binding.root, "配置已删除", Snackbar.LENGTH_SHORT)
                        .setBackgroundTint(requireContext().getColor(R.color.minecraft_red))
                        .setTextColor(requireContext().getColor(R.color.white))
                        .show()
                }
            }
            .setNegativeButton("取消", null)
            .show()
    }
    
    private fun exportConfig(config: ConfigData) {
        pendingExportConfig = config
        val intent = Intent(Intent.ACTION_CREATE_DOCUMENT).apply {
            addCategory(Intent.CATEGORY_OPENABLE)
            type = "application/json"
            putExtra(Intent.EXTRA_TITLE, ConfigImportExport.generateExportFileName(config))
        }
        exportLauncher.launch(intent)
    }
    
    private fun exportAllConfigs() {
        val intent = Intent(Intent.ACTION_CREATE_DOCUMENT).apply {
            addCategory(Intent.CATEGORY_OPENABLE)
            type = "application/json"
            putExtra(Intent.EXTRA_TITLE, ConfigImportExport.generateBatchExportFileName())
        }
        exportLauncher.launch(intent)
    }
    
    private fun importConfigs() {
        val intent = Intent(Intent.ACTION_OPEN_DOCUMENT).apply {
            addCategory(Intent.CATEGORY_OPENABLE)
            type = "application/json"
        }
        importLauncher.launch(intent)
    }
    
    private fun handleExport(uri: android.net.Uri) {
        val success = if (pendingExportConfig != null) {
            ConfigImportExport.exportConfig(requireContext(), pendingExportConfig!!, uri)
        } else {
            ConfigImportExport.exportConfigs(requireContext(), ConfigManager.getAllConfigs(), uri)
        }
        
        val message = if (success) "导出成功" else "导出失败"
        val color = if (success) R.color.minecraft_green else R.color.minecraft_red
        
        Snackbar.make(binding.root, message, Snackbar.LENGTH_SHORT)
            .setBackgroundTint(requireContext().getColor(color))
            .setTextColor(requireContext().getColor(R.color.white))
            .show()
        
        pendingExportConfig = null
    }
    
    private fun handleImport(uri: android.net.Uri) {
        when (val result = ConfigImportExport.importConfig(requireContext(), uri)) {
            is ImportResult.Success -> {
                val processedConfigs = ConfigImportExport.processImportedConfigs(result.configs)
                processedConfigs.forEach { config ->
                    ConfigManager.saveConfig(config)
                }
                
                Snackbar.make(binding.root, "成功导入 ${processedConfigs.size} 个配置", Snackbar.LENGTH_SHORT)
                    .setBackgroundTint(requireContext().getColor(R.color.minecraft_green))
                    .setTextColor(requireContext().getColor(R.color.white))
                    .show()
            }
            is ImportResult.Error -> {
                Snackbar.make(binding.root, "导入失败: ${result.message}", Snackbar.LENGTH_LONG)
                    .setBackgroundTint(requireContext().getColor(R.color.minecraft_red))
                    .setTextColor(requireContext().getColor(R.color.white))
                    .show()
            }
        }
    }
    
    private fun showCreateConfigDialog() {
        val dialogView = LayoutInflater.from(requireContext())
            .inflate(R.layout.dialog_create_config, null)
        
        val etName = dialogView.findViewById<TextInputEditText>(R.id.etConfigName)
        val etDescription = dialogView.findViewById<TextInputEditText>(R.id.etConfigDescription)
        
        MaterialAlertDialogBuilder(requireContext())
            .setTitle("创建新配置")
            .setView(dialogView)
            .setPositiveButton("创建") { _, _ ->
                val name = etName.text.toString().trim()
                val description = etDescription.text.toString().trim()
                
                if (name.isNotEmpty()) {
                    val config = ConfigManager.createConfig(name, description)
                    Snackbar.make(binding.root, "配置 \"${config.name}\" 已创建", Snackbar.LENGTH_SHORT)
                        .setBackgroundTint(requireContext().getColor(R.color.minecraft_green))
                        .setTextColor(requireContext().getColor(R.color.white))
                        .show()
                }
            }
            .setNegativeButton("取消", null)
            .show()
    }
    
    private fun showEditConfigDialog(config: ConfigData) {
        val dialogView = LayoutInflater.from(requireContext())
            .inflate(R.layout.dialog_create_config, null)
        
        val etName = dialogView.findViewById<TextInputEditText>(R.id.etConfigName)
        val etDescription = dialogView.findViewById<TextInputEditText>(R.id.etConfigDescription)
        
        etName.setText(config.name)
        etDescription.setText(config.description)
        
        MaterialAlertDialogBuilder(requireContext())
            .setTitle("编辑配置")
            .setView(dialogView)
            .setPositiveButton("保存") { _, _ ->
                val name = etName.text.toString().trim()
                val description = etDescription.text.toString().trim()
                
                if (name.isNotEmpty()) {
                    val updatedConfig = config.copy(name = name, description = description)
                    ConfigManager.saveConfig(updatedConfig)
                    Snackbar.make(binding.root, "配置已更新", Snackbar.LENGTH_SHORT)
                        .setBackgroundTint(requireContext().getColor(R.color.minecraft_green))
                        .setTextColor(requireContext().getColor(R.color.white))
                        .show()
                }
            }
            .setNegativeButton("取消", null)
            .show()
    }
    
    override fun onConfigChanged() {
        updateUI()
    }
    
    override fun onCurrentConfigChanged(config: ConfigData?) {
        updateUI()
    }
    
    override fun onDestroyView() {
        super.onDestroyView()
        ConfigManager.removeListener(this)
        _binding = null
    }
}
