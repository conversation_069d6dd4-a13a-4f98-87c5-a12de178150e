<?xml version="1.0" encoding="utf-8"?>
<androidx.cardview.widget.CardView xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="210dp"
    android:layout_height="wrap_content"
    android:alpha="0.94"
    app:cardCornerRadius="16dp"
    app:cardElevation="12dp"
    app:cardBackgroundColor="@color/md_theme_light_primaryContainer">

    <!-- 收起状态的图标 -->
    <ImageView
        android:id="@+id/collapsedDot"
        android:layout_width="24dp"
        android:layout_height="24dp"
        android:layout_gravity="center"
        android:src="@drawable/ic_kasumi_floating"
        android:visibility="gone"
        android:background="?attr/selectableItemBackgroundBorderless"
        android:clickable="true"
        android:focusable="true" />

    <!-- 精简模式内容 -->
    <LinearLayout
        android:id="@+id/compactContent"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="vertical"
        android:padding="12dp">

        <!-- 顶部操作栏 -->
        <LinearLayout
            android:id="@+id/topBar"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="horizontal"
            android:gravity="center_vertical"
            android:layout_marginBottom="8dp">

            <!-- 展开按钮 -->
            <ImageView
                android:id="@+id/btnExpand"
                android:layout_width="18dp"
                android:layout_height="18dp"
                android:src="@drawable/ic_zoom_out_map"
                android:tint="@color/md_theme_light_onPrimaryContainer"
                android:background="?attr/selectableItemBackgroundBorderless"
                android:clickable="true"
                android:focusable="true"
                android:layout_marginEnd="6dp" />

            <!-- 分类指示器 -->
            <TextView
                android:id="@+id/tvCategoryName"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:text="战斗类"
                android:textSize="11sp"
                android:textStyle="bold"
                android:textColor="@color/md_theme_light_onPrimaryContainer"
                android:gravity="center" />

            <!-- 收起按钮 -->
            <ImageView
                android:id="@+id/btnCollapse"
                android:layout_width="18dp"
                android:layout_height="18dp"
                android:src="@drawable/ic_close"
                android:tint="@color/md_theme_light_onPrimaryContainer"
                android:background="?attr/selectableItemBackgroundBorderless"
                android:clickable="true"
                android:focusable="true" />

        </LinearLayout>

        <!-- ViewPager2 用于流畅的分类切换 -->
        <androidx.viewpager2.widget.ViewPager2
            android:id="@+id/vpCompactCategories"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginBottom="8dp"
            android:orientation="horizontal" />

        <!-- 分类切换指示器 -->
        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="horizontal"
            android:gravity="center">

            <View
                android:id="@+id/indicatorCombat"
                android:layout_width="5dp"
                android:layout_height="5dp"
                android:layout_margin="2dp"
                android:background="@drawable/category_indicator"
                android:backgroundTint="@color/md_theme_light_primary" />

            <View
                android:id="@+id/indicatorMovement"
                android:layout_width="5dp"
                android:layout_height="5dp"
                android:layout_margin="2dp"
                android:background="@drawable/category_indicator"
                android:backgroundTint="@color/md_theme_light_outline" />

            <View
                android:id="@+id/indicatorWorld"
                android:layout_width="5dp"
                android:layout_height="5dp"
                android:layout_margin="2dp"
                android:background="@drawable/category_indicator"
                android:backgroundTint="@color/md_theme_light_outline" />

            <View
                android:id="@+id/indicatorPlayer"
                android:layout_width="5dp"
                android:layout_height="5dp"
                android:layout_margin="2dp"
                android:background="@drawable/category_indicator"
                android:backgroundTint="@color/md_theme_light_outline" />

            <View
                android:id="@+id/indicatorVisual"
                android:layout_width="5dp"
                android:layout_height="5dp"
                android:layout_margin="2dp"
                android:background="@drawable/category_indicator"
                android:backgroundTint="@color/md_theme_light_outline" />

        </LinearLayout>

    </LinearLayout>

</androidx.cardview.widget.CardView>
