<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:orientation="vertical"
    android:background="?attr/colorSurface">

    <!-- Simple Header -->
    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="vertical"
        android:background="?attr/colorPrimary"
        android:paddingBottom="24dp">

        <!-- Toolbar -->
        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="56dp"
            android:orientation="horizontal"
            android:gravity="center_vertical"
            android:paddingHorizontal="16dp">

            <ImageButton
                android:id="@+id/btnBack"
                android:layout_width="48dp"
                android:layout_height="48dp"
                android:src="@drawable/ic_arrow_back_24"
                android:background="?attr/selectableItemBackgroundBorderless"
                android:contentDescription="返回"
                app:tint="?attr/colorOnPrimary" />

            <TextView
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:text="模块设置"
                android:textAppearance="?attr/textAppearanceTitleLarge"
                android:textColor="?attr/colorOnPrimary"
                android:layout_marginStart="8dp" />

            <com.google.android.material.materialswitch.MaterialSwitch
                android:id="@+id/switchModuleEnabled"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                app:thumbTint="?attr/colorOnPrimary"
                app:trackTint="?attr/colorPrimaryContainer" />

        </LinearLayout>

        <!-- Module Info -->
        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="horizontal"
            android:gravity="center_vertical"
            android:paddingHorizontal="24dp"
            android:paddingTop="8dp">

            <ImageView
                android:id="@+id/ivModuleIcon"
                android:layout_width="48dp"
                android:layout_height="48dp"
                android:src="@drawable/ic_features_24"
                app:tint="?attr/colorOnPrimary" />

            <LinearLayout
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:orientation="vertical"
                android:layout_marginStart="16dp">

                <TextView
                    android:id="@+id/tvModuleName"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="模块名称"
                    android:textAppearance="?attr/textAppearanceHeadlineSmall"
                    android:textColor="?attr/colorOnPrimary"
                    android:textStyle="bold" />

                <LinearLayout
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:orientation="horizontal"
                    android:gravity="center_vertical"
                    android:layout_marginTop="4dp">

                    <View
                        android:id="@+id/viewCategoryIndicator"
                        android:layout_width="6dp"
                        android:layout_height="6dp"
                        android:background="@drawable/ic_circle"
                        app:backgroundTint="?attr/colorOnPrimary" />

                    <TextView
                        android:id="@+id/tvModuleCategory"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="分类"
                        android:textAppearance="?attr/textAppearanceBodyMedium"
                        android:textColor="?attr/colorOnPrimary"
                        android:layout_marginStart="8dp" />

                </LinearLayout>

            </LinearLayout>

        </LinearLayout>

    </LinearLayout>

    <!-- Content -->
    <androidx.core.widget.NestedScrollView
        android:layout_width="match_parent"
        android:layout_height="0dp"
        android:layout_weight="1"
        android:fillViewport="true">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="vertical"
            android:padding="16dp">

            <!-- Module Description -->
            <TextView
                android:id="@+id/tvModuleDescription"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:text="模块描述"
                android:textAppearance="?attr/textAppearanceBodyLarge"
                android:textColor="?attr/colorOnSurfaceVariant"
                android:layout_marginBottom="24dp"
                android:lineSpacingExtra="2dp" />

            <!-- Settings List -->
            <androidx.recyclerview.widget.RecyclerView
                android:id="@+id/recyclerViewSettings"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:nestedScrollingEnabled="false" />

            <!-- No Settings State -->
            <LinearLayout
                android:id="@+id/layoutNoSettings"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="vertical"
                android:gravity="center"
                android:padding="48dp"
                android:visibility="gone">

                <ImageView
                    android:layout_width="64dp"
                    android:layout_height="64dp"
                    android:src="@drawable/ic_settings_24"
                    android:alpha="0.5"
                    app:tint="?attr/colorOnSurfaceVariant" />

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="此模块没有可配置的设置"
                    android:textAppearance="?attr/textAppearanceTitleMedium"
                    android:textColor="?attr/colorOnSurfaceVariant"
                    android:layout_marginTop="16dp" />

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="您可以通过上方的开关来启用或禁用此模块"
                    android:textAppearance="?attr/textAppearanceBodyMedium"
                    android:textColor="?attr/colorOnSurfaceVariant"
                    android:layout_marginTop="8dp"
                    android:gravity="center" />

            </LinearLayout>

            <!-- Reset Button -->
            <com.google.android.material.button.MaterialButton
                android:id="@+id/btnResetSettings"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:text="重置为默认设置"
                android:layout_marginTop="24dp"
                style="@style/Widget.Material3.Button.OutlinedButton" />

        </LinearLayout>

    </androidx.core.widget.NestedScrollView>

</LinearLayout>
