<?xml version="1.0" encoding="utf-8"?>
<selector xmlns:android="http://schemas.android.com/apk/res/android">

    <!-- 选中状态 - 使用和功能卡片一样的棕色背景 -->
    <item android:state_selected="true">
        <shape android:shape="rectangle">
            <corners android:radius="12dp" />
            <solid android:color="@color/md_theme_light_primary" />
        </shape>
    </item>

    <!-- 按下状态 - 轻微反馈 -->
    <item android:state_pressed="true">
        <shape android:shape="rectangle">
            <corners android:radius="12dp" />
            <solid android:color="@color/md_theme_light_primaryContainer" />
        </shape>
    </item>

    <!-- 默认状态 - 透明 -->
    <item>
        <shape android:shape="rectangle">
            <corners android:radius="12dp" />
            <solid android:color="@android:color/transparent" />
        </shape>
    </item>

</selector>
