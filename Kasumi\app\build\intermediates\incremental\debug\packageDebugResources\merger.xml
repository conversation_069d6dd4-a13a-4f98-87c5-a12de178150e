<?xml version="1.0" encoding="utf-8"?>
<merger version="3" xmlns:ns1="http://schemas.android.com/tools"><dataSet aapt-namespace="http://schemas.android.com/apk/res-auto" config="main$Generated" generated="true" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="D:\Kasumi\Kasumi\app\src\main\res"/></dataSet><dataSet aapt-namespace="http://schemas.android.com/apk/res-auto" config="main" generated-set="main$Generated" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="D:\Kasu<PERSON>\Kasumi\app\src\main\res"><file name="bounce" path="D:\Kasumi\Kasumi\app\src\main\res\anim\bounce.xml" qualifiers="" type="anim"/><file name="fade_in" path="D:\Kasumi\Kasumi\app\src\main\res\anim\fade_in.xml" qualifiers="" type="anim"/><file name="fade_out" path="D:\Kasumi\Kasumi\app\src\main\res\anim\fade_out.xml" qualifiers="" type="anim"/><file name="fragment_fade_enter" path="D:\Kasumi\Kasumi\app\src\main\res\anim\fragment_fade_enter.xml" qualifiers="" type="anim"/><file name="fragment_fade_exit" path="D:\Kasumi\Kasumi\app\src\main\res\anim\fragment_fade_exit.xml" qualifiers="" type="anim"/><file name="slide_in_right" path="D:\Kasumi\Kasumi\app\src\main\res\anim\slide_in_right.xml" qualifiers="" type="anim"/><file name="slide_out_left" path="D:\Kasumi\Kasumi\app\src\main\res\anim\slide_out_left.xml" qualifiers="" type="anim"/><file name="bottom_nav_item_color" path="D:\Kasumi\Kasumi\app\src\main\res\color\bottom_nav_item_color.xml" qualifiers="" type="color"/><file name="button_ripple" path="D:\Kasumi\Kasumi\app\src\main\res\drawable\button_ripple.xml" qualifiers="" type="drawable"/><file name="card_background" path="D:\Kasumi\Kasumi\app\src\main\res\drawable\card_background.xml" qualifiers="" type="drawable"/><file name="category_indicator" path="D:\Kasumi\Kasumi\app\src\main\res\drawable\category_indicator.xml" qualifiers="" type="drawable"/><file name="custom_scrollbar_thumb" path="D:\Kasumi\Kasumi\app\src\main\res\drawable\custom_scrollbar_thumb.xml" qualifiers="" type="drawable"/><file name="custom_scrollbar_track" path="D:\Kasumi\Kasumi\app\src\main\res\drawable\custom_scrollbar_track.xml" qualifiers="" type="drawable"/><file name="drag_indicator" path="D:\Kasumi\Kasumi\app\src\main\res\drawable\drag_indicator.xml" qualifiers="" type="drawable"/><file name="floating_button_background" path="D:\Kasumi\Kasumi\app\src\main\res\drawable\floating_button_background.xml" qualifiers="" type="drawable"/><file name="floating_button_ripple" path="D:\Kasumi\Kasumi\app\src\main\res\drawable\floating_button_ripple.xml" qualifiers="" type="drawable"/><file name="floating_button_style" path="D:\Kasumi\Kasumi\app\src\main\res\drawable\floating_button_style.xml" qualifiers="" type="drawable"/><file name="floating_divider" path="D:\Kasumi\Kasumi\app\src\main\res\drawable\floating_divider.xml" qualifiers="" type="drawable"/><file name="floating_item_background" path="D:\Kasumi\Kasumi\app\src\main\res\drawable\floating_item_background.xml" qualifiers="" type="drawable"/><file name="ic_arrow_back_24" path="D:\Kasumi\Kasumi\app\src\main\res\drawable\ic_arrow_back_24.xml" qualifiers="" type="drawable"/><file name="ic_arrow_drop_down_24" path="D:\Kasumi\Kasumi\app\src\main\res\drawable\ic_arrow_drop_down_24.xml" qualifiers="" type="drawable"/><file name="ic_check_circle_24" path="D:\Kasumi\Kasumi\app\src\main\res\drawable\ic_check_circle_24.xml" qualifiers="" type="drawable"/><file name="ic_close" path="D:\Kasumi\Kasumi\app\src\main\res\drawable\ic_close.xml" qualifiers="" type="drawable"/><file name="ic_close_24" path="D:\Kasumi\Kasumi\app\src\main\res\drawable\ic_close_24.xml" qualifiers="" type="drawable"/><file name="ic_collapse_24" path="D:\Kasumi\Kasumi\app\src\main\res\drawable\ic_collapse_24.xml" qualifiers="" type="drawable"/><file name="ic_compress" path="D:\Kasumi\Kasumi\app\src\main\res\drawable\ic_compress.xml" qualifiers="" type="drawable"/><file name="ic_expand_more" path="D:\Kasumi\Kasumi\app\src\main\res\drawable\ic_expand_more.xml" qualifiers="" type="drawable"/><file name="ic_features_24" path="D:\Kasumi\Kasumi\app\src\main\res\drawable\ic_features_24.xml" qualifiers="" type="drawable"/><file name="ic_home_24" path="D:\Kasumi\Kasumi\app\src\main\res\drawable\ic_home_24.xml" qualifiers="" type="drawable"/><file name="ic_kasumi_floating" path="D:\Kasumi\Kasumi\app\src\main\res\drawable\ic_kasumi_floating.xml" qualifiers="" type="drawable"/><file name="ic_kasumi_logo" path="D:\Kasumi\Kasumi\app\src\main\res\drawable\ic_kasumi_logo.xml" qualifiers="" type="drawable"/><file name="ic_launcher_background" path="D:\Kasumi\Kasumi\app\src\main\res\drawable\ic_launcher_background.xml" qualifiers="" type="drawable"/><file name="ic_launcher_foreground" path="D:\Kasumi\Kasumi\app\src\main\res\drawable\ic_launcher_foreground.xml" qualifiers="" type="drawable"/><file name="ic_resize" path="D:\Kasumi\Kasumi\app\src\main\res\drawable\ic_resize.xml" qualifiers="" type="drawable"/><file name="ic_settings_24" path="D:\Kasumi\Kasumi\app\src\main\res\drawable\ic_settings_24.xml" qualifiers="" type="drawable"/><file name="ic_zoom_out_map" path="D:\Kasumi\Kasumi\app\src\main\res\drawable\ic_zoom_out_map.xml" qualifiers="" type="drawable"/><file name="mode_option_indicator" path="D:\Kasumi\Kasumi\app\src\main\res\drawable\mode_option_indicator.xml" qualifiers="" type="drawable"/><file name="resize_handle_background" path="D:\Kasumi\Kasumi\app\src\main\res\drawable\resize_handle_background.xml" qualifiers="" type="drawable"/><file name="settings_panel_background" path="D:\Kasumi\Kasumi\app\src\main\res\drawable\settings_panel_background.xml" qualifiers="" type="drawable"/><file name="splash_gradient" path="D:\Kasumi\Kasumi\app\src\main\res\drawable\splash_gradient.xml" qualifiers="" type="drawable"/><file name="status_indicator" path="D:\Kasumi\Kasumi\app\src\main\res\drawable\status_indicator.xml" qualifiers="" type="drawable"/><file name="tab_background_selector" path="D:\Kasumi\Kasumi\app\src\main\res\drawable\tab_background_selector.xml" qualifiers="" type="drawable"/><file name="tab_indicator_rounded" path="D:\Kasumi\Kasumi\app\src\main\res\drawable\tab_indicator_rounded.xml" qualifiers="" type="drawable"/><file name="tab_indicator_simple" path="D:\Kasumi\Kasumi\app\src\main\res\drawable\tab_indicator_simple.xml" qualifiers="" type="drawable"/><file name="tab_layout_background" path="D:\Kasumi\Kasumi\app\src\main\res\drawable\tab_layout_background.xml" qualifiers="" type="drawable"/><file name="activity_main" path="D:\Kasumi\Kasumi\app\src\main\res\layout\activity_main.xml" qualifiers="" type="layout"/><file name="activity_main_with_navigation" path="D:\Kasumi\Kasumi\app\src\main\res\layout\activity_main_with_navigation.xml" qualifiers="" type="layout"/><file name="activity_splash" path="D:\Kasumi\Kasumi\app\src\main\res\layout\activity_splash.xml" qualifiers="" type="layout"/><file name="compact_category_page" path="D:\Kasumi\Kasumi\app\src\main\res\layout\compact_category_page.xml" qualifiers="" type="layout"/><file name="dialog_create_config" path="D:\Kasumi\Kasumi\app\src\main\res\layout\dialog_create_config.xml" qualifiers="" type="layout"/><file name="floating_window_compact_v2" path="D:\Kasumi\Kasumi\app\src\main\res\layout\floating_window_compact_v2.xml" qualifiers="" type="layout"/><file name="floating_window_expanded_new" path="D:\Kasumi\Kasumi\app\src\main\res\layout\floating_window_expanded_new.xml" qualifiers="" type="layout"/><file name="fragment_config_management" path="D:\Kasumi\Kasumi\app\src\main\res\layout\fragment_config_management.xml" qualifiers="" type="layout"/><file name="fragment_hack_features" path="D:\Kasumi\Kasumi\app\src\main\res\layout\fragment_hack_features.xml" qualifiers="" type="layout"/><file name="fragment_home" path="D:\Kasumi\Kasumi\app\src\main\res\layout\fragment_home.xml" qualifiers="" type="layout"/><file name="fragment_module_settings" path="D:\Kasumi\Kasumi\app\src\main\res\layout\fragment_module_settings.xml" qualifiers="" type="layout"/><file name="fragment_settings" path="D:\Kasumi\Kasumi\app\src\main\res\layout\fragment_settings.xml" qualifiers="" type="layout"/><file name="item_compact_module" path="D:\Kasumi\Kasumi\app\src\main\res\layout\item_compact_module.xml" qualifiers="" type="layout"/><file name="item_config" path="D:\Kasumi\Kasumi\app\src\main\res\layout\item_config.xml" qualifiers="" type="layout"/><file name="item_expanded_module" path="D:\Kasumi\Kasumi\app\src\main\res\layout\item_expanded_module.xml" qualifiers="" type="layout"/><file name="item_mode_option" path="D:\Kasumi\Kasumi\app\src\main\res\layout\item_mode_option.xml" qualifiers="" type="layout"/><file name="item_module_grid" path="D:\Kasumi\Kasumi\app\src\main\res\layout\item_module_grid.xml" qualifiers="" type="layout"/><file name="item_module_list" path="D:\Kasumi\Kasumi\app\src\main\res\layout\item_module_list.xml" qualifiers="" type="layout"/><file name="item_setting_mode" path="D:\Kasumi\Kasumi\app\src\main\res\layout\item_setting_mode.xml" qualifiers="" type="layout"/><file name="item_setting_slider" path="D:\Kasumi\Kasumi\app\src\main\res\layout\item_setting_slider.xml" qualifiers="" type="layout"/><file name="item_setting_toggle" path="D:\Kasumi\Kasumi\app\src\main\res\layout\item_setting_toggle.xml" qualifiers="" type="layout"/><file name="module_settings_panel" path="D:\Kasumi\Kasumi\app\src\main\res\layout\module_settings_panel.xml" qualifiers="" type="layout"/><file name="bottom_navigation_menu" path="D:\Kasumi\Kasumi\app\src\main\res\menu\bottom_navigation_menu.xml" qualifiers="" type="menu"/><file name="ic_launcher" path="D:\Kasumi\Kasumi\app\src\main\res\mipmap-anydpi\ic_launcher.xml" qualifiers="anydpi-v4" type="mipmap"/><file name="ic_launcher_round" path="D:\Kasumi\Kasumi\app\src\main\res\mipmap-anydpi\ic_launcher_round.xml" qualifiers="anydpi-v4" type="mipmap"/><file name="ic_launcher" path="D:\Kasumi\Kasumi\app\src\main\res\mipmap-hdpi\ic_launcher.webp" qualifiers="hdpi-v4" type="mipmap"/><file name="ic_launcher_round" path="D:\Kasumi\Kasumi\app\src\main\res\mipmap-hdpi\ic_launcher_round.webp" qualifiers="hdpi-v4" type="mipmap"/><file name="ic_launcher" path="D:\Kasumi\Kasumi\app\src\main\res\mipmap-mdpi\ic_launcher.webp" qualifiers="mdpi-v4" type="mipmap"/><file name="ic_launcher_round" path="D:\Kasumi\Kasumi\app\src\main\res\mipmap-mdpi\ic_launcher_round.webp" qualifiers="mdpi-v4" type="mipmap"/><file name="ic_launcher" path="D:\Kasumi\Kasumi\app\src\main\res\mipmap-xhdpi\ic_launcher.webp" qualifiers="xhdpi-v4" type="mipmap"/><file name="ic_launcher_round" path="D:\Kasumi\Kasumi\app\src\main\res\mipmap-xhdpi\ic_launcher_round.webp" qualifiers="xhdpi-v4" type="mipmap"/><file name="ic_launcher" path="D:\Kasumi\Kasumi\app\src\main\res\mipmap-xxhdpi\ic_launcher.webp" qualifiers="xxhdpi-v4" type="mipmap"/><file name="ic_launcher_round" path="D:\Kasumi\Kasumi\app\src\main\res\mipmap-xxhdpi\ic_launcher_round.webp" qualifiers="xxhdpi-v4" type="mipmap"/><file name="ic_launcher" path="D:\Kasumi\Kasumi\app\src\main\res\mipmap-xxxhdpi\ic_launcher.webp" qualifiers="xxxhdpi-v4" type="mipmap"/><file name="ic_launcher_round" path="D:\Kasumi\Kasumi\app\src\main\res\mipmap-xxxhdpi\ic_launcher_round.webp" qualifiers="xxxhdpi-v4" type="mipmap"/><file name="nav_graph" path="D:\Kasumi\Kasumi\app\src\main\res\navigation\nav_graph.xml" qualifiers="" type="navigation"/><file path="D:\Kasumi\Kasumi\app\src\main\res\values\colors.xml" qualifiers=""><color name="md_theme_light_primary">#B8956A</color><color name="md_theme_light_onPrimary">#FFFFFF</color><color name="md_theme_light_primaryContainer">#F7E4D0</color><color name="md_theme_light_onPrimaryContainer">#3D2914</color><color name="md_theme_light_secondary">#8B7355</color><color name="md_theme_light_onSecondary">#FFFFFF</color><color name="md_theme_light_secondaryContainer">#F0E1CC</color><color name="md_theme_light_onSecondaryContainer">#2A1F0F</color><color name="md_theme_light_tertiary">#A67C52</color><color name="md_theme_light_onTertiary">#FFFFFF</color><color name="md_theme_light_tertiaryContainer">#F5DCC0</color><color name="md_theme_light_onTertiaryContainer">#2F1A08</color><color name="md_theme_light_error">#BA1A1A</color><color name="md_theme_light_errorContainer">#FFDAD6</color><color name="md_theme_light_onError">#FFFFFF</color><color name="md_theme_light_onErrorContainer">#410002</color><color name="md_theme_light_background">#FDF9F3</color><color name="md_theme_light_onBackground">#1F1B16</color><color name="md_theme_light_surface">#FDF9F3</color><color name="md_theme_light_onSurface">#1F1B16</color><color name="md_theme_light_surfaceVariant">#F0E6D8</color><color name="md_theme_light_onSurfaceVariant">#4F453A</color><color name="md_theme_light_outline">#7F7368</color><color name="md_theme_light_inverseOnSurface">#F7F2EC</color><color name="md_theme_light_inverseSurface">#343026</color><color name="md_theme_light_inversePrimary">#E6D1A8</color><color name="md_theme_light_shadow">#000000</color><color name="md_theme_light_surfaceTint">#B8956A</color><color name="md_theme_light_outlineVariant">#D4C4B0</color><color name="md_theme_light_scrim">#000000</color><color name="md_theme_dark_primary">#E6D1A8</color><color name="md_theme_dark_onPrimary">#3D2914</color><color name="md_theme_dark_primaryContainer">#6B4E2A</color><color name="md_theme_dark_onPrimaryContainer">#F7E4D0</color><color name="md_theme_dark_secondary">#D4C4B0</color><color name="md_theme_dark_onSecondary">#3A2F20</color><color name="md_theme_dark_secondaryContainer">#524635</color><color name="md_theme_dark_onSecondaryContainer">#F0E1CC</color><color name="md_theme_dark_tertiary">#E0C49A</color><color name="md_theme_dark_onTertiary">#3F2A15</color><color name="md_theme_dark_tertiaryContainer">#5A3F2A</color><color name="md_theme_dark_onTertiaryContainer">#F5DCC0</color><color name="md_theme_dark_error">#FFB4AB</color><color name="md_theme_dark_errorContainer">#93000A</color><color name="md_theme_dark_onError">#690005</color><color name="md_theme_dark_onErrorContainer">#FFDAD6</color><color name="md_theme_dark_background">#1A1611</color><color name="md_theme_dark_onBackground">#E8E2DC</color><color name="md_theme_dark_surface">#1A1611</color><color name="md_theme_dark_onSurface">#E8E2DC</color><color name="md_theme_dark_surfaceVariant">#4F453A</color><color name="md_theme_dark_onSurfaceVariant">#D4C4B0</color><color name="md_theme_dark_outline">#9D8F7E</color><color name="md_theme_dark_inverseOnSurface">#1A1611</color><color name="md_theme_dark_inverseSurface">#E8E2DC</color><color name="md_theme_dark_inversePrimary">#B8956A</color><color name="md_theme_dark_shadow">#000000</color><color name="md_theme_dark_surfaceTint">#E6D1A8</color><color name="md_theme_dark_outlineVariant">#4F453A</color><color name="md_theme_dark_scrim">#000000</color><color name="minecraft_green">#00FF00</color><color name="minecraft_red">#FF5555</color><color name="minecraft_blue">#5555FF</color><color name="minecraft_yellow">#FFFF55</color><color name="minecraft_purple">#AA00AA</color><color name="minecraft_orange">#FFAA00</color><color name="minecraft_green_dark">#00CC00</color><color name="minecraft_red_dark">#CC4444</color><color name="minecraft_blue_dark">#4444CC</color><color name="minecraft_yellow_dark">#CCCC44</color><color name="minecraft_purple_dark">#880088</color><color name="minecraft_orange_dark">#CC8800</color><color name="card_background_primary">#F7E4D0</color><color name="card_background_secondary">#F9E8D4</color><color name="card_background_tertiary">#FBECD8</color><color name="purple_200">#FFBB86FC</color><color name="purple_500">#FF6200EE</color><color name="purple_700">#FF3700B3</color><color name="teal_200">#FF03DAC5</color><color name="teal_700">#FF018786</color><color name="black">#FF000000</color><color name="white">#FFFFFFFF</color></file><file path="D:\Kasumi\Kasumi\app\src\main\res\values\ids.xml" qualifiers=""><item name="btnBackInTitle" type="id"/><item name="tvModuleTitleInTitle" type="id"/></file><file path="D:\Kasumi\Kasumi\app\src\main\res\values\strings.xml" qualifiers=""><string name="app_name">Kasumi</string><string name="welcome_title">欢迎使用 Kasumi</string><string name="welcome_description">强大的Minecraft辅助工具，让你的游戏体验更上一层楼</string><string name="connection_status">连接状态</string><string name="connected">已连接到Minecraft</string><string name="disconnected">未连接</string><string name="connecting">正在连接...</string><string name="settings">设置</string><string name="nav_home">主页</string><string name="nav_features">功能</string><string name="nav_settings">设置</string><string name="combat_features">战斗功能</string><string name="kill_aura">杀戮光环</string><string name="kill_aura_desc">自动攻击附近的敌人</string><string name="auto_clicker">自动点击</string><string name="auto_clicker_desc">自动连续点击攻击</string><string name="reach">攻击距离</string><string name="reach_desc">扩展攻击范围</string><string name="movement_features">移动功能</string><string name="fly">飞行模式</string><string name="fly_desc">在生存模式中飞行</string><string name="speed">速度提升</string><string name="speed_desc">提高移动速度</string><string name="no_fall">无摔落伤害</string><string name="no_fall_desc">免疫摔落伤害</string><string name="general_settings">通用设置</string><string name="dark_mode">深色模式</string><string name="dark_mode_desc">切换应用主题</string><string name="notifications">通知</string><string name="notifications_desc">启用功能状态通知</string><string name="auto_connect">自动连接</string><string name="auto_connect_desc">启动时自动连接Minecraft</string><string name="about">关于</string><string name="version">Kasumi Hack v1.0</string><string name="about_desc">强大的Minecraft辅助工具，采用Material You设计语言，为您提供最佳的用户体验。</string><string name="check_update">检查更新</string><string name="feature_enabled">%s 已启用</string><string name="feature_disabled">%s 已禁用</string><string name="setting_enabled">%s 已启用</string><string name="setting_disabled">%s 已禁用</string><string name="checking_updates">正在检查更新...</string><string name="latest_version">您正在使用最新版本！</string><string name="settings_coming_soon">设置功能即将推出！</string><string name="floating_window_title">游戏内悬浮窗</string><string name="floating_window_desc">在游戏中显示悬浮窗，快速切换hack功能</string><string name="start_floating_window">启动悬浮窗</string><string name="floating_window_started">悬浮窗已启动！可以在游戏中使用了</string><string name="floating_window_permission_needed">需要悬浮窗权限才能使用此功能</string><string name="floating_window_notification_title">Kasumi Hack</string><string name="floating_window_notification_text">悬浮窗正在运行</string></file><file path="D:\Kasumi\Kasumi\app\src\main\res\values\styles.xml" qualifiers=""><style name="App.Widget.BottomNavigationView.ActiveIndicator" parent="Widget.Material3.BottomNavigationView.ActiveIndicator">
        <item name="android:color">?attr/colorPrimaryContainer</item>
    </style><style name="App.Card.Elevated" parent="Widget.Material3.CardView.Elevated">
        <item name="cardElevation">4dp</item>
        <item name="cardCornerRadius">16dp</item>
        <item name="android:layout_margin">8dp</item>
    </style><style name="App.Card.Outlined" parent="Widget.Material3.CardView.Outlined">
        <item name="cardCornerRadius">16dp</item>
        <item name="android:layout_margin">8dp</item>
        <item name="strokeWidth">1dp</item>
        <item name="strokeColor">?attr/colorOutline</item>
    </style><style name="App.Button.Primary" parent="Widget.Material3.Button">
        <item name="backgroundTint">?attr/colorPrimary</item>
        <item name="android:textColor">?attr/colorOnPrimary</item>
        <item name="cornerRadius">12dp</item>
    </style><style name="App.Button.Secondary" parent="Widget.Material3.Button.TonalButton">
        <item name="backgroundTint">?attr/colorSecondaryContainer</item>
        <item name="android:textColor">?attr/colorOnSecondaryContainer</item>
        <item name="cornerRadius">12dp</item>
    </style><style name="App.Switch.Material" parent="Widget.Material3.CompoundButton.MaterialSwitch">
        <item name="thumbTint">?attr/colorPrimary</item>
        <item name="trackTint">?attr/colorSurfaceVariant</item>
    </style><style name="App.Text.Headline" parent="@style/TextAppearance.Material3.HeadlineSmall">
        <item name="android:textColor">?attr/colorOnSurface</item>
        <item name="android:fontFamily">sans-serif-medium</item>
    </style><style name="App.Text.Title" parent="@style/TextAppearance.Material3.TitleMedium">
        <item name="android:textColor">?attr/colorOnSurface</item>
        <item name="android:fontFamily">sans-serif-medium</item>
    </style><style name="App.Text.Body" parent="@style/TextAppearance.Material3.BodyMedium">
        <item name="android:textColor">?attr/colorOnSurfaceVariant</item>
    </style><style name="App.Toolbar" parent="Widget.Material3.Toolbar">
        <item name="titleTextColor">?attr/colorOnSurface</item>
        <item name="subtitleTextColor">?attr/colorOnSurfaceVariant</item>
    </style><style name="App.FloatingActionButton" parent="Widget.Material3.FloatingActionButton.Primary">
        <item name="backgroundTint">?attr/colorPrimaryContainer</item>
        <item name="tint">?attr/colorOnPrimaryContainer</item>
        <item name="rippleColor">?attr/colorPrimary</item>
    </style><style name="App.TabLayout.Rounded" parent="Widget.Material3.TabLayout">
        <item name="android:background">@drawable/tab_layout_background</item>
        <item name="tabIndicatorColor">@color/md_theme_light_primary</item>
        <item name="tabSelectedTextColor">@color/md_theme_light_primary</item>
        <item name="tabTextColor">@color/md_theme_light_onSurfaceVariant</item>
        <item name="tabIndicatorHeight">2dp</item>
        <item name="tabTextAppearance">@style/App.TabLayout.TextAppearance</item>
    </style><style name="App.TabLayout.TextAppearance" parent="TextAppearance.Material3.LabelMedium">
        <item name="android:textSize">13sp</item>
        <item name="android:fontFamily">sans-serif-medium</item>
        <item name="textAllCaps">false</item>
    </style></file><file path="D:\Kasumi\Kasumi\app\src\main\res\values\themes.xml" qualifiers=""><style name="Theme.Kasumi" parent="Theme.Material3.DayNight.NoActionBar">
        
        <item name="colorPrimary">@color/md_theme_light_primary</item>
        <item name="colorOnPrimary">@color/md_theme_light_onPrimary</item>
        <item name="colorPrimaryContainer">@color/md_theme_light_primaryContainer</item>
        <item name="colorOnPrimaryContainer">@color/md_theme_light_onPrimaryContainer</item>
        <item name="colorSecondary">@color/md_theme_light_secondary</item>
        <item name="colorOnSecondary">@color/md_theme_light_onSecondary</item>
        <item name="colorSecondaryContainer">@color/md_theme_light_secondaryContainer</item>
        <item name="colorOnSecondaryContainer">@color/md_theme_light_onSecondaryContainer</item>
        <item name="colorTertiary">@color/md_theme_light_tertiary</item>
        <item name="colorOnTertiary">@color/md_theme_light_onTertiary</item>
        <item name="colorTertiaryContainer">@color/md_theme_light_tertiaryContainer</item>
        <item name="colorOnTertiaryContainer">@color/md_theme_light_onTertiaryContainer</item>
        <item name="colorError">@color/md_theme_light_error</item>
        <item name="colorErrorContainer">@color/md_theme_light_errorContainer</item>
        <item name="colorOnError">@color/md_theme_light_onError</item>
        <item name="colorOnErrorContainer">@color/md_theme_light_onErrorContainer</item>
        <item name="android:colorBackground">@color/md_theme_light_background</item>
        <item name="colorOnBackground">@color/md_theme_light_onBackground</item>
        <item name="colorSurface">@color/md_theme_light_surface</item>
        <item name="colorOnSurface">@color/md_theme_light_onSurface</item>
        <item name="colorSurfaceVariant">@color/md_theme_light_surfaceVariant</item>
        <item name="colorOnSurfaceVariant">@color/md_theme_light_onSurfaceVariant</item>
        <item name="colorOutline">@color/md_theme_light_outline</item>
        <item name="colorOnSurfaceInverse">@color/md_theme_light_inverseOnSurface</item>
        <item name="colorSurfaceInverse">@color/md_theme_light_inverseSurface</item>
        <item name="colorPrimaryInverse">@color/md_theme_light_inversePrimary</item>

        
        <item name="android:statusBarColor">@android:color/transparent</item>
        <item name="android:navigationBarColor">@android:color/transparent</item>
        <item name="android:windowLightStatusBar">true</item>
        <item name="android:windowLightNavigationBar" ns1:targetApi="o_mr1">true</item>

        
        <item name="android:windowDrawsSystemBarBackgrounds">true</item>
        <item name="android:enforceStatusBarContrast" ns1:targetApi="q">false</item>
        <item name="android:enforceNavigationBarContrast" ns1:targetApi="q">false</item>
    </style><style name="Theme.Kasumi.FloatingWindow" parent="Theme.MaterialComponents.DayNight.NoActionBar">
        
        <item name="colorPrimary">@color/md_theme_light_primary</item>
        <item name="colorPrimaryDark">@color/md_theme_light_primary</item>
        <item name="colorAccent">@color/md_theme_light_secondary</item>
        <item name="colorSecondary">@color/md_theme_light_secondary</item>
        <item name="android:colorBackground">@color/md_theme_light_background</item>
        <item name="colorSurface">@color/md_theme_light_surface</item>
        <item name="colorOnSurface">@color/md_theme_light_onSurface</item>
        <item name="colorOnPrimary">@color/md_theme_light_onPrimary</item>
        <item name="colorOnSecondary">@color/md_theme_light_onSecondary</item>

        
        <item name="materialCardViewStyle">@style/Widget.MaterialComponents.CardView</item>
    </style></file><file path="D:\Kasumi\Kasumi\app\src\main\res\values-night\themes.xml" qualifiers="night-v8"><style name="Theme.Kasumi" parent="Theme.Material3.DayNight.NoActionBar">
        
        <item name="colorPrimary">@color/md_theme_dark_primary</item>
        <item name="colorOnPrimary">@color/md_theme_dark_onPrimary</item>
        <item name="colorPrimaryContainer">@color/md_theme_dark_primaryContainer</item>
        <item name="colorOnPrimaryContainer">@color/md_theme_dark_onPrimaryContainer</item>
        <item name="colorSecondary">@color/md_theme_dark_secondary</item>
        <item name="colorOnSecondary">@color/md_theme_dark_onSecondary</item>
        <item name="colorSecondaryContainer">@color/md_theme_dark_secondaryContainer</item>
        <item name="colorOnSecondaryContainer">@color/md_theme_dark_onSecondaryContainer</item>
        <item name="colorTertiary">@color/md_theme_dark_tertiary</item>
        <item name="colorOnTertiary">@color/md_theme_dark_onTertiary</item>
        <item name="colorTertiaryContainer">@color/md_theme_dark_tertiaryContainer</item>
        <item name="colorOnTertiaryContainer">@color/md_theme_dark_onTertiaryContainer</item>
        <item name="colorError">@color/md_theme_dark_error</item>
        <item name="colorErrorContainer">@color/md_theme_dark_errorContainer</item>
        <item name="colorOnError">@color/md_theme_dark_onError</item>
        <item name="colorOnErrorContainer">@color/md_theme_dark_onErrorContainer</item>
        <item name="android:colorBackground">@color/md_theme_dark_background</item>
        <item name="colorOnBackground">@color/md_theme_dark_onBackground</item>
        <item name="colorSurface">@color/md_theme_dark_surface</item>
        <item name="colorOnSurface">@color/md_theme_dark_onSurface</item>
        <item name="colorSurfaceVariant">@color/md_theme_dark_surfaceVariant</item>
        <item name="colorOnSurfaceVariant">@color/md_theme_dark_onSurfaceVariant</item>
        <item name="colorOutline">@color/md_theme_dark_outline</item>
        <item name="colorOnSurfaceInverse">@color/md_theme_dark_inverseOnSurface</item>
        <item name="colorSurfaceInverse">@color/md_theme_dark_inverseSurface</item>
        <item name="colorPrimaryInverse">@color/md_theme_dark_inversePrimary</item>

        
        <item name="android:statusBarColor">@android:color/transparent</item>
        <item name="android:navigationBarColor">@android:color/transparent</item>
        <item name="android:windowLightStatusBar">false</item>
        <item name="android:windowLightNavigationBar" ns1:targetApi="o_mr1">false</item>

        
        <item name="android:windowDrawsSystemBarBackgrounds">true</item>
        <item name="android:enforceStatusBarContrast" ns1:targetApi="q">false</item>
        <item name="android:enforceNavigationBarContrast" ns1:targetApi="q">false</item>
    </style></file><file name="backup_rules" path="D:\Kasumi\Kasumi\app\src\main\res\xml\backup_rules.xml" qualifiers="" type="xml"/><file name="data_extraction_rules" path="D:\Kasumi\Kasumi\app\src\main\res\xml\data_extraction_rules.xml" qualifiers="" type="xml"/><file name="ic_circle" path="D:\Kasumi\Kasumi\app\src\main\res\drawable\ic_circle.xml" qualifiers="" type="drawable"/><file name="ic_export_24" path="D:\Kasumi\Kasumi\app\src\main\res\drawable\ic_export_24.xml" qualifiers="" type="drawable"/><file name="ic_import_24" path="D:\Kasumi\Kasumi\app\src\main\res\drawable\ic_import_24.xml" qualifiers="" type="drawable"/></source></dataSet><dataSet aapt-namespace="http://schemas.android.com/apk/res-auto" config="debug$Generated" generated="true" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="D:\Kasumi\Kasumi\app\src\debug\res"/></dataSet><dataSet aapt-namespace="http://schemas.android.com/apk/res-auto" config="debug" generated-set="debug$Generated" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="D:\Kasumi\Kasumi\app\src\debug\res"/></dataSet><dataSet aapt-namespace="http://schemas.android.com/apk/res-auto" config="generated$Generated" generated="true" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="D:\Kasumi\Kasumi\app\build\generated\res\resValues\debug"/></dataSet><dataSet aapt-namespace="http://schemas.android.com/apk/res-auto" config="generated" generated-set="generated$Generated" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="D:\Kasumi\Kasumi\app\build\generated\res\resValues\debug"/></dataSet><mergedItems/></merger>