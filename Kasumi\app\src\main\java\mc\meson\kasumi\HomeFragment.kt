package mc.meson.kasumi

import android.os.Bundle
import android.util.Log
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import androidx.fragment.app.Fragment
import com.google.android.material.snackbar.Snackbar
import mc.meson.kasumi.databinding.FragmentHomeBinding
import mc.meson.kasumi.overlay.FloatingWindowService
import mc.meson.kasumi.overlay.OverlayPermissionHelper

class HomeFragment : Fragment() {

    private var _binding: FragmentHomeBinding? = null
    private val binding get() = _binding!!
    private lateinit var overlayPermissionHelper: OverlayPermissionHelper

    override fun onCreateView(
        inflater: LayoutInflater,
        container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View {
        _binding = FragmentHomeBinding.inflate(inflater, container, false)
        return binding.root
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)

        overlayPermissionHelper = OverlayPermissionHelper(requireActivity())

        setupUI()
        setupAnimations()
        setupClickListeners()
    }

    private fun setupUI() {
        // Update welcome text
        binding.welcomeText.text = getString(R.string.welcome_description)

        // Set initial status
        updateConnectionStatus(true)
    }

    private fun setupAnimations() {
        // Simple fade in animation for the entire view
        // Only animate on first load, not on navigation
        if (binding.root.alpha == 0f) {
            binding.root.alpha = 0f
            binding.root.animate()
                .alpha(1f)
                .setDuration(300)
                .setStartDelay(50)
                .start()
        }
    }

    private fun setupClickListeners() {
        // 悬浮窗按钮
        binding.btnFloatingWindow.setOnClickListener {
            animateButton(it)
            showFloatingWindowOptions()
        }

        // 横屏按钮
        binding.btnLandscape.setOnClickListener {
            animateButton(it)
            requireActivity().requestedOrientation = android.content.pm.ActivityInfo.SCREEN_ORIENTATION_LANDSCAPE
            showSnackbar("已切换到横屏模式")
        }
    }

    private fun animateButton(button: View) {
        button.animate()
            .scaleX(0.95f)
            .scaleY(0.95f)
            .setDuration(100)
            .withEndAction {
                button.animate()
                    .scaleX(1.0f)
                    .scaleY(1.0f)
                    .setDuration(100)
                    .start()
            }
            .start()
    }

    private fun showFloatingWindowOptions() {
        try {
            Log.d(TAG, "Showing floating window options")
            OverlayPermissionHelper.showPermissionExplanationDialog(requireActivity()) {
                requestFloatingWindowPermission()
            }
        } catch (e: Exception) {
            Log.e(TAG, "Error showing floating window options", e)
            showSnackbar("启动悬浮窗时发生错误")
        }
    }

    private fun requestFloatingWindowPermission() {
        try {
            Log.d(TAG, "Requesting floating window permission")
            overlayPermissionHelper.requestOverlayPermission { granted ->
                try {
                    if (granted) {
                        Log.d(TAG, "Permission granted, starting floating window service")
                        FloatingWindowService.startService(requireContext())
                        showSnackbar("悬浮窗已启动！可以在游戏中使用了")
                    } else {
                        Log.w(TAG, "Permission denied")
                        showSnackbar("需要悬浮窗权限才能使用此功能")
                    }
                } catch (e: Exception) {
                    Log.e(TAG, "Error handling permission result", e)
                    showSnackbar("启动悬浮窗时发生错误")
                }
            }
        } catch (e: Exception) {
            Log.e(TAG, "Error requesting floating window permission", e)
            showSnackbar("请求权限时发生错误")
        }
    }

    private fun updateConnectionStatus(connected: Boolean) {
        if (connected) {
            binding.statusText.text = getString(R.string.connected)
            binding.statusIndicator.setBackgroundResource(R.drawable.status_indicator)
        } else {
            binding.statusText.text = getString(R.string.disconnected)
            binding.statusIndicator.setBackgroundColor(requireContext().getColor(R.color.minecraft_red))
        }
    }

    private fun showSnackbar(message: String) {
        view?.let { v ->
            Snackbar.make(v, message, Snackbar.LENGTH_SHORT)
                .setBackgroundTint(requireContext().getColor(R.color.md_theme_light_primaryContainer))
                .setTextColor(requireContext().getColor(R.color.md_theme_light_onPrimaryContainer))
                .show()
        }
    }

    companion object {
        private const val TAG = "HomeFragment"
    }

    override fun onDestroyView() {
        super.onDestroyView()
        _binding = null
    }
}
