<?xml version="1.0" encoding="utf-8" standalone="yes"?><Layout layout="item_config" modulePackage="mc.meson.kasumi" filePath="app\src\main\res\layout\item_config.xml" directory="layout" isMerge="false" isBindingData="false" rootNodeType="com.google.android.material.card.MaterialCardView" rootNodeViewId="@+id/cardConfig"><Targets><Target id="@+id/cardConfig" tag="layout/item_config_0" view="com.google.android.material.card.MaterialCardView"><Expressions/><location startLine="1" startOffset="0" endLine="136" endOffset="51"/></Target><Target id="@+id/tvConfigName" view="TextView"><Expressions/><location startLine="33" startOffset="16" endLine="43" endOffset="45"/></Target><Target id="@+id/tvConfigStats" view="TextView"><Expressions/><location startLine="45" startOffset="16" endLine="53" endOffset="54"/></Target><Target id="@+id/tvConfigDescription" view="TextView"><Expressions/><location startLine="65" startOffset="16" endLine="74" endOffset="45"/></Target><Target id="@+id/tvConfigTime" view="TextView"><Expressions/><location startLine="76" startOffset="16" endLine="83" endOffset="54"/></Target><Target id="@+id/ivCurrentIndicator" view="ImageView"><Expressions/><location startLine="88" startOffset="12" endLine="92" endOffset="43"/></Target><Target id="@+id/btnEditConfig" view="ImageButton"><Expressions/><location startLine="103" startOffset="12" endLine="110" endOffset="56"/></Target><Target id="@+id/btnExportConfig" view="ImageButton"><Expressions/><location startLine="112" startOffset="12" endLine="120" endOffset="50"/></Target><Target id="@+id/btnDeleteConfig" view="ImageButton"><Expressions/><location startLine="122" startOffset="12" endLine="130" endOffset="50"/></Target></Targets></Layout>