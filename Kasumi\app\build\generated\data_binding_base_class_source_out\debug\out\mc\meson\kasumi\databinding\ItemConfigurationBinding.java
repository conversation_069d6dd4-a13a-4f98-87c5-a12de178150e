// Generated by view binder compiler. Do not edit!
package mc.meson.kasumi.databinding;

import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.TextView;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.viewbinding.ViewBinding;
import androidx.viewbinding.ViewBindings;
import com.google.android.material.button.MaterialButton;
import com.google.android.material.card.MaterialCardView;
import java.lang.NullPointerException;
import java.lang.Override;
import java.lang.String;
import mc.meson.kasumi.R;

public final class ItemConfigurationBinding implements ViewBinding {
  @NonNull
  private final MaterialCardView rootView;

  @NonNull
  public final MaterialButton btnApplyConfig;

  @NonNull
  public final MaterialButton btnConfigMenu;

  @NonNull
  public final TextView tvConfigDescription;

  @NonNull
  public final TextView tvConfigName;

  @NonNull
  public final TextView tvConfigTime;

  @NonNull
  public final TextView tvCurrentIndicator;

  private ItemConfigurationBinding(@NonNull MaterialCardView rootView,
      @NonNull MaterialButton btnApplyConfig, @NonNull MaterialButton btnConfigMenu,
      @NonNull TextView tvConfigDescription, @NonNull TextView tvConfigName,
      @NonNull TextView tvConfigTime, @NonNull TextView tvCurrentIndicator) {
    this.rootView = rootView;
    this.btnApplyConfig = btnApplyConfig;
    this.btnConfigMenu = btnConfigMenu;
    this.tvConfigDescription = tvConfigDescription;
    this.tvConfigName = tvConfigName;
    this.tvConfigTime = tvConfigTime;
    this.tvCurrentIndicator = tvCurrentIndicator;
  }

  @Override
  @NonNull
  public MaterialCardView getRoot() {
    return rootView;
  }

  @NonNull
  public static ItemConfigurationBinding inflate(@NonNull LayoutInflater inflater) {
    return inflate(inflater, null, false);
  }

  @NonNull
  public static ItemConfigurationBinding inflate(@NonNull LayoutInflater inflater,
      @Nullable ViewGroup parent, boolean attachToParent) {
    View root = inflater.inflate(R.layout.item_configuration, parent, false);
    if (attachToParent) {
      parent.addView(root);
    }
    return bind(root);
  }

  @NonNull
  public static ItemConfigurationBinding bind(@NonNull View rootView) {
    // The body of this method is generated in a way you would not otherwise write.
    // This is done to optimize the compiled bytecode for size and performance.
    int id;
    missingId: {
      id = R.id.btnApplyConfig;
      MaterialButton btnApplyConfig = ViewBindings.findChildViewById(rootView, id);
      if (btnApplyConfig == null) {
        break missingId;
      }

      id = R.id.btnConfigMenu;
      MaterialButton btnConfigMenu = ViewBindings.findChildViewById(rootView, id);
      if (btnConfigMenu == null) {
        break missingId;
      }

      id = R.id.tvConfigDescription;
      TextView tvConfigDescription = ViewBindings.findChildViewById(rootView, id);
      if (tvConfigDescription == null) {
        break missingId;
      }

      id = R.id.tvConfigName;
      TextView tvConfigName = ViewBindings.findChildViewById(rootView, id);
      if (tvConfigName == null) {
        break missingId;
      }

      id = R.id.tvConfigTime;
      TextView tvConfigTime = ViewBindings.findChildViewById(rootView, id);
      if (tvConfigTime == null) {
        break missingId;
      }

      id = R.id.tvCurrentIndicator;
      TextView tvCurrentIndicator = ViewBindings.findChildViewById(rootView, id);
      if (tvCurrentIndicator == null) {
        break missingId;
      }

      return new ItemConfigurationBinding((MaterialCardView) rootView, btnApplyConfig,
          btnConfigMenu, tvConfigDescription, tvConfigName, tvConfigTime, tvCurrentIndicator);
    }
    String missingId = rootView.getResources().getResourceName(id);
    throw new NullPointerException("Missing required view with ID: ".concat(missingId));
  }
}
