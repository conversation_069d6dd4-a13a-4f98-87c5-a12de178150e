{"logs": [{"outputFile": "mc.meson.kasumi.app-mergeDebugResources-47:/values-pt-rBR/values-pt-rBR.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-4\\c6de447ab1a7b632fe56ac0b139a6541\\transformed\\navigation-ui-2.7.7\\res\\values-pt-rBR\\values-pt-rBR.xml", "from": {"startLines": "2,3", "startColumns": "4,4", "startOffsets": "55,167", "endColumns": "111,119", "endOffsets": "162,282"}, "to": {"startLines": "111,112", "startColumns": "4,4", "startOffsets": "9579,9691", "endColumns": "111,119", "endOffsets": "9686,9806"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-4\\c1f1d66677a2054cc4b3d0f2744c5c9f\\transformed\\material-1.12.0\\res\\values-pt-rBR\\values-pt-rBR.xml", "from": {"startLines": "2,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "100,273,354,432,516,611,700,801,921,1002,1062,1126,1218,1297,1357,1447,1511,1582,1645,1720,1784,1838,1965,2023,2085,2139,2218,2359,2446,2522,2617,2698,2780,2919,3002,3086,3225,3312,3392,3448,3499,3565,3639,3719,3790,3873,3946,4023,4092,4166,4268,4356,4433,4526,4622,4696,4776,4873,4925,5009,5075,5162,5250,5312,5376,5439,5507,5616,5727,5831,5941,6001,6056,6133,6216,6293", "endLines": "5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80", "endColumns": "12,80,77,83,94,88,100,119,80,59,63,91,78,59,89,63,70,62,74,63,53,126,57,61,53,78,140,86,75,94,80,81,138,82,83,138,86,79,55,50,65,73,79,70,82,72,76,68,73,101,87,76,92,95,73,79,96,51,83,65,86,87,61,63,62,67,108,110,103,109,59,54,76,82,76,78", "endOffsets": "268,349,427,511,606,695,796,916,997,1057,1121,1213,1292,1352,1442,1506,1577,1640,1715,1779,1833,1960,2018,2080,2134,2213,2354,2441,2517,2612,2693,2775,2914,2997,3081,3220,3307,3387,3443,3494,3560,3634,3714,3785,3868,3941,4018,4087,4161,4263,4351,4428,4521,4617,4691,4771,4868,4920,5004,5070,5157,5245,5307,5371,5434,5502,5611,5722,5826,5936,5996,6051,6128,6211,6288,6367"}, "to": {"startLines": "2,33,34,35,36,37,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,113,115,116,117", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "150,3061,3142,3220,3304,3399,4223,4324,4444,4525,4585,4649,4741,4820,4880,4970,5034,5105,5168,5243,5307,5361,5488,5546,5608,5662,5741,5882,5969,6045,6140,6221,6303,6442,6525,6609,6748,6835,6915,6971,7022,7088,7162,7242,7313,7396,7469,7546,7615,7689,7791,7879,7956,8049,8145,8219,8299,8396,8448,8532,8598,8685,8773,8835,8899,8962,9030,9139,9250,9354,9464,9524,9811,9974,10057,10134", "endLines": "5,33,34,35,36,37,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,113,115,116,117", "endColumns": "12,80,77,83,94,88,100,119,80,59,63,91,78,59,89,63,70,62,74,63,53,126,57,61,53,78,140,86,75,94,80,81,138,82,83,138,86,79,55,50,65,73,79,70,82,72,76,68,73,101,87,76,92,95,73,79,96,51,83,65,86,87,61,63,62,67,108,110,103,109,59,54,76,82,76,78", "endOffsets": "318,3137,3215,3299,3394,3483,4319,4439,4520,4580,4644,4736,4815,4875,4965,5029,5100,5163,5238,5302,5356,5483,5541,5603,5657,5736,5877,5964,6040,6135,6216,6298,6437,6520,6604,6743,6830,6910,6966,7017,7083,7157,7237,7308,7391,7464,7541,7610,7684,7786,7874,7951,8044,8140,8214,8294,8391,8443,8527,8593,8680,8768,8830,8894,8957,9025,9134,9245,9349,9459,9519,9574,9883,10052,10129,10208"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-4\\588763d874820f4a3b3fefea157e8181\\transformed\\core-1.13.1\\res\\values-pt-rBR\\values-pt-rBR.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,152,254,353,453,560,670,790", "endColumns": "96,101,98,99,106,109,119,100", "endOffsets": "147,249,348,448,555,665,785,886"}, "to": {"startLines": "38,39,40,41,42,43,44,118", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "3488,3585,3687,3786,3886,3993,4103,10213", "endColumns": "96,101,98,99,106,109,119,100", "endOffsets": "3580,3682,3781,3881,3988,4098,4218,10309"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-4\\f3bee05a55d0be5267577c93938c9880\\transformed\\appcompat-1.7.1\\res\\values-pt-rBR\\values-pt-rBR.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,225,331,438,527,628,747,832,912,1003,1096,1191,1285,1385,1478,1573,1668,1759,1850,1935,2042,2153,2255,2363,2471,2581,2743,2843", "endColumns": "119,105,106,88,100,118,84,79,90,92,94,93,99,92,94,94,90,90,84,106,110,101,107,107,109,161,99,85", "endOffsets": "220,326,433,522,623,742,827,907,998,1091,1186,1280,1380,1473,1568,1663,1754,1845,1930,2037,2148,2250,2358,2466,2576,2738,2838,2924"}, "to": {"startLines": "6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,114", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "323,443,549,656,745,846,965,1050,1130,1221,1314,1409,1503,1603,1696,1791,1886,1977,2068,2153,2260,2371,2473,2581,2689,2799,2961,9888", "endColumns": "119,105,106,88,100,118,84,79,90,92,94,93,99,92,94,94,90,90,84,106,110,101,107,107,109,161,99,85", "endOffsets": "438,544,651,740,841,960,1045,1125,1216,1309,1404,1498,1598,1691,1786,1881,1972,2063,2148,2255,2366,2468,2576,2684,2794,2956,3056,9969"}}]}]}